/**
 * Visual Asset Manager
 * Centralized management system for organizing, storing, and retrieving
 * all visual assets including wireframes, diagrams, and interactive elements
 */

import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';

/**
 * Visual Asset Manager
 */
export class VisualAssetManager {
  constructor(config = {}) {
    this.config = {
      baseDirectory: config.baseDirectory || '.guidant/deliverables',
      enableVersioning: config.enableVersioning !== false,
      enableMetadata: config.enableMetadata !== false,
      enableSearch: config.enableSearch !== false,
      enableBackup: config.enableBackup || false,
      maxVersions: config.maxVersions || 10,
      compressionEnabled: config.compressionEnabled || false,
      ...config
    };

    this.assetRegistry = new Map();
    this.versionHistory = new Map();
    this.searchIndex = new Map();
    this.metadata = new Map();

    this.assetTypes = {
      wireframe: {
        extension: '.txt',
        directory: 'wireframes',
        mimeType: 'text/plain'
      },
      mermaid: {
        extension: '.mmd',
        directory: 'user-flows',
        mimeType: 'text/plain'
      },
      interactive: {
        extension: '.html',
        directory: 'interactive',
        mimeType: 'text/html'
      },
      comparison: {
        extension: '.md',
        directory: 'comparisons',
        mimeType: 'text/markdown'
      },
      javascript: {
        extension: '.js',
        directory: 'helpers',
        mimeType: 'application/javascript'
      }
    };

    this.managementStats = {
      totalAssets: 0,
      assetsByType: {},
      storageUsed: 0,
      versionsManaged: 0,
      searchQueries: 0
    };
  }

  /**
   * Store visual asset with metadata and versioning
   */
  async storeAsset(assetData, assetType, metadata = {}) {
    try {
      const assetId = this.generateAssetId(assetData, assetType, metadata);
      const assetConfig = this.assetTypes[assetType];
      
      if (!assetConfig) {
        throw new Error(`Unknown asset type: ${assetType}`);
      }

      // Prepare asset metadata
      const assetMetadata = {
        id: assetId,
        type: assetType,
        createdAt: new Date().toISOString(),
        size: this.calculateSize(assetData),
        checksum: this.calculateChecksum(assetData),
        version: 1,
        ...metadata
      };

      // Check for existing asset
      const existingAsset = this.assetRegistry.get(assetId);
      if (existingAsset && this.config.enableVersioning) {
        assetMetadata.version = existingAsset.version + 1;
        await this.createVersion(assetId, existingAsset);
      }

      // Store asset to filesystem
      const filePath = await this.writeAssetToFile(assetData, assetType, assetMetadata);
      assetMetadata.filePath = filePath;

      // Update registry
      this.assetRegistry.set(assetId, assetMetadata);
      
      // Update metadata if enabled
      if (this.config.enableMetadata) {
        this.metadata.set(assetId, assetMetadata);
      }

      // Update search index if enabled
      if (this.config.enableSearch) {
        this.updateSearchIndex(assetId, assetData, assetMetadata);
      }

      // Update statistics
      this.updateStats(assetType, assetMetadata.size);

      console.log(`✅ Stored ${assetType} asset: ${assetId} (v${assetMetadata.version})`);
      
      return {
        success: true,
        assetId,
        version: assetMetadata.version,
        filePath,
        metadata: assetMetadata
      };

    } catch (error) {
      console.error('Failed to store asset:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Retrieve visual asset by ID
   */
  async retrieveAsset(assetId, version = null) {
    try {
      const assetMetadata = this.assetRegistry.get(assetId);
      if (!assetMetadata) {
        throw new Error(`Asset not found: ${assetId}`);
      }

      let targetMetadata = assetMetadata;
      
      // Handle version retrieval
      if (version !== null && this.config.enableVersioning) {
        const versionHistory = this.versionHistory.get(assetId) || [];
        const versionData = versionHistory.find(v => v.version === version);
        if (versionData) {
          targetMetadata = versionData;
        } else {
          throw new Error(`Version ${version} not found for asset ${assetId}`);
        }
      }

      // Read asset from filesystem
      const assetData = await this.readAssetFromFile(targetMetadata.filePath);

      return {
        success: true,
        assetId,
        version: targetMetadata.version,
        data: assetData,
        metadata: targetMetadata
      };

    } catch (error) {
      console.error('Failed to retrieve asset:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Search assets by criteria
   */
  async searchAssets(criteria = {}) {
    if (!this.config.enableSearch) {
      throw new Error('Search functionality is disabled');
    }

    this.managementStats.searchQueries++;

    const results = [];
    
    for (const [assetId, metadata] of this.assetRegistry) {
      let matches = true;

      // Filter by type
      if (criteria.type && metadata.type !== criteria.type) {
        matches = false;
      }

      // Filter by date range
      if (criteria.dateFrom && new Date(metadata.createdAt) < new Date(criteria.dateFrom)) {
        matches = false;
      }
      if (criteria.dateTo && new Date(metadata.createdAt) > new Date(criteria.dateTo)) {
        matches = false;
      }

      // Filter by size
      if (criteria.minSize && metadata.size < criteria.minSize) {
        matches = false;
      }
      if (criteria.maxSize && metadata.size > criteria.maxSize) {
        matches = false;
      }

      // Text search in content
      if (criteria.searchText && this.searchIndex.has(assetId)) {
        const searchContent = this.searchIndex.get(assetId);
        if (!searchContent.toLowerCase().includes(criteria.searchText.toLowerCase())) {
          matches = false;
        }
      }

      // Custom metadata filters
      if (criteria.metadata) {
        for (const [key, value] of Object.entries(criteria.metadata)) {
          if (metadata[key] !== value) {
            matches = false;
            break;
          }
        }
      }

      if (matches) {
        results.push({
          assetId,
          metadata,
          relevanceScore: this.calculateRelevanceScore(metadata, criteria)
        });
      }
    }

    // Sort by relevance score
    results.sort((a, b) => b.relevanceScore - a.relevanceScore);

    return {
      success: true,
      results,
      totalFound: results.length,
      searchCriteria: criteria
    };
  }

  /**
   * List all assets with optional filtering
   */
  async listAssets(options = {}) {
    const assets = [];
    
    for (const [assetId, metadata] of this.assetRegistry) {
      // Apply filters
      if (options.type && metadata.type !== options.type) {
        continue;
      }
      
      if (options.limit && assets.length >= options.limit) {
        break;
      }

      assets.push({
        assetId,
        type: metadata.type,
        version: metadata.version,
        createdAt: metadata.createdAt,
        size: metadata.size,
        filePath: metadata.filePath
      });
    }

    // Sort by creation date (newest first)
    assets.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    return {
      success: true,
      assets,
      totalAssets: this.assetRegistry.size,
      filteredCount: assets.length
    };
  }

  /**
   * Delete asset and its versions
   */
  async deleteAsset(assetId, deleteVersions = true) {
    try {
      const assetMetadata = this.assetRegistry.get(assetId);
      if (!assetMetadata) {
        throw new Error(`Asset not found: ${assetId}`);
      }

      // Delete main asset file
      await this.deleteAssetFile(assetMetadata.filePath);

      // Delete versions if requested
      if (deleteVersions && this.config.enableVersioning) {
        const versionHistory = this.versionHistory.get(assetId) || [];
        for (const version of versionHistory) {
          await this.deleteAssetFile(version.filePath);
        }
        this.versionHistory.delete(assetId);
      }

      // Remove from registries
      this.assetRegistry.delete(assetId);
      this.metadata.delete(assetId);
      this.searchIndex.delete(assetId);

      // Update statistics
      this.managementStats.totalAssets--;
      this.managementStats.assetsByType[assetMetadata.type]--;
      this.managementStats.storageUsed -= assetMetadata.size;

      console.log(`✅ Deleted asset: ${assetId}`);
      
      return {
        success: true,
        assetId,
        deletedVersions: deleteVersions
      };

    } catch (error) {
      console.error('Failed to delete asset:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get asset versions
   */
  async getAssetVersions(assetId) {
    if (!this.config.enableVersioning) {
      throw new Error('Versioning is disabled');
    }

    const currentAsset = this.assetRegistry.get(assetId);
    if (!currentAsset) {
      throw new Error(`Asset not found: ${assetId}`);
    }

    const versions = [
      {
        version: currentAsset.version,
        createdAt: currentAsset.createdAt,
        size: currentAsset.size,
        isCurrent: true
      }
    ];

    const versionHistory = this.versionHistory.get(assetId) || [];
    for (const version of versionHistory) {
      versions.push({
        version: version.version,
        createdAt: version.createdAt,
        size: version.size,
        isCurrent: false
      });
    }

    // Sort by version number (newest first)
    versions.sort((a, b) => b.version - a.version);

    return {
      success: true,
      assetId,
      versions,
      totalVersions: versions.length
    };
  }

  /**
   * Export assets to archive
   */
  async exportAssets(exportPath, criteria = {}) {
    try {
      const searchResult = await this.searchAssets(criteria);
      const assetsToExport = searchResult.results;

      // Create export directory
      await fs.mkdir(exportPath, { recursive: true });

      const exportManifest = {
        exportedAt: new Date().toISOString(),
        totalAssets: assetsToExport.length,
        criteria,
        assets: []
      };

      for (const { assetId, metadata } of assetsToExport) {
        const assetResult = await this.retrieveAsset(assetId);
        if (assetResult.success) {
          const exportFilename = `${assetId}_v${metadata.version}${this.assetTypes[metadata.type].extension}`;
          const exportFilePath = path.join(exportPath, exportFilename);
          
          await fs.writeFile(exportFilePath, assetResult.data, 'utf8');
          
          exportManifest.assets.push({
            assetId,
            originalPath: metadata.filePath,
            exportPath: exportFilePath,
            metadata
          });
        }
      }

      // Write export manifest
      const manifestPath = path.join(exportPath, 'export-manifest.json');
      await fs.writeFile(manifestPath, JSON.stringify(exportManifest, null, 2));

      console.log(`✅ Exported ${assetsToExport.length} assets to ${exportPath}`);
      
      return {
        success: true,
        exportPath,
        assetsExported: assetsToExport.length,
        manifestPath
      };

    } catch (error) {
      console.error('Failed to export assets:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get management statistics
   */
  getManagementStats() {
    return {
      ...this.managementStats,
      storageUsedMB: (this.managementStats.storageUsed / 1024 / 1024).toFixed(2),
      averageAssetSize: this.managementStats.totalAssets > 0 ? 
        Math.round(this.managementStats.storageUsed / this.managementStats.totalAssets) : 0,
      registrySize: this.assetRegistry.size,
      searchIndexSize: this.searchIndex.size,
      versionHistorySize: this.versionHistory.size
    };
  }

  // Helper methods
  generateAssetId(assetData, assetType, metadata) {
    const content = `${assetType}_${JSON.stringify(metadata)}_${assetData}`;
    return crypto.createHash('md5').update(content).digest('hex').substring(0, 16);
  }

  calculateSize(data) {
    return Buffer.byteLength(data, 'utf8');
  }

  calculateChecksum(data) {
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  async writeAssetToFile(assetData, assetType, metadata) {
    const assetConfig = this.assetTypes[assetType];
    const directory = path.join(this.config.baseDirectory, assetConfig.directory);
    
    await fs.mkdir(directory, { recursive: true });
    
    const filename = `${metadata.id}_v${metadata.version}${assetConfig.extension}`;
    const filePath = path.join(directory, filename);
    
    await fs.writeFile(filePath, assetData, 'utf8');
    
    return filePath;
  }

  async readAssetFromFile(filePath) {
    return await fs.readFile(filePath, 'utf8');
  }

  async deleteAssetFile(filePath) {
    try {
      await fs.unlink(filePath);
    } catch (error) {
      console.warn(`Failed to delete file ${filePath}:`, error.message);
    }
  }

  async createVersion(assetId, currentMetadata) {
    if (!this.versionHistory.has(assetId)) {
      this.versionHistory.set(assetId, []);
    }
    
    const versions = this.versionHistory.get(assetId);
    versions.push({ ...currentMetadata });
    
    // Limit version history
    if (versions.length > this.config.maxVersions) {
      const oldVersion = versions.shift();
      await this.deleteAssetFile(oldVersion.filePath);
    }
    
    this.managementStats.versionsManaged++;
  }

  updateSearchIndex(assetId, assetData, metadata) {
    const searchContent = `${metadata.title || ''} ${metadata.description || ''} ${assetData}`;
    this.searchIndex.set(assetId, searchContent);
  }

  updateStats(assetType, size) {
    this.managementStats.totalAssets++;
    this.managementStats.storageUsed += size;
    
    if (!this.managementStats.assetsByType[assetType]) {
      this.managementStats.assetsByType[assetType] = 0;
    }
    this.managementStats.assetsByType[assetType]++;
  }

  calculateRelevanceScore(metadata, criteria) {
    let score = 1.0;
    
    // Boost score for exact type match
    if (criteria.type && metadata.type === criteria.type) {
      score += 0.5;
    }
    
    // Boost score for recent assets
    const age = Date.now() - new Date(metadata.createdAt).getTime();
    const daysSinceCreation = age / (1000 * 60 * 60 * 24);
    if (daysSinceCreation < 7) {
      score += 0.3;
    }
    
    // Boost score for larger assets (more content)
    if (metadata.size > 1000) {
      score += 0.2;
    }
    
    return score;
  }
}
