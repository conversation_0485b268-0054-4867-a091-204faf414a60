/**
 * Visual Intelligence Cache & Optimization
 * Advanced caching system for research results with intelligent invalidation,
 * result scoring, and API call optimization
 */

import crypto from 'crypto';
import fs from 'fs/promises';
import path from 'path';

/**
 * Research Result Scorer
 * Evaluates and scores research results for cache quality assessment
 */
export class ResearchResultScorer {
  constructor(config = {}) {
    this.config = {
      qualityWeights: {
        confidence: 0.3,
        completeness: 0.25,
        freshness: 0.2,
        relevance: 0.15,
        sourceQuality: 0.1
      },
      ...config
    };
  }

  /**
   * Score research result quality
   */
  scoreResult(result, metadata = {}) {
    const scores = {
      confidence: this.scoreConfidence(result),
      completeness: this.scoreCompleteness(result),
      freshness: this.scoreFreshness(result, metadata),
      relevance: this.scoreRelevance(result, metadata),
      sourceQuality: this.scoreSourceQuality(result, metadata)
    };

    // Calculate weighted total score
    let totalScore = 0;
    for (const [factor, score] of Object.entries(scores)) {
      totalScore += score * (this.config.qualityWeights[factor] || 0);
    }

    return {
      totalScore: Math.min(totalScore, 1),
      breakdown: scores,
      qualityLevel: this.getQualityLevel(totalScore),
      recommendedTTL: this.calculateRecommendedTTL(totalScore, scores)
    };
  }

  /**
   * Score confidence level of result
   */
  scoreConfidence(result) {
    if (result.metadata?.confidence) {
      return result.metadata.confidence;
    }

    // Estimate confidence based on result structure
    let confidence = 0.5;
    
    if (result.bestPractices?.length > 0) confidence += 0.2;
    if (result.recommendations?.length > 0) confidence += 0.2;
    if (result.patterns?.length > 0) confidence += 0.1;
    
    return Math.min(confidence, 1);
  }

  /**
   * Score completeness of result
   */
  scoreCompleteness(result) {
    const expectedFields = ['bestPractices', 'recommendations', 'patterns', 'insights'];
    const presentFields = expectedFields.filter(field => 
      result[field] && Array.isArray(result[field]) && result[field].length > 0
    );
    
    return presentFields.length / expectedFields.length;
  }

  /**
   * Score freshness based on age
   */
  scoreFreshness(result, metadata) {
    const age = Date.now() - (metadata.timestamp || Date.now());
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    return Math.max(0, 1 - (age / maxAge));
  }

  /**
   * Score relevance to request
   */
  scoreRelevance(result, metadata) {
    // Basic relevance scoring - can be enhanced with ML
    if (metadata.requestType && result.componentType) {
      return metadata.requestType === result.componentType ? 1 : 0.7;
    }
    return 0.8; // Default relevance
  }

  /**
   * Score source quality
   */
  scoreSourceQuality(result, metadata) {
    const highQualitySources = ['context7', 'enhanced context7', 'tavily'];
    const provider = result.metadata?.provider?.toLowerCase() || '';
    
    if (highQualitySources.includes(provider)) return 0.9;
    if (provider.includes('research')) return 0.7;
    return 0.5;
  }

  /**
   * Get quality level description
   */
  getQualityLevel(score) {
    if (score >= 0.8) return 'excellent';
    if (score >= 0.6) return 'good';
    if (score >= 0.4) return 'fair';
    return 'poor';
  }

  /**
   * Calculate recommended TTL based on quality
   */
  calculateRecommendedTTL(totalScore, scores) {
    const baseTTL = 3600000; // 1 hour
    const maxTTL = 24 * 60 * 60 * 1000; // 24 hours
    const minTTL = 300000; // 5 minutes

    // High quality results get longer TTL
    let multiplier = 1;
    if (totalScore >= 0.8) multiplier = 4; // 4 hours
    else if (totalScore >= 0.6) multiplier = 2; // 2 hours
    else if (totalScore >= 0.4) multiplier = 1; // 1 hour
    else multiplier = 0.25; // 15 minutes

    // Adjust based on freshness
    if (scores.freshness < 0.5) multiplier *= 0.5;

    return Math.min(Math.max(baseTTL * multiplier, minTTL), maxTTL);
  }
}

/**
 * Cache Invalidation Manager
 * Manages intelligent cache invalidation strategies
 */
export class CacheInvalidationManager {
  constructor(config = {}) {
    this.config = {
      strategies: {
        timeBasedInvalidation: config.timeBasedInvalidation !== false,
        qualityBasedInvalidation: config.qualityBasedInvalidation !== false,
        usageBasedInvalidation: config.usageBasedInvalidation !== false,
        contentBasedInvalidation: config.contentBasedInvalidation !== false
      },
      invalidationThresholds: {
        minQualityScore: config.minQualityScore || 0.3,
        maxAge: config.maxAge || 24 * 60 * 60 * 1000, // 24 hours
        minAccessCount: config.minAccessCount || 2,
        stalenessThreshold: config.stalenessThreshold || 0.2
      },
      ...config
    };

    this.invalidationHistory = new Map();
  }

  /**
   * Check if cache entry should be invalidated
   */
  shouldInvalidate(cacheEntry, accessPattern = {}) {
    const reasons = [];

    // Time-based invalidation
    if (this.config.strategies.timeBasedInvalidation) {
      if (this.isTimeExpired(cacheEntry)) {
        reasons.push('time_expired');
      }
    }

    // Quality-based invalidation
    if (this.config.strategies.qualityBasedInvalidation) {
      if (this.isQualityTooLow(cacheEntry)) {
        reasons.push('quality_too_low');
      }
    }

    // Usage-based invalidation
    if (this.config.strategies.usageBasedInvalidation) {
      if (this.isUnderused(cacheEntry, accessPattern)) {
        reasons.push('underused');
      }
    }

    // Content-based invalidation
    if (this.config.strategies.contentBasedInvalidation) {
      if (this.isContentStale(cacheEntry)) {
        reasons.push('content_stale');
      }
    }

    return {
      shouldInvalidate: reasons.length > 0,
      reasons,
      priority: this.calculateInvalidationPriority(reasons)
    };
  }

  /**
   * Check if entry is time expired
   */
  isTimeExpired(cacheEntry) {
    const age = Date.now() - cacheEntry.timestamp;
    const ttl = cacheEntry.qualityScore?.recommendedTTL || this.config.invalidationThresholds.maxAge;
    return age > ttl;
  }

  /**
   * Check if quality is too low
   */
  isQualityTooLow(cacheEntry) {
    const quality = cacheEntry.qualityScore?.totalScore || 0;
    return quality < this.config.invalidationThresholds.minQualityScore;
  }

  /**
   * Check if entry is underused
   */
  isUnderused(cacheEntry, accessPattern) {
    const accessCount = accessPattern.accessCount || 0;
    const age = Date.now() - cacheEntry.timestamp;
    const ageInHours = age / (60 * 60 * 1000);
    
    // If older than 1 hour and accessed less than threshold, consider underused
    return ageInHours > 1 && accessCount < this.config.invalidationThresholds.minAccessCount;
  }

  /**
   * Check if content is stale
   */
  isContentStale(cacheEntry) {
    const staleness = cacheEntry.qualityScore?.breakdown?.freshness || 1;
    return staleness < this.config.invalidationThresholds.stalenessThreshold;
  }

  /**
   * Calculate invalidation priority
   */
  calculateInvalidationPriority(reasons) {
    const priorityMap = {
      time_expired: 3,
      quality_too_low: 2,
      content_stale: 2,
      underused: 1
    };

    return Math.max(...reasons.map(reason => priorityMap[reason] || 0));
  }

  /**
   * Record invalidation for analytics
   */
  recordInvalidation(cacheKey, reasons) {
    this.invalidationHistory.set(cacheKey, {
      timestamp: Date.now(),
      reasons,
      count: (this.invalidationHistory.get(cacheKey)?.count || 0) + 1
    });
  }

  /**
   * Get invalidation statistics
   */
  getInvalidationStats() {
    const stats = {
      totalInvalidations: this.invalidationHistory.size,
      reasonBreakdown: {},
      averageInvalidationsPerKey: 0
    };

    let totalCount = 0;
    for (const [key, data] of this.invalidationHistory) {
      totalCount += data.count;
      for (const reason of data.reasons) {
        stats.reasonBreakdown[reason] = (stats.reasonBreakdown[reason] || 0) + 1;
      }
    }

    stats.averageInvalidationsPerKey = this.invalidationHistory.size > 0 ? 
      totalCount / this.invalidationHistory.size : 0;

    return stats;
  }
}

/**
 * API Call Optimizer
 * Optimizes API calls through deduplication, batching, and rate limiting
 */
export class APICallOptimizer {
  constructor(config = {}) {
    this.config = {
      enableDeduplication: config.enableDeduplication !== false,
      enableBatching: config.enableBatching !== false,
      enableRateLimiting: config.enableRateLimiting !== false,
      batchSize: config.batchSize || 5,
      batchTimeout: config.batchTimeout || 1000, // 1 second
      rateLimit: config.rateLimit || 10, // requests per minute
      ...config
    };

    this.pendingRequests = new Map();
    this.requestQueue = [];
    this.rateLimitTracker = new Map();
    this.optimizationStats = {
      totalRequests: 0,
      deduplicatedRequests: 0,
      batchedRequests: 0,
      rateLimitedRequests: 0
    };
  }

  /**
   * Optimize API call with deduplication and batching
   */
  async optimizeCall(requestKey, requestFn, context = {}) {
    this.optimizationStats.totalRequests++;

    // Check for deduplication
    if (this.config.enableDeduplication && this.pendingRequests.has(requestKey)) {
      this.optimizationStats.deduplicatedRequests++;
      return await this.pendingRequests.get(requestKey);
    }

    // Check rate limiting
    if (this.config.enableRateLimiting && this.isRateLimited(context.provider)) {
      this.optimizationStats.rateLimitedRequests++;
      await this.waitForRateLimit(context.provider);
    }

    // Execute request with deduplication protection
    const requestPromise = this.executeWithDeduplication(requestKey, requestFn);
    
    return await requestPromise;
  }

  /**
   * Execute request with deduplication protection
   */
  async executeWithDeduplication(requestKey, requestFn) {
    if (this.pendingRequests.has(requestKey)) {
      return await this.pendingRequests.get(requestKey);
    }

    const promise = requestFn().finally(() => {
      this.pendingRequests.delete(requestKey);
    });

    this.pendingRequests.set(requestKey, promise);
    return await promise;
  }

  /**
   * Check if provider is rate limited
   */
  isRateLimited(provider) {
    if (!provider) return false;

    const now = Date.now();
    const windowStart = now - 60000; // 1 minute window
    
    const requests = this.rateLimitTracker.get(provider) || [];
    const recentRequests = requests.filter(timestamp => timestamp > windowStart);
    
    return recentRequests.length >= this.config.rateLimit;
  }

  /**
   * Wait for rate limit to clear
   */
  async waitForRateLimit(provider) {
    const requests = this.rateLimitTracker.get(provider) || [];
    const oldestRequest = Math.min(...requests);
    const waitTime = Math.max(0, 60000 - (Date.now() - oldestRequest));
    
    if (waitTime > 0) {
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  /**
   * Record API call for rate limiting
   */
  recordAPICall(provider) {
    if (!provider) return;

    const now = Date.now();
    const requests = this.rateLimitTracker.get(provider) || [];
    
    // Keep only requests from the last minute
    const recentRequests = requests.filter(timestamp => timestamp > now - 60000);
    recentRequests.push(now);
    
    this.rateLimitTracker.set(provider, recentRequests);
  }

  /**
   * Get optimization statistics
   */
  getOptimizationStats() {
    return {
      ...this.optimizationStats,
      deduplicationRate: this.optimizationStats.totalRequests > 0 ? 
        this.optimizationStats.deduplicatedRequests / this.optimizationStats.totalRequests : 0,
      rateLimitingRate: this.optimizationStats.totalRequests > 0 ? 
        this.optimizationStats.rateLimitedRequests / this.optimizationStats.totalRequests : 0
    };
  }
}

/**
 * Cache Performance Monitor
 * Monitors and analyzes cache performance metrics
 */
export class CachePerformanceMonitor {
  constructor(config = {}) {
    this.config = {
      enableMetrics: config.enableMetrics !== false,
      metricsRetention: config.metricsRetention || 24 * 60 * 60 * 1000, // 24 hours
      performanceThresholds: {
        hitRateThreshold: config.hitRateThreshold || 0.7,
        avgResponseTimeThreshold: config.avgResponseTimeThreshold || 100, // ms
        memoryUsageThreshold: config.memoryUsageThreshold || 100 * 1024 * 1024 // 100MB
      },
      ...config
    };

    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      evictions: 0,
      invalidations: 0,
      responseTimes: [],
      memoryUsage: 0,
      qualityScores: []
    };

    this.performanceHistory = [];
    this.alerts = [];
  }

  /**
   * Record cache hit
   */
  recordHit(responseTime = 0, qualityScore = null) {
    this.metrics.hits++;
    if (responseTime > 0) this.metrics.responseTimes.push(responseTime);
    if (qualityScore !== null) this.metrics.qualityScores.push(qualityScore);
    this.checkPerformanceThresholds();
  }

  /**
   * Record cache miss
   */
  recordMiss(responseTime = 0) {
    this.metrics.misses++;
    if (responseTime > 0) this.metrics.responseTimes.push(responseTime);
    this.checkPerformanceThresholds();
  }

  /**
   * Record cache set operation
   */
  recordSet(memoryDelta = 0) {
    this.metrics.sets++;
    this.metrics.memoryUsage += memoryDelta;
    this.checkPerformanceThresholds();
  }

  /**
   * Record cache eviction
   */
  recordEviction(memoryDelta = 0) {
    this.metrics.evictions++;
    this.metrics.memoryUsage -= memoryDelta;
  }

  /**
   * Record cache invalidation
   */
  recordInvalidation(memoryDelta = 0) {
    this.metrics.invalidations++;
    this.metrics.memoryUsage -= memoryDelta;
  }

  /**
   * Get current performance metrics
   */
  getMetrics() {
    const totalRequests = this.metrics.hits + this.metrics.misses;
    const hitRate = totalRequests > 0 ? this.metrics.hits / totalRequests : 0;

    const avgResponseTime = this.metrics.responseTimes.length > 0 ?
      this.metrics.responseTimes.reduce((sum, time) => sum + time, 0) / this.metrics.responseTimes.length : 0;

    const avgQualityScore = this.metrics.qualityScores.length > 0 ?
      this.metrics.qualityScores.reduce((sum, score) => sum + score, 0) / this.metrics.qualityScores.length : 0;

    return {
      hitRate,
      totalRequests,
      hits: this.metrics.hits,
      misses: this.metrics.misses,
      sets: this.metrics.sets,
      evictions: this.metrics.evictions,
      invalidations: this.metrics.invalidations,
      avgResponseTime,
      avgQualityScore,
      memoryUsage: this.metrics.memoryUsage,
      alerts: this.alerts.length
    };
  }

  /**
   * Check performance thresholds and generate alerts
   */
  checkPerformanceThresholds() {
    const metrics = this.getMetrics();
    const now = Date.now();

    // Check hit rate
    if (metrics.hitRate < this.config.performanceThresholds.hitRateThreshold) {
      this.addAlert('low_hit_rate', `Hit rate ${(metrics.hitRate * 100).toFixed(1)}% below threshold`, now);
    }

    // Check response time
    if (metrics.avgResponseTime > this.config.performanceThresholds.avgResponseTimeThreshold) {
      this.addAlert('high_response_time', `Average response time ${metrics.avgResponseTime.toFixed(1)}ms above threshold`, now);
    }

    // Check memory usage
    if (metrics.memoryUsage > this.config.performanceThresholds.memoryUsageThreshold) {
      this.addAlert('high_memory_usage', `Memory usage ${(metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB above threshold`, now);
    }
  }

  /**
   * Add performance alert
   */
  addAlert(type, message, timestamp) {
    // Avoid duplicate alerts within 5 minutes
    const recentAlert = this.alerts.find(alert =>
      alert.type === type && timestamp - alert.timestamp < 300000
    );

    if (!recentAlert) {
      this.alerts.push({ type, message, timestamp });

      // Keep only recent alerts
      this.alerts = this.alerts.filter(alert =>
        timestamp - alert.timestamp < this.config.metricsRetention
      );
    }
  }

  /**
   * Get performance analysis
   */
  getPerformanceAnalysis() {
    const metrics = this.getMetrics();
    const analysis = {
      overall: 'good',
      recommendations: [],
      strengths: [],
      concerns: []
    };

    // Analyze hit rate
    if (metrics.hitRate >= 0.8) {
      analysis.strengths.push('Excellent cache hit rate');
    } else if (metrics.hitRate >= 0.6) {
      analysis.strengths.push('Good cache hit rate');
    } else {
      analysis.concerns.push('Low cache hit rate - consider cache warming or TTL adjustment');
      analysis.recommendations.push('Implement cache warming strategies');
    }

    // Analyze response time
    if (metrics.avgResponseTime <= 50) {
      analysis.strengths.push('Excellent response times');
    } else if (metrics.avgResponseTime <= 100) {
      analysis.strengths.push('Good response times');
    } else {
      analysis.concerns.push('High response times - cache may be inefficient');
      analysis.recommendations.push('Optimize cache lookup algorithms');
    }

    // Analyze quality
    if (metrics.avgQualityScore >= 0.8) {
      analysis.strengths.push('High quality cached results');
    } else if (metrics.avgQualityScore >= 0.6) {
      analysis.strengths.push('Good quality cached results');
    } else if (metrics.avgQualityScore > 0) {
      analysis.concerns.push('Low quality cached results');
      analysis.recommendations.push('Implement stricter quality thresholds');
    }

    // Overall assessment
    if (analysis.concerns.length === 0) {
      analysis.overall = 'excellent';
    } else if (analysis.concerns.length <= 1) {
      analysis.overall = 'good';
    } else {
      analysis.overall = 'needs_improvement';
    }

    return analysis;
  }

  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      evictions: 0,
      invalidations: 0,
      responseTimes: [],
      memoryUsage: 0,
      qualityScores: []
    };
    this.alerts = [];
  }
}

/**
 * Visual Intelligence Cache
 * Main intelligent caching system for visual generation research
 */
export class VisualIntelligenceCache {
  constructor(config = {}) {
    this.config = {
      maxCacheSize: config.maxCacheSize || 1000,
      defaultTTL: config.defaultTTL || 3600000, // 1 hour
      enableQualityScoring: config.enableQualityScoring !== false,
      enableIntelligentInvalidation: config.enableIntelligentInvalidation !== false,
      enableAPIOptimization: config.enableAPIOptimization !== false,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring !== false,
      persistenceEnabled: config.persistenceEnabled || false,
      persistencePath: config.persistencePath || './.cache/visual-intelligence',
      ...config
    };

    // Core cache storage
    this.cache = new Map();
    this.accessPatterns = new Map();

    // Initialize components
    this.resultScorer = new ResearchResultScorer(config.scoring || {});
    this.invalidationManager = new CacheInvalidationManager(config.invalidation || {});
    this.apiOptimizer = new APICallOptimizer(config.apiOptimization || {});
    this.performanceMonitor = new CachePerformanceMonitor(config.monitoring || {});

    // Cache statistics
    this.stats = {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      cacheEvictions: 0,
      qualityImprovements: 0
    };

    // Initialize persistence if enabled
    if (this.config.persistenceEnabled) {
      this.initializePersistence();
    }

    // Start background maintenance
    this.startMaintenanceTimer();
  }

  /**
   * Get cached result with intelligent optimization
   */
  async get(cacheKey, context = {}) {
    const startTime = Date.now();
    this.stats.totalRequests++;

    // Check if entry exists
    const entry = this.cache.get(cacheKey);
    if (!entry) {
      this.stats.cacheMisses++;
      this.performanceMonitor.recordMiss(Date.now() - startTime);
      return null;
    }

    // Update access pattern
    this.updateAccessPattern(cacheKey);

    // Check if entry should be invalidated
    const accessPattern = this.accessPatterns.get(cacheKey);
    const invalidationCheck = this.invalidationManager.shouldInvalidate(entry, accessPattern);

    if (invalidationCheck.shouldInvalidate) {
      this.invalidate(cacheKey, invalidationCheck.reasons);
      this.stats.cacheMisses++;
      this.performanceMonitor.recordMiss(Date.now() - startTime);
      return null;
    }

    // Record successful hit
    this.stats.cacheHits++;
    const qualityScore = entry.qualityScore?.totalScore || 0;
    this.performanceMonitor.recordHit(Date.now() - startTime, qualityScore);

    return {
      ...entry.data,
      metadata: {
        ...entry.data.metadata,
        fromCache: true,
        cacheHit: true,
        qualityScore: entry.qualityScore,
        accessCount: accessPattern?.accessCount || 1
      }
    };
  }

  /**
   * Set cached result with quality scoring and optimization
   */
  async set(cacheKey, data, context = {}) {
    const startTime = Date.now();

    // Score the result quality
    let qualityScore = null;
    if (this.config.enableQualityScoring) {
      qualityScore = this.resultScorer.scoreResult(data, context);
    }

    // Check if we need to evict entries
    if (this.cache.size >= this.config.maxCacheSize) {
      await this.evictLeastValuable();
    }

    // Create cache entry
    const entry = {
      data,
      timestamp: Date.now(),
      qualityScore,
      context,
      size: this.estimateSize(data)
    };

    // Store in cache
    this.cache.set(cacheKey, entry);
    this.initializeAccessPattern(cacheKey);

    // Record metrics
    this.performanceMonitor.recordSet(entry.size);

    // Persist if enabled
    if (this.config.persistenceEnabled && qualityScore?.qualityLevel === 'excellent') {
      await this.persistEntry(cacheKey, entry);
    }

    return true;
  }

  /**
   * Optimized research call with caching and API optimization
   */
  async optimizedResearchCall(cacheKey, researchFn, context = {}) {
    // Try cache first
    const cached = await this.get(cacheKey, context);
    if (cached) {
      return cached;
    }

    // Optimize API call
    const result = await this.apiOptimizer.optimizeCall(
      cacheKey,
      researchFn,
      context
    );

    // Record API call for rate limiting
    this.apiOptimizer.recordAPICall(context.provider);

    // Cache the result
    await this.set(cacheKey, result, context);

    return result;
  }

  /**
   * Invalidate cache entry
   */
  invalidate(cacheKey, reasons = []) {
    const entry = this.cache.get(cacheKey);
    if (entry) {
      this.cache.delete(cacheKey);
      this.accessPatterns.delete(cacheKey);
      this.invalidationManager.recordInvalidation(cacheKey, reasons);
      this.performanceMonitor.recordInvalidation(entry.size);
      return true;
    }
    return false;
  }

  /**
   * Clear all cache entries
   */
  async clearAll() {
    const size = this.cache.size;
    this.cache.clear();
    this.accessPatterns.clear();

    // Reset statistics
    this.stats = {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      cacheEvictions: 0,
      qualityImprovements: 0
    };

    console.log(`Cleared ${size} cache entries`);
    return size;
  }

  /**
   * Update access pattern for cache key
   */
  updateAccessPattern(cacheKey) {
    const pattern = this.accessPatterns.get(cacheKey) || {
      accessCount: 0,
      lastAccessed: Date.now(),
      firstAccessed: Date.now()
    };

    pattern.accessCount++;
    pattern.lastAccessed = Date.now();

    this.accessPatterns.set(cacheKey, pattern);
  }

  /**
   * Initialize access pattern for new cache key
   */
  initializeAccessPattern(cacheKey) {
    this.accessPatterns.set(cacheKey, {
      accessCount: 1,
      lastAccessed: Date.now(),
      firstAccessed: Date.now()
    });
  }

  /**
   * Evict least valuable cache entries
   */
  async evictLeastValuable() {
    const entries = Array.from(this.cache.entries());

    // Score entries for eviction (lower score = more likely to evict)
    const scoredEntries = entries.map(([key, entry]) => {
      const accessPattern = this.accessPatterns.get(key) || {};
      const qualityScore = entry.qualityScore?.totalScore || 0;
      const age = Date.now() - entry.timestamp;
      const accessFrequency = accessPattern.accessCount || 1;

      // Calculate eviction score (higher = keep, lower = evict)
      const evictionScore = (qualityScore * 0.4) +
                           (Math.min(accessFrequency / 10, 1) * 0.3) +
                           (Math.max(0, 1 - age / (24 * 60 * 60 * 1000)) * 0.3);

      return { key, entry, evictionScore };
    });

    // Sort by eviction score (lowest first)
    scoredEntries.sort((a, b) => a.evictionScore - b.evictionScore);

    // Evict lowest scoring entries (25% of cache)
    const evictionCount = Math.max(1, Math.floor(this.config.maxCacheSize * 0.25));

    for (let i = 0; i < evictionCount && i < scoredEntries.length; i++) {
      const { key, entry } = scoredEntries[i];
      this.cache.delete(key);
      this.accessPatterns.delete(key);
      this.performanceMonitor.recordEviction(entry.size);
      this.stats.cacheEvictions++;
    }

    return evictionCount;
  }

  /**
   * Estimate memory size of data
   */
  estimateSize(data) {
    try {
      return JSON.stringify(data).length * 2; // Rough estimate
    } catch {
      return 1024; // Default size
    }
  }

  /**
   * Get comprehensive cache statistics
   */
  getStats() {
    const hitRate = this.stats.totalRequests > 0 ?
      this.stats.cacheHits / this.stats.totalRequests : 0;

    return {
      ...this.stats,
      hitRate,
      cacheSize: this.cache.size,
      maxCacheSize: this.config.maxCacheSize,
      memoryUsage: this.estimateTotalMemoryUsage(),
      performanceMetrics: this.performanceMonitor.getMetrics(),
      invalidationStats: this.invalidationManager.getInvalidationStats(),
      apiOptimizationStats: this.apiOptimizer.getOptimizationStats()
    };
  }

  /**
   * Get performance analysis
   */
  getPerformanceAnalysis() {
    return this.performanceMonitor.getPerformanceAnalysis();
  }

  /**
   * Estimate total memory usage
   */
  estimateTotalMemoryUsage() {
    let totalSize = 0;
    for (const [key, entry] of this.cache) {
      totalSize += entry.size || 0;
    }
    return totalSize;
  }

  /**
   * Initialize persistence system
   */
  async initializePersistence() {
    try {
      await fs.mkdir(this.config.persistencePath, { recursive: true });
      console.log('Cache persistence initialized');
    } catch (error) {
      console.warn('Failed to initialize cache persistence:', error.message);
      this.config.persistenceEnabled = false;
    }
  }

  /**
   * Persist high-quality cache entry
   */
  async persistEntry(cacheKey, entry) {
    if (!this.config.persistenceEnabled) return;

    try {
      const filename = this.generatePersistenceFilename(cacheKey);
      const filepath = path.join(this.config.persistencePath, filename);

      const persistData = {
        cacheKey,
        entry,
        persistedAt: Date.now()
      };

      await fs.writeFile(filepath, JSON.stringify(persistData, null, 2));
    } catch (error) {
      console.warn('Failed to persist cache entry:', error.message);
    }
  }

  /**
   * Load persisted cache entries
   */
  async loadPersistedEntries() {
    if (!this.config.persistenceEnabled) return 0;

    try {
      const files = await fs.readdir(this.config.persistencePath);
      let loadedCount = 0;

      for (const file of files) {
        if (file.endsWith('.json')) {
          try {
            const filepath = path.join(this.config.persistencePath, file);
            const data = await fs.readFile(filepath, 'utf8');
            const persistData = JSON.parse(data);

            // Check if entry is still valid
            const age = Date.now() - persistData.persistedAt;
            if (age < 24 * 60 * 60 * 1000) { // 24 hours
              this.cache.set(persistData.cacheKey, persistData.entry);
              this.initializeAccessPattern(persistData.cacheKey);
              loadedCount++;
            }
          } catch (error) {
            console.warn(`Failed to load persisted entry ${file}:`, error.message);
          }
        }
      }

      console.log(`Loaded ${loadedCount} persisted cache entries`);
      return loadedCount;
    } catch (error) {
      console.warn('Failed to load persisted entries:', error.message);
      return 0;
    }
  }

  /**
   * Generate filename for persisted entry
   */
  generatePersistenceFilename(cacheKey) {
    const hash = crypto.createHash('md5').update(cacheKey).digest('hex');
    return `cache_${hash}.json`;
  }

  /**
   * Start background maintenance timer
   */
  startMaintenanceTimer() {
    // Run maintenance every 5 minutes
    setInterval(() => {
      this.performMaintenance();
    }, 5 * 60 * 1000);
  }

  /**
   * Perform background maintenance
   */
  async performMaintenance() {
    try {
      // Check for entries that should be invalidated
      const entriesToInvalidate = [];

      for (const [key, entry] of this.cache) {
        const accessPattern = this.accessPatterns.get(key);
        const invalidationCheck = this.invalidationManager.shouldInvalidate(entry, accessPattern);

        if (invalidationCheck.shouldInvalidate) {
          entriesToInvalidate.push({ key, reasons: invalidationCheck.reasons });
        }
      }

      // Invalidate entries
      for (const { key, reasons } of entriesToInvalidate) {
        this.invalidate(key, reasons);
      }

      // Evict if cache is too full
      if (this.cache.size > this.config.maxCacheSize * 0.9) {
        await this.evictLeastValuable();
      }

      console.log(`Cache maintenance completed: invalidated ${entriesToInvalidate.length} entries`);
    } catch (error) {
      console.warn('Cache maintenance failed:', error.message);
    }
  }

  /**
   * Generate cache key from components
   */
  generateCacheKey(type, componentType, framework, context = {}) {
    const keyData = {
      type,
      componentType,
      framework,
      industry: context.industry,
      complexity: context.complexity,
      version: context.version || '1.0'
    };

    const keyString = JSON.stringify(keyData);
    return crypto.createHash('md5').update(keyString).digest('hex');
  }

  /**
   * Warm cache with common requests
   */
  async warmCache(commonRequests = []) {
    console.log('Starting cache warming...');
    let warmedCount = 0;

    for (const request of commonRequests) {
      try {
        const cacheKey = this.generateCacheKey(
          request.type,
          request.componentType,
          request.framework,
          request.context
        );

        // Only warm if not already cached
        if (!this.cache.has(cacheKey)) {
          await request.researchFn();
          warmedCount++;
        }
      } catch (error) {
        console.warn('Cache warming failed for request:', error.message);
      }
    }

    console.log(`Cache warming completed: ${warmedCount} entries added`);
    return warmedCount;
  }

  /**
   * Export cache statistics for analysis
   */
  exportStats() {
    const stats = this.getStats();
    const analysis = this.getPerformanceAnalysis();

    return {
      timestamp: new Date().toISOString(),
      statistics: stats,
      performanceAnalysis: analysis,
      cacheEntries: Array.from(this.cache.keys()).map(key => ({
        key,
        qualityScore: this.cache.get(key)?.qualityScore?.totalScore || 0,
        accessPattern: this.accessPatterns.get(key)
      }))
    };
  }
}
