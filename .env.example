# Guidant AI Configuration - .env.example
# -----------------------------------------------------------------------------
# This file provides a template for the essential environment variables required
# by the new, refactored AI configuration system.
#
# Unlike the previous system, all non-sensitive configuration (model IDs,
# temperature, etc.) is now managed in `.guidant/config.json` and the
# defaults.js file. This .env file should ONLY contain credentials.
#
# Instructions:
# 1. Copy this file to a new file named `.env` in the project root.
# 2. Fill in the values for the services you intend to use.
# -----------------------------------------------------------------------------

# --- Google Vertex AI (for main, analysis, generation roles) ---
# Your Google Cloud Project ID.
GOOGLE_CLOUD_PROJECT="your-gcp-project-id"
# Absolute path to your Google Cloud service account key file (JSON).
# e.g., /Users/<USER>/secrets/my-gcp-key.json
GOOGLE_APPLICATION_CREDENTIALS=""

# --- Perplexity (for research role) ---
# Your Perplexity API Key.
PERPLEXITY_API_KEY=""

# --- Anthropic ---
# Your Anthropic API Key (if you configure a role to use it).
ANTHROPIC_API_KEY=""

# --- OpenRouter ---
# Your OpenRouter API Key (if you configure a role to use it).
OPENROUTER_API_KEY=""