/**
 * General Project Question Flow
 * 
 * Default question flow for any project type.
 * Used when a specific project type is not identified or available.
 * Enhanced with conditional questions to better handle diverse project types.
 */

import { QUESTION_TYPES } from '../question-flow-engine.js';

const generalFlow = [
  {
    id: 'project-name',
    type: QUESTION_TYPES.TEXT,
    prompt: "What would you like to name your project?",
    required: true,
    minLength: 2,
    maxLength: 50
  },
  {
    id: 'project-description',
    type: QUESTION_TYPES.TEXT,
    prompt: "Please provide a brief description of your project:",
    required: true,
    minLength: 10,
    maxLength: 500
  },
  {
    id: 'project-type',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "Which category best describes your project?",
    required: true,
    options: [
      { value: 'web_app', label: 'Web Application' },
      { value: 'mobile_app', label: 'Mobile Application' },
      { value: 'desktop_app', label: 'Desktop Application' },
      { value: 'api_service', label: 'API/Backend Service' },
      { value: 'data_analytics', label: 'Data Analytics/Visualization' },
      { value: 'ai_ml', label: 'AI/Machine Learning' },
      { value: 'iot', label: 'IoT/Hardware' },
      { value: 'game', label: 'Game' },
      { value: 'other', label: 'Other' }
    ]
  },
  {
    id: 'project-goals',
    type: QUESTION_TYPES.TEXT,
    prompt: "What are the main goals of this project?",
    required: true,
    minLength: 10,
    maxLength: 1000
  },
  {
    id: 'target-audience',
    type: QUESTION_TYPES.TEXT,
    prompt: "Who is the target audience for this project?",
    required: true,
    minLength: 5,
    maxLength: 200
  },
  {
    id: 'tech-comfort',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "How would you describe your comfort level with technical details?",
    required: true,
    options: [
      { value: 'novice', label: 'Novice - I prefer simple explanations without technical terms' },
      { value: 'intermediate', label: 'Intermediate - I understand some technical concepts' },
      { value: 'advanced', label: 'Advanced - I\'m comfortable with technical details' }
    ]
  },
  // Mobile-specific questions
  {
    id: 'mobile-platforms',
    type: QUESTION_TYPES.TAGS,
    prompt: "Which mobile platforms do you want to support?",
    required: true,
    tags: [
      'iOS',
      'Android',
      'Both',
      'Progressive Web App'
    ],
    conditions: [
      { questionId: 'project-type', operator: 'equals', value: 'mobile_app' }
    ]
  },
  {
    id: 'mobile-features',
    type: QUESTION_TYPES.TAGS,
    prompt: "Which mobile-specific features are important for your app?",
    required: true,
    tags: [
      'Push Notifications',
      'Camera Access',
      'Location Services',
      'Offline Mode',
      'Touch ID/Face ID',
      'AR Features',
      'File Access'
    ],
    conditions: [
      { questionId: 'project-type', operator: 'equals', value: 'mobile_app' }
    ]
  },
  // Web app specific questions
  {
    id: 'web-app-type',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "What type of web application are you building?",
    required: true,
  options: [
      { value: 'static', label: 'Static website (informational)' },
      { value: 'dynamic', label: 'Dynamic web application (interactive)' },
      { value: 'spa', label: 'Single Page Application (SPA)' },
      { value: 'pwa', label: 'Progressive Web App (PWA)' }
    ],
    conditions: [
      { questionId: 'project-type', operator: 'equals', value: 'web_app' }
    ]
  },
  // AI/ML specific questions
  {
    id: 'ai-ml-type',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "What type of AI/ML project are you building?",
    required: true,
  options: [
      { value: 'classification', label: 'Classification/Prediction' },
      { value: 'nlp', label: 'Natural Language Processing' },
      { value: 'computer_vision', label: 'Computer Vision' },
      { value: 'recommendation', label: 'Recommendation System' },
      { value: 'generative', label: 'Generative AI' },
      { value: 'other_ai', label: 'Other AI/ML' }
    ],
    conditions: [
      { questionId: 'project-type', operator: 'equals', value: 'ai_ml' }
    ]
  },
  {
    id: 'data-sources',
    type: QUESTION_TYPES.TAGS,
    prompt: "What are your data sources for this project?",
    required: true,
  tags: [
      'User Generated',
      'APIs',
      'Databases',
      'File Uploads',
      'Sensors/IoT',
      'Public Datasets',
      'Web Scraping'
    ],
    conditions: [
      { questionId: 'project-type', operator: 'equals', value: 'ai_ml' },
      { questionId: 'project-type', operator: 'equals', value: 'data_analytics' }
    ]
  },
  // IoT specific questions
  {
    id: 'iot-devices',
    type: QUESTION_TYPES.TEXT,
    prompt: "What IoT devices or hardware will your project use?",
    required: true,
  minLength: 5,
    maxLength: 500,
    conditions: [
      { questionId: 'project-type', operator: 'equals', value: 'iot' }
    ]
  },
  // Game specific questions
  {
    id: 'game-type',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "What type of game are you creating?",
    required: true,
  options: [
      { value: '2d', label: '2D Game' },
      { value: '3d', label: '3D Game' },
      { value: 'text', label: 'Text-based Game' },
      { value: 'ar_vr', label: 'AR/VR Game' },
      { value: 'other_game', label: 'Other Game Type' }
    ],
    conditions: [
      { questionId: 'project-type', operator: 'equals', value: 'game' }
    ]
  },
  // Common questions for all project types
  {
    id: 'project-timeline',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "What's your timeline for this project?",
    required: true,
  options: [
      { value: 'urgent', label: 'Urgent - I need this as soon as possible' },
      { value: 'standard', label: 'Standard - I have a reasonable timeframe' },
      { value: 'relaxed', label: 'Relaxed - I\'m not in a hurry' }
    ]
  },
  {
    id: 'project-budget',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "What's your budget approach for this project?",
    required: true,
  options: [
      { value: 'minimal', label: 'Minimal - Keep costs as low as possible' },
      { value: 'balanced', label: 'Balanced - Reasonable quality within budget' },
      { value: 'premium', label: 'Premium - Quality is the priority over cost' }
    ]
  },
  {
    id: 'user-accounts',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "Will your project require user accounts?",
    required: true,
  options: [
      { value: 'no_accounts', label: 'No user accounts needed' },
      { value: 'simple_accounts', label: 'Simple accounts (email/password)' },
      { value: 'social_login', label: 'Social login (Google, Facebook, etc.)' },
      { value: 'enterprise', label: 'Enterprise authentication (SSO, SAML, etc.)' }
    ]
  },
  {
    id: 'data-storage',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "What kind of data will your project need to store?",
    required: true,
  options: [
      { value: 'minimal_data', label: 'Minimal data (configuration, settings)' },
      { value: 'user_content', label: 'User-generated content' },
      { value: 'large_data', label: 'Large datasets' },
      { value: 'sensitive_data', label: 'Sensitive/regulated data' }
    ]
  },
  {
    id: 'feature-priorities',
    type: QUESTION_TYPES.TAGS,
    prompt: "Which aspects of the project are most important to you?",
    required: true,
  tags: [
      'Performance',
      'User Experience',
      'Security',
      'Scalability',
      'Easy Maintenance',
      'Visual Design',
      'Accessibility',
      'Low Cost'
    ]
  },
  {
    id: 'inspiration-urls',
    type: QUESTION_TYPES.LIST,
    prompt: "Please share links to any projects that inspire you (one at a time):",
    required: true,
  continuationPrompt: "Add another inspiration link or type 'done' to continue:",
    listItemName: "inspiration links",
    itemType: QUESTION_TYPES.URL,
    minLength: 5
  },
  {
    id: 'technical-requirements',
    type: QUESTION_TYPES.TEXT,
    prompt: "Do you have any specific technical requirements or preferences?",
    required: true,
  minLength: 0,
    maxLength: 1000
  },
  // E-commerce specific questions
  {
    id: 'payment-methods',
    type: QUESTION_TYPES.TAGS,
    prompt: "Which payment methods would you like to support?",
    required: true,
    tags: [
      'Credit Card',
      'PayPal',
      'Apple Pay',
      'Google Pay',
      'Bank Transfer',
      'Cryptocurrency',
      'Buy Now Pay Later'
    ],
    conditions: [
      { questionId: 'project-type', operator: 'equals', value: 'web_app' },
      { questionId: 'project-type', operator: 'equals', value: 'mobile_app' }
    ]
  },
  {
    id: 'business-model',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "What's your primary business model?",
    required: true,
    options: [
      { value: 'b2c', label: 'B2C (Business to Consumer)' },
      { value: 'b2b', label: 'B2B (Business to Business)' },
      { value: 'marketplace', label: 'Marketplace (Multiple Sellers)' },
      { value: 'saas', label: 'SaaS (Software as a Service)' },
      { value: 'community', label: 'Community Platform' },
      { value: 'other', label: 'Other' }
    ],
    conditions: [
      { questionId: 'project-type', operator: 'equals', value: 'web_app' },
      { questionId: 'project-type', operator: 'equals', value: 'mobile_app' },
      { questionId: 'project-type', operator: 'equals', value: 'api_service' }
    ]
  },
  // SaaS specific questions
  {
    id: 'subscription-model',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "What subscription model are you planning to use?",
    required: true,
    options: [
      { value: 'freemium', label: 'Freemium (Free tier + paid upgrades)' },
      { value: 'subscription', label: 'Pure subscription (Monthly/Annual billing)' },
      { value: 'usage_based', label: 'Usage-based pricing (Pay as you go)' },
      { value: 'one_time', label: 'One-time purchase with ongoing service' },
      { value: 'enterprise', label: 'Enterprise (Custom pricing)' }
    ],
    conditions: [
      { questionId: 'business-model', operator: 'equals', value: 'saas' }
    ]
  },
  {
    id: 'data-sensitivity',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "How sensitive is the data your application will handle?",
    required: true,
    options: [
      { value: 'public', label: 'Public (No sensitive data)' },
      { value: 'business', label: 'Business sensitive (Proprietary but not regulated)' },
      { value: 'personal', label: 'Personal data (Subject to privacy regulations)' },
      { value: 'financial', label: 'Financial data (Highly regulated)' }
    ],
    conditions: [
      { questionId: 'business-model', operator: 'equals', value: 'saas' },
      { questionId: 'business-model', operator: 'equals', value: 'b2b' }
    ]
  },
  // Community specific questions
  {
    id: 'community-size',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "What size community are you planning for initially?",
    required: true,
    options: [
      { value: 'small', label: 'Small (Up to 100 members)' },
      { value: 'medium', label: 'Medium (100-1,000 members)' },
      { value: 'large', label: 'Large (1,000-10,000 members)' },
      { value: 'very_large', label: 'Very large (10,000+ members)' }
    ],
    conditions: [
      { questionId: 'business-model', operator: 'equals', value: 'community' }
    ]
  },
  {
    id: 'moderation-approach',
    type: QUESTION_TYPES.MULTIPLE_CHOICE,
    prompt: "What's your approach to content moderation?",
    required: true,
    options: [
      { value: 'pre_moderation', label: 'Pre-moderation (approve before publishing)' },
      { value: 'post_moderation', label: 'Post-moderation (review after publishing)' },
      { value: 'community', label: 'Community moderation (flagging/reporting)' },
      { value: 'automated', label: 'Automated moderation (AI/filters)' },
      { value: 'hybrid', label: 'Hybrid approach (combination of methods)' }
    ],
    conditions: [
      { questionId: 'business-model', operator: 'equals', value: 'community' }
    ]
  },
  {
    id: 'additional-info',
    type: QUESTION_TYPES.TEXT,
    prompt: "Is there anything else you'd like to share about your project?",
    required: false,
    minLength: 0,
    maxLength: 1000
  }
];

export default generalFlow; 