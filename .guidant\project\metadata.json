{"targetUsers": [], "businessGoals": [], "technicalRequirements": [], "constraints": [], "workflow": {"projectType": "feature", "complexity": "standard", "phases": ["requirements", "design", "implementation", "deployment"], "estimatedDuration": "2-4 weeks", "qualityLevel": "standard", "researchRequirement": "moderate", "generated": "2025-06-18T16:17:22.319Z", "adaptive": true}, "researchRequirements": {"mandatory": ["user_impact_analysis", "integration_research"], "checkpoints": [{"name": "requirements_validation", "required": true, "status": "pending", "description": "Research validation checkpoint: requirements validation"}, {"name": "design_validation", "required": true, "status": "pending", "description": "Research validation checkpoint: design validation"}], "depth": "moderate", "enforced": true}}