/**
 * MCP Prompt Schemas
 * Centralized schema definitions for MCP prompt arguments and validation
 * Provides consistent validation for all prompt templates
 */

import { z } from 'zod';

/**
 * Base prompt argument schema
 */
export const BasePromptArgumentSchema = z.object({
  name: z.string().min(1).describe("Argument name"),
  description: z.string().min(1).describe("Argument description"),
  required: z.boolean().default(false).describe("Whether argument is required"),
  type: z.enum(["string", "number", "boolean", "object", "array"]).optional().describe("Argument type"),
  default: z.any().optional().describe("Default value"),
  enum: z.array(z.string()).optional().describe("Allowed values"),
  pattern: z.string().optional().describe("Validation pattern"),
  minLength: z.number().optional().describe("Minimum length for strings"),
  maxLength: z.number().optional().describe("Maximum length for strings")
});

/**
 * Analysis Prompt Schemas
 */
export const AnalysisPromptSchemas = {
  "analyze-project-phase": z.object({
    phase: z.string().describe("Project phase to analyze"),
    focus_area: z.string().optional().describe("Specific area to focus on"),
    timeframe: z.enum(["current", "last_week", "last_month", "all"]).optional().describe("Analysis timeframe"),
    detail_level: z.enum(["summary", "detailed", "comprehensive"]).optional().describe("Level of detail required"),
    include_recommendations: z.boolean().optional().describe("Include actionable recommendations"),
    context: z.object({
      project_type: z.string().optional(),
      team_size: z.number().optional(),
      budget_constraints: z.object({}).optional(),
      timeline_pressure: z.string().optional()
    }).optional().describe("Additional context for analysis")
  }),
  
  "analyze-deliverable-quality": z.object({
    deliverable_path: z.string().describe("Path to deliverable file"),
    deliverable_type: z.string().describe("Type of deliverable"),
    quality_criteria: z.array(z.string()).optional().describe("Specific quality criteria to evaluate"),
    comparison_baseline: z.string().optional().describe("Baseline for comparison"),
    include_improvement_suggestions: z.boolean().optional().describe("Include improvement suggestions"),
    context: z.object({
      phase: z.string().optional(),
      stakeholder_requirements: z.object({}).optional(),
      compliance_standards: z.array(z.string()).optional()
    }).optional().describe("Quality analysis context")
  }),
  
  "analyze-workflow-performance": z.object({
    workflow_id: z.string().describe("Workflow identifier"),
    metrics: z.array(z.string()).optional().describe("Specific metrics to analyze"),
    timeframe: z.string().describe("Analysis time period"),
    comparison_period: z.string().optional().describe("Period for comparison"),
    include_bottlenecks: z.boolean().optional().describe("Identify performance bottlenecks"),
    context: z.object({
      workflow_type: z.string().optional(),
      expected_performance: z.object({}).optional(),
      resource_constraints: z.object({}).optional()
    }).optional().describe("Performance analysis context")
  })
};

/**
 * Workflow Prompt Schemas
 */
export const WorkflowPromptSchemas = {
  "generate-workflow-template": z.object({
    workflow_type: z.enum(["research", "development", "analysis", "deployment"]).describe("Type of workflow"),
    project_context: z.object({
      type: z.string(),
      complexity: z.enum(["simple", "medium", "complex"]),
      timeline: z.string(),
      resources: z.object({}).optional()
    }).describe("Project context for workflow generation"),
    customization_preferences: z.object({
      automation_level: z.enum(["manual", "semi-automated", "fully-automated"]).optional(),
      quality_gates: z.boolean().optional(),
      parallel_execution: z.boolean().optional(),
      error_handling: z.enum(["strict", "lenient", "custom"]).optional()
    }).optional().describe("Workflow customization preferences"),
    constraints: z.object({
      max_duration: z.number().optional(),
      resource_limits: z.object({}).optional(),
      compliance_requirements: z.array(z.string()).optional()
    }).optional().describe("Workflow constraints")
  }),
  
  "optimize-workflow-execution": z.object({
    current_workflow: z.object({}).describe("Current workflow definition"),
    performance_data: z.object({
      execution_times: z.array(z.number()).optional(),
      success_rates: z.array(z.number()).optional(),
      resource_usage: z.object({}).optional(),
      bottlenecks: z.array(z.string()).optional()
    }).describe("Historical performance data"),
    optimization_goals: z.array(z.enum(["speed", "reliability", "cost", "quality"])).describe("Optimization objectives"),
    constraints: z.object({
      maintain_quality: z.boolean().optional(),
      resource_limits: z.object({}).optional(),
      compatibility_requirements: z.array(z.string()).optional()
    }).optional().describe("Optimization constraints")
  }),
  
  "design-workflow-integration": z.object({
    source_workflow: z.object({}).describe("Source workflow to integrate"),
    target_workflow: z.object({}).describe("Target workflow for integration"),
    integration_points: z.array(z.string()).describe("Specific integration points"),
    data_flow_requirements: z.object({
      inputs: z.array(z.string()),
      outputs: z.array(z.string()),
      transformations: z.array(z.object({})).optional()
    }).describe("Data flow requirements"),
    synchronization_strategy: z.enum(["sequential", "parallel", "event-driven"]).optional().describe("Synchronization approach")
  })
};

/**
 * Onboarding Prompt Schemas
 */
export const OnboardingPromptSchemas = {
  "generate-onboarding-questions": z.object({
    project_type: z.string().describe("Type of project for onboarding"),
    user_experience_level: z.enum(["beginner", "intermediate", "advanced"]).describe("User's experience level"),
    focus_areas: z.array(z.string()).optional().describe("Specific areas to focus on during onboarding"),
    time_constraints: z.object({
      total_time: z.number().optional(),
      session_length: z.number().optional()
    }).optional().describe("Time constraints for onboarding"),
    customization_level: z.enum(["basic", "standard", "comprehensive"]).optional().describe("Level of customization needed")
  }),
  
  "create-project-setup-guide": z.object({
    project_template: z.string().describe("Project template to use"),
    user_preferences: z.object({
      development_style: z.string().optional(),
      tool_preferences: z.array(z.string()).optional(),
      workflow_preferences: z.object({}).optional()
    }).describe("User preferences and requirements"),
    environment_constraints: z.object({
      platform: z.string().optional(),
      resource_limits: z.object({}).optional(),
      security_requirements: z.array(z.string()).optional()
    }).optional().describe("Environment and constraint information"),
    integration_requirements: z.array(z.string()).optional().describe("Required integrations")
  }),
  
  "design-learning-path": z.object({
    current_knowledge: z.object({
      technical_skills: z.array(z.string()),
      domain_knowledge: z.array(z.string()),
      tool_familiarity: z.array(z.string())
    }).describe("Current knowledge and skills"),
    learning_objectives: z.array(z.string()).describe("Desired learning outcomes"),
    learning_style: z.enum(["visual", "hands-on", "theoretical", "mixed"]).optional().describe("Preferred learning style"),
    time_availability: z.object({
      total_time: z.number().optional(),
      session_frequency: z.string().optional(),
      deadline: z.string().optional()
    }).optional().describe("Time availability for learning")
  })
};

/**
 * Decision Prompt Schemas
 */
export const DecisionPromptSchemas = {
  "evaluate-technical-options": z.object({
    decision_context: z.object({
      problem_statement: z.string(),
      constraints: z.object({}).optional(),
      success_criteria: z.array(z.string()).optional(),
      stakeholders: z.array(z.string()).optional()
    }).describe("Context for the technical decision"),
    options: z.array(z.object({
      name: z.string(),
      description: z.string(),
      pros: z.array(z.string()).optional(),
      cons: z.array(z.string()).optional(),
      cost: z.object({}).optional(),
      risk_level: z.enum(["low", "medium", "high"]).optional()
    })).describe("Available options to evaluate"),
    evaluation_criteria: z.array(z.object({
      name: z.string(),
      weight: z.number(),
      description: z.string().optional()
    })).describe("Criteria for evaluation"),
    additional_context: z.object({}).optional().describe("Additional context for decision making")
  }),
  
  "recommend-project-direction": z.object({
    current_state: z.object({
      phase: z.string(),
      progress: z.number(),
      completed_deliverables: z.array(z.string()),
      pending_tasks: z.array(z.string()),
      blockers: z.array(z.string()).optional()
    }).describe("Current project state"),
    available_paths: z.array(z.object({
      name: z.string(),
      description: z.string(),
      estimated_effort: z.string(),
      risk_assessment: z.string(),
      expected_outcomes: z.array(z.string())
    })).describe("Available project directions"),
    business_context: z.object({
      timeline_pressure: z.string().optional(),
      budget_constraints: z.object({}).optional(),
      market_conditions: z.string().optional(),
      stakeholder_priorities: z.array(z.string()).optional()
    }).optional().describe("Business context for decision"),
    success_metrics: z.array(z.string()).describe("Metrics for measuring success")
  }),
  
  "prioritize-features": z.object({
    feature_list: z.array(z.object({
      name: z.string(),
      description: z.string(),
      estimated_effort: z.string(),
      business_value: z.enum(["low", "medium", "high"]),
      technical_complexity: z.enum(["low", "medium", "high"]),
      dependencies: z.array(z.string()).optional(),
      user_impact: z.string().optional()
    })).describe("List of features to prioritize"),
    prioritization_criteria: z.object({
      business_value_weight: z.number().optional(),
      effort_weight: z.number().optional(),
      risk_weight: z.number().optional(),
      strategic_alignment_weight: z.number().optional()
    }).optional().describe("Weights for prioritization criteria"),
    constraints: z.object({
      timeline: z.string().optional(),
      resource_limits: z.object({}).optional(),
      must_have_features: z.array(z.string()).optional(),
      nice_to_have_features: z.array(z.string()).optional()
    }).optional().describe("Prioritization constraints")
  })
};

/**
 * Consolidated prompt schemas
 */
export const ConsolidatedPromptSchemas = {
  Analysis: AnalysisPromptSchemas,
  Workflow: WorkflowPromptSchemas,
  Onboarding: OnboardingPromptSchemas,
  Decision: DecisionPromptSchemas
};

/**
 * Prompt validation utilities
 */
export const PromptValidators = {
  /**
   * Validate prompt arguments against schema
   * @param {string} category - Prompt category
   * @param {string} promptName - Prompt name
   * @param {object} arguments - Arguments to validate
   * @returns {object} Validation result
   */
  validatePromptArguments(category, promptName, arguments) {
    const categorySchemas = ConsolidatedPromptSchemas[category];
    if (!categorySchemas) {
      return { success: false, error: `Unknown prompt category: ${category}` };
    }
    
    const schema = categorySchemas[promptName];
    if (!schema) {
      return { success: false, error: `Unknown prompt: ${promptName} in category ${category}` };
    }
    
    try {
      const validated = schema.parse(arguments);
      return { success: true, data: validated };
    } catch (error) {
      return { 
        success: false, 
        error: error.message,
        details: error.errors || []
      };
    }
  },
  
  /**
   * Get schema for prompt
   * @param {string} category - Prompt category
   * @param {string} promptName - Prompt name
   * @returns {object} Schema definition
   */
  getPromptSchema(category, promptName) {
    const categorySchemas = ConsolidatedPromptSchemas[category];
    return categorySchemas?.[promptName] || null;
  },
  
  /**
   * List all available prompt schemas
   * @returns {object} Prompt schema categories and names
   */
  listPromptSchemas() {
    const result = {};
    for (const [category, schemas] of Object.entries(ConsolidatedPromptSchemas)) {
      result[category] = Object.keys(schemas);
    }
    return result;
  },
  
  /**
   * Generate prompt argument definitions from schema
   * @param {object} schema - Zod schema
   * @returns {Array} Argument definitions
   */
  generateArgumentDefinitions(schema) {
    if (!schema._def || !schema._def.shape) {
      return [];
    }
    
    const arguments = [];
    for (const [name, fieldSchema] of Object.entries(schema._def.shape())) {
      const arg = {
        name,
        description: fieldSchema.description || `${name} parameter`,
        required: !fieldSchema.isOptional(),
        type: this.getSchemaType(fieldSchema)
      };
      
      // Add additional constraints if available
      if (fieldSchema._def.values) {
        arg.enum = fieldSchema._def.values;
      }
      
      arguments.push(arg);
    }
    
    return arguments;
  },
  
  /**
   * Get schema type from Zod schema
   * @param {object} schema - Zod schema
   * @returns {string} Schema type
   */
  getSchemaType(schema) {
    const typeName = schema._def.typeName;
    switch (typeName) {
      case 'ZodString': return 'string';
      case 'ZodNumber': return 'number';
      case 'ZodBoolean': return 'boolean';
      case 'ZodObject': return 'object';
      case 'ZodArray': return 'array';
      case 'ZodEnum': return 'string';
      default: return 'string';
    }
  }
};

/**
 * Prompt template metadata schema
 */
export const PromptMetadataSchema = z.object({
  name: z.string().min(1).describe("Prompt template name"),
  description: z.string().min(1).describe("Prompt description"),
  category: z.enum(["Analysis", "Workflow", "Onboarding", "Decision"]).describe("Prompt category"),
  version: z.string().optional().describe("Prompt version"),
  author: z.string().optional().describe("Prompt author"),
  tags: z.array(z.string()).optional().describe("Prompt tags"),
  usage_examples: z.array(z.object({
    title: z.string(),
    description: z.string(),
    arguments: z.object({})
  })).optional().describe("Usage examples"),
  related_prompts: z.array(z.string()).optional().describe("Related prompt names"),
  complexity: z.enum(["simple", "medium", "complex"]).optional().describe("Prompt complexity"),
  estimated_tokens: z.number().optional().describe("Estimated token usage")
});

export default {
  ConsolidatedPromptSchemas,
  BasePromptArgumentSchema,
  PromptMetadataSchema,
  PromptValidators
};
