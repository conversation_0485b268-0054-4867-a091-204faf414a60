# TASK-001: MCP Tool Architecture Overhaul

## 🚨 **STATUS: ON HOLD**

**Decision Date**: 2025-06-16
**Decision Maker**: Senior Developer Assessment
**Reason**: Premature optimization - current 48-tool architecture is functional and working well

## 📋 Overview

This directory contains the complete implementation plan for transforming Guidant's 48-tool architecture into a MCP-native system with 15 primitives, achieving a 69% reduction in complexity while maintaining 100% backward compatibility.

**⚠️ IMPORTANT**: This task has been deferred in favor of TASK-002 (Research Tools Integration) which provides immediate user value by adding intelligent research capabilities to the existing proven architecture.

## 🎯 Key Transformation

**Before**: 48 individual tools across 12 categories
**After**: 15 MCP primitives (4 Resources + 4 Tools + 4 Prompts + 3 Sampling capabilities)

## 📁 Documentation Structure

### Core Planning Documents

| Document | Purpose | Status |
|----------|---------|--------|
| [`implementation-plan.md`](./implementation-plan.md) | High-level strategy and timeline | ✅ Complete |
| [`primitive-mapping-analysis.md`](./primitive-mapping-analysis.md) | Detailed tool-to-primitive mapping | ✅ Complete |
| [`technical-specification.md`](./technical-specification.md) | Implementation details and code specs | ✅ Complete |
| [`migration-guide.md`](./migration-guide.md) | Step-by-step migration instructions | ✅ Complete |

### Original Task Definition

| Document | Purpose |
|----------|---------|
| [`TASK-001-mcp-tool-architecture-overhaul.md`](./TASK-001-mcp-tool-architecture-overhaul.md) | Original task specification |

## 🏗️ Architecture Transformation

### MCP Primitive-Based Design

```
Current: 48 Individual Tools
├── core-project-management (3)
├── workflow-control (3)
├── deliverable-analysis (3)
├── agent-discovery (4)
├── capability-analysis (4)
├── adaptive-workflow (6)
├── quality-validation (5)
├── orchestration (8)
├── analytics (4)
├── context-management (3)
├── interactive-onboarding (4)
└── business-decisions (4)

New: 15 MCP Primitives
├── Resources (4) - Application-Controlled Data
│   ├── Project Resources (guidant://project/{id}/*)
│   ├── Deliverable Resources (guidant://deliverable/{id}/*)
│   ├── Analytics Resources (guidant://analytics/{id}/*)
│   └── Workflow Resources (guidant://workflow/{id}/*)
├── Tools (4) - Model-Controlled Actions
│   ├── Workflow Executor (consolidates 15 tools)
│   ├── Project Manager (consolidates 8 tools)
│   ├── Analytics Engine (consolidates 9 tools)
│   └── Context Manager (consolidates 6 tools)
├── Prompts (4) - User-Controlled Templates
│   ├── Analysis Prompts
│   ├── Onboarding Prompts
│   ├── Decision Prompts
│   └── Quality Prompts
└── Sampling (3) - Server-Controlled Intelligence
    ├── Intelligent Decisions
    ├── Content Generation
    └── Predictive Analysis
```

## 🔑 Key Insights from MCP Documentation

### 1. **Four Core Primitives Pattern**
MCP is built around exactly 4 primitives with specific control paradigms:
- **Resources** (Application-Controlled) - Data exposure through URI templates
- **Tools** (Model-Controlled) - Executable functionality with annotations
- **Prompts** (User-Controlled) - Reusable templates with dynamic arguments
- **Sampling** (Server-Controlled) - LLM completion requests

### 2. **JSON-RPC 2.0 Foundation**
All communication uses standardized patterns:
- Requests (expect responses)
- Notifications (one-way messages)
- Results (successful responses)
- Errors (failure responses)

### 3. **URI Template Standards (RFC 6570)**
Resources use standardized URI templates for dynamic data access:
```
guidant://project/{projectId}/state
guidant://deliverable/{deliverableId}/analysis
guidant://workflow/{workflowId}/metrics/{timeframe?}
```

### 4. **Tool Annotations for Smart Routing**
MCP tool annotations enable intelligent routing:
```javascript
annotations: {
  title: "Workflow Executor",
  readOnlyHint: false,
  destructiveHint: false, 
  idempotentHint: true,
  openWorldHint: true
}
```

## 📊 Success Metrics

### Quantitative Goals
- ✅ **Tool Reduction**: 48 → 15 primitives (69% reduction)
- ✅ **Discovery Time**: <5 seconds for AI agents
- ✅ **Performance**: <5% impact from current baseline
- ✅ **Compatibility**: 100% backward compatibility maintained

### Qualitative Goals
- ✅ **MCP Compliance**: Full adherence to MCP specification
- ✅ **Maintainability**: Clear primitive-based organization
- ✅ **Extensibility**: Easy addition of new capabilities
- ✅ **Standards**: Industry-standard JSON-RPC 2.0 and RFC 6570

## 🚀 Implementation Timeline

### Phase 1: Resource Layer (Week 1)
- Implement URI template parser (RFC 6570)
- Create resource handlers for project, deliverable, analytics data
- Register MCP resource capabilities
- Test resource discovery and reading

### Phase 2: Tool Consolidation (Week 2-3)
- Build 4 consolidated tools with operation-based parameters
- Implement MCP tool annotations for smart routing
- Create backward compatibility layer for all 48 legacy tools
- Test tool execution and routing

### Phase 3: Prompts & Sampling (Week 3-4)
- Implement prompt templates with dynamic arguments
- Add sampling capabilities for intelligent decisions
- Integrate with existing orchestration infrastructure
- Comprehensive testing and validation

### Phase 4: Integration & Optimization (Week 4)
- Performance optimization and monitoring
- Documentation updates
- User acceptance testing
- Production deployment

## 🔧 Technical Highlights

### Resource Implementation
```javascript
// RFC 6570 URI Templates
"guidant://project/{projectId}/phase/{phase}/deliverables"
"guidant://analytics/{projectId}/insights/{timeframe?}"
"guidant://workflow/{workflowId}/execution/{executionId}"
```

### Tool Consolidation
```javascript
// Operation-based parameters with smart routing
{
  name: "guidant_execute_workflow",
  parameters: {
    operation: ["research", "onboarding", "development"],
    workflow_type: ["market", "competitive", "technical"]
  },
  annotations: { /* MCP-compliant annotations */ }
}
```

### Backward Compatibility
```javascript
// Legacy tool mapping
'guidant_research_market' → {
  type: 'tool',
  name: 'guidant_execute_workflow', 
  parameters: { operation: 'research', workflow_type: 'market' }
}
```

## 🛡️ Risk Mitigation

### Technical Risks
- **Breaking Changes**: Gradual migration with compatibility layer
- **Performance**: Benchmark-driven optimization
- **Complexity**: Phase-based implementation with clear milestones

### Implementation Risks
- **Timeline**: 4-week phased approach with weekly deliverables
- **Testing**: Comprehensive test coverage for each primitive
- **Rollback**: Complete rollback plan with emergency procedures

## 📈 Expected Benefits

### For AI Agents
- **Faster Discovery**: <5 seconds to find relevant capabilities
- **Better Context**: Rich resource data through URI templates
- **Smarter Routing**: MCP annotations guide tool selection
- **Intelligent Decisions**: Sampling enables adaptive behavior

### For Developers
- **Cleaner Architecture**: MCP primitive-based organization
- **Easier Maintenance**: 69% fewer tools to manage
- **Standards Compliance**: Industry-standard MCP patterns
- **Better Testing**: Clear primitive boundaries

### For Users
- **Improved Performance**: Optimized tool discovery and execution
- **Enhanced Capabilities**: New sampling-based intelligence
- **Consistent Experience**: Standardized MCP interactions
- **Future-Proof**: Extensible architecture for new features

## 🔄 Next Steps

1. **Review Documentation**: Examine all planning documents
2. **Validate Approach**: Confirm MCP-native strategy alignment
3. **Begin Implementation**: Start with Phase 1 (Resource layer)
4. **Iterative Development**: Weekly milestone reviews
5. **Testing & Validation**: Continuous compatibility verification

## 📞 Support & Questions

For questions about this implementation plan:
- Review the detailed technical specification
- Check the migration guide for step-by-step instructions
- Refer to primitive mapping analysis for tool categorization
- Consult MCP documentation in `mcp-docs/` directory

## 🚨 **WHY THIS TASK IS ON HOLD**

### **Senior Developer Assessment (2025-06-16)**

**Current State Analysis:**
- ✅ **48 MCP tools are functional and working well**
- ✅ **No performance issues or user complaints**
- ✅ **WLR-001 through WLR-005 provide solid foundation**
- ✅ **Users can effectively use Guidant with current architecture**

**Risk/Benefit Analysis:**
- ❌ **High Risk**: 4-week complex refactoring of working system
- ❌ **Low Immediate Value**: No user-facing benefits or broken functionality to fix
- ❌ **Blocks Real Value**: Delays research integration (TASK-002) and task management (TASK-003)
- ❌ **Premature Optimization**: Should happen when we have actual tool proliferation issues

**Better Priorities:**
- 🎯 **TASK-002**: Research Tools Integration (6h) - Immediate intelligence value
- 🎯 **TASK-003**: Rich Task Infrastructure (12h) - Foundation for task management
- 🎯 **TASK-005**: Conversational Task Management (8h) - User experience improvement

**Future Timing for TASK-001:**
- Revisit when we reach 60+ tools (currently 48 + 3-5 research tools = 51-53)
- Implement when we have actual performance issues
- Consider after TASK-002, TASK-003, TASK-005 are completed

---

**Implementation Status**: 🚨 **ON HOLD** - Deferred for higher-value priorities
**Timeline**: TBD (when tool proliferation becomes actual issue)
**Priority**: Deferred (TASK-002 takes priority)
**Complexity**: High
**Risk Level**: High (unnecessary refactoring of working system)
