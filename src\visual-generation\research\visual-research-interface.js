/**
 * Visual Research Integration Interface
 * Integrates TASK-002 research tools with visual generation for intelligent insights
 */

import { TavilyProvider } from '../../research-providers/tavily-provider.js';
import { Context7Provider } from '../../research-providers/context7-provider.js';
import { FirecrawlProvider } from '../../research-providers/firecrawl-provider.js';
import { ComponentLibraryResearch } from './component-library-research.js';
import { configManager } from '../../config/config-manager.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * Visual Research Interface
 * Provides research-enhanced insights for visual generation
 */
export class VisualResearchInterface {
  constructor(config = {}) {
    this.config = {
      cacheEnabled: config.cacheEnabled !== false,
      cacheDuration: config.cacheDuration || 3600000, // 1 hour
      maxConcurrentRequests: config.maxConcurrentRequests || 3,
      researchWeight: config.researchWeight || 0.3, // 30% research influence
      ...config
    };

    this.cache = new Map();
    this.providers = {};
    this.initialized = false;

    // Initialize enhanced component library research
    this.componentLibraryResearch = new ComponentLibraryResearch({
      enableResearch: config.enableComponentResearch !== false,
      cacheResults: config.cacheEnabled !== false,
      maxLibrariesPerFramework: config.maxLibrariesPerFramework || 4,
      parallelResearch: config.parallelResearch !== false
    });

    // Load domain configuration from config or use defaults (async initialization)
    this.domainConfig = null;
    this.libraryConfig = null;
    this.configLoaded = false;
  }

  /**
   * Initialize research providers and configuration
   */
  async initialize() {
    if (this.initialized) return;

    try {
      // Load configuration first
      if (!this.configLoaded) {
        this.domainConfig = await this.loadDomainConfiguration(this.config.domains);
        this.libraryConfig = await this.loadLibraryConfiguration(this.config.libraries);
        this.configLoaded = true;
      }

      const researchConfig = configManager.getResearchConfig();

      // Initialize research providers
      this.providers = {
        tavily: new TavilyProvider(researchConfig.providers.tavily),
        context7: new Context7Provider(researchConfig.providers.context7),
        firecrawl: new FirecrawlProvider(researchConfig.providers.firecrawl)
      };

      this.initialized = true;
    } catch (error) {
      console.warn('Research providers initialization failed, continuing without research:', error.message);
      this.providers = {};

      // Ensure fallback config is loaded
      if (!this.configLoaded) {
        this.domainConfig = this.getFallbackDomainConfiguration(this.config.domains);
        this.libraryConfig = this.getFallbackLibraryConfiguration(this.config.libraries);
        this.configLoaded = true;
      }
    }
  }

  /**
   * Research UI best practices for component type
   */
  async researchUIBestPractices(componentType, context = {}) {
    await this.initialize();
    
    const cacheKey = `ui_practices_${componentType}_${JSON.stringify(context)}`;
    if (this.config.cacheEnabled && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.config.cacheDuration) {
        return cached.data;
      }
    }

    const queries = this.generateUIBestPracticesQueries(componentType, context);
    const results = await this.executeResearchQueries(queries, 'ui_practices');
    
    const insights = this.synthesizeUIBestPractices(results, componentType);
    
    if (this.config.cacheEnabled) {
      this.cache.set(cacheKey, {
        data: insights,
        timestamp: Date.now()
      });
    }

    return insights;
  }

  /**
   * Research component design patterns using enhanced library research
   */
  async researchComponentPatterns(componentType, framework = 'react', context = {}) {
    await this.initialize();

    const cacheKey = `component_patterns_${componentType}_${framework}_${JSON.stringify(context)}`;
    if (this.config.cacheEnabled && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.config.cacheDuration) {
        return cached.data;
      }
    }

    try {
      // Use enhanced component library research for comprehensive analysis
      const enhancedResearch = await this.componentLibraryResearch.researchComponentLibraries(
        componentType,
        framework,
        context
      );

      // Transform to maintain backward compatibility while adding enhanced data
      const patterns = {
        componentType,
        framework,
        patterns: enhancedResearch.patterns || [],
        codeExamples: enhancedResearch.codeExamples || [],
        recommendations: enhancedResearch.recommendations || [],
        insights: enhancedResearch.insights || [],
        libraries: enhancedResearch.libraries || [],
        metadata: {
          ...enhancedResearch.metadata,
          provider: 'Enhanced Context7',
          enhancedResearch: true,
          timestamp: new Date().toISOString()
        }
      };

      if (this.config.cacheEnabled) {
        this.cache.set(cacheKey, {
          data: patterns,
          timestamp: Date.now()
        });
      }

      return patterns;
    } catch (error) {
      console.warn('Enhanced component pattern research failed, using fallback:', error.message);
      // Fallback to original implementation
      const patterns = await this.executeComponentPatternResearch(componentType, framework, context);

      if (this.config.cacheEnabled) {
        this.cache.set(cacheKey, {
          data: patterns,
          timestamp: Date.now()
        });
      }

      return patterns;
    }
  }

  /**
   * Research accessibility guidelines for component
   */
  async researchAccessibilityGuidelines(componentType, context = {}) {
    await this.initialize();
    
    const cacheKey = `accessibility_${componentType}_${JSON.stringify(context)}`;
    if (this.config.cacheEnabled && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.config.cacheDuration) {
        return cached.data;
      }
    }

    const guidelines = await this.executeAccessibilityResearch(componentType, context);
    
    if (this.config.cacheEnabled) {
      this.cache.set(cacheKey, {
        data: guidelines,
        timestamp: Date.now()
      });
    }

    return guidelines;
  }

  /**
   * Research design system recommendations
   */
  async researchDesignSystemRecommendations(components, context = {}) {
    await this.initialize();
    
    const cacheKey = `design_system_${components.map(c => c.type).join('_')}_${JSON.stringify(context)}`;
    if (this.config.cacheEnabled && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.config.cacheDuration) {
        return cached.data;
      }
    }

    const recommendations = await this.executeDesignSystemResearch(components, context);
    
    if (this.config.cacheEnabled) {
      this.cache.set(cacheKey, {
        data: recommendations,
        timestamp: Date.now()
      });
    }

    return recommendations;
  }

  /**
   * Generate UI best practices queries
   */
  generateUIBestPracticesQueries(componentType, context) {
    const baseQueries = [
      `${componentType} UI best practices 2025 design guidelines`,
      `${componentType} user experience UX patterns accessibility`,
      `${componentType} design system component standards`
    ];

    // Add context-specific queries
    if (context.industry) {
      baseQueries.push(`${componentType} ${context.industry} industry UI standards`);
    }

    if (context.platform) {
      baseQueries.push(`${componentType} ${context.platform} platform design guidelines`);
    }

    return baseQueries;
  }

  /**
   * Execute research queries across providers
   */
  async executeResearchQueries(queries, type) {
    if (!this.providers.tavily) {
      return { results: [], metadata: { provider: 'none', reason: 'providers_not_available' } };
    }

    const results = [];
    const maxQueries = Math.min(queries.length, 3); // Limit to avoid rate limits

    for (let i = 0; i < maxQueries; i++) {
      try {
        const result = await this.providers.tavily.search(queries[i], {
          maxResults: 3,
          searchDepth: 'basic',
          includeDomains: this.getRelevantDomains(type)
        });
        results.push(result);
      } catch (error) {
        console.warn(`Research query failed: ${queries[i]}`, error.message);
      }
    }

    return results;
  }

  /**
   * Execute component pattern research using Context7
   */
  async executeComponentPatternResearch(componentType, framework, context) {
    if (!this.providers.context7) {
      return this.getFallbackComponentPatterns(componentType, framework);
    }

    try {
      // Research component libraries and patterns
      const libraryQueries = this.getComponentLibraries(framework);
      const patterns = [];

      for (const library of libraryQueries.slice(0, 2)) { // Limit to 2 libraries
        try {
          const research = await this.providers.context7.researchLibrary(library, [
            componentType,
            'examples',
            'best-practices'
          ], { tokensPerTopic: 3000 });
          
          patterns.push({
            library,
            patterns: this.extractPatterns(research),
            examples: research.codeExamples || [],
            documentation: research.topics || []
          });
        } catch (error) {
          console.warn(`Library research failed for ${library}:`, error.message);
        }
      }

      return {
        componentType,
        framework,
        patterns,
        recommendations: this.generatePatternRecommendations(patterns),
        metadata: {
          provider: 'Context7',
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.warn('Component pattern research failed:', error.message);
      return this.getFallbackComponentPatterns(componentType, framework);
    }
  }

  /**
   * Execute accessibility research
   */
  async executeAccessibilityResearch(componentType, context) {
    if (!this.providers.tavily) {
      return this.getFallbackAccessibilityGuidelines(componentType);
    }

    try {
      const queries = [
        `${componentType} WCAG accessibility guidelines ARIA`,
        `${componentType} screen reader keyboard navigation`,
        `${componentType} accessibility best practices 2025`
      ];

      const results = await this.executeResearchQueries(queries, 'accessibility');
      
      return {
        componentType,
        guidelines: this.extractAccessibilityGuidelines(results),
        ariaAttributes: this.extractAriaAttributes(results),
        keyboardNavigation: this.extractKeyboardNavigation(results),
        screenReaderSupport: this.extractScreenReaderGuidelines(results),
        recommendations: this.generateAccessibilityRecommendations(results),
        metadata: {
          provider: 'Tavily',
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.warn('Accessibility research failed:', error.message);
      return this.getFallbackAccessibilityGuidelines(componentType);
    }
  }

  /**
   * Execute design system research
   */
  async executeDesignSystemResearch(components, context) {
    if (!this.providers.tavily) {
      return this.getFallbackDesignSystemRecommendations(components);
    }

    try {
      const componentTypes = components.map(c => c.type).join(' ');
      const queries = [
        `design system ${componentTypes} component library`,
        `${componentTypes} design tokens spacing typography`,
        `design system best practices ${componentTypes} consistency`
      ];

      const results = await this.executeResearchQueries(queries, 'design_system');
      
      return {
        components: componentTypes,
        designTokens: this.extractDesignTokens(results),
        spacing: this.extractSpacingGuidelines(results),
        typography: this.extractTypographyGuidelines(results),
        colorPalette: this.extractColorGuidelines(results),
        consistency: this.extractConsistencyGuidelines(results),
        recommendations: this.generateDesignSystemRecommendations(results),
        metadata: {
          provider: 'Tavily',
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.warn('Design system research failed:', error.message);
      return this.getFallbackDesignSystemRecommendations(components);
    }
  }

  // Configuration loading methods
  async loadDomainConfiguration(customDomains = {}) {
    try {
      // Try to load from configuration file first
      const configPath = path.join(process.cwd(), 'src/visual-generation/config/research-domains.json');
      const configData = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configData);

      // Merge with custom domains
      const merged = { ...config.domains };
      for (const [type, domains] of Object.entries(customDomains)) {
        if (merged[type]) {
          merged[type] = [...new Set([...merged[type], ...domains])];
        } else {
          merged[type] = domains;
        }
      }

      return merged;
    } catch (error) {
      console.warn('Could not load research domains config, using fallback:', error.message);
      return this.getFallbackDomainConfiguration(customDomains);
    }
  }

  getFallbackDomainConfiguration(customDomains = {}) {
    // Fallback configuration if file loading fails
    const defaultDomains = {
      ui_practices: [
        'material.io', 'developer.apple.com', 'design.microsoft.com',
        'ant.design', 'chakra-ui.com', 'mui.com', 'ui.shadcn.com',
        'tailwindui.com', 'headlessui.com', 'mantine.dev'
      ],
      accessibility: [
        'w3.org', 'webaim.org', 'a11y.org', 'accessibility.blog.gov.uk',
        'deque.com', 'axe-core.org'
      ],
      design_system: [
        'designsystems.com', 'atomicdesign.bradfrost.com', 'material.io',
        'spectrum.adobe.com', 'polaris.shopify.com', 'primer.style'
      ],
      component_libraries: [
        'storybook.js.org', 'bit.dev', 'github.com'
      ]
    };

    // Merge custom domains with defaults
    const merged = { ...defaultDomains };
    for (const [type, domainConfig] of Object.entries(customDomains)) {
      if (Array.isArray(domainConfig)) {
        // Simple array format
        if (merged[type]) {
          merged[type] = [...new Set([...merged[type], ...domainConfig])];
        } else {
          merged[type] = domainConfig;
        }
      } else if (domainConfig && typeof domainConfig === 'object') {
        // Object format with add/remove
        if (!merged[type]) {
          merged[type] = [];
        }

        if (domainConfig.add && Array.isArray(domainConfig.add)) {
          merged[type] = [...new Set([...merged[type], ...domainConfig.add])];
        }

        if (domainConfig.remove && Array.isArray(domainConfig.remove)) {
          merged[type] = merged[type].filter(domain => !domainConfig.remove.includes(domain));
        }
      }
    }

    return merged;
  }

  async loadLibraryConfiguration(customLibraries = {}) {
    try {
      // Try to load from configuration file first
      const configPath = path.join(process.cwd(), 'src/visual-generation/config/research-domains.json');
      const configData = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configData);

      // Merge with custom libraries
      const merged = { ...config.libraries };
      for (const [framework, libraries] of Object.entries(customLibraries)) {
        if (merged[framework]) {
          merged[framework] = [...new Set([...merged[framework], ...libraries])];
        } else {
          merged[framework] = libraries;
        }
      }

      return merged;
    } catch (error) {
      console.warn('Could not load library config, using fallback:', error.message);
      return this.getFallbackLibraryConfiguration(customLibraries);
    }
  }

  getFallbackLibraryConfiguration(customLibraries = {}) {
    const defaultLibraries = {
      react: [
        'react', 'mui', 'chakra-ui', 'ant-design', 'mantine',
        'react-bootstrap', 'semantic-ui-react', 'headlessui',
        'shadcn/ui', 'nextui', 'arco-design'
      ],
      vue: [
        'vue', 'vuetify', 'quasar', 'element-plus', 'naive-ui',
        'ant-design-vue', 'primevue', 'vuestic-ui'
      ],
      angular: [
        'angular', 'angular-material', 'ng-bootstrap', 'primeng',
        'ng-zorro-antd', 'clarity', 'nebular'
      ],
      svelte: [
        'svelte', 'svelteui', 'carbon-components-svelte', 'smelte',
        'attractions', 'svelte-material-ui'
      ],
      solid: [
        'solid-js', 'hope-ui', 'solid-ui', 'kobalte'
      ],
      web_components: [
        'lit', 'stencil', 'fast', 'shoelace', 'lion'
      ]
    };

    // Merge custom libraries with defaults
    const merged = { ...defaultLibraries };
    for (const [framework, libraryConfig] of Object.entries(customLibraries)) {
      if (Array.isArray(libraryConfig)) {
        // Simple array format
        if (merged[framework]) {
          merged[framework] = [...new Set([...merged[framework], ...libraryConfig])];
        } else {
          merged[framework] = libraryConfig;
        }
      } else if (libraryConfig && typeof libraryConfig === 'object') {
        // Object format with add/remove
        if (!merged[framework]) {
          merged[framework] = [];
        }

        if (libraryConfig.add && Array.isArray(libraryConfig.add)) {
          merged[framework] = [...new Set([...merged[framework], ...libraryConfig.add])];
        }

        if (libraryConfig.remove && Array.isArray(libraryConfig.remove)) {
          merged[framework] = merged[framework].filter(lib => !libraryConfig.remove.includes(lib));
        }
      }
    }

    return merged;
  }

  // Helper methods
  getRelevantDomains(type) {
    if (!this.domainConfig) {
      console.warn('Domain config not loaded, using empty array');
      return [];
    }
    return this.domainConfig[type] || [];
  }

  getComponentLibraries(framework) {
    if (!this.libraryConfig) {
      console.warn('Library config not loaded, using empty array');
      return [];
    }
    return this.libraryConfig[framework] || this.libraryConfig.react || [];
  }

  // Synthesis methods
  synthesizeUIBestPractices(results, componentType) {
    const practices = [];
    
    for (const result of results) {
      if (result.results) {
        for (const item of result.results) {
          practices.push(...this.extractPracticesFromContent(item.content || item.snippet || ''));
        }
      }
    }

    return {
      componentType,
      bestPractices: this.deduplicatePractices(practices),
      recommendations: this.generateUIRecommendations(practices, componentType),
      confidence: this.calculateConfidence(results),
      metadata: {
        provider: 'Tavily',
        timestamp: new Date().toISOString()
      }
    };
  }

  extractPracticesFromContent(content) {
    const practices = [];
    const practicePatterns = [
      /best practice[s]?:?\s*([^.!?]+)/gi,
      /should\s+([^.!?]+)/gi,
      /recommended?\s+([^.!?]+)/gi,
      /guideline[s]?:?\s*([^.!?]+)/gi
    ];

    for (const pattern of practicePatterns) {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        if (match[1] && match[1].length > 10 && match[1].length < 200) {
          practices.push(match[1].trim());
        }
      }
    }

    return practices;
  }

  deduplicatePractices(practices) {
    const unique = new Set();
    const result = [];
    
    for (const practice of practices) {
      const normalized = practice.toLowerCase().replace(/[^\w\s]/g, '');
      if (!unique.has(normalized) && normalized.length > 10) {
        unique.add(normalized);
        result.push(practice);
      }
    }
    
    return result.slice(0, 10); // Limit to top 10
  }

  generateUIRecommendations(practices, componentType) {
    // Generate specific recommendations based on component type and practices
    const recommendations = [];
    
    if (componentType === 'form') {
      recommendations.push('Use clear labels and validation feedback');
      recommendations.push('Implement proper error handling and messaging');
      recommendations.push('Ensure keyboard accessibility and tab order');
    } else if (componentType === 'table') {
      recommendations.push('Include sorting and filtering capabilities');
      recommendations.push('Implement pagination for large datasets');
      recommendations.push('Use zebra striping for better readability');
    } else if (componentType === 'navigation') {
      recommendations.push('Maintain consistent navigation patterns');
      recommendations.push('Provide clear visual hierarchy');
      recommendations.push('Support both mouse and keyboard navigation');
    }

    return recommendations;
  }

  calculateConfidence(results) {
    if (!results || results.length === 0) return 0;
    
    const totalResults = results.reduce((sum, result) => 
      sum + (result.results ? result.results.length : 0), 0
    );
    
    return Math.min(totalResults / 10, 1); // Max confidence of 1.0
  }

  // Fallback methods for when research providers are unavailable
  getFallbackComponentPatterns(componentType, framework) {
    return {
      componentType,
      framework,
      patterns: [],
      recommendations: [`Use standard ${framework} ${componentType} patterns`],
      metadata: { provider: 'fallback', timestamp: new Date().toISOString() }
    };
  }

  getFallbackAccessibilityGuidelines(componentType) {
    return {
      componentType,
      guidelines: ['Follow WCAG 2.1 AA guidelines', 'Use semantic HTML', 'Provide ARIA labels'],
      recommendations: ['Ensure keyboard navigation', 'Support screen readers'],
      metadata: { provider: 'fallback', timestamp: new Date().toISOString() }
    };
  }

  getFallbackDesignSystemRecommendations(components) {
    return {
      components: components.map(c => c.type).join(' '),
      recommendations: ['Use consistent spacing', 'Maintain visual hierarchy', 'Follow brand guidelines'],
      metadata: { provider: 'fallback', timestamp: new Date().toISOString() }
    };
  }

  // Additional extraction methods (simplified for brevity)
  extractPatterns(research) { return research.patterns || []; }
  extractAccessibilityGuidelines(results) { return ['Use ARIA labels', 'Ensure keyboard navigation']; }
  extractAriaAttributes(results) { return ['aria-label', 'aria-describedby', 'role']; }
  extractKeyboardNavigation(results) { return ['Tab navigation', 'Enter/Space activation']; }
  extractScreenReaderGuidelines(results) { return ['Descriptive labels', 'Status announcements']; }
  extractDesignTokens(results) { return { spacing: '8px', colors: 'brand palette' }; }
  extractSpacingGuidelines(results) { return { base: '8px', scale: '1.5' }; }
  extractTypographyGuidelines(results) { return { fontFamily: 'system-ui', scale: 'modular' }; }
  extractColorGuidelines(results) { return { primary: '#007bff', contrast: 'AA' }; }
  extractConsistencyGuidelines(results) { return ['Use design tokens', 'Follow patterns']; }
  
  generatePatternRecommendations(patterns) { return ['Follow established patterns']; }
  generateAccessibilityRecommendations(results) { return ['Test with screen readers']; }
  generateDesignSystemRecommendations(results) { return ['Use consistent tokens']; }
}
