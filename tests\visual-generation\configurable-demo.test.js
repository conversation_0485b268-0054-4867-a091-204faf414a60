/**
 * Demo of Configurable Research System
 */

import { describe, it, expect } from 'bun:test';
import { VisualResearchInterface } from '../../src/visual-generation/research/visual-research-interface.js';

describe('Configurable Research Demo', () => {
  it('should demonstrate configurable domains and libraries', async () => {
    console.log('\n🔧 CONFIGURABLE RESEARCH SYSTEM DEMO');
    console.log('=' .repeat(60));

    // Example 1: Default configuration
    console.log('\n📋 DEFAULT CONFIGURATION:');
    const defaultInterface = new VisualResearchInterface();
    await defaultInterface.initialize();
    
    const defaultUIDomains = defaultInterface.getRelevantDomains('ui_practices');
    const defaultReactLibs = defaultInterface.getComponentLibraries('react');
    
    console.log('\n  UI Practices Domains:');
    defaultUIDomains.slice(0, 5).forEach(domain => console.log(`    • ${domain}`));
    console.log(`    ... and ${defaultUIDomains.length - 5} more`);
    
    console.log('\n  React Libraries:');
    defaultReactLibs.slice(0, 5).forEach(lib => console.log(`    • ${lib}`));
    console.log(`    ... and ${defaultReactLibs.length - 5} more`);

    // Example 2: Custom configuration for a startup
    console.log('\n🚀 STARTUP CONFIGURATION:');
    const startupInterface = new VisualResearchInterface({
      domains: {
        ui_practices: {
          add: ['ui.shadcn.com', 'daisyui.com', 'tailwindui.com'],
          remove: ['material.io'] // Don't want Google's heavy design system
        },
        modern_tools: {
          add: ['vercel.com', 'netlify.com', 'railway.app']
        }
      },
      libraries: {
        react: {
          add: ['shadcn/ui', 'radix-ui', 'lucide-react'],
          remove: ['mui', 'ant-design'] // Too heavy for startup
        },
        css: {
          add: ['tailwindcss', 'styled-components', 'stitches']
        }
      }
    });
    
    await startupInterface.initialize();
    
    const startupUIDomains = startupInterface.getRelevantDomains('ui_practices');
    const startupReactLibs = startupInterface.getComponentLibraries('react');
    const modernTools = startupInterface.getRelevantDomains('modern_tools');
    const cssLibs = startupInterface.getComponentLibraries('css');
    
    console.log('\n  UI Practices Domains (customized):');
    startupUIDomains.forEach(domain => console.log(`    • ${domain}`));
    
    console.log('\n  React Libraries (lightweight focus):');
    startupReactLibs.forEach(lib => console.log(`    • ${lib}`));
    
    console.log('\n  Modern Tools (custom category):');
    modernTools.forEach(tool => console.log(`    • ${tool}`));
    
    console.log('\n  CSS Libraries (custom framework):');
    cssLibs.forEach(lib => console.log(`    • ${lib}`));

    // Example 3: Enterprise configuration
    console.log('\n🏢 ENTERPRISE CONFIGURATION:');
    const enterpriseInterface = new VisualResearchInterface({
      domains: {
        ui_practices: {
          add: ['company-design-system.com', 'internal-guidelines.com']
        },
        compliance: {
          add: ['sox-compliance.org', 'gdpr-guidelines.eu', 'hipaa-guide.com']
        },
        security: {
          add: ['owasp.org', 'nist.gov', 'security-standards.org']
        }
      },
      libraries: {
        react: {
          add: ['@company/ui-kit', '@company/components', 'enterprise-react']
        },
        internal: {
          add: ['legacy-components', 'shared-ui', 'company-widgets']
        },
        approved: {
          add: ['vetted-library-1', 'approved-components', 'secure-ui-kit']
        }
      }
    });
    
    await enterpriseInterface.initialize();
    
    const enterpriseUIDomains = enterpriseInterface.getRelevantDomains('ui_practices');
    const complianceDomains = enterpriseInterface.getRelevantDomains('compliance');
    const securityDomains = enterpriseInterface.getRelevantDomains('security');
    const internalLibs = enterpriseInterface.getComponentLibraries('internal');
    const approvedLibs = enterpriseInterface.getComponentLibraries('approved');
    
    console.log('\n  UI Practices (with internal):');
    enterpriseUIDomains.forEach(domain => console.log(`    • ${domain}`));
    
    console.log('\n  Compliance Domains:');
    complianceDomains.forEach(domain => console.log(`    • ${domain}`));
    
    console.log('\n  Security Domains:');
    securityDomains.forEach(domain => console.log(`    • ${domain}`));
    
    console.log('\n  Internal Libraries:');
    internalLibs.forEach(lib => console.log(`    • ${lib}`));
    
    console.log('\n  Approved Libraries:');
    approvedLibs.forEach(lib => console.log(`    • ${lib}`));

    // Example 4: Agency configuration
    console.log('\n🎨 AGENCY CONFIGURATION:');
    const agencyInterface = new VisualResearchInterface({
      domains: {
        ui_practices: {
          add: ['dribbble.com', 'behance.net', 'awwwards.com']
        },
        client_brands: {
          add: ['client1-brand.com', 'client2-style.com', 'client3-design.com']
        }
      },
      libraries: {
        react: { add: ['framer-motion', 'react-spring', 'styled-components'] },
        vue: { add: ['nuxt', 'vuetify', 'quasar'] },
        angular: { add: ['angular-material', 'primeng'] },
        animation: { add: ['gsap', 'lottie', 'anime.js'] }
      }
    });
    
    await agencyInterface.initialize();
    
    const agencyUIDomains = agencyInterface.getRelevantDomains('ui_practices');
    const clientBrands = agencyInterface.getRelevantDomains('client_brands');
    const animationLibs = agencyInterface.getComponentLibraries('animation');
    
    console.log('\n  UI Practices (design-focused):');
    agencyUIDomains.forEach(domain => console.log(`    • ${domain}`));
    
    console.log('\n  Client Brand Guidelines:');
    clientBrands.forEach(brand => console.log(`    • ${brand}`));
    
    console.log('\n  Animation Libraries:');
    animationLibs.forEach(lib => console.log(`    • ${lib}`));

    console.log('\n💡 CONFIGURATION BENEFITS:');
    console.log('  ✅ No hardcoded domains - fully customizable');
    console.log('  ✅ Add/remove domains and libraries dynamically');
    console.log('  ✅ Support for custom categories and frameworks');
    console.log('  ✅ Industry-specific configurations');
    console.log('  ✅ Company-specific internal resources');
    console.log('  ✅ Fallback to defaults if config fails');

    // Verify the configurations work
    expect(defaultUIDomains.length).toBeGreaterThan(0);
    expect(startupUIDomains.length).toBeGreaterThan(0);
    expect(enterpriseUIDomains.length).toBeGreaterThan(0);
    expect(agencyUIDomains.length).toBeGreaterThan(0);
    
    expect(modernTools.length).toBeGreaterThan(0);
    expect(complianceDomains.length).toBeGreaterThan(0);
    expect(clientBrands.length).toBeGreaterThan(0);
    
    expect(cssLibs.length).toBeGreaterThan(0);
    expect(internalLibs.length).toBeGreaterThan(0);
    expect(animationLibs.length).toBeGreaterThan(0);
  });

  it('should demonstrate query generation with custom domains', async () => {
    console.log('\n🔍 CUSTOM QUERY GENERATION DEMO:');
    console.log('=' .repeat(50));

    const customInterface = new VisualResearchInterface({
      domains: {
        ui_practices: {
          add: ['ui.shadcn.com', 'daisyui.com']
        }
      }
    });
    
    await customInterface.initialize();

    // Generate queries for different component types
    const formQueries = customInterface.generateUIBestPracticesQueries('form', {
      industry: 'healthcare'
    });
    
    const tableQueries = customInterface.generateUIBestPracticesQueries('table', {
      industry: 'finance'
    });

    console.log('\n📝 Form Queries (Healthcare):');
    formQueries.forEach(query => console.log(`    • ${query}`));

    console.log('\n📊 Table Queries (Finance):');
    tableQueries.forEach(query => console.log(`    • ${query}`));

    expect(formQueries.length).toBeGreaterThan(0);
    expect(tableQueries.length).toBeGreaterThan(0);
    expect(formQueries.some(q => q.includes('form'))).toBe(true);
    expect(formQueries.some(q => q.includes('healthcare'))).toBe(true);
    expect(tableQueries.some(q => q.includes('table'))).toBe(true);
    expect(tableQueries.some(q => q.includes('finance'))).toBe(true);
  });
});
