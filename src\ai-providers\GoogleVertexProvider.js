import { createVertex } from '@ai-sdk/google-vertex';
import { generateText } from 'ai';
import { GoogleAuth } from 'google-auth-library';
import { BaseAIProvider } from './BaseAIProvider.js';

/**
 * AI Provider for Google Vertex AI (Gemini models).
 *
 * This class encapsulates all logic for interacting with Google's Vertex AI,
 * including the primary AI-SDK method and a direct API fallback. It is designed
 * to be instantiated by the `ai-services-unified` layer with a specific
 * configuration for a given task role.
 */
export class GoogleVertexProvider extends BaseAIProvider {
  constructor(config) {
    super(config);
    this.projectId = config.projectId || process.env.GOOGLE_CLOUD_PROJECT;
    this.credentialsPath = config.credentialsPath || process.env.GOOGLE_APPLICATION_CREDENTIALS;
    this.location = config.location || 'global';
    this.apiEndpoint = 'aiplatform.googleapis.com';

    // Extract relevant settings from the role-specific config
    this.safetySettings = config.safetySettings || [];
    this.thinkingConfig = config.thinkingConfig || {};

    // Debug logging for configuration (only in debug mode)
    if (process.env.GUIDANT_DEBUG === 'true') {
      console.log(`🔧 [DEBUG] GoogleVertexProvider constructor config:`, {
        modelId: config.modelId,
        projectId: this.projectId,
        location: this.location,
        credentialsPath: this.credentialsPath,
        supportsThinking: config.supportsThinking,
        thinkingConfig: config.thinkingConfig,
        hasThinkingConfig: !!config.thinkingConfig,
        thinkingConfigKeys: config.thinkingConfig ? Object.keys(config.thinkingConfig) : []
      });
    }

    this.vertex = null;
    this.auth = null;
    this.initialized = false;
  }

  /**
   * Initializes the Vertex AI clients (both AI-SDK and Google Auth).
   * This is called lazily on the first generation request.
   */
  async initialize() {
    if (this.initialized) return;

    try {
      this.vertex = createVertex({
        projectId: this.projectId,
        location: this.location,
        googleAuthOptions: {
          keyFilename: this.credentialsPath,
        },
      });

      this.auth = new GoogleAuth({
        keyFilename: this.credentialsPath,
        scopes: ['https://www.googleapis.com/auth/cloud-platform'],
      });

      this.initialized = true;
      console.log(`✅ GoogleVertexProvider initialized for model: ${this.modelId}`);
    } catch (error) {
      throw new Error(`Failed to initialize GoogleVertexProvider: ${error.message}`);
    }
  }

  /**
   * The main method for generating text, overriding the base provider method.
   * It orchestrates the process, including initialization and the retry/fallback logic.
   *
   * @param {string} prompt The input prompt for the AI model.
   * @param {object} options Runtime options like maxRetries.
   * @returns {Promise<object>} An object containing the generated text and metadata.
   */
  async generate(prompt, options = {}) {
    await this.initialize();

    const maxRetries = options.maxRetries || 2;
    let lastError;

    // 1. Try AI SDK approach
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[${this.modelId}] SDK Attempt ${attempt}/${maxRetries}...`);
        const result = await this.generateWithSDK(prompt, options);
        console.log(`✅ [${this.modelId}] Success with AI SDK.`);
        return result;
      } catch (error) {
        lastError = error;
        console.warn(`❌ [${this.modelId}] SDK Attempt ${attempt} failed: ${error.message}`);
        if (attempt < maxRetries) await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // 2. Fallback to Direct API approach
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`[${this.modelId}] Fallback API Attempt ${attempt}/${maxRetries}...`);
        const result = await this.generateWithDirectAPI(prompt, options);
        console.log(`✅ [${this.modelId}] Success with Direct API.`);
        return result;
      } catch (error) {
        lastError = error;
        console.warn(`❌ [${this.modelId}] Direct API Attempt ${attempt} failed: ${error.message}`);
        if (attempt < maxRetries) await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    throw new Error(`[${this.modelId}] All generation attempts failed. Last error: ${lastError.message}`);
  }

  /**
   * Generates text using the Vercel AI SDK.
   */
  async generateWithSDK(prompt, options) {
    const maxTokens = Math.min(options.maxTokens || this.config.maxTokens, 65535);
    const providerOptions = {};
    if (this.config.supportsThinking) {
      providerOptions.google = { thinkingConfig: this.thinkingConfig };
      if (process.env.GUIDANT_DEBUG === 'true') {
        console.log(`🧠 [DEBUG] Using thinkingConfig:`, JSON.stringify(this.thinkingConfig));
      }
    }

    if (process.env.GUIDANT_DEBUG === 'true') {
      console.log(`🔧 [DEBUG] SDK Request config:`, {
        modelId: this.modelId,
        temperature: options.temperature || this.config.temperature,
        maxTokens,
        supportsThinking: this.config.supportsThinking,
        providerOptions
      });
    }

    const generateTextOptions = {
      model: this.vertex(this.modelId, {
        project: this.projectId,
        location: this.location,
      }),
      prompt: prompt,
      temperature: options.temperature || this.config.temperature,
      maxTokens: maxTokens,
      safetySettings: this.safetySettings,
    };

    // Only add providerOptions if we have thinking config
    if (Object.keys(providerOptions).length > 0) {
      generateTextOptions.providerOptions = providerOptions;
    }

    if (process.env.GUIDANT_DEBUG === 'true') {
      console.log(`🔧 [DEBUG] Final generateText options:`, generateTextOptions);
    }

    const result = await generateText(generateTextOptions);

    if (process.env.GUIDANT_DEBUG === 'true') {
      console.log(`🔧 [DEBUG] SDK Response:`, {
        hasText: !!result.text,
        textLength: result.text?.length || 0,
        textPreview: result.text?.substring(0, 100) + '...',
        finishReason: result.finishReason,
        usage: result.usage
      });
    }

    if (!result.text || result.text.trim().length === 0) {
      throw new Error('Empty response from AI SDK');
    }

    return {
      text: result.text,
      usage: this.getUsage(result),
      method: 'ai-sdk',
      finishReason: result.finishReason,
    };
  }

  /**
   * Generates text using a direct call to the Google Cloud API.
   */
  async generateWithDirectAPI(prompt, options) {
    const client = await this.auth.getClient();
    const accessToken = await client.getAccessToken();
    const maxOutputTokens = Math.min(options.maxTokens || this.config.maxTokens, 65535);

    const generationConfig = {
      temperature: options.temperature || this.config.temperature,
      maxOutputTokens: maxOutputTokens,
      topP: 1,
    };

    if (this.config.supportsThinking) {
      generationConfig.thinkingConfig = this.thinkingConfig;
    }

    const requestBody = {
      contents: [{ role: 'user', parts: [{ text: prompt }] }],
      generationConfig: generationConfig,
      safetySettings: this.safetySettings.map(s => ({
        category: s.category,
        threshold: s.threshold, // Use the actual threshold from config
      })),
    };

    const apiUrl = `https://${this.location}-${this.apiEndpoint}/v1/projects/${this.projectId}/locations/${this.location}/publishers/google/models/${this.modelId}:generateContent`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken.token}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
        const errorBody = await response.text();
        throw new Error(`API request failed: ${response.status} ${response.statusText} - ${errorBody}`);
    }

    const data = await response.json();

    if (data.error) throw new Error(`API Error: ${data.error.message}`);
    if (!data.candidates?.[0]) throw new Error('No candidates in response');

    const candidate = data.candidates[0];
    if (candidate.finishReason === 'SAFETY') throw new Error('Response blocked by safety filters');
    if (!candidate.content?.parts?.[0]?.text) throw new Error('No content in response');

    return {
      text: candidate.content.parts[0].text,
      usage: this.getUsage({ usage: data.usageMetadata }),
      method: 'direct-api',
      finishReason: candidate.finishReason,
      safetyRatings: candidate.safetyRatings || [],
    };
  }

  /**
   * Overrides the base method to parse usage data from Vertex AI responses.
   */
  getUsage(response) {
    const usage = response.usage || {};
    // Vertex AI SDK provides camelCase, direct API provides snake_case
    return {
      inputTokens: usage.inputTokens || usage.prompt_token_count || 0,
      outputTokens: usage.outputTokens || usage.candidates_token_count || 0,
      totalTokens: usage.totalTokens || usage.total_token_count || 0,
      cost: 0, // Cost calculation would be implemented here
    };
  }
} 