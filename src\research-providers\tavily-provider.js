/**
 * Tavily Research Provider
 * Implements real-time web search, market research, competitive analysis, 
 * news monitoring, and fact verification using Tavily API
 */

import { BaseResearchProvider } from './base-research-provider.js';

/**
 * Tavily-specific result ranking and filtering
 */
export class TavilyResultRanker {
  constructor(config = {}) {
    this.relevanceThreshold = config.relevanceThreshold || 0.7;
    this.maxAge = config.maxAge || 30; // days
    this.domainWeights = config.domainWeights || {};
  }

  /**
   * Rank and filter search results
   * @param {Array} results - Raw Tavily results
   * @param {object} context - Search context for ranking
   * @returns {Array} Ranked and filtered results
   */
  rankResults(results, context = {}) {
    return results
      .map(result => this.scoreResult(result, context))
      .filter(result => result.score >= this.relevanceThreshold)
      .sort((a, b) => b.score - a.score);
  }

  /**
   * Score individual result based on multiple factors
   * @param {object} result - Individual search result
   * @param {object} context - Search context
   * @returns {object} Result with score
   */
  scoreResult(result, context) {
    let score = result.score || 0.5; // Base relevance score from Tavily
    
    // Domain authority boost
    const domain = this.extractDomain(result.url);
    if (this.domainWeights[domain]) {
      score += this.domainWeights[domain];
    }
    
    // Recency boost
    if (result.published_date) {
      const ageInDays = this.getAgeInDays(result.published_date);
      if (ageInDays <= this.maxAge) {
        score += (this.maxAge - ageInDays) / this.maxAge * 0.2;
      }
    }
    
    // Content quality indicators
    if (result.content && result.content.length > 500) {
      score += 0.1; // Longer content bonus
    }
    
    // Query relevance in title
    if (context.query && result.title) {
      const queryWords = context.query.toLowerCase().split(' ');
      const titleWords = result.title.toLowerCase();
      const matches = queryWords.filter(word => titleWords.includes(word));
      score += (matches.length / queryWords.length) * 0.15;
    }
    
    return {
      ...result,
      score: Math.min(score, 1.0) // Cap at 1.0
    };
  }

  extractDomain(url) {
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }

  getAgeInDays(publishedDate) {
    try {
      const published = new Date(publishedDate);
      const now = new Date();
      return Math.floor((now - published) / (1000 * 60 * 60 * 24));
    } catch {
      return Infinity;
    }
  }
}

/**
 * Tavily Research Provider
 * Provides real-time web search capabilities with intelligent result ranking
 */
export class TavilyProvider extends BaseResearchProvider {
  constructor(config = {}) {
    super({
      name: 'Tavily',
      baseUrl: config.baseUrl || 'https://api.tavily.com',
      ...config
    });

    this.ranker = new TavilyResultRanker(config.ranking || {});
    
    // Tavily-specific configuration
    this.defaultSearchDepth = config.defaultSearchDepth || 'basic';
    this.defaultMaxResults = config.defaultMaxResults || 10;
    this.includeImages = config.includeImages || false;
    this.includeDomains = config.includeDomains || [];
    this.excludeDomains = config.excludeDomains || [];
  }

  /**
   * Validate Tavily configuration
   * @returns {Promise<boolean>} Configuration validity
   */
  async validateConfig() {
    if (!this.apiKey) {
      throw new Error('Tavily API key is required');
    }

    if (!this.baseUrl) {
      throw new Error('Tavily base URL is required');
    }

    // Test API key validity with a minimal request
    try {
      await this.makeRequest('/search', {
        query: 'test',
        max_results: 1
      });
      return true;
    } catch (error) {
      throw new Error(`Tavily API validation failed: ${error.message}`);
    }
  }

  /**
   * Perform web search using Tavily API
   * @param {string} query - Search query (max 400 characters per Tavily best practices)
   * @param {object} options - Search options
   * @returns {Promise<object>} Search results with metadata
   */
  async search(query, options = {}) {
    // Validate and truncate query per Tavily best practices
    const truncatedQuery = query.substring(0, 400);
    
    const searchParams = {
      query: truncatedQuery,
      max_results: options.maxResults || this.defaultMaxResults,
      search_depth: options.searchDepth || this.defaultSearchDepth,
      include_images: options.includeImages || this.includeImages,
      include_answer: options.includeAnswer !== false,
      include_raw_content: options.includeRawContent || false
    };

    // Add domain filters if specified
    if (options.includeDomains || this.includeDomains.length > 0) {
      searchParams.include_domains = options.includeDomains || this.includeDomains;
    }
    
    if (options.excludeDomains || this.excludeDomains.length > 0) {
      searchParams.exclude_domains = options.excludeDomains || this.excludeDomains;
    }

    try {
      const response = await this.makeRequest('/search', searchParams);
      
      // Rank and filter results
      const rankedResults = this.ranker.rankResults(
        response.results || [], 
        { query: truncatedQuery, ...options }
      );

      return {
        query: truncatedQuery,
        answer: response.answer,
        results: rankedResults,
        metadata: {
          totalResults: response.results?.length || 0,
          rankedResults: rankedResults.length,
          searchDepth: searchParams.search_depth,
          provider: 'Tavily',
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.emit('searchError', { query: truncatedQuery, error });
      throw new Error(`Tavily search failed: ${error.message}`);
    }
  }

  /**
   * Perform market research using optimized search strategies
   * @param {string} market - Market or industry to research
   * @param {object} options - Research options
   * @returns {Promise<object>} Market research results
   */
  async marketResearch(market, options = {}) {
    const queries = [
      `${market} market size trends 2024 2025`,
      `${market} industry analysis competitive landscape`,
      `${market} market opportunities challenges`,
      `${market} key players companies leaders`
    ];

    const searchOptions = {
      maxResults: options.maxResults || 5,
      searchDepth: 'advanced',
      includeDomains: options.includeDomains || [
        'statista.com', 'mckinsey.com', 'deloitte.com', 
        'pwc.com', 'reuters.com', 'bloomberg.com'
      ]
    };

    const results = await Promise.allSettled(
      queries.map(query => this.search(query, searchOptions))
    );

    return this.synthesizeMarketResearch(results, market);
  }

  /**
   * Perform competitive analysis
   * @param {string} company - Company or product to analyze
   * @param {object} options - Analysis options
   * @returns {Promise<object>} Competitive analysis results
   */
  async competitiveAnalysis(company, options = {}) {
    const queries = [
      `${company} competitors alternatives comparison`,
      `${company} market share competitive position`,
      `${company} strengths weaknesses analysis`,
      `${company} pricing strategy business model`
    ];

    const searchOptions = {
      maxResults: options.maxResults || 5,
      searchDepth: 'advanced'
    };

    const results = await Promise.allSettled(
      queries.map(query => this.search(query, searchOptions))
    );

    return this.synthesizeCompetitiveAnalysis(results, company);
  }

  /**
   * Monitor news and updates for specific topics
   * @param {string} topic - Topic to monitor
   * @param {object} options - Monitoring options
   * @returns {Promise<object>} News monitoring results
   */
  async newsMonitoring(topic, options = {}) {
    const timeframe = options.timeframe || 'week';
    const query = `${topic} news updates ${timeframe}`;

    const searchOptions = {
      maxResults: options.maxResults || 10,
      searchDepth: 'basic',
      includeDomains: options.newsSources || [
        'reuters.com', 'bloomberg.com', 'techcrunch.com',
        'wsj.com', 'ft.com', 'cnn.com', 'bbc.com'
      ]
    };

    const results = await this.search(query, searchOptions);
    
    return {
      ...results,
      metadata: {
        ...results.metadata,
        monitoringType: 'news',
        timeframe,
        topic
      }
    };
  }

  /**
   * Verify facts and claims
   * @param {string} claim - Claim to verify
   * @param {object} options - Verification options
   * @returns {Promise<object>} Fact verification results
   */
  async factVerification(claim, options = {}) {
    const queries = [
      `${claim} fact check verification`,
      `${claim} evidence sources research`,
      `${claim} debunked false true accurate`
    ];

    const searchOptions = {
      maxResults: options.maxResults || 5,
      searchDepth: 'advanced',
      includeDomains: options.factCheckSources || [
        'snopes.com', 'factcheck.org', 'politifact.com',
        'reuters.com', 'ap.org', 'bbc.com'
      ]
    };

    const results = await Promise.allSettled(
      queries.map(query => this.search(query, searchOptions))
    );

    return this.synthesizeFactVerification(results, claim);
  }

  /**
   * Synthesize market research results
   * @param {Array} results - Search results from multiple queries
   * @param {string} market - Market being researched
   * @returns {object} Synthesized market research
   */
  synthesizeMarketResearch(results, market) {
    const successfulResults = results
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value);

    return {
      market,
      synthesis: {
        marketSize: this.extractMarketSize(successfulResults),
        trends: this.extractTrends(successfulResults),
        competitors: this.extractCompetitors(successfulResults),
        opportunities: this.extractOpportunities(successfulResults)
      },
      sources: successfulResults,
      metadata: {
        type: 'market_research',
        queriesExecuted: results.length,
        successfulQueries: successfulResults.length,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * Synthesize competitive analysis results
   * @param {Array} results - Search results from multiple queries
   * @param {string} company - Company being analyzed
   * @returns {object} Synthesized competitive analysis
   */
  synthesizeCompetitiveAnalysis(results, company) {
    const successfulResults = results
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value);

    return {
      company,
      analysis: {
        competitors: this.extractCompetitors(successfulResults),
        marketPosition: this.extractMarketPosition(successfulResults),
        strengths: this.extractStrengths(successfulResults),
        weaknesses: this.extractWeaknesses(successfulResults)
      },
      sources: successfulResults,
      metadata: {
        type: 'competitive_analysis',
        queriesExecuted: results.length,
        successfulQueries: successfulResults.length,
        timestamp: new Date().toISOString()
      }
    };
  }

  /**
   * Synthesize fact verification results
   * @param {Array} results - Search results from multiple queries
   * @param {string} claim - Claim being verified
   * @returns {object} Synthesized fact verification
   */
  synthesizeFactVerification(results, claim) {
    const successfulResults = results
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value);

    return {
      claim,
      verification: {
        credibility: this.assessCredibility(successfulResults),
        sources: this.extractVerificationSources(successfulResults),
        evidence: this.extractEvidence(successfulResults),
        consensus: this.determineConsensus(successfulResults)
      },
      sources: successfulResults,
      metadata: {
        type: 'fact_verification',
        queriesExecuted: results.length,
        successfulQueries: successfulResults.length,
        timestamp: new Date().toISOString()
      }
    };
  }

  // Helper methods for synthesis (simplified implementations)
  extractMarketSize(results) { return 'Market size data extracted from search results'; }
  extractTrends(results) { return 'Trend analysis from search results'; }
  extractCompetitors(results) { return 'Competitor information from search results'; }
  extractOpportunities(results) { return 'Market opportunities from search results'; }
  extractMarketPosition(results) { return 'Market position analysis from search results'; }
  extractStrengths(results) { return 'Strengths analysis from search results'; }
  extractWeaknesses(results) { return 'Weaknesses analysis from search results'; }
  assessCredibility(results) { return 'Credibility assessment from search results'; }
  extractVerificationSources(results) { return 'Verification sources from search results'; }
  extractEvidence(results) { return 'Evidence analysis from search results'; }
  determineConsensus(results) { return 'Consensus determination from search results'; }
}

export default TavilyProvider;
