/**
 * @file test-decision-translator.js
 * @description Test script for the Business Decision Translation System
 */

import { DecisionTranslator } from '../../src/business-decisions/decision-translator.js';
import { OptionPresenter } from '../../src/business-decisions/option-presenter.js';
import path from 'path';

// Set project root to the current directory
const projectRoot = '.';

/**
 * Test the decision translator
 */
async function testDecisionTranslator() {
  console.log('🧪 Testing Business Decision Translation System...');
  
  try {
    // Create translator instance
    const translator = new DecisionTranslator(projectRoot);
    console.log('✅ Translator instance created successfully');
    
    // Test term translation
    const technicalTerm = 'API';
    const translatedTerm = translator.translateTerm(technicalTerm, 'novice');
    console.log(`✅ Term translation: "${technicalTerm}" → "${translatedTerm}"`);
    
    // Test decision options translation
    const decisionContext = 'framework_selection';
    const translatedOptions = await translator.translateDecisionOptions(decisionContext);
    console.log('✅ Decision options translated successfully:');
    console.log(`   Title: ${translatedOptions.title}`);
    console.log(`   Options: ${translatedOptions.options.length}`);
    
    // Create presenter instance
    const presenter = new OptionPresenter(projectRoot);
    console.log('✅ Presenter instance created successfully');
    
    // Test decision presentation
    const presentation = await presenter.presentDecision(decisionContext);
    console.log('✅ Decision presented successfully:');
    console.log(`   Title: ${presentation.title}`);
    console.log(`   Options: ${presentation.options.length}`);
    
    // Test decision recording
    const decisionId = translatedOptions.decisionId;
    const choiceId = translatedOptions.options[0].id;
    const rationale = 'This option best fits our project needs and timeline.';
    
    const recordedDecision = await translator.recordDecision(decisionId, choiceId, rationale);
    console.log('✅ Decision recorded successfully:');
    console.log(`   Decision ID: ${recordedDecision.id}`);
    console.log(`   Choice: ${recordedDecision.choiceTitle}`);
    console.log(`   Confidence: ${recordedDecision.confidenceScore}%`);
    
    console.log('\n🎉 All tests passed! Business Decision Translation System is working correctly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testDecisionTranslator(); 