/**
 * Research Services Unified Integration
 * Integrates all research providers with existing ai-services-unified.js architecture
 * Provides unified research capabilities with intelligent provider routing
 */

import { TavilyProvider } from '../research-providers/tavily-provider.js';
import { Context7Provider } from '../research-providers/context7-provider.js';
import { FirecrawlProvider } from '../research-providers/firecrawl-provider.js';
import { performAIOperation } from '../ai-integration/ai-services-unified.js';

/**
 * Research routing rules for intelligent provider selection
 */
export class ResearchRoutingRules {
  constructor() {
    this.rules = new Map([
      ['market_research', ['tavily']],
      ['competitive_analysis', ['tavily', 'firecrawl']],
      ['technical_documentation', ['context7']],
      ['library_research', ['context7']],
      ['web_content_extraction', ['firecrawl']],
      ['news_monitoring', ['tavily']],
      ['fact_verification', ['tavily']],
      ['api_documentation', ['context7']],
      ['website_analysis', ['firecrawl']],
      ['general_search', ['tavily']]
    ]);
  }

  /**
   * Plan research execution based on request type
   * @param {object} researchRequest - Research request details
   * @returns {Array} Execution plan with provider steps
   */
  planResearch(researchRequest) {
    const { type, query, options = {} } = researchRequest;
    const providers = this.rules.get(type) || ['tavily'];
    
    const plan = [];
    
    providers.forEach(provider => {
      switch (provider) {
        case 'tavily':
          plan.push(this.createTavilyStep(type, query, options));
          break;
        case 'context7':
          plan.push(this.createContext7Step(type, query, options));
          break;
        case 'firecrawl':
          plan.push(this.createFirecrawlStep(type, query, options));
          break;
      }
    });

    return plan;
  }

  createTavilyStep(type, query, options) {
    const methodMap = {
      'market_research': 'marketResearch',
      'competitive_analysis': 'competitiveAnalysis',
      'news_monitoring': 'newsMonitoring',
      'fact_verification': 'factVerification',
      'general_search': 'search'
    };

    return {
      provider: 'tavily',
      method: methodMap[type] || 'search',
      args: [query, options],
      priority: 1
    };
  }

  createContext7Step(type, query, options) {
    const methodMap = {
      'technical_documentation': 'search',
      'library_research': 'libraryResearch',
      'api_documentation': 'apiDocumentationAnalysis'
    };

    return {
      provider: 'context7',
      method: methodMap[type] || 'search',
      args: [query, options],
      priority: 1
    };
  }

  createFirecrawlStep(type, query, options) {
    const methodMap = {
      'web_content_extraction': 'scrapeUrl',
      'website_analysis': 'crawlWebsite',
      'competitive_analysis': 'bulkContentProcessing'
    };

    return {
      provider: 'firecrawl',
      method: methodMap[type] || 'search',
      args: [query, options],
      priority: 2
    };
  }
}

/**
 * Research synthesis engine for combining results from multiple providers
 */
export class ResearchSynthesizer {
  constructor(aiConfig) {
    this.aiConfig = aiConfig;
  }

  /**
   * Combine and synthesize research results
   * @param {Array} results - Results from multiple providers
   * @param {object} context - Research context
   * @returns {Promise<object>} Synthesized research findings
   */
  async combine(results, context = {}) {
    const successfulResults = results
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value);

    if (successfulResults.length === 0) {
      return {
        synthesis: 'No research results available for synthesis',
        confidence: 0,
        sources: [],
        metadata: {
          timestamp: new Date().toISOString(),
          resultsProcessed: 0
        }
      };
    }

    // Use AI to synthesize findings
    const synthesisPrompt = this.createSynthesisPrompt(successfulResults, context);
    
    try {
      const aiSynthesis = await performAIOperation(
        this.aiConfig.models?.research || this.aiConfig.models?.main || 'research',
        synthesisPrompt,
        { temperature: 0.3 }
      );

      return {
        synthesis: aiSynthesis.text || aiSynthesis.content || aiSynthesis,
        confidence: this.calculateConfidence(successfulResults),
        sources: this.extractSources(successfulResults),
        rawResults: successfulResults,
        metadata: {
          timestamp: new Date().toISOString(),
          resultsProcessed: successfulResults.length,
          synthesisMethod: 'ai_enhanced'
        }
      };
    } catch (error) {
      // Fallback to simple synthesis
      return this.simpleSynthesis(successfulResults, context);
    }
  }

  /**
   * Create synthesis prompt for AI processing
   * @param {Array} results - Research results
   * @param {object} context - Research context
   * @returns {string} Synthesis prompt
   */
  createSynthesisPrompt(results, context) {
    const resultsText = results.map((result, index) => {
      const provider = result.metadata?.provider || 'Unknown';
      const content = this.extractResultContent(result);
      return `Source ${index + 1} (${provider}):\n${content}\n`;
    }).join('\n');

    return `
Synthesize the following research findings into a comprehensive analysis:

Research Query: ${context.query || 'Not specified'}
Research Type: ${context.type || 'General'}

Research Results:
${resultsText}

Please provide:
1. Key findings summary
2. Common themes and patterns
3. Conflicting information (if any)
4. Confidence assessment
5. Actionable insights
6. Recommendations for further research

Format the response as a structured analysis that would be valuable for business decision-making.
`;
  }

  /**
   * Extract content from research result for synthesis
   * @param {object} result - Individual research result
   * @returns {string} Extracted content
   */
  extractResultContent(result) {
    if (result.synthesis) return JSON.stringify(result.synthesis, null, 2);
    if (result.answer) return result.answer;
    if (result.results && Array.isArray(result.results)) {
      return result.results.slice(0, 3).map(r => 
        r.title + ': ' + (r.content || r.summary || '')
      ).join('\n');
    }
    if (result.documentation) return result.documentation.substring(0, 1000);
    return JSON.stringify(result, null, 2).substring(0, 1000);
  }

  /**
   * Calculate confidence score based on result quality
   * @param {Array} results - Research results
   * @returns {number} Confidence score (0-1)
   */
  calculateConfidence(results) {
    if (results.length === 0) return 0;

    let totalConfidence = 0;
    let validResults = 0;

    results.forEach(result => {
      let resultConfidence = 0.5; // Base confidence

      // Provider reliability
      const provider = result.metadata?.provider;
      if (provider === 'Context7') resultConfidence += 0.2; // High reliability for docs
      if (provider === 'Tavily') resultConfidence += 0.15; // Good for web search
      if (provider === 'Firecrawl') resultConfidence += 0.1; // Good for content extraction

      // Result quality indicators
      if (result.results && result.results.length > 0) resultConfidence += 0.1;
      if (result.synthesis) resultConfidence += 0.15;
      if (result.metadata?.timestamp) resultConfidence += 0.05;

      totalConfidence += Math.min(resultConfidence, 1.0);
      validResults++;
    });

    return validResults > 0 ? totalConfidence / validResults : 0;
  }

  /**
   * Extract sources from research results
   * @param {Array} results - Research results
   * @returns {Array} Source information
   */
  extractSources(results) {
    const sources = [];

    results.forEach(result => {
      const provider = result.metadata?.provider || 'Unknown';
      const timestamp = result.metadata?.timestamp || new Date().toISOString();

      if (result.results && Array.isArray(result.results)) {
        result.results.forEach(item => {
          if (item.url) {
            sources.push({
              url: item.url,
              title: item.title || 'Untitled',
              provider,
              timestamp,
              relevanceScore: item.score || item.relevanceScore
            });
          }
        });
      } else if (result.libraryId) {
        sources.push({
          library: result.libraryId,
          provider,
          timestamp,
          type: 'documentation'
        });
      }
    });

    return sources.slice(0, 10); // Limit to top 10 sources
  }

  /**
   * Simple synthesis fallback when AI synthesis fails
   * @param {Array} results - Research results
   * @param {object} context - Research context
   * @returns {object} Simple synthesis
   */
  simpleSynthesis(results, context) {
    const summary = results.map(result => {
      const provider = result.metadata?.provider || 'Unknown';
      return `${provider}: ${this.extractResultContent(result).substring(0, 200)}...`;
    }).join('\n\n');

    return {
      synthesis: `Research Summary:\n\n${summary}`,
      confidence: this.calculateConfidence(results),
      sources: this.extractSources(results),
      rawResults: results,
      metadata: {
        timestamp: new Date().toISOString(),
        resultsProcessed: results.length,
        synthesisMethod: 'simple_fallback'
      }
    };
  }
}

/**
 * Research Services Manager
 * Main orchestrator for all research operations
 */
export class ResearchServicesManager {
  constructor(config) {
    this.config = config;
    this.providers = this.initializeProviders(config);
    this.routingRules = new ResearchRoutingRules();
    this.synthesizer = new ResearchSynthesizer(config.ai || {});
    
    // Statistics tracking
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      providerUsage: {}
    };
  }

  /**
   * Initialize research providers
   * @param {object} config - Configuration object
   * @returns {object} Initialized providers
   */
  initializeProviders(config) {
    const providers = {};

    if (config.providers?.tavily) {
      providers.tavily = new TavilyProvider(config.providers.tavily);
    }

    if (config.providers?.context7) {
      providers.context7 = new Context7Provider(config.providers.context7);
    }

    if (config.providers?.firecrawl) {
      providers.firecrawl = new FirecrawlProvider(config.providers.firecrawl);
    }

    return providers;
  }

  /**
   * Conduct research using intelligent provider routing
   * @param {object} researchRequest - Research request
   * @returns {Promise<object>} Research results with synthesis
   */
  async conductResearch(researchRequest) {
    const startTime = Date.now();
    this.stats.totalRequests++;

    try {
      // Plan research execution
      const executionPlan = this.routingRules.planResearch(researchRequest);
      
      // Execute research steps
      const results = await Promise.allSettled(
        executionPlan.map(step => this.executeResearchStep(step))
      );

      // Synthesize results
      const synthesis = await this.synthesizer.combine(results, researchRequest);

      // Update statistics
      const responseTime = Date.now() - startTime;
      this.updateStats(true, responseTime, executionPlan);

      return {
        request: researchRequest,
        executionPlan,
        synthesis,
        metadata: {
          timestamp: new Date().toISOString(),
          responseTime,
          stepsExecuted: executionPlan.length,
          successfulSteps: results.filter(r => r.status === 'fulfilled').length
        }
      };
    } catch (error) {
      this.updateStats(false, Date.now() - startTime, []);
      throw new Error(`Research execution failed: ${error.message}`);
    }
  }

  /**
   * Execute individual research step
   * @param {object} step - Research step from execution plan
   * @returns {Promise<object>} Step execution result
   */
  async executeResearchStep(step) {
    const provider = this.providers[step.provider];
    if (!provider) {
      throw new Error(`Provider ${step.provider} not available`);
    }

    // Track provider usage
    this.stats.providerUsage[step.provider] = 
      (this.stats.providerUsage[step.provider] || 0) + 1;

    return await provider[step.method](...step.args);
  }

  /**
   * Update service statistics
   * @param {boolean} success - Whether request was successful
   * @param {number} responseTime - Response time in milliseconds
   * @param {Array} executionPlan - Execution plan that was used
   */
  updateStats(success, responseTime, executionPlan) {
    if (success) {
      this.stats.successfulRequests++;
    } else {
      this.stats.failedRequests++;
    }

    // Update average response time
    const totalTime = this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime;
    this.stats.averageResponseTime = totalTime / this.stats.totalRequests;
  }

  /**
   * Get service statistics
   * @returns {object} Service statistics
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalRequests > 0 
        ? (this.stats.successfulRequests / this.stats.totalRequests) * 100 
        : 0,
      providerStats: Object.fromEntries(
        Object.entries(this.providers).map(([name, provider]) => [
          name, 
          provider.getStats ? provider.getStats() : {}
        ])
      )
    };
  }

  /**
   * Test all provider connections
   * @returns {Promise<object>} Connection test results
   */
  async testConnections() {
    const tests = await Promise.allSettled(
      Object.entries(this.providers).map(async ([name, provider]) => {
        const result = await provider.testConnection();
        return { name, ...result };
      })
    );

    return {
      totalProviders: Object.keys(this.providers).length,
      results: tests.map(test => 
        test.status === 'fulfilled' ? test.value : { 
          name: 'unknown', 
          success: false, 
          error: test.reason?.message 
        }
      ),
      allHealthy: tests.every(test => 
        test.status === 'fulfilled' && test.value.success
      )
    };
  }

  /**
   * Validate configuration for all providers
   * @returns {Promise<object>} Configuration validation results
   */
  async validateConfiguration() {
    const validations = await Promise.allSettled(
      Object.entries(this.providers).map(async ([name, provider]) => {
        try {
          await provider.validateConfig();
          return { name, valid: true };
        } catch (error) {
          return { name, valid: false, error: error.message };
        }
      })
    );

    return {
      totalProviders: Object.keys(this.providers).length,
      validProviders: validations.filter(v => 
        v.status === 'fulfilled' && v.value.valid
      ).length,
      results: validations.map(v => 
        v.status === 'fulfilled' ? v.value : { 
          name: 'unknown', 
          valid: false, 
          error: v.reason?.message 
        }
      )
    };
  }
}

export default ResearchServicesManager;
