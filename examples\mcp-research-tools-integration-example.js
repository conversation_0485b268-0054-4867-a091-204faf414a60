/**
 * MCP Research Tools Integration Example (Task 5.1)
 * 
 * Demonstrates how research tools are integrated into the existing MCP tool architecture
 * following the consolidated tool pattern with intelligent operation routing.
 */

import { WorkflowExecutorTool } from '../mcp-server/src/primitives/tools/workflow-executor.js';
import { createMockInfrastructure } from '../mcp-server/src/primitives/tools/index.js';

/**
 * Example: Research Workflow Execution via MCP
 */
async function researchWorkflowMCPExample() {
  console.log('🔬 Research Workflow Execution via MCP...\n');

  // Create workflow executor with mock infrastructure
  const mockInfrastructure = createMockInfrastructure();
  const workflowExecutor = new WorkflowExecutorTool(mockInfrastructure);

  try {
    // Example 1: Market Research Workflow
    console.log('1. Executing market research workflow...');
    const marketResearchArgs = {
      operation: 'research',
      workflow_type: 'market',
      context: {
        project_id: 'restaurant-discovery-app',
        domain: 'restaurant_discovery',
        business_model: 'marketplace',
        phase: 'planning'
      },
      parameters: {
        query: 'restaurant discovery app market trends and opportunities 2024',
        providers: ['tavily'],
        max_results: 5,
        research_context: {
          targetAudience: 'food_enthusiasts',
          budget: { category: 'small' },
          timeline: { category: 'standard' }
        }
      }
    };

    const marketResult = await workflowExecutor.execute(marketResearchArgs);
    console.log(`   Status: ${marketResult.success ? 'Success' : 'Failed'}`);
    if (marketResult.success) {
      console.log(`   Workflow ID: ${marketResult.data.workflow_id}`);
      console.log(`   Research Type: ${marketResult.data.research_type}`);
      console.log(`   Duration: ${marketResult.data.metrics.duration}s`);
      console.log(`   Sources: ${marketResult.data.metrics.sources_analyzed}`);
      console.log(`   Quality Score: ${marketResult.data.metrics.quality_score}%`);
    } else {
      console.log(`   Error: ${marketResult.error}`);
    }

    // Example 2: Technical Research Workflow
    console.log('\n2. Executing technical research workflow...');
    const technicalResearchArgs = {
      operation: 'research',
      workflow_type: 'technical',
      context: {
        project_id: 'ecommerce-platform',
        domain: 'ecommerce',
        business_model: 'b2c',
        phase: 'architecture'
      },
      parameters: {
        query: 'scalable ecommerce platform architecture best practices',
        providers: ['tavily', 'context7'],
        max_results: 3,
        decision_context: 'framework_selection'
      }
    };

    const technicalResult = await workflowExecutor.execute(technicalResearchArgs);
    console.log(`   Status: ${technicalResult.success ? 'Success' : 'Failed'}`);
    if (technicalResult.success) {
      console.log(`   Workflow ID: ${technicalResult.data.workflow_id}`);
      console.log(`   Decision Options: ${technicalResult.data.results.decision_options ? 'Generated' : 'Not generated'}`);
      console.log(`   Confidence: ${Math.round(technicalResult.data.results.confidence * 100)}%`);
    }

    console.log('\n✅ Research workflow examples completed!');

  } catch (error) {
    console.error('❌ Research workflow example failed:', error);
  }
}

/**
 * Example: Research-Informed Decision via MCP
 */
async function researchInformedDecisionMCPExample() {
  console.log('\n🎯 Research-Informed Decision via MCP...\n');

  const mockInfrastructure = createMockInfrastructure();
  const workflowExecutor = new WorkflowExecutorTool(mockInfrastructure);

  try {
    // Database selection with research backing
    console.log('1. Getting research-informed database selection...');
    const decisionArgs = {
      operation: 'research_decision',
      context: {
        domain: 'fintech',
        business_model: 'saas'
      },
      parameters: {
        decision_context: 'database_selection',
        domain: 'fintech',
        business_model: 'saas',
        budget: { category: 'medium' },
        timeline: { category: 'fast' },
        expertise: { category: 'intermediate' },
        enable_research: true,
        research_providers: ['tavily'],
        include_cli_formatting: true
      }
    };

    const decisionResult = await workflowExecutor.execute(decisionArgs);
    console.log(`   Status: ${decisionResult.success ? 'Success' : 'Failed'}`);
    if (decisionResult.success) {
      const summary = decisionResult.data.summary;
      console.log(`   Decision Context: ${decisionResult.data.decision_context}`);
      console.log(`   Options Available: ${summary.options_count}`);
      console.log(`   Research Backed: ${summary.research_backed ? 'Yes' : 'No'}`);
      console.log(`   Recommended: ${summary.recommended_option || 'None'}`);
      console.log(`   Confidence: ${Math.round(summary.confidence_level * 100)}%`);
    }

    console.log('\n✅ Research-informed decision example completed!');

  } catch (error) {
    console.error('❌ Research-informed decision example failed:', error);
  }
}

/**
 * Example: Conversation Intelligence via MCP
 */
async function conversationIntelligenceMCPExample() {
  console.log('\n🧠 Conversation Intelligence via MCP...\n');

  const mockInfrastructure = createMockInfrastructure();
  const workflowExecutor = new WorkflowExecutorTool(mockInfrastructure);

  try {
    const sessionId = `test-session-${Date.now()}`;

    // Initialize conversation intelligence
    console.log('1. Initializing conversation intelligence...');
    const initArgs = {
      operation: 'conversation_intelligence',
      context: {
        project_root: process.cwd()
      },
      parameters: {
        session_id: sessionId,
        intelligence_operation: 'initialize',
        options: {
          enableIntelligence: true,
          enableBackgroundResearch: true
        }
      }
    };

    const initResult = await workflowExecutor.execute(initArgs);
    console.log(`   Status: ${initResult.success ? 'Success' : 'Failed'}`);
    if (initResult.success) {
      console.log(`   Session ID: ${initResult.data.session_id}`);
      console.log(`   Operation: ${initResult.data.operation}`);
    }

    // Process a message
    console.log('\n2. Processing conversation message...');
    const messageArgs = {
      operation: 'conversation_intelligence',
      context: {
        project_root: process.cwd()
      },
      parameters: {
        session_id: sessionId,
        intelligence_operation: 'process_message',
        message: {
          content: 'I need to build a restaurant discovery app with real-time reviews',
          sender: 'user',
          type: 'requirement'
        }
      }
    };

    const messageResult = await workflowExecutor.execute(messageArgs);
    console.log(`   Status: ${messageResult.success ? 'Success' : 'Failed'}`);
    if (messageResult.success) {
      console.log(`   Message processed for session: ${messageResult.data.session_id}`);
    }

    // Get status
    console.log('\n3. Getting intelligence status...');
    const statusArgs = {
      operation: 'conversation_intelligence',
      context: {
        project_root: process.cwd()
      },
      parameters: {
        session_id: sessionId,
        intelligence_operation: 'get_status'
      }
    };

    const statusResult = await workflowExecutor.execute(statusArgs);
    console.log(`   Status: ${statusResult.success ? 'Success' : 'Failed'}`);

    console.log('\n✅ Conversation intelligence example completed!');

  } catch (error) {
    console.error('❌ Conversation intelligence example failed:', error);
  }
}

/**
 * Example: Research Services Testing via MCP
 */
async function researchServicesTestMCPExample() {
  console.log('\n🧪 Research Services Testing via MCP...\n');

  const mockInfrastructure = createMockInfrastructure();
  const workflowExecutor = new WorkflowExecutorTool(mockInfrastructure);

  try {
    // Test all research services
    console.log('1. Testing all research services...');
    const testAllArgs = {
      operation: 'test_research_services',
      parameters: {}
    };

    const testAllResult = await workflowExecutor.execute(testAllArgs);
    console.log(`   Status: ${testAllResult.success ? 'Success' : 'Failed'}`);
    if (testAllResult.success) {
      const summary = testAllResult.data.summary;
      console.log(`   Healthy Services: ${summary.healthy_count}/${summary.total_count}`);
      console.log(`   All Healthy: ${summary.all_healthy ? 'Yes' : 'No'}`);
    }

    // Test specific providers
    console.log('\n2. Testing specific providers...');
    const testSpecificArgs = {
      operation: 'test_research_services',
      parameters: {
        providers: ['tavily', 'context7']
      }
    };

    const testSpecificResult = await workflowExecutor.execute(testSpecificArgs);
    console.log(`   Status: ${testSpecificResult.success ? 'Success' : 'Failed'}`);
    if (testSpecificResult.success) {
      const summary = testSpecificResult.data.summary;
      console.log(`   Tested Providers: ${summary.tested_providers.join(', ')}`);
      console.log(`   Results: ${summary.healthy_count}/${summary.total_count} healthy`);
    }

    console.log('\n✅ Research services testing example completed!');

  } catch (error) {
    console.error('❌ Research services testing example failed:', error);
  }
}

/**
 * Example: MCP Tool Definition and Registration
 */
async function mcpToolDefinitionExample() {
  console.log('\n🔧 MCP Tool Definition and Registration...\n');

  const mockInfrastructure = createMockInfrastructure();
  const workflowExecutor = new WorkflowExecutorTool(mockInfrastructure);

  // Show tool definition
  const toolDefinition = workflowExecutor.getToolDefinition();
  
  console.log('📋 Enhanced Workflow Executor Tool Definition:');
  console.log(`   Name: ${toolDefinition.name}`);
  console.log(`   Description: ${toolDefinition.description}`);
  console.log(`   Operations: ${toolDefinition.inputSchema.properties.operation.enum.length} operations`);
  console.log('   Research Operations:');
  console.log('     • research - Execute research workflows with real providers');
  console.log('     • research_decision - Get research-informed decision options');
  console.log('     • conversation_intelligence - Manage conversation intelligence');
  console.log('     • test_research_services - Test research provider connectivity');
  
  console.log('\n📊 MCP Integration Benefits:');
  console.log('   ✅ Consolidated tool architecture (5 tools vs 48)');
  console.log('   ✅ Intelligent operation routing');
  console.log('   ✅ Real research provider integration');
  console.log('   ✅ Business-friendly decision presentation');
  console.log('   ✅ Conversation intelligence automation');
  console.log('   ✅ Backward compatibility with legacy tools');

  console.log('\n✅ MCP tool definition example completed!');
}

/**
 * Run all MCP research tools integration examples
 */
async function runMCPResearchToolsIntegrationExamples() {
  try {
    await researchWorkflowMCPExample();
    await researchInformedDecisionMCPExample();
    await conversationIntelligenceMCPExample();
    await researchServicesTestMCPExample();
    await mcpToolDefinitionExample();
    
    console.log('\n🎉 All MCP research tools integration examples completed successfully!');
  } catch (error) {
    console.error('❌ MCP research tools integration examples failed:', error);
    process.exit(1);
  }
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMCPResearchToolsIntegrationExamples();
}

export {
  researchWorkflowMCPExample,
  researchInformedDecisionMCPExample,
  conversationIntelligenceMCPExample,
  researchServicesTestMCPExample,
  mcpToolDefinitionExample,
  runMCPResearchToolsIntegrationExamples
};
