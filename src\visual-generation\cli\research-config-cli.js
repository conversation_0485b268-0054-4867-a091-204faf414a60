#!/usr/bin/env node

/**
 * Research Configuration CLI
 * Command-line utility for managing research domains and libraries
 */

import { researchConfigManager } from '../config/research-config-manager.js';

/**
 * Research Configuration CLI
 */
class ResearchConfigCLI {
  constructor() {
    this.commands = {
      'list': this.listConfig.bind(this),
      'add-domain': this.addDomain.bind(this),
      'remove-domain': this.removeDomain.bind(this),
      'add-library': this.addLibrary.bind(this),
      'remove-library': this.removeLibrary.bind(this),
      'reset': this.resetConfig.bind(this),
      'help': this.showHelp.bind(this)
    };
  }

  /**
   * Run CLI command
   */
  async run(args) {
    const command = args[0];
    const commandArgs = args.slice(1);

    if (!command || command === 'help') {
      this.showHelp();
      return;
    }

    if (this.commands[command]) {
      try {
        await this.commands[command](commandArgs);
      } catch (error) {
        console.error(`❌ Error: ${error.message}`);
        process.exit(1);
      }
    } else {
      console.error(`❌ Unknown command: ${command}`);
      this.showHelp();
      process.exit(1);
    }
  }

  /**
   * List current configuration
   */
  async listConfig() {
    console.log('🔍 Current Research Configuration:');
    console.log('=' .repeat(50));

    const config = await researchConfigManager.getCurrentConfig();

    // Show domains
    console.log('\n📍 Research Domains:');
    for (const [type, domains] of Object.entries(config.domains || {})) {
      console.log(`\n  ${type}:`);
      domains.forEach(domain => console.log(`    • ${domain}`));
    }

    // Show libraries
    console.log('\n📚 Component Libraries:');
    for (const [framework, libraries] of Object.entries(config.libraries || {})) {
      console.log(`\n  ${framework}:`);
      libraries.forEach(library => console.log(`    • ${library}`));
    }

    // Show priorities
    if (config.research_priorities) {
      console.log('\n⚖️ Research Priorities:');
      for (const [type, priority] of Object.entries(config.research_priorities)) {
        console.log(`  ${type}: weight=${priority.weight}, max_results=${priority.max_results}`);
      }
    }
  }

  /**
   * Add domain to a type
   */
  async addDomain(args) {
    if (args.length < 2) {
      console.error('❌ Usage: add-domain <type> <domain1> [domain2] ...');
      console.error('   Types: ui_practices, accessibility, design_system, component_libraries');
      return;
    }

    const type = args[0];
    const domains = args.slice(1);

    const success = await researchConfigManager.addDomains(type, domains);
    
    if (success) {
      console.log(`✅ Added ${domains.length} domain(s) to ${type}:`);
      domains.forEach(domain => console.log(`   • ${domain}`));
    } else {
      console.error('❌ Failed to add domains');
    }
  }

  /**
   * Remove domain from a type
   */
  async removeDomain(args) {
    if (args.length < 2) {
      console.error('❌ Usage: remove-domain <type> <domain1> [domain2] ...');
      return;
    }

    const type = args[0];
    const domains = args.slice(1);

    const success = await researchConfigManager.removeDomains(type, domains);
    
    if (success) {
      console.log(`✅ Removed ${domains.length} domain(s) from ${type}:`);
      domains.forEach(domain => console.log(`   • ${domain}`));
    } else {
      console.error('❌ Failed to remove domains');
    }
  }

  /**
   * Add library to a framework
   */
  async addLibrary(args) {
    if (args.length < 2) {
      console.error('❌ Usage: add-library <framework> <library1> [library2] ...');
      console.error('   Frameworks: react, vue, angular, svelte, solid, web_components');
      return;
    }

    const framework = args[0];
    const libraries = args.slice(1);

    const success = await researchConfigManager.addLibraries(framework, libraries);
    
    if (success) {
      console.log(`✅ Added ${libraries.length} library(ies) to ${framework}:`);
      libraries.forEach(library => console.log(`   • ${library}`));
    } else {
      console.error('❌ Failed to add libraries');
    }
  }

  /**
   * Remove library from a framework
   */
  async removeLibrary(args) {
    if (args.length < 2) {
      console.error('❌ Usage: remove-library <framework> <library1> [library2] ...');
      return;
    }

    const framework = args[0];
    const libraries = args.slice(1);

    const success = await researchConfigManager.removeLibraries(framework, libraries);
    
    if (success) {
      console.log(`✅ Removed ${libraries.length} library(ies) from ${framework}:`);
      libraries.forEach(library => console.log(`   • ${library}`));
    } else {
      console.error('❌ Failed to remove libraries');
    }
  }

  /**
   * Reset configuration to defaults
   */
  async resetConfig() {
    console.log('⚠️  This will reset your research configuration to defaults.');
    console.log('   All custom domains and libraries will be removed.');
    
    // In a real CLI, you'd prompt for confirmation here
    const success = await researchConfigManager.resetToDefaults();
    
    if (success) {
      console.log('✅ Configuration reset to defaults');
    } else {
      console.error('❌ Failed to reset configuration');
    }
  }

  /**
   * Show help
   */
  showHelp() {
    console.log('🔧 Guidant Research Configuration CLI');
    console.log('=' .repeat(40));
    console.log('');
    console.log('Commands:');
    console.log('  list                           List current configuration');
    console.log('  add-domain <type> <domains>    Add research domains');
    console.log('  remove-domain <type> <domains> Remove research domains');
    console.log('  add-library <fw> <libraries>   Add component libraries');
    console.log('  remove-library <fw> <libs>     Remove component libraries');
    console.log('  reset                          Reset to default configuration');
    console.log('  help                           Show this help');
    console.log('');
    console.log('Domain Types:');
    console.log('  ui_practices      UI/UX design best practices sites');
    console.log('  accessibility     Accessibility guidelines and tools');
    console.log('  design_system     Design system documentation');
    console.log('  component_libraries Component library sites');
    console.log('');
    console.log('Frameworks:');
    console.log('  react, vue, angular, svelte, solid, web_components');
    console.log('');
    console.log('Examples:');
    console.log('  research-config add-domain ui_practices ui.shadcn.com daisyui.com');
    console.log('  research-config add-library react shadcn/ui radix-ui');
    console.log('  research-config remove-domain accessibility old-site.com');
    console.log('  research-config list');
  }
}

// Run CLI if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const cli = new ResearchConfigCLI();
  const args = process.argv.slice(2);
  cli.run(args);
}

export { ResearchConfigCLI };
