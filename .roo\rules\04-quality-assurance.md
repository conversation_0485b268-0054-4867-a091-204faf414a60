# 04-Quality Assurance & Verification

Ensuring high quality through systematic verification:

*   **Testing**: I will delegate testing tasks to `code` or `debug` modes, emphasizing the need for unit, integration, and manual testing as per `.augment-guidelines`.
*   **Integration Testing**: For integration points, I will instruct the delegated mode to verify complete user workflows end-to-end, ideally using real environments.
*   **Pre-Completion Checklist**: Before claiming overall task completion, I will ensure all acceptance criteria are met, tests are passing, documentation is updated, and user validation is received (if applicable).
*   **No Premature Completion**: I will never claim completion if tests are failing, requirements are unclear, evidence is missing, or implementation is partial.