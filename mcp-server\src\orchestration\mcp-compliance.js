/**
 * MCP Compliance Validator
 * Standards validation for Model Context Protocol compliance
 * Ensures all tools, resources, prompts, and sampling adhere to MCP specifications
 */

import { z } from 'zod';

/**
 * MCP Compliance Validator Class
 * Validates MCP protocol adherence and standards compliance
 */
export class MCPComplianceValidator {
  constructor() {
    this.validationRules = this.initializeValidationRules();
    this.complianceReport = {
      lastValidation: null,
      overallCompliance: 0,
      violations: [],
      recommendations: []
    };
  }

  /**
   * Initialize MCP validation rules
   * @returns {object} Validation rules
   */
  initializeValidationRules() {
    return {
      tools: {
        requiredFields: ['name', 'description', 'inputSchema'],
        namePattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
        maxDescriptionLength: 500,
        requiredAnnotations: ['title', 'description'],
        allowedHints: ['readOnlyHint', 'destructiveHint', 'idempotentHint', 'openWorldHint']
      },
      resources: {
        requiredFields: ['uri', 'name', 'description'],
        uriPattern: /^[a-zA-Z][a-zA-Z0-9+.-]*:\/\/.+$/,
        maxDescriptionLength: 300,
        requiredMimeTypes: ['text/plain', 'application/json', 'text/markdown']
      },
      prompts: {
        requiredFields: ['name', 'description'],
        namePattern: /^[a-zA-Z][a-zA-Z0-9_-]*$/,
        maxDescriptionLength: 400,
        argumentValidation: true
      },
      sampling: {
        requiredFields: ['method', 'params'],
        allowedMethods: ['createMessage', 'complete'],
        maxTokens: 4096
      }
    };
  }

  /**
   * Validate complete MCP server compliance
   * @param {object} server - MCP server instance
   * @param {object} primitives - All registered primitives
   * @returns {object} Compliance validation result
   */
  async validateCompliance(server, primitives = {}) {
    const validationStart = Date.now();
    const violations = [];
    const recommendations = [];
    
    try {
      console.log('🔍 Starting MCP compliance validation...');
      
      // Validate tools
      if (primitives.tools) {
        const toolValidation = await this.validateTools(primitives.tools);
        violations.push(...toolValidation.violations);
        recommendations.push(...toolValidation.recommendations);
      }
      
      // Validate resources
      if (primitives.resources) {
        const resourceValidation = await this.validateResources(primitives.resources);
        violations.push(...resourceValidation.violations);
        recommendations.push(...resourceValidation.recommendations);
      }
      
      // Validate prompts
      if (primitives.prompts) {
        const promptValidation = await this.validatePrompts(primitives.prompts);
        violations.push(...promptValidation.violations);
        recommendations.push(...promptValidation.recommendations);
      }
      
      // Validate sampling
      if (primitives.sampling) {
        const samplingValidation = await this.validateSampling(primitives.sampling);
        violations.push(...samplingValidation.violations);
        recommendations.push(...samplingValidation.recommendations);
      }
      
      // Validate server capabilities
      const capabilityValidation = await this.validateServerCapabilities(server);
      violations.push(...capabilityValidation.violations);
      recommendations.push(...capabilityValidation.recommendations);
      
      // Calculate overall compliance score
      const totalChecks = this.getTotalChecks(primitives);
      const overallCompliance = Math.max(0, (totalChecks - violations.length) / totalChecks * 100);
      
      // Update compliance report
      this.complianceReport = {
        lastValidation: new Date().toISOString(),
        validationDuration: Date.now() - validationStart,
        overallCompliance: Math.round(overallCompliance),
        totalChecks,
        violations,
        recommendations,
        primitivesSummary: {
          tools: primitives.tools ? Object.keys(primitives.tools).length : 0,
          resources: primitives.resources ? Object.keys(primitives.resources).length : 0,
          prompts: primitives.prompts ? Object.keys(primitives.prompts).length : 0,
          sampling: primitives.sampling ? Object.keys(primitives.sampling).length : 0
        }
      };
      
      console.log(`✅ MCP compliance validation completed: ${overallCompliance.toFixed(1)}% compliant`);
      
      return {
        success: true,
        compliance: this.complianceReport,
        isCompliant: overallCompliance >= 95, // 95% threshold for compliance
        summary: this.generateComplianceSummary()
      };
      
    } catch (error) {
      console.error('❌ MCP compliance validation failed:', error);
      return {
        success: false,
        error: error.message,
        compliance: this.complianceReport
      };
    }
  }

  /**
   * Validate tools compliance
   * @param {object} tools - Tools to validate
   * @returns {object} Tool validation result
   */
  async validateTools(tools) {
    const violations = [];
    const recommendations = [];
    
    for (const [toolName, toolDef] of Object.entries(tools)) {
      // Validate required fields
      for (const field of this.validationRules.tools.requiredFields) {
        if (!toolDef[field]) {
          violations.push({
            type: 'tool',
            severity: 'error',
            tool: toolName,
            issue: `Missing required field: ${field}`,
            rule: 'MCP-TOOL-001'
          });
        }
      }
      
      // Validate tool name pattern
      if (!this.validationRules.tools.namePattern.test(toolName)) {
        violations.push({
          type: 'tool',
          severity: 'error',
          tool: toolName,
          issue: 'Tool name must match pattern: alphanumeric with underscores',
          rule: 'MCP-TOOL-002'
        });
      }
      
      // Validate description length
      if (toolDef.description && toolDef.description.length > this.validationRules.tools.maxDescriptionLength) {
        violations.push({
          type: 'tool',
          severity: 'warning',
          tool: toolName,
          issue: `Description exceeds maximum length (${this.validationRules.tools.maxDescriptionLength} chars)`,
          rule: 'MCP-TOOL-003'
        });
      }
      
      // Validate annotations
      if (toolDef.annotations) {
        for (const annotation of this.validationRules.tools.requiredAnnotations) {
          if (!toolDef.annotations[annotation]) {
            recommendations.push({
              type: 'tool',
              tool: toolName,
              suggestion: `Consider adding ${annotation} annotation for better tool discovery`,
              rule: 'MCP-TOOL-004'
            });
          }
        }
        
        // Validate hint annotations
        for (const hint of Object.keys(toolDef.annotations)) {
          if (hint.endsWith('Hint') && !this.validationRules.tools.allowedHints.includes(hint)) {
            violations.push({
              type: 'tool',
              severity: 'warning',
              tool: toolName,
              issue: `Unknown hint annotation: ${hint}`,
              rule: 'MCP-TOOL-005'
            });
          }
        }
      }
      
      // Validate input schema
      if (toolDef.inputSchema) {
        const schemaValidation = this.validateSchema(toolDef.inputSchema, toolName);
        violations.push(...schemaValidation.violations);
        recommendations.push(...schemaValidation.recommendations);
      }
    }
    
    return { violations, recommendations };
  }

  /**
   * Validate resources compliance
   * @param {object} resources - Resources to validate
   * @returns {object} Resource validation result
   */
  async validateResources(resources) {
    const violations = [];
    const recommendations = [];
    
    for (const [resourceName, resourceDef] of Object.entries(resources)) {
      // Validate required fields
      for (const field of this.validationRules.resources.requiredFields) {
        if (!resourceDef[field]) {
          violations.push({
            type: 'resource',
            severity: 'error',
            resource: resourceName,
            issue: `Missing required field: ${field}`,
            rule: 'MCP-RESOURCE-001'
          });
        }
      }
      
      // Validate URI pattern
      if (resourceDef.uri && !this.validationRules.resources.uriPattern.test(resourceDef.uri)) {
        violations.push({
          type: 'resource',
          severity: 'error',
          resource: resourceName,
          issue: 'Resource URI must be a valid URI with scheme',
          rule: 'MCP-RESOURCE-002'
        });
      }
      
      // Validate MIME type
      if (resourceDef.mimeType && !this.validationRules.resources.requiredMimeTypes.includes(resourceDef.mimeType)) {
        recommendations.push({
          type: 'resource',
          resource: resourceName,
          suggestion: `Consider using standard MIME types: ${this.validationRules.resources.requiredMimeTypes.join(', ')}`,
          rule: 'MCP-RESOURCE-003'
        });
      }
    }
    
    return { violations, recommendations };
  }

  /**
   * Validate prompts compliance
   * @param {object} prompts - Prompts to validate
   * @returns {object} Prompt validation result
   */
  async validatePrompts(prompts) {
    const violations = [];
    const recommendations = [];
    
    for (const [promptName, promptDef] of Object.entries(prompts)) {
      // Validate required fields
      for (const field of this.validationRules.prompts.requiredFields) {
        if (!promptDef[field]) {
          violations.push({
            type: 'prompt',
            severity: 'error',
            prompt: promptName,
            issue: `Missing required field: ${field}`,
            rule: 'MCP-PROMPT-001'
          });
        }
      }
      
      // Validate prompt name pattern
      if (!this.validationRules.prompts.namePattern.test(promptName)) {
        violations.push({
          type: 'prompt',
          severity: 'error',
          prompt: promptName,
          issue: 'Prompt name must match pattern: alphanumeric with hyphens/underscores',
          rule: 'MCP-PROMPT-002'
        });
      }
      
      // Validate arguments
      if (promptDef.arguments) {
        for (const arg of promptDef.arguments) {
          if (!arg.name || !arg.description) {
            violations.push({
              type: 'prompt',
              severity: 'warning',
              prompt: promptName,
              issue: 'Prompt arguments should have name and description',
              rule: 'MCP-PROMPT-003'
            });
          }
        }
      }
    }
    
    return { violations, recommendations };
  }

  /**
   * Validate sampling compliance
   * @param {object} sampling - Sampling to validate
   * @returns {object} Sampling validation result
   */
  async validateSampling(sampling) {
    const violations = [];
    const recommendations = [];
    
    for (const [samplingName, samplingDef] of Object.entries(sampling)) {
      // Validate required fields
      for (const field of this.validationRules.sampling.requiredFields) {
        if (!samplingDef[field]) {
          violations.push({
            type: 'sampling',
            severity: 'error',
            sampling: samplingName,
            issue: `Missing required field: ${field}`,
            rule: 'MCP-SAMPLING-001'
          });
        }
      }
      
      // Validate method
      if (samplingDef.method && !this.validationRules.sampling.allowedMethods.includes(samplingDef.method)) {
        violations.push({
          type: 'sampling',
          severity: 'error',
          sampling: samplingName,
          issue: `Invalid sampling method: ${samplingDef.method}`,
          rule: 'MCP-SAMPLING-002'
        });
      }
      
      // Validate token limits
      if (samplingDef.params && samplingDef.params.maxTokens > this.validationRules.sampling.maxTokens) {
        recommendations.push({
          type: 'sampling',
          sampling: samplingName,
          suggestion: `Consider reducing maxTokens to ${this.validationRules.sampling.maxTokens} or less`,
          rule: 'MCP-SAMPLING-003'
        });
      }
    }
    
    return { violations, recommendations };
  }

  /**
   * Validate server capabilities
   * @param {object} server - MCP server instance
   * @returns {object} Server validation result
   */
  async validateServerCapabilities(server) {
    const violations = [];
    const recommendations = [];
    
    // Check if server has proper capability advertisement
    if (!server.capabilities) {
      violations.push({
        type: 'server',
        severity: 'error',
        issue: 'Server must advertise capabilities',
        rule: 'MCP-SERVER-001'
      });
    }
    
    // Check for proper error handling
    if (!server.errorHandler) {
      recommendations.push({
        type: 'server',
        suggestion: 'Implement centralized error handling for better debugging',
        rule: 'MCP-SERVER-002'
      });
    }
    
    return { violations, recommendations };
  }

  /**
   * Validate JSON schema
   * @param {object} schema - Schema to validate
   * @param {string} context - Context for error reporting
   * @returns {object} Schema validation result
   */
  validateSchema(schema, context) {
    const violations = [];
    const recommendations = [];
    
    try {
      // Basic schema structure validation
      if (!schema.type && !schema.properties && !schema.items) {
        violations.push({
          type: 'schema',
          severity: 'warning',
          context,
          issue: 'Schema should specify type, properties, or items',
          rule: 'MCP-SCHEMA-001'
        });
      }
      
      // Check for required fields specification
      if (schema.properties && !schema.required) {
        recommendations.push({
          type: 'schema',
          context,
          suggestion: 'Consider specifying required fields for better validation',
          rule: 'MCP-SCHEMA-002'
        });
      }
      
    } catch (error) {
      violations.push({
        type: 'schema',
        severity: 'error',
        context,
        issue: `Schema validation error: ${error.message}`,
        rule: 'MCP-SCHEMA-003'
      });
    }
    
    return { violations, recommendations };
  }

  /**
   * Get total number of checks performed
   * @param {object} primitives - All primitives
   * @returns {number} Total checks
   */
  getTotalChecks(primitives) {
    let total = 0;
    
    if (primitives.tools) {
      total += Object.keys(primitives.tools).length * 5; // 5 checks per tool
    }
    if (primitives.resources) {
      total += Object.keys(primitives.resources).length * 3; // 3 checks per resource
    }
    if (primitives.prompts) {
      total += Object.keys(primitives.prompts).length * 3; // 3 checks per prompt
    }
    if (primitives.sampling) {
      total += Object.keys(primitives.sampling).length * 3; // 3 checks per sampling
    }
    
    total += 2; // Server capability checks
    
    return total;
  }

  /**
   * Generate compliance summary
   * @returns {object} Compliance summary
   */
  generateComplianceSummary() {
    const { violations, recommendations, overallCompliance } = this.complianceReport;
    
    return {
      complianceLevel: overallCompliance >= 95 ? 'excellent' : 
                      overallCompliance >= 85 ? 'good' : 
                      overallCompliance >= 70 ? 'acceptable' : 'poor',
      criticalIssues: violations.filter(v => v.severity === 'error').length,
      warnings: violations.filter(v => v.severity === 'warning').length,
      recommendations: recommendations.length,
      topIssues: violations.slice(0, 5),
      nextSteps: this.generateNextSteps()
    };
  }

  /**
   * Generate next steps for compliance improvement
   * @returns {Array} Next steps
   */
  generateNextSteps() {
    const { violations, recommendations } = this.complianceReport;
    const steps = [];
    
    const criticalIssues = violations.filter(v => v.severity === 'error');
    if (criticalIssues.length > 0) {
      steps.push('Fix critical compliance violations first');
    }
    
    const warnings = violations.filter(v => v.severity === 'warning');
    if (warnings.length > 0) {
      steps.push('Address warning-level issues');
    }
    
    if (recommendations.length > 0) {
      steps.push('Implement recommended improvements');
    }
    
    if (steps.length === 0) {
      steps.push('Maintain current compliance level');
    }
    
    return steps;
  }

  /**
   * Get current compliance report
   * @returns {object} Current compliance report
   */
  getComplianceReport() {
    return this.complianceReport;
  }
}

/**
 * Create MCP compliance validator
 * @returns {MCPComplianceValidator} Validator instance
 */
export function createMCPComplianceValidator() {
  return new MCPComplianceValidator();
}

export default MCPComplianceValidator;
