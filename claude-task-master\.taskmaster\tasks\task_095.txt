# Task ID: 95
# Title: Implement .taskmaster Directory Structure
# Status: done
# Dependencies: 1, 3, 4, 17
# Priority: high
# Description: Consolidate all Task Master-managed files in user projects into a clean, centralized .taskmaster/ directory structure to improve organization and keep user project directories clean, based on GitHub issue #275.
# Details:
This task involves restructuring how Task Master organizes files within user projects to improve maintainability and keep project directories clean:

1. Create a new `.taskmaster/` directory structure in user projects:
   - Move task files from `tasks/` to `.taskmaster/tasks/`
   - Move PRD files from `scripts/` to `.taskmaster/docs/`
   - Move analysis reports to `.taskmaster/reports/`
   - Move configuration from `.taskmasterconfig` to `.taskmaster/config.json`
   - Create `.taskmaster/templates/` for user templates

2. Update all Task Master code that creates/reads user files:
   - Modify task file generation to use `.taskmaster/tasks/`
   - Update PRD file handling to use `.taskmaster/docs/`
   - Adjust report generation to save to `.taskmaster/reports/`
   - Update configuration loading to look for `.taskmaster/config.json`
   - Modify any path resolution logic in Task Master's codebase

3. Ensure backward compatibility during migration:
   - Implement path fallback logic that checks both old and new locations
   - Add deprecation warnings when old paths are detected
   - Create a migration command to help users transition to the new structure
   - Preserve existing user data during migration

4. Update the project initialization process:
   - Modify the init command to create the new `.taskmaster/` directory structure
   - Update default file creation to use new paths

5. Benefits of the new structure:
   - Keeps user project directories clean and organized
   - Clearly separates Task Master files from user project files
   - Makes it easier to add Task Master to .gitignore if desired
   - Provides logical grouping of different file types

6. Test thoroughly to ensure all functionality works with the new structure:
   - Verify all Task Master commands work with the new paths
   - Ensure backward compatibility functions correctly
   - Test migration process preserves all user data

7. Update documentation:
   - Update README.md to reflect the new user file structure
   - Add migration guide for existing users
   - Document the benefits of the cleaner organization

# Test Strategy:
1. Unit Testing:
   - Create unit tests for path resolution that verify both new and old paths work
   - Test configuration loading with both `.taskmasterconfig` and `.taskmaster/config.json`
   - Verify the migration command correctly moves files and preserves content
   - Test file creation in all new subdirectories

2. Integration Testing:
   - Run all existing integration tests with the new directory structure
   - Verify that all Task Master commands function correctly with new paths
   - Test backward compatibility by running commands with old file structure

3. Migration Testing:
   - Test the migration process on sample projects with existing tasks and files
   - Verify all tasks, PRDs, reports, and configurations are correctly moved
   - Ensure no data loss occurs during migration
   - Test migration with partial existing structures (e.g., only tasks/ exists)

4. User Workflow Testing:
   - Test complete workflows: init → create tasks → generate reports → update PRDs
   - Verify all generated files go to correct locations in `.taskmaster/`
   - Test that user project directories remain clean

5. Manual Testing:
   - Perform end-to-end testing with the new structure
   - Create, update, and delete tasks using the new structure
   - Generate reports and verify they're saved to `.taskmaster/reports/`

6. Documentation Verification:
   - Review all documentation to ensure it accurately reflects the new user file structure
   - Verify the migration guide provides clear instructions

7. Regression Testing:
   - Run the full test suite to ensure no regressions were introduced
   - Verify existing user projects continue to work during transition period

# Subtasks:
## 1. Create .taskmaster directory structure [done]
### Dependencies: None
### Description: Create the new .taskmaster directory and move existing files to their new locations
### Details:
Create a new .taskmaster/ directory in the project root. Move the tasks/ directory to .taskmaster/tasks/. Move the scripts/ directory to .taskmaster/scripts/. Move the .taskmasterconfig file to .taskmaster/config.json. Ensure proper file permissions are maintained during the move.
<info added on 2025-05-29T15:03:56.912Z>
Create the new .taskmaster/ directory structure in user projects with subdirectories for tasks/, docs/, reports/, and templates/. Move the existing .taskmasterconfig file to .taskmaster/config.json. Since this project is also a Task Master user, move this project's current user files (tasks.json, PRD files, etc.) to the new .taskmaster/ structure to test the implementation. This subtask focuses on user project directory structure, not Task Master source code relocation.
</info added on 2025-05-29T15:03:56.912Z>

## 2. Update Task Master code for new user file paths [done]
### Dependencies: 95.1
### Description: Modify all Task Master code that creates or reads user project files to use the new .taskmaster structure
### Details:
Update Task Master's file handling code to use the new paths: tasks in .taskmaster/tasks/, PRD files in .taskmaster/docs/, reports in .taskmaster/reports/, and config in .taskmaster/config.json. Modify path resolution logic throughout the Task Master codebase to reference the new user file locations.

## 3. Update task file generation system [done]
### Dependencies: 95.1
### Description: Modify the task file generation system to use the new directory structure
### Details:
Update the task file generation system to create and read task files from .taskmaster/tasks/ instead of tasks/. Ensure all template paths are updated. Modify any path resolution logic specific to task file handling.

## 4. Implement backward compatibility logic [done]
### Dependencies: 95.2, 95.3
### Description: Add fallback mechanisms to support both old and new file locations during transition
### Details:
Implement path fallback logic that checks both old and new locations when files aren't found. Add deprecation warnings when old paths are used, informing users about the new structure. Ensure error messages are clear about the transition.

## 5. Create migration command for users [done]
### Dependencies: 95.1, 95.4
### Description: Develop a Task Master command to help users transition their existing projects to the new structure
### Details:
Create a 'taskmaster migrate' command that automatically moves user files from old locations to the new .taskmaster structure. Move tasks/ to .taskmaster/tasks/, scripts/prd.txt to .taskmaster/docs/, reports to .taskmaster/reports/, and .taskmasterconfig to .taskmaster/config.json. Include backup functionality and validation to ensure migration completed successfully.

## 6. Update project initialization process [done]
### Dependencies: 95.1
### Description: Modify the init command to create the new directory structure for new projects
### Details:
Update the init command to create the .taskmaster directory and its subdirectories (tasks/, docs/, reports/, templates/). Modify default file creation to use the new paths. Ensure new projects are created with the correct structure from the start.

## 7. Update PRD and report file handling [done]
### Dependencies: 95.2, 95.6
### Description: Modify PRD file creation and report generation to use the new directory structure
### Details:
Update PRD file handling to create and read files from .taskmaster/docs/ instead of scripts/. Modify report generation (like task-complexity-report.json) to save to .taskmaster/reports/. Ensure all file operations use the new paths consistently.

## 8. Update documentation and create migration guide [done]
### Dependencies: 95.5, 95.6, 95.7
### Description: Update all documentation to reflect the new directory structure and provide migration guidance
### Details:
Update README.md and other documentation to reflect the new .taskmaster structure for user projects. Create a comprehensive migration guide explaining the benefits of the new structure and how to migrate existing projects. Include examples of the new directory layout and explain how it keeps user project directories clean.

## 9. Add templates directory support [done]
### Dependencies: 95.2, 95.6
### Description: Implement support for user templates in the .taskmaster/templates/ directory
### Details:
Create functionality to support user-defined templates in .taskmaster/templates/. Allow users to store custom task templates, PRD templates, or other reusable files. Update Task Master commands to recognize and use templates from this directory when available.

## 10. Verify clean user project directories [done]
### Dependencies: 95.8, 95.9
### Description: Ensure the new structure keeps user project root directories clean and organized
### Details:
Validate that after implementing the new structure, user project root directories only contain their actual project files plus the single .taskmaster/ directory. Verify that no Task Master files are created outside of .taskmaster/. Test that users can easily add .taskmaster/ to .gitignore if they choose to exclude Task Master files from version control.

