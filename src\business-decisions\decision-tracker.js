import path from 'path';
import fs from 'fs/promises';

/**
 * Record a user's decision with business rationale
 * 
 * @param {Object} params - Parameters
 * @param {string} params.decisionId - The ID of the decision being made
 * @param {string} params.choiceId - The ID of the chosen option
 * @param {string} params.rationale - The user's business rationale for the decision
 * @param {Object} [params.customDetails] - Optional custom details for custom choices
 * @param {string} params.projectRoot - Project root directory
 * @param {Object} [params.session] - Session information
 * @returns {Promise<Object>} Recorded decision and next steps
 */
export async function recordDecision({
  decisionId,
  choiceId,
  rationale,
  customDetails = {},
  projectRoot,
  session
}) {
  try {
    const guidantDir = path.join(projectRoot, '.guidant');
    const contextDir = path.join(guidantDir, 'context');
    const decisionsFile = path.join(contextDir, 'decisions.json');
    
    // Read existing decisions
    let decisions;
    try {
      const fileContent = await fs.readFile(decisionsFile, 'utf8');
      decisions = JSON.parse(fileContent);
    } catch (error) {
      throw new Error(`Failed to read decisions file: ${error.message}`);
    }
    
    // Check if the decision exists
    if (!decisions.pendingDecisions || !decisions.pendingDecisions[decisionId]) {
      throw new Error(`Decision with ID ${decisionId} not found`);
    }
    
    const pendingDecision = decisions.pendingDecisions[decisionId];
    
    // Find the selected option
    const selectedOption = pendingDecision.options.find(option => option.id === choiceId);
    if (!selectedOption) {
      throw new Error(`Option with ID ${choiceId} not found in decision ${decisionId}`);
    }
    
    // Create the recorded decision
    const recordedDecision = {
      ...pendingDecision,
      selectedOption,
      rationale,
      timestamp: new Date().toISOString(),
      isCustomChoice: selectedOption.isCustom || false
    };
    
    // If this is a custom choice, add the custom details
    if (selectedOption.isCustom && customDetails) {
      recordedDecision.customDetails = customDetails;
    }
    
    // Move from pending to completed
    if (!decisions.completedDecisions) {
      decisions.completedDecisions = {};
    }
    decisions.completedDecisions[decisionId] = recordedDecision;
    delete decisions.pendingDecisions[decisionId];
    
    // Write back to file
    await fs.writeFile(decisionsFile, JSON.stringify(decisions, null, 2), 'utf8');
    
    // Generate next steps based on the decision
    const nextSteps = generateNextSteps(recordedDecision);
    
    return {
      recordedDecision,
      nextSteps
    };
  } catch (error) {
    throw new Error(`Failed to record decision: ${error.message}`);
  }
}

/**
 * Generate next steps based on a recorded decision
 * 
 * @param {Object} decision - The recorded decision
 * @returns {Array<string>} List of next steps
 */
function generateNextSteps(decision) {
  const nextSteps = [];
  const { context, selectedOption } = decision;
  
  // Add general next step
  nextSteps.push(`Your choice of "${selectedOption.title}" has been recorded.`);
  
  // Add context-specific next steps
  switch (context) {
    case 'framework_selection':
      nextSteps.push(`Next, you'll need to choose a design approach.`);
      break;
    case 'database_choice':
      nextSteps.push(`Next, you'll need to define your data models.`);
      break;
    case 'auth_strategy':
      nextSteps.push(`Next, you'll need to set up user roles and permissions.`);
      break;
    case 'deployment_platform':
      nextSteps.push(`Next, you'll need to configure your deployment settings.`);
      break;
    default:
      nextSteps.push(`Continue with the next steps in your project.`);
  }
  
  // Add option-specific next step if it's a custom choice
  if (decision.isCustomChoice) {
    nextSteps.push(`Since you chose a custom option, make sure to document your specific requirements.`);
  }
  
  return nextSteps;
}

/**
 * Get a list of all recorded decisions for a project
 * 
 * @param {string} projectRoot - Project root directory
 * @returns {Promise<Object>} Object containing pending and completed decisions
 */
export async function getDecisionHistory(projectRoot) {
  try {
    const decisionsFile = path.join(projectRoot, '.guidant', 'context', 'decisions.json');
    
    try {
      const fileContent = await fs.readFile(decisionsFile, 'utf8');
      return JSON.parse(fileContent);
    } catch (error) {
      // File doesn't exist or is invalid
      return { pendingDecisions: {}, completedDecisions: {} };
    }
  } catch (error) {
    throw new Error(`Failed to get decision history: ${error.message}`);
  }
} 