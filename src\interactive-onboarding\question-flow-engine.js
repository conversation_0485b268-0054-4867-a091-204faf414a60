/**
 * Question Flow Engine
 *
 * Manages the flow of questions during interactive onboarding, including:
 * - Question type definitions and validation
 * - Comprehensive general flow that handles all project types
 * - Dynamic question selection based on previous answers
 * - Support for list-type questions (allowing multiple inputs)
 * - Conditional questions based on project type and user responses
 *
 * Uses a single comprehensive general flow with conditional logic to handle
 * all project types (web apps, mobile apps, SaaS, e-commerce, community platforms, etc.)
 */

import path from 'path';
import fs from 'fs';

// Question type constants
export const QUESTION_TYPES = {
  TEXT: 'text',
  MULTIPLE_CHOICE: 'multiple_choice',
  TAGS: 'tags',
  URL: 'url',
  SCALE: 'scale',
  YES_NO: 'yes_no',
  LIST: 'list', // Added based on prototype feedback
  FEEDBACK_PROMPT: 'feedback_prompt' // For soliciting open-ended feedback
};

export class QuestionFlowEngine {
  constructor(projectRoot) {
    this.projectRoot = projectRoot || process.cwd();
    this.guidantDir = path.join(this.projectRoot, '.guidant');
    // Use absolute path to flows directory
    this.flowsDir = path.join(this.projectRoot, 'src', 'interactive-onboarding', 'flows');
    this.ensureDirectoriesExist();

    // Load question flows
    this.questionFlows = this.loadQuestionFlows();
  }

  /**
   * Ensure required directories exist
   */
  ensureDirectoriesExist() {
    if (!fs.existsSync(this.flowsDir)) {
      fs.mkdirSync(this.flowsDir, { recursive: true });
    }
  }

  /**
   * Load the general question flow
   *
   * @returns {Object} Map containing the general flow
   */
  loadQuestionFlows() {
    const flows = {};

    // Load the comprehensive general flow that handles all project types
    try {
      flows.general = require(path.join(this.flowsDir, 'general-flow.js')).default;
    } catch (error) {
      console.error(`Failed to load general flow: ${error.message}`);
      flows.general = []; // Fallback to empty flow
    }

    return flows;
  }

  /**
   * Get the general question flow (handles all project types)
   *
   * @param {string} projectType - The project type (ignored, always returns general flow)
   * @returns {Array} The comprehensive general question flow
   */
  getQuestionFlow(projectType) {
    return this.questionFlows.general;
  }

  /**
   * Get the next question based on session state and previous answers
   * 
   * @param {Object} session - The conversation session
   * @returns {Object|null} Next question or null if no more questions
   */
  getNextQuestion(session) {
    if (!session) {
      throw new Error('Session is required');
    }

    // Handle feedback states first
    if (session.conversationData?.state === 'awaiting_feedback') {
      return {
        id: 'feedback_general',
        type: QUESTION_TYPES.FEEDBACK_PROMPT,
        prompt: 'What feedback do you have, or what changes would you like to request?',
        // This is a conceptual placeholder for a feedback prompt
      };
    }

    if (session.conversationData?.state === 'processing_feedback') {
      return {
        id: 'feedback_processing',
        type: QUESTION_TYPES.TEXT, // Or a new 'status_update' type
        prompt: 'We are currently processing your feedback. Please wait.',
        isInformational: true, // Indicates no input expected
      };
    }

    const projectType = session.projectType || 'general';
    const flow = this.getQuestionFlow(projectType);

    // Handle list question continuation
    if (session.currentListQuestion) {
      return this.continueListQuestion(session);
    }

    // Find the next unanswered question
    const answeredQuestionIds = Object.keys(session.answers || {});
    const skippedQuestions = session.skippedQuestions || [];

    const nextQuestion = flow.find(q =>
      !answeredQuestionIds.includes(q.id) &&
      !skippedQuestions.includes(q.id) &&
      this.checkConditions(q, session)
    );

    // If no more questions in the main flow, check skipped questions
    if (!nextQuestion && skippedQuestions.length > 0) {
      const nextSkipped = flow.find(q =>
        skippedQuestions.includes(q.id) &&
        this.checkConditions(q, session)
      );

      if (nextSkipped) {
        return {
          ...nextSkipped,
          wasSkipped: true
        };
      }
    }
    
    // If no regular questions and no skipped questions, and not in a feedback state,
    // it might be an implicit point to solicit feedback if the conversation isn't formally 'completed'.
    // For now, we'll rely on explicit state changes to AWAITING_FEEDBACK.

    return nextQuestion || null;
  }

  /**
   * Continue a list-type question that's collecting multiple inputs
   * 
   * @param {Object} session - The conversation session
   * @returns {Object} Continued list question
   */
  continueListQuestion(session) {
    const { currentListQuestion } = session;
    
    if (!currentListQuestion) {
      return null;
    }
    
    // Return a modified version of the question for continuation
    return {
      ...currentListQuestion,
      isContinuation: true,
      prompt: currentListQuestion.continuationPrompt || 
              `Add another item or say "done" to finish adding ${currentListQuestion.listItemName || 'items'}`
    };
  }

  /**
   * Check if a question's conditions are met based on previous answers
   * 
   * @param {Object} question - The question to check
   * @param {Object} session - The conversation session
   * @returns {boolean} Whether conditions are met
   */
  checkConditions(question, session) {
    if (!question.conditions || question.conditions.length === 0) {
      return true;
    }
    
    // If conditionLogic is 'OR', any condition can pass
    // Default is 'AND' where all conditions must pass
    const conditionLogic = question.conditionLogic || 'AND';
    
    if (conditionLogic === 'OR') {
      return question.conditions.some(condition => this.evaluateCondition(condition, session));
    } else {
      return question.conditions.every(condition => this.evaluateCondition(condition, session));
    }
  }

  /**
   * Evaluate a single condition against the session answers
   * 
   * @param {Object} condition - The condition to evaluate
   * @param {Object} session - The conversation session
   * @returns {boolean} Whether the condition is met
   */
  evaluateCondition(condition, session) {
    const answer = session.answers[condition.questionId];
    
    if (!answer) {
      return false;
    }
    
    switch (condition.operator) {
      case 'equals':
        return answer.value === condition.value;
      case 'notEquals':
        return answer.value !== condition.value;
      case 'contains':
        return Array.isArray(answer.value) 
          ? answer.value.includes(condition.value)
          : String(answer.value).includes(condition.value);
      case 'greaterThan':
        return Number(answer.value) > Number(condition.value);
      case 'lessThan':
        return Number(answer.value) < Number(condition.value);
      case 'in':
        return Array.isArray(condition.value) && condition.value.includes(answer.value);
      case 'notIn':
        return Array.isArray(condition.value) && !condition.value.includes(answer.value);
      default:
        return true;
    }
  }

  /**
   * Process an answer to a question
   * 
   * @param {Object} question - The question being answered
   * @param {*} answer - The raw answer value
   * @param {Object} session - The conversation session
   * @returns {Object} Processed answer and validation result
   */
  processAnswer(question, answer, session) {
    // For list questions that are continuing
    // Conceptual: NLP Pre-processing to distinguish feedback from answers
    // This is where more sophisticated NLP would analyze the 'answer' input.
    // For now, we'll rely on the question type or current conversation state.

    if (question.type === QUESTION_TYPES.FEEDBACK_PROMPT) {
      // Input is assumed to be feedback.
      // Here, we would interpret the feedback (e.g., identify intent: general comment, change request, bug report)
      // And then map it to actions (e.g., call ConversationStateManager.recordFeedback, trigger other tools)
      
      // For demonstration, let's assume any input here is valid feedback text.
      if (typeof answer !== 'string' || answer.trim() === '') {
        return { valid: false, error: 'Please provide some feedback.' };
      }
      return {
        valid: true,
        processed: answer.trim(),
        isFeedback: true, // Custom flag to indicate this was processed as feedback
        // Potentially add interpreted_intent: 'change_request', target_element: 'preview_button', etc.
      };
    }

    if (question.type === QUESTION_TYPES.LIST && question.isContinuation) {
      return this.processListContinuation(answer, session);
    }
    
    const validation = this.validateAnswer(question, answer);
    
    if (!validation.valid) {
      return {
        valid: false,
        error: validation.error
      };
    }
    
    // For list questions that are starting
    if (question.type === QUESTION_TYPES.LIST && !question.isContinuation) {
      return this.startListCollection(question, validation.processed, session);
    }
    
    return {
      valid: true,
      processed: validation.processed
    };
  }

  /**
   * Start collecting items for a list question
   * 
   * @param {Object} question - The list question
   * @param {*} firstItem - The first processed item
   * @param {Object} session - The conversation session
   * @returns {Object} Result indicating list collection has started
   */
  startListCollection(question, firstItem, session) {
    // Set up the list collection state
    return {
      valid: true,
      processed: [firstItem],
      listStarted: true,
      currentListQuestion: {
        ...question,
        collectedItems: [firstItem]
      }
    };
  }

  /**
   * Process a continuation of a list question
   * 
   * @param {*} answer - The raw answer
   * @param {Object} session - The conversation session
   * @returns {Object} Result of processing the list continuation
   */
  processListContinuation(answer, session) {
    const { currentListQuestion } = session;
    
    if (!currentListQuestion) {
      return {
        valid: false,
        error: 'No active list question to continue.'
      };
    }
    
    // Check if the user wants to finish the list
    if (typeof answer === 'string' && 
        ['done', 'finish', 'complete', 'end', 'that\'s all', 'that\'s it'].includes(answer.trim().toLowerCase())) {
      
      return {
        valid: true,
        processed: currentListQuestion.collectedItems || [],
        listCompleted: true
      };
    }
    
    // Validate the new item using the item type validator
    const validation = this.validateListItemAnswer(currentListQuestion, answer);
    
    if (!validation.valid) {
      return {
        valid: false,
        error: validation.error
      };
    }
    
    // Add the new item to the collection
    const updatedItems = [...(currentListQuestion.collectedItems || []), validation.processed];
    
    return {
      valid: true,
      processed: updatedItems,
      listContinued: true,
      currentListQuestion: {
        ...currentListQuestion,
        collectedItems: updatedItems
      }
    };
  }

  /**
   * Validate an answer based on question type
   * 
   * @param {Object} question - The question
   * @param {*} answer - The raw answer
   * @returns {Object} Validation result
   */
  validateAnswer(question, answer) {
    if (!question || !question.type) {
      throw new Error('Invalid question');
    }
    
    switch (question.type) {
      case QUESTION_TYPES.TEXT:
        return this.validateTextAnswer(question, answer);
      case QUESTION_TYPES.MULTIPLE_CHOICE:
        return this.validateMultipleChoiceAnswer(question, answer);
      case QUESTION_TYPES.TAGS:
        return this.validateTagsAnswer(question, answer);
      case QUESTION_TYPES.URL:
        return this.validateUrlAnswer(question, answer);
      case QUESTION_TYPES.SCALE:
        return this.validateScaleAnswer(question, answer);
      case QUESTION_TYPES.YES_NO:
        return this.validateYesNoAnswer(question, answer);
      case QUESTION_TYPES.LIST:
        return this.validateListItemAnswer(question, answer);
      // FEEDBACK_PROMPT is handled earlier in processAnswer, but we can add a validator if direct validation is needed.
      case QUESTION_TYPES.FEEDBACK_PROMPT:
        // This specific validator might not be strictly necessary if handled in processAnswer
        // but good for consistency.
        if (typeof answer !== 'string' || answer.trim() === '') {
            return { valid: false, error: 'Feedback cannot be empty.' };
        }
        return { valid: true, processed: answer.trim() };
      default:
        return {
          valid: false,
          error: `Unsupported question type: ${question.type}`
        };
    }
  }

  /**
   * Validate a text answer
   * 
   * @param {Object} question - The question
   * @param {string} answer - The raw answer
   * @returns {Object} Validation result
   */
  validateTextAnswer(question, answer) {
    if (!answer && question.minLength > 0) {
      return {
        valid: false,
        error: 'Please provide an answer.'
      };
    }
    
    // Allow empty answers if minLength is 0
    if (!answer && question.minLength === 0) {
      return {
        valid: true,
        processed: ''
      };
    }
    
    if (typeof answer !== 'string') {
      return {
        valid: false,
        error: 'Answer must be text.'
      };
    }
    
    const processedAnswer = answer.trim();
    
    if (question.minLength && processedAnswer.length < question.minLength) {
      return {
        valid: false,
        error: `Answer must be at least ${question.minLength} characters.`
      };
    }
    
    if (question.maxLength && processedAnswer.length > question.maxLength) {
      return {
        valid: false,
        error: `Answer must be no more than ${question.maxLength} characters.`
      };
    }
    
    return {
      valid: true,
      processed: processedAnswer
    };
  }

  /**
   * Validate a multiple choice answer
   * 
   * @param {Object} question - The question
   * @param {string} answer - The raw answer
   * @returns {Object} Validation result
   */
  validateMultipleChoiceAnswer(question, answer) {
    if (!answer || typeof answer !== 'string' || answer.trim() === '') {
      return {
        valid: false,
        error: 'Please select an option.'
      };
    }
    
    const processedAnswer = answer.trim();
    const options = question.options || [];
    
    // Check if the answer is a letter (A, B, C, etc.)
    const letterMatch = processedAnswer.match(/^([A-Za-z])(\s*-.*)?$/);
    if (letterMatch) {
      const letterIndex = letterMatch[1].toUpperCase().charCodeAt(0) - 65; // Convert A->0, B->1, etc.
      
      if (letterIndex >= 0 && letterIndex < options.length) {
        return {
          valid: true,
          processed: options[letterIndex].value
        };
      }
    }
    
    // Check if the answer is a number (1, 2, 3, etc.)
    const numberMatch = processedAnswer.match(/^(\d+)(\s*-.*)?$/);
    if (numberMatch) {
      const numberIndex = parseInt(numberMatch[1], 10) - 1; // Convert 1->0, 2->1, etc.
      
      if (numberIndex >= 0 && numberIndex < options.length) {
        return {
          valid: true,
          processed: options[numberIndex].value
        };
      }
    }
    
    // Check if the answer matches an option value or label
    const matchingOption = options.find(option => 
      option.value.toLowerCase() === processedAnswer.toLowerCase() ||
      option.label.toLowerCase() === processedAnswer.toLowerCase() ||
      option.label.toLowerCase().includes(processedAnswer.toLowerCase())
    );
    
    if (matchingOption) {
      return {
        valid: true,
        processed: matchingOption.value
      };
    }
    
    return {
      valid: false,
      error: `Please select a valid option: ${options.map((o, i) => `${String.fromCharCode(65 + i)}) ${o.label}`).join(', ')}`
    };
  }

  /**
   * Validate a tags answer
   * 
   * @param {Object} question - The question
   * @param {string} answer - The raw answer
   * @returns {Object} Validation result
   */
  validateTagsAnswer(question, answer) {
    if (!answer || typeof answer !== 'string' || answer.trim() === '') {
      return {
        valid: false,
        error: 'Please select at least one tag.'
      };
    }
    
    const processedAnswer = answer.trim();
    const availableTags = question.tags || [];
    
    // Split by commas and/or "and" and trim each part
    const selectedTags = processedAnswer
      .replace(/\s+and\s+/gi, ', ')
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag !== '');
    
    if (selectedTags.length === 0) {
      return {
        valid: false,
        error: 'Please select at least one valid tag.'
      };
    }
    
    // Validate that all selected tags exist in available tags
    const validTags = selectedTags.filter(tag => 
      availableTags.some(availableTag => 
        availableTag.toLowerCase() === tag.toLowerCase()
      )
    );
    
    const invalidTags = selectedTags.filter(tag => 
      !availableTags.some(availableTag => 
        availableTag.toLowerCase() === tag.toLowerCase()
      )
    );
    
    if (invalidTags.length > 0) {
      return {
        valid: false,
        error: `The following tags are not valid: ${invalidTags.join(', ')}. Please select from: ${availableTags.join(', ')}`
      };
    }
    
    return {
      valid: true,
      processed: validTags
    };
  }

  /**
   * Validate a URL answer
   * 
   * @param {Object} question - The question
   * @param {string} answer - The raw answer
   * @returns {Object} Validation result
   */
  validateUrlAnswer(question, answer) {
    if (!answer || typeof answer !== 'string' || answer.trim() === '') {
      return {
        valid: false,
        error: 'Please provide a URL.'
      };
    }
    
    let processedAnswer = answer.trim();
    
    // Add https:// if no protocol is specified
    if (!/^https?:\/\//i.test(processedAnswer)) {
      processedAnswer = `https://${processedAnswer}`;
    }
    
    // Basic URL validation
    try {
      const url = new URL(processedAnswer);
      return {
        valid: true,
        processed: processedAnswer
      };
    } catch (e) {
      return {
        valid: false,
        error: 'Please provide a valid URL (e.g., https://example.com).'
      };
    }
  }

  /**
   * Validate a scale answer
   * 
   * @param {Object} question - The question
   * @param {string} answer - The raw answer
   * @returns {Object} Validation result
   */
  validateScaleAnswer(question, answer) {
    const min = question.min || 1;
    const max = question.max || 5;
    
    // Try to parse as number
    const numValue = parseInt(answer, 10);
    
    if (isNaN(numValue)) {
      return {
        valid: false,
        error: `Please provide a number between ${min} and ${max}.`
      };
    }
    
    if (numValue < min || numValue > max) {
      return {
        valid: false,
        error: `Please provide a number between ${min} and ${max}.`
      };
    }
    
    return {
      valid: true,
      processed: numValue
    };
  }

  /**
   * Validate a yes/no answer
   * 
   * @param {Object} question - The question
   * @param {string} answer - The raw answer
   * @returns {Object} Validation result
   */
  validateYesNoAnswer(question, answer) {
    if (!answer || typeof answer !== 'string' || answer.trim() === '') {
      return {
        valid: false,
        error: 'Please answer with yes or no.'
      };
    }
    
    const processedAnswer = answer.trim().toLowerCase();
    
    if (processedAnswer === 'yes' || processedAnswer === 'y' || processedAnswer === 'true') {
      return {
        valid: true,
        processed: true
      };
    }
    
    if (processedAnswer === 'no' || processedAnswer === 'n' || processedAnswer === 'false') {
      return {
        valid: true,
        processed: false
      };
    }
    
    return {
      valid: false,
      error: 'Please answer with yes or no.'
    };
  }

  /**
   * Validate a list item answer
   * 
   * @param {Object} question - The question
   * @param {string} answer - The raw answer
   * @returns {Object} Validation result
   */
  validateListItemAnswer(question, answer) {
    // Delegate to the appropriate validator based on the list item type
    const itemType = question.itemType || QUESTION_TYPES.TEXT;
    
    const itemQuestion = {
      ...question,
      type: itemType
    };
    
    return this.validateAnswer(itemQuestion, answer);
  }

  /**
   * Get the progress information for a session
   * 
   * @param {Object} session - The conversation session
   * @returns {Object} Progress information
   */
  getProgressInfo(session) {
    if (!session) {
      throw new Error('Session is required');
    }
    
    const projectType = session.projectType || 'general';
    const flow = this.getQuestionFlow(projectType);
    
    // Filter questions based on conditions to get only applicable questions
    const applicableQuestions = flow.filter(q => this.checkConditions(q, session));
    
    const answeredQuestionIds = Object.keys(session.answers);
    const totalQuestions = applicableQuestions.length;
    const answeredQuestions = answeredQuestionIds.length;
    
    // Calculate percentage, handling edge case of no applicable questions
    const percentComplete = totalQuestions > 0 
      ? Math.round((answeredQuestions / totalQuestions) * 100)
      : 0;
    
    return {
      totalQuestions,
      answeredQuestions,
      remainingQuestions: totalQuestions - answeredQuestions,
      percentComplete,
      currentQuestion: session.currentQuestion || null,
      projectType
    };
  }

  /**
   * Check if all required questions have been answered
   *
   * @param {Object} session - The conversation session
   * @returns {boolean} Whether all required questions are answered
   */
  areRequiredQuestionsAnswered(session) {
    if (!session) {
      throw new Error('Session is required');
    }

    const projectType = session.projectType || 'general';
    const flow = this.getQuestionFlow(projectType);

    // Filter questions based on conditions to get only applicable questions
    const applicableQuestions = flow.filter(q => this.checkConditions(q, session));

    // Find required questions (those marked as required or with minLength > 0)
    const requiredQuestions = applicableQuestions.filter(q =>
      q.required === true ||
      (q.type === 'text' && q.minLength > 0) ||
      (q.type === 'multiple_choice') ||
      (q.type === 'tags')
    );

    const answeredQuestionIds = Object.keys(session.answers || {});

    // Check if all required questions are answered
    return requiredQuestions.every(q => answeredQuestionIds.includes(q.id));
  }

  /**
   * Get a list of missing required questions
   *
   * @param {Object} session - The conversation session
   * @returns {Array} Array of missing required questions
   */
  getMissingRequiredQuestions(session) {
    if (!session) {
      throw new Error('Session is required');
    }

    const projectType = session.projectType || 'general';
    const flow = this.getQuestionFlow(projectType);

    // Filter questions based on conditions to get only applicable questions
    const applicableQuestions = flow.filter(q => this.checkConditions(q, session));

    // Find required questions (those marked as required or with minLength > 0)
    const requiredQuestions = applicableQuestions.filter(q =>
      q.required === true ||
      (q.type === 'text' && q.minLength > 0) ||
      (q.type === 'multiple_choice') ||
      (q.type === 'tags')
    );

    const answeredQuestionIds = Object.keys(session.answers || {});

    // Return questions that are required but not answered
    return requiredQuestions.filter(q => !answeredQuestionIds.includes(q.id));
  }
}

export default QuestionFlowEngine;