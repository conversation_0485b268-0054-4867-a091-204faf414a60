/**
 * @file decision-templates.js
 * @description Contains templates for common decision types and translations
 */

/**
 * Decision templates with business-friendly translations
 */
export const DECISION_TEMPLATES = {
  /**
   * Translation patterns for technical terms to business language
   */
  translation_patterns: {
    technical_term_mapping: [
      { "API": "Data connection" },
      { "Database": "Information storage" },
      { "Framework": "Building foundation" },
      { "Deployment": "Making it live" },
      { "Authentication": "User login system" },
      { "Frontend": "User interface" },
      { "Backend": "Server-side processing" },
      { "React": "Modern user interface system" },
      { "Vue": "Flexible user interface system" },
      { "Angular": "Enterprise user interface system" },
      { "Next.js": "Enhanced React system" },
      { "Svelte": "Efficient user interface system" },
      { "PostgreSQL": "Structured information storage" },
      { "MongoDB": "Flexible information storage" },
      { "MySQL": "Traditional information storage" },
      { "Redis": "Fast temporary storage" },
      { "Vercel": "Simple hosting platform" },
      { "AWS": "Powerful hosting platform" },
      { "Azure": "Microsoft's hosting platform" },
      { "Google Cloud": "Google's hosting platform" },
      { "Netlify": "Simple hosting platform" },
      { "Tailwind": "Custom styling system" },
      { "Bootstrap": "Standard styling system" },
      { "Material UI": "Google-style design system" },
      { "Chakra UI": "Accessible design system" },
      { "REST API": "Standard data connection" },
      { "GraphQL": "Efficient data connection" },
      { "Docker": "Portable environment system" },
      { "Kubernetes": "Advanced deployment system" },
      { "CI/CD": "Automatic update system" },
      { "Jenkins": "Build automation system" },
      { "GitHub Actions": "Code automation system" }
    ]
  },
  
  /**
   * Decision templates for common decision types
   */
  decisions: [
    {
      id: "framework_selection",
      context: "framework_selection",
      title: "Choose Your Website Building Foundation",
      description: "Select the approach for building your website's user interface. This affects how quickly you can develop features and how your site will perform.",
      options: [
        {
          id: "react",
          title: "React",
          description: "A modern, widely-used approach for building interactive websites",
          businessImpact: {
            level: "high",
            description: "Excellent for complex, interactive applications"
          },
          timeImplication: {
            level: "medium",
            description: "Moderate development time with rapid iterations"
          },
          costImplication: {
            level: "medium",
            description: "Mid-range development cost"
          },
          riskAssessment: {
            level: "low",
            description: "Low risk due to widespread adoption and support"
          },
          technicalDetails: "React is a JavaScript library maintained by Facebook with a component-based architecture and virtual DOM for efficient updates.",
          isRecommended: true
        },
        {
          id: "vue",
          title: "Vue",
          description: "A flexible, easy-to-learn approach for building websites",
          businessImpact: {
            level: "medium",
            description: "Good for both simple and complex websites"
          },
          timeImplication: {
            level: "low",
            description: "Faster initial development time"
          },
          costImplication: {
            level: "low",
            description: "Lower development cost"
          },
          riskAssessment: {
            level: "medium",
            description: "Moderate risk due to smaller community than React"
          },
          technicalDetails: "Vue is a progressive JavaScript framework with an incrementally adoptable architecture that focuses on declarative rendering and component composition."
        },
        {
          id: "angular",
          title: "Angular",
          description: "A comprehensive, enterprise-grade approach for building complex websites",
          businessImpact: {
            level: "high",
            description: "Excellent for large-scale business applications"
          },
          timeImplication: {
            level: "high",
            description: "Longer development time due to complexity"
          },
          costImplication: {
            level: "high",
            description: "Higher development cost"
          },
          riskAssessment: {
            level: "medium",
            description: "Moderate risk due to steeper learning curve"
          },
          technicalDetails: "Angular is a TypeScript-based open-source framework led by the Angular Team at Google and a community of individuals and corporations."
        },
        {
          id: "custom_framework",
          title: "Custom Choice",
          description: "Use a different website building approach of your choice",
          businessImpact: {
            level: "medium",
            description: "Impact depends on the specific technology chosen"
          },
          timeImplication: {
            level: "medium",
            description: "Timeline depends on team familiarity with the chosen technology"
          },
          costImplication: {
            level: "medium",
            description: "Costs vary based on the specific technology"
          },
          riskAssessment: {
            level: "medium",
            description: "Risk varies based on technology maturity and team expertise"
          },
          technicalDetails: "This option allows you to specify a custom framework or technology not listed in the standard options.",
          isCustom: true
        }
      ]
    },
    {
      id: "database_selection",
      context: "database_selection",
      title: "Choose Your Information Storage System",
      description: "Select how your application will store and manage information. This affects data organization, query capabilities, and scaling.",
      options: [
        {
          id: "postgresql",
          title: "PostgreSQL",
          description: "A structured storage system with strong data relationships",
          businessImpact: {
            level: "high",
            description: "Excellent for complex data with relationships"
          },
          timeImplication: {
            level: "medium",
            description: "Requires upfront data modeling"
          },
          costImplication: {
            level: "medium",
            description: "Standard hosting costs with potential for optimization"
          },
          riskAssessment: {
            level: "low",
            description: "Low risk with proven reliability"
          },
          technicalDetails: "PostgreSQL is an advanced open-source relational database with strong compliance to SQL standards and support for complex queries and data types."
        },
        {
          id: "mongodb",
          title: "MongoDB",
          description: "A flexible storage system that adapts to changing needs",
          businessImpact: {
            level: "medium",
            description: "Good for rapidly evolving data structures"
          },
          timeImplication: {
            level: "low",
            description: "Faster initial development with flexible schema"
          },
          costImplication: {
            level: "medium",
            description: "Standard hosting costs, potentially higher at scale"
          },
          riskAssessment: {
            level: "medium",
            description: "Moderate risk for complex data relationships"
          },
          technicalDetails: "MongoDB is a document-oriented NoSQL database that stores data in flexible, JSON-like documents, allowing for dynamic schemas."
        },
        {
          id: "custom_database",
          title: "Custom Choice",
          description: "Use a different storage system of your choice",
          businessImpact: {
            level: "medium",
            description: "Impact depends on the specific technology chosen"
          },
          timeImplication: {
            level: "medium",
            description: "Timeline depends on team familiarity with the chosen technology"
          },
          costImplication: {
            level: "medium",
            description: "Costs vary based on the specific technology"
          },
          riskAssessment: {
            level: "medium",
            description: "Risk varies based on technology maturity and team expertise"
          },
          technicalDetails: "This option allows you to specify a custom database or storage system not listed in the standard options.",
          isCustom: true
        }
      ]
    },
    {
      id: "hosting_selection",
      context: "hosting_selection",
      title: "Choose Where Your Website Will Live",
      description: "Select the platform that will host your website and make it available to users. This affects reliability, scaling, and management complexity.",
      options: [
        {
          id: "vercel",
          title: "Vercel",
          description: "A simple, modern hosting platform designed for websites",
          businessImpact: {
            level: "medium",
            description: "Great for marketing sites and modern web apps"
          },
          timeImplication: {
            level: "low",
            description: "Very quick setup and deployment"
          },
          costImplication: {
            level: "low",
            description: "Free tier available, affordable scaling"
          },
          riskAssessment: {
            level: "low",
            description: "Low risk with good reliability"
          },
          technicalDetails: "Vercel is a cloud platform for static sites and Serverless Functions that fits perfectly with modern web frameworks and workflows."
        },
        {
          id: "aws",
          title: "AWS",
          description: "A powerful, comprehensive hosting platform with many features",
          businessImpact: {
            level: "high",
            description: "Excellent for complex, high-traffic applications"
          },
          timeImplication: {
            level: "high",
            description: "More setup time and configuration required"
          },
          costImplication: {
            level: "medium",
            description: "Pay-as-you-go with potential for optimization"
          },
          riskAssessment: {
            level: "medium",
            description: "Moderate risk due to complexity"
          },
          technicalDetails: "Amazon Web Services (AWS) offers a broad set of global cloud-based products including compute, storage, databases, analytics, networking, mobile, developer tools, management tools, IoT, security, and enterprise applications."
        },
        {
          id: "custom_hosting",
          title: "Custom Choice",
          description: "Use a different hosting platform of your choice",
          businessImpact: {
            level: "medium",
            description: "Impact depends on the specific platform chosen"
          },
          timeImplication: {
            level: "medium",
            description: "Timeline depends on team familiarity with the chosen platform"
          },
          costImplication: {
            level: "medium",
            description: "Costs vary based on the specific platform"
          },
          riskAssessment: {
            level: "medium",
            description: "Risk varies based on platform reliability and team expertise"
          },
          technicalDetails: "This option allows you to specify a custom hosting platform not listed in the standard options.",
          isCustom: true
        }
      ]
    },
    {
      id: "ui_framework_selection",
      context: "ui_framework_selection",
      title: "Choose Your Visual Design Approach",
      description: "Select how you'll create the visual appearance of your website. This affects design flexibility, development speed, and visual consistency.",
      options: [
        {
          id: "tailwind",
          title: "Tailwind CSS",
          description: "A customizable approach for unique, branded designs",
          businessImpact: {
            level: "high",
            description: "Excellent for custom-designed experiences"
          },
          timeImplication: {
            level: "medium",
            description: "Initial learning curve but faster iterations"
          },
          costImplication: {
            level: "low",
            description: "No additional cost beyond development time"
          },
          riskAssessment: {
            level: "low",
            description: "Low risk with growing adoption"
          },
          technicalDetails: "Tailwind CSS is a utility-first CSS framework packed with classes that can be composed to build any design, directly in your markup."
        },
        {
          id: "bootstrap",
          title: "Bootstrap",
          description: "A standardized approach with pre-designed components",
          businessImpact: {
            level: "medium",
            description: "Good for consistent, professional interfaces"
          },
          timeImplication: {
            level: "low",
            description: "Faster initial development with pre-built components"
          },
          costImplication: {
            level: "low",
            description: "No additional cost beyond development time"
          },
          riskAssessment: {
            level: "low",
            description: "Very low risk with proven track record"
          },
          technicalDetails: "Bootstrap is a free and open-source CSS framework directed at responsive, mobile-first front-end web development. It contains CSS and JavaScript-based design templates for typography, forms, buttons, navigation, and other interface components."
        },
        {
          id: "custom_ui_framework",
          title: "Custom Choice",
          description: "Use a different visual style system of your choice",
          businessImpact: {
            level: "medium",
            description: "Impact depends on the specific system chosen"
          },
          timeImplication: {
            level: "medium",
            description: "Timeline depends on team familiarity with the chosen system"
          },
          costImplication: {
            level: "low",
            description: "Most styling systems have similar cost implications"
          },
          riskAssessment: {
            level: "medium",
            description: "Risk varies based on system maturity and team expertise"
          },
          technicalDetails: "This option allows you to specify a custom UI framework or styling system not listed in the standard options.",
          isCustom: true
        }
      ]
    },
    {
      id: "feature_prioritization",
      context: "feature_prioritization",
      title: "Choose Your Product Launch Strategy",
      description: "Decide how quickly to launch your product and which features to include initially. This affects time-to-market and initial user experience.",
      options: [
        {
          id: "mvp",
          title: "Minimum Viable Product (MVP)",
          description: "Launch quickly with core features only",
          businessImpact: {
            level: "medium",
            description: "Faster market entry with essential functionality"
          },
          timeImplication: {
            level: "low",
            description: "Shortest path to launch"
          },
          costImplication: {
            level: "low",
            description: "Lowest initial investment"
          },
          riskAssessment: {
            level: "medium",
            description: "Moderate risk of underwhelming users"
          },
          technicalDetails: "An MVP approach focuses on implementing only the critical features needed to validate the product concept with real users."
        },
        {
          id: "full",
          title: "Complete Product",
          description: "Launch when all planned features are ready",
          businessImpact: {
            level: "high",
            description: "Comprehensive solution at launch"
          },
          timeImplication: {
            level: "high",
            description: "Longer development before launch"
          },
          costImplication: {
            level: "high",
            description: "Higher initial investment"
          },
          riskAssessment: {
            level: "high",
            description: "Higher risk of delayed market feedback"
          },
          technicalDetails: "A complete product approach implements the full feature set before initial launch, focusing on a polished and comprehensive user experience."
        },
        {
          id: "phased",
          title: "Phased Rollout",
          description: "Launch core features first, then add more in planned phases",
          businessImpact: {
            level: "high",
            description: "Balance between quick launch and complete feature set"
          },
          timeImplication: {
            level: "medium",
            description: "Moderate time to initial launch, with planned updates"
          },
          costImplication: {
            level: "medium",
            description: "Distributed investment over time"
          },
          riskAssessment: {
            level: "low",
            description: "Lower risk with balanced approach"
          },
          technicalDetails: "A phased rollout strategy implements features in prioritized waves, allowing for user feedback between releases while maintaining a clear roadmap."
        },
        {
          id: "custom_launch_strategy",
          title: "Custom Choice",
          description: "Use a different launch strategy of your choice",
          businessImpact: {
            level: "medium",
            description: "Impact depends on the specific strategy chosen"
          },
          timeImplication: {
            level: "medium",
            description: "Timeline depends on the specific strategy details"
          },
          costImplication: {
            level: "medium",
            description: "Costs vary based on the specific strategy"
          },
          riskAssessment: {
            level: "medium",
            description: "Risk varies based on strategy and execution"
          },
          technicalDetails: "This option allows you to specify a custom launch strategy not listed in the standard options.",
          isCustom: true
        }
      ]
    },
    {
      id: "api_selection",
      context: "api_selection",
      title: "Choose Your Data Connection Approach",
      description: "Select how your application will communicate with servers and exchange data. This affects efficiency, flexibility, and development complexity.",
      options: [
        {
          id: "rest",
          title: "REST API",
          description: "A standard approach for connecting to data sources",
          businessImpact: {
            level: "medium",
            description: "Good for most business applications"
          },
          timeImplication: {
            level: "low",
            description: "Well-understood with many tools available"
          },
          costImplication: {
            level: "low",
            description: "Standard implementation cost"
          },
          riskAssessment: {
            level: "low",
            description: "Very low risk with proven approach"
          },
          technicalDetails: "REST (Representational State Transfer) is an architectural style that uses standard HTTP methods and is widely supported across platforms."
        },
        {
          id: "graphql",
          title: "GraphQL",
          description: "A flexible approach for efficient data retrieval",
          businessImpact: {
            level: "high",
            description: "Excellent for complex data needs and mobile apps"
          },
          timeImplication: {
            level: "medium",
            description: "More initial setup but can speed up later development"
          },
          costImplication: {
            level: "medium",
            description: "Higher initial investment but potential savings later"
          },
          riskAssessment: {
            level: "medium",
            description: "Moderate risk with growing adoption"
          },
          technicalDetails: "GraphQL is a query language and runtime for APIs that enables clients to request exactly the data they need, making it efficient for complex applications and slow network connections."
        },
        {
          id: "custom_api",
          title: "Custom Choice",
          description: "Use a different data connection approach of your choice",
          businessImpact: {
            level: "medium",
            description: "Impact depends on the specific approach chosen"
          },
          timeImplication: {
            level: "medium",
            description: "Timeline depends on team familiarity with the chosen approach"
          },
          costImplication: {
            level: "medium",
            description: "Costs vary based on the specific approach"
          },
          riskAssessment: {
            level: "medium",
            description: "Risk varies based on approach maturity and team expertise"
          },
          technicalDetails: "This option allows you to specify a custom API approach not listed in the standard options.",
          isCustom: true
        }
      ]
    }
  ],
  
  /**
   * Function to dynamically create a decision template for custom technologies
   * This can be used when a user wants to use a technology not in our predefined list
   * @param {string} context - The decision context (e.g., 'custom_framework')
   * @param {string} title - The title for the decision
   * @param {string} description - The description of the decision
   * @param {Array} options - Array of technology options
   * @returns {Object} A formatted decision template
   */
  createCustomDecisionTemplate(context, title, description, options) {
    // Generate IDs for the decision and options
    const decisionId = `custom_${context}_${Date.now()}`;
    
    // Format the options
    const formattedOptions = options.map((option, index) => {
      return {
        id: option.id || `option_${index}`,
        title: option.title || `Option ${index + 1}`,
        description: option.description || `Custom option ${index + 1}`,
        businessImpact: option.businessImpact || {
          level: "medium",
          description: "Impact depends on the specific technology chosen"
        },
        timeImplication: option.timeImplication || {
          level: "medium",
          description: "Timeline depends on team familiarity"
        },
        costImplication: option.costImplication || {
          level: "medium",
          description: "Costs vary based on the specific technology"
        },
        riskAssessment: option.riskAssessment || {
          level: "medium",
          description: "Risk varies based on technology maturity and team expertise"
        },
        technicalDetails: option.technicalDetails || "Custom technology option.",
        isRecommended: option.isRecommended || false,
        isCustom: option.isCustom || false
      };
    });
    
    // Create the decision template
    return {
      id: decisionId,
      context,
      title: title || `Choose Your ${context.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}`,
      description: description || `Select the best option for your needs.`,
      options: formattedOptions,
      isCustom: true
    };
  }
};

/**
 * Get a decision template by context
 * @param {string} context - The decision context (e.g., 'framework_selection')
 * @returns {Object|null} The decision template or null if not found
 */
export function getDecisionTemplate(context) {
  // Find the template in the decisions array
  const template = DECISION_TEMPLATES.decisions.find(
    decision => decision.context === context
  );
  
  return template || null;
}

/**
 * Generate a custom template when no predefined template exists
 * @param {string} context - The decision context
 * @param {Object} additionalContext - Additional context for the decision
 * @returns {Promise<Object>} A generated decision template
 */
export async function generateCustomTemplate(context, additionalContext = {}) {
  // Create a default title based on the context
  const title = `Choose Your ${context.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}`;
  
  // Create a default description
  const description = `Select the best ${context.replace(/_/g, ' ')} option for your project.`;
  
  // Create default options with a custom choice
  const options = [
    {
      id: 'option1',
      title: 'Option 1',
      description: `A standard ${context.replace(/_/g, ' ')} approach`,
      businessImpact: 'Standard business impact',
      timeImpact: 'Standard implementation time',
      costImpact: 'Standard cost implications',
      riskLevel: 'medium'
    },
    {
      id: 'custom',
      title: 'Custom Choice',
      description: `Your preferred ${context.replace(/_/g, ' ')} solution`,
      businessImpact: 'Tailored to your specific requirements',
      timeImpact: 'Depends on your specific choice',
      costImpact: 'Varies based on implementation',
      riskLevel: 'medium',
      isCustom: true
    }
  ];
  
  // Use the createCustomDecisionTemplate function from DECISION_TEMPLATES
  return DECISION_TEMPLATES.createCustomDecisionTemplate(context, title, description, options);
}

export default DECISION_TEMPLATES;