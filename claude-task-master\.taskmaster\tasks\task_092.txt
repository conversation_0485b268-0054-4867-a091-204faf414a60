# Task ID: 92
# Title: Implement Project Root Environment Variable Support in MCP Configuration
# Status: review
# Dependencies: 1, 3, 17
# Priority: medium
# Description: Add support for a 'TASK_MASTER_PROJECT_ROOT' environment variable in MCP configuration, allowing it to be set in both mcp.json and .env, with precedence over other methods. This will define the root directory for the MCP server and take precedence over all other project root resolution methods. The implementation should be backward compatible with existing workflows that don't use this variable.
# Details:
Update the MCP server configuration system to support the TASK_MASTER_PROJECT_ROOT environment variable as the standard way to specify the project root directory. This provides better namespacing and avoids conflicts with other tools that might use a generic PROJECT_ROOT variable. Implement a clear precedence order for project root resolution:

1. TASK_MASTER_PROJECT_ROOT environment variable (from shell or .env file)
2. 'projectRoot' key in mcp_config.toml or mcp.json configuration files
3. Existing resolution logic (CLI args, current working directory, etc.)

Modify the configuration loading logic to check for these sources in the specified order, ensuring backward compatibility. All MCP tools and components should use this standardized project root resolution logic. The TASK_MASTER_PROJECT_ROOT environment variable will be required because path resolution is delegated to the MCP client implementation, ensuring consistent behavior across different environments.

Implementation steps:
1. Identify all code locations where project root is determined (initialization, utility functions)
2. Update configuration loaders to check for TASK_MASTER_PROJECT_ROOT in environment variables
3. Add support for 'projectRoot' in configuration files as a fallback
4. Refactor project root resolution logic to follow the new precedence rules
5. Ensure all MCP tools and functions use the updated resolution logic
6. Add comprehensive error handling for cases where TASK_MASTER_PROJECT_ROOT is not set or invalid
7. Implement validation to ensure the specified directory exists and is accessible

# Test Strategy:
1. Write unit tests to verify that the config loader correctly reads project root from environment variables and configuration files with the expected precedence:
   - Test TASK_MASTER_PROJECT_ROOT environment variable takes precedence when set
   - Test 'projectRoot' in configuration files is used when environment variable is absent
   - Test fallback to existing resolution logic when neither is specified

2. Add integration tests to ensure that the MCP server and all tools use the correct project root:
   - Test server startup with TASK_MASTER_PROJECT_ROOT set to various valid and invalid paths
   - Test configuration file loading from the specified project root
   - Test path resolution for resources relative to the project root

3. Test backward compatibility:
   - Verify existing workflows function correctly without the new variables
   - Ensure no regression in projects not using the new configuration options

4. Manual testing:
   - Set TASK_MASTER_PROJECT_ROOT in shell environment and verify correct behavior
   - Set TASK_MASTER_PROJECT_ROOT in .env file and verify it's properly loaded
   - Configure 'projectRoot' in configuration files and test precedence
   - Test with invalid or non-existent directories to verify error handling

# Subtasks:
## 1. Update configuration loader to check for TASK_MASTER_PROJECT_ROOT environment variable [pending]
### Dependencies: None
### Description: Modify the configuration loading system to check for the TASK_MASTER_PROJECT_ROOT environment variable as the primary source for project root directory. Ensure proper error handling if the variable is set but points to a non-existent or inaccessible directory.
### Details:


## 2. Add support for 'projectRoot' in configuration files [pending]
### Dependencies: None
### Description: Implement support for a 'projectRoot' key in mcp_config.toml and mcp.json configuration files as a fallback when the environment variable is not set. Update the configuration parser to recognize and validate this field.
### Details:


## 3. Refactor project root resolution logic with clear precedence rules [pending]
### Dependencies: None
### Description: Create a unified project root resolution function that follows the precedence order: 1) TASK_MASTER_PROJECT_ROOT environment variable, 2) 'projectRoot' in config files, 3) existing resolution methods. Ensure this function is used consistently throughout the codebase.
### Details:


## 4. Update all MCP tools to use the new project root resolution [pending]
### Dependencies: None
### Description: Identify all MCP tools and components that need to access the project root and update them to use the new resolution logic. Ensure consistent behavior across all parts of the system.
### Details:


## 5. Add comprehensive tests for the new project root resolution [pending]
### Dependencies: None
### Description: Create unit and integration tests to verify the correct behavior of the project root resolution logic under various configurations and edge cases.
### Details:


## 6. Update documentation with new configuration options [pending]
### Dependencies: None
### Description: Update the project documentation to clearly explain the new TASK_MASTER_PROJECT_ROOT environment variable, the 'projectRoot' configuration option, and the precedence rules. Include examples of different configuration scenarios.
### Details:


## 7. Implement validation for project root directory [pending]
### Dependencies: None
### Description: Add validation to ensure the specified project root directory exists and has the necessary permissions. Provide clear error messages when validation fails.
### Details:


## 8. Implement support for loading environment variables from .env files [pending]
### Dependencies: None
### Description: Add functionality to load the TASK_MASTER_PROJECT_ROOT variable from .env files in the workspace, following best practices for environment variable management in MCP servers.
### Details:


