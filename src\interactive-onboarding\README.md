# Interactive Onboarding System

This module implements the conversational onboarding experience for Guidant, allowing users to set up new projects through a natural chat interface rather than technical commands.

## Components

### 1. Conversation State Manager (`conversation-state-manager.js`)

Manages the state of interactive onboarding conversations, including:
- Session creation and retrieval
- State transitions
- Answer storage and retrieval
- Navigation (back, skip, etc.)
- Progress tracking

This is the core state machine that powers the conversational onboarding experience. It integrates with the existing SessionRecoveryManager to leverage the established session management infrastructure.

### 2. Question Flow Engine (`question-flow-engine.js`)

Manages the flow of questions during interactive onboarding, including:
- Question type definitions and validation
- Project-specific question flows
- Dynamic question selection based on previous answers
- Support for list-type questions (allowing multiple inputs)

Based on prototype feedback from WLR-000, this implementation includes support for list-type questions where users can provide multiple inputs.

### 3. Flow Templates

Project-specific question flows that define the onboarding experience for different types of projects:

- **General Flow** (`flows/general-flow.js`): Default flow for any project type
- **E-commerce Flow** (`flows/e-commerce-flow.js`): Specialized flow for e-commerce projects
- **SaaS App Flow** (`flows/saas-app-flow.js`): Specialized flow for SaaS applications
- **Community Platform Flow** (`flows/community-platform-flow.js`): Specialized flow for community platforms

### 4. MCP Tools

The MCP tools that expose the onboarding functionality to AI agents:

- `guidant_init_project`: Start interactive onboarding session with project idea
- `guidant_answer_question`: Submit user's answer and get next question
- `guidant_get_session_state`: Retrieve current session state for recovery
- `guidant_complete_onboarding`: Finalize project setup and begin development

## Question Types

The system supports various question types:

1. **Text**: Simple text input
2. **Multiple Choice**: Selection from predefined options
3. **Tags**: Selection of multiple tags from a set
4. **URL**: URL input with validation
5. **Scale**: Numeric scale (e.g., 1-5)
6. **Yes/No**: Simple boolean choice
7. **List**: Collection of multiple inputs (added based on prototype feedback)

## Conversation Flow Example

```
User: "I want to build a restaurant app"
AI Agent calls: guidant_init_project({ idea: "restaurant app" })
Returns: { sessionId, currentQuestion: "What's your restaurant's name?", progress: "1/8" }
AI Agent: "Great! What's your restaurant's name?"
User: "Mario's Pizza Palace"
AI Agent calls: guidant_answer_question({ sessionId, answer: "Mario's Pizza Palace" })
Returns: { nextQuestion: "What type of cuisine?", progress: "2/8" }
```

## Memory Support Features

The system includes several features to support users with memory/executive function challenges:

- Project orientation: "You're building Mario's Pizza Palace"
- Progress tracking: "Question 3 of 8 - Choosing features"
- Session recovery: "Welcome back! We were choosing your design style..."
- Conversation history: Record of all previous questions and answers
- Navigation support: Ability to go back, skip questions, and change answers

## Implementation Notes

- The conversation state is persisted in the `.guidant/context/conversations` directory
- Session recovery is integrated with the existing session recovery system
- The system supports dynamic question selection based on previous answers
- List questions allow collecting multiple inputs until the user indicates completion

## Usage

AI agents should interact with the onboarding system through the MCP tools. The typical flow is:

1. Call `guidant_init_project` to start onboarding
2. Present the current question to the user
3. Call `guidant_answer_question` with the user's response
4. Repeat steps 2-3 until all questions are answered
5. Call `guidant_complete_onboarding` to finalize the project setup

## Testing

Tests are available for all components:
- `conversation-state-manager.test.js`: Tests for the conversation state manager
- `question-flow-engine.test.js`: Tests for the question flow engine
- `e-commerce-flow.test.js`: Tests for the e-commerce flow template

Run tests with: `bun test src/interactive-onboarding` 