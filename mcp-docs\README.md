# Model Context Protocol (MCP) Documentation

This directory contains documentation about the Model Context Protocol (MCP), an open protocol that standardizes how applications provide context to LLMs.

## Contents

- [Summary](summary.md) - Concise overview of MCP and its key components
- [Introduction](introduction.md) - Overview of MCP and its purpose
- [Core Architecture](core-architecture.md) - Details on the client-server architecture and protocol design
- [Resources](resources.md) - Information about exposing data and content through MCP
- [Tools](tools.md) - Information about implementing and using MCP tools
- [Prompts](prompts.md) - Documentation on creating and using prompt templates
- [Sampling](sampling.md) - Details on requesting LLM completions through MCP
- [Roots](roots.md) - Information about defining operational boundaries for servers
- [Transports](transports.md) - Documentation on communication mechanisms between clients and servers

## What is MCP?

MCP (Model Context Protocol) is an open standard developed by Anthropic that provides a standardized way for AI models to connect with external data sources, tools, and applications. It serves as a communication protocol between AI systems (like Claude or GPT models) and various external resources.

Think of MCP like a USB-C port for AI applications. Just as USB-C provides a standardized way to connect your devices to various peripherals and accessories, MCP provides a standardized way to connect AI models to different data sources and tools.

## Key Features

- **Client-Server Architecture**: MCP follows a client-server model where hosts (like Claude Desktop) connect to servers that provide specific capabilities
- **Standardized Communication**: Uses JSON-RPC 2.0 for message exchange
- **Multiple Transport Options**: Supports stdio for local processes and HTTP for remote communication
- **Core Primitives**:
  - **Resources**: Static or dynamic content that can be shared with LLMs
  - **Tools**: Executable functions that LLMs can invoke
  - **Prompts**: Reusable templates for common LLM interactions
  - **Sampling**: Ability for servers to request completions from LLMs
  - **Roots**: Boundaries that define where servers can operate
  - **Transports**: Communication mechanisms between clients and servers

## Official Resources

- [Official MCP Documentation](https://modelcontextprotocol.io)
- [MCP GitHub Organization](https://github.com/model-context-protocol)
- [MCP Specification](https://modelcontextprotocol.io/specification/2025-03-26)

## Source

This documentation was scraped from the official MCP website at https://modelcontextprotocol.io on the date of creation. 