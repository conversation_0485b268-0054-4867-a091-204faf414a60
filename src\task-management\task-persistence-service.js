/**
 * Task Persistence Service for Guidant
 * Handles CRUD operations for tasks, storing them in .guidant/tasks/tasks.json.
 * Uses RichTaskSchema for validation.
 */

import { readProjectFile, writeProjectFileWithLock, updateProjectFile } from '../file-management/project-structure.js';
import { TASKS_STORAGE } from '../constants/paths.js';
import { RichTaskSchema, TASK_STATUSES } from './task-schema.js'; // Assuming a validation function will be added here or use a library
import { v4 as uuidv4 } from 'uuid'; // For generating unique IDs

// Basic schema validation (can be replaced with a more robust library like Ajv)
function validateTask(taskData) {
  const { properties, required } = RichTaskSchema;
  for (const key of required) {
    if (!(key in taskData)) {
      return { valid: false, error: `Missing required field: ${key}` };
    }
  }

  for (const key in taskData) {
    if (properties[key]) {
      const schemaField = properties[key];
      if (schemaField.type && typeof taskData[key] !== schemaField.type && !(schemaField.type === 'array' && Array.isArray(taskData[key]))) {
        // Allow for format: 'date-time' which is a string
        if (schemaField.format === 'date-time' && typeof taskData[key] === 'string') {
          // Potentially add date-time format validation here
        } else {
          return { valid: false, error: `Invalid type for field ${key}. Expected ${schemaField.type}, got ${typeof taskData[key]}` };
        }
      }
      if (schemaField.enum && !schemaField.enum.includes(taskData[key])) {
        return { valid: false, error: `Invalid value for field ${key}. Allowed values: ${schemaField.enum.join(', ')}` };
      }
    }
  }
  return { valid: true };
}

export class TaskPersistenceService {
  constructor(projectRoot = process.cwd()) {
    this.projectRoot = projectRoot;
    this.tasksFilePath = TASKS_STORAGE;
  }

  /**
   * Reads all tasks from the storage file.
   * @returns {Promise<Array>} An array of tasks.
   */
  async getAllTasks() {
    try {
      const tasks = await readProjectFile(this.tasksFilePath, this.projectRoot);
      return Array.isArray(tasks) ? tasks : [];
    } catch (error) {
      // If the file doesn't exist or is invalid, return an empty array
      if (error.message.includes('Failed to read') || error.message.includes('File not found')) {
        await this.ensureTasksFileExists(); // Ensure file is created for next operations
        return [];
      }
      console.error('Error reading tasks:', error);
      throw new Error(`Failed to get all tasks: ${error.message}`);
    }
  }

  /**
   * Ensures the tasks.json file exists, creating it with an empty array if not.
   */
  async ensureTasksFileExists() {
    try {
      await readProjectFile(this.tasksFilePath, this.projectRoot);
    } catch (error) {
      if (error.message.includes('Failed to read') || error.message.includes('File not found')) {
        console.log(`Tasks file not found at ${this.tasksFilePath}. Creating new file.`);
        await writeProjectFileWithLock(this.tasksFilePath, [], this.projectRoot);
      } else {
        throw error; // Re-throw other errors
      }
    }
  }

  /**
   * Creates a new task.
   * @param {Object} taskData - The data for the new task.
   * @returns {Promise<Object>} The created task object.
   */
  async createTask(taskData) {
    const newTask = {
      id: taskData.id || uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: taskData.status || TASK_STATUSES.PENDING,
      ...taskData,
    };

    const validation = validateTask(newTask);
    if (!validation.valid) {
      throw new Error(`Task validation failed: ${validation.error}`);
    }

    try {
      await this.ensureTasksFileExists(); // Ensure file exists before updating
      
      let allTasksForDepCheck = [];
      try {
        allTasksForDepCheck = await this.getAllTasks();
      } catch (e) {
        // If getAllTasks fails (e.g., file just created or empty), proceed with empty list for dep check
        // This is okay for createTask as new task won't have invalid old deps.
      }
      
      if (newTask.dependencies && newTask.dependencies.length > 0) {
        for (const depId of newTask.dependencies) {
          if (!allTasksForDepCheck.some(t => t.id === depId)) {
            throw new Error(`Dependency validation failed: Task with ID ${depId} listed in dependencies does not exist.`);
          }
        }
      }

      const updatedTasksResult = await updateProjectFile(
        this.tasksFilePath,
        (tasks = []) => {
          if (!Array.isArray(tasks)) {
            console.warn(`Tasks file at ${this.tasksFilePath} was not an array. Reinitializing.`);
            tasks = [];
          }
          if (tasks.some(task => task.id === newTask.id)) {
            throw new Error(`Task with ID ${newTask.id} already exists.`);
          }
          tasks.push(newTask);
          return tasks;
        },
        this.projectRoot
      );
      return updatedTasksResult.data.find(t => t.id === newTask.id);
    } catch (error) {
      console.error('Error creating task:', error);
      throw new Error(`Failed to create task: ${error.message}`);
    }
  }

  /**
   * Retrieves a task by its ID.
   * @param {string} taskId - The ID of the task to retrieve.
   * @returns {Promise<Object|null>} The task object, or null if not found.
   */
  async getTaskById(taskId) {
    const tasks = await this.getAllTasks();
    return tasks.find(task => task.id === taskId) || null;
  }

  /**
   * Updates an existing task.
   * @param {string} taskId - The ID of the task to update.
   * @param {Object} updates - An object containing the fields to update.
   * @returns {Promise<Object|null>} The updated task object, or null if not found.
   */
  async updateTask(taskId, updates) {
    if (updates.id && updates.id !== taskId) {
      throw new Error('Cannot change task ID during update.');
    }

    let updatedTask = null;
    try {
      await this.ensureTasksFileExists();
      
      // For dependency validation during update, we need the current list of all tasks.
      // This could be optimized if performance becomes an issue for very large task lists.
      const allTasksForDepCheck = await this.getAllTasks();

      if (updates.dependencies && updates.dependencies.length > 0) {
        for (const depId of updates.dependencies) {
          if (!allTasksForDepCheck.some(t => t.id === depId)) {
            throw new Error(`Dependency validation failed during update: Task with ID ${depId} listed in dependencies does not exist.`);
          }
        }
      }

      const updateResult = await updateProjectFile(
        this.tasksFilePath,
        (tasks = []) => {
          if (!Array.isArray(tasks)) {
            console.warn(`Tasks file at ${this.tasksFilePath} was not an array. Reinitializing.`);
            tasks = [];
          }
          const taskIndex = tasks.findIndex(task => task.id === taskId);
          if (taskIndex === -1) {
            throw new Error(`Task with ID ${taskId} not found.`);
          }
          const taskToUpdate = { ...tasks[taskIndex], ...updates, updatedAt: new Date().toISOString() };
          
          const validation = validateTask(taskToUpdate);
          if (!validation.valid) {
            throw new Error(`Updated task validation failed: ${validation.error}`);
          }
          
          tasks[taskIndex] = taskToUpdate;
          updatedTask = taskToUpdate;
          return tasks;
        },
        this.projectRoot
      );
      return updatedTask; // Return the task that was updated
    } catch (error) {
      console.error(`Error updating task ${taskId}:`, error);
      throw new Error(`Failed to update task ${taskId}: ${error.message}`);
    }
  }

  /**
   * Deletes a task by its ID.
   * @param {string} taskId - The ID of the task to delete.
   * @returns {Promise<boolean>} True if the task was deleted, false otherwise.
   */
  async deleteTask(taskId) {
    let deleted = false;
    try {
      await this.ensureTasksFileExists();
      await updateProjectFile(
        this.tasksFilePath,
        (tasks = []) => {
          if (!Array.isArray(tasks)) {
            console.warn(`Tasks file at ${this.tasksFilePath} was not an array. Reinitializing.`);
            tasks = [];
          }
          const initialLength = tasks.length;
          const filteredTasks = tasks.filter(task => task.id !== taskId);
          if (filteredTasks.length < initialLength) {
            deleted = true;
          }
          return filteredTasks;
        },
        this.projectRoot
      );
      return deleted;
    } catch (error) {
      console.error(`Error deleting task ${taskId}:`, error);
      throw new Error(`Failed to delete task ${taskId}: ${error.message}`);
    }
  }

  /**
   * Queries tasks based on a filter object.
   * Example: { status: 'pending', priority: 'high' }
   * @param {Object} filter - An object with key-value pairs to filter by.
   * @returns {Promise<Array>} An array of tasks matching the filter.
   */
  async queryTasks(filter = {}) {
    const tasks = await this.getAllTasks();
    if (Object.keys(filter).length === 0) {
      return tasks;
    }
    return tasks.filter(task => {
      return Object.entries(filter).every(([key, value]) => {
        if (key in task) {
          if (Array.isArray(task[key]) && Array.isArray(value)) {
            return value.every(v => task[key].includes(v));
          } else if (Array.isArray(task[key])) {
            return task[key].includes(value);
          }
          return task[key] === value;
        }
        return false;
      });
    });
  }
}

// Example Usage:
// const taskService = new TaskPersistenceService();
// (async () => {
//   try {
//     const newTask = await taskService.createTask({ title: 'Test Task 1', description: 'My first test task' });
//     console.log('Created Task:', newTask);

//     const task = await taskService.getTaskById(newTask.id);
//     console.log('Retrieved Task:', task);

//     const updated = await taskService.updateTask(newTask.id, { status: TASK_STATUSES.IN_PROGRESS, priority: 'high' });
//     console.log('Updated Task:', updated);

//     const pendingHighPriority = await taskService.queryTasks({ status: TASK_STATUSES.IN_PROGRESS, priority: 'high' });
//     console.log('Pending High Priority Tasks:', pendingHighPriority);

//     // const deleted = await taskService.deleteTask(newTask.id);
//     // console.log('Deleted Task:', deleted);

//     const allTasks = await taskService.getAllTasks();
//     console.log('All tasks:', allTasks);
//   } catch (e) {
//     console.error('Service Error:', e.message);
//   }
// })();