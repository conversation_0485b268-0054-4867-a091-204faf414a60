# Business Decision Translation System

This module implements the Business Decision Translation System for Guidant, which translates technical complexity into simple business decisions for non-technical users. It's a critical component for Guidant's vision of hiding technical complexity while enabling informed business choices.

## Core Components

- **Decision Translator**: Converts technical terms to business-friendly language
- **Option Presenter**: Formats and presents business decisions with clear options
- **Decision Templates**: Predefined templates for common technical decisions
- **Decision Tracker**: Records user decisions and their rationales
- **AI Translation Service**: Enhances translation capabilities with AI

## MCP Tools

The system exposes the following MCP tools for AI agent integration:

### `guidant_get_decision_options`

Gets business-friendly decision options for a technical context.

**Parameters:**
- `context`: The technical context/decision type (e.g., 'framework_selection', 'database_choice')
- `userExpertiseLevel`: (Optional) The user's technical expertise level ('novice', 'intermediate', 'advanced')
- `additionalContext`: (Optional) Additional context for the decision (project type, timeline, budget)
- `projectRoot`: (Optional) The root directory of the project

**Response:**
```json
{
  "decisionId": "framework_selection_123456",
  "title": "Choose a Frontend Framework",
  "description": "Select the best approach for building your user interface",
  "options": [
    {
      "id": "react",
      "title": "React",
      "description": "A modern approach for interactive websites",
      "businessImpact": "Great for complex, interactive applications",
      "timeImpact": "Medium setup time, faster development later",
      "costImpact": "Free to use, may require specialized developers",
      "riskLevel": "low"
    },
    {
      "id": "vue",
      "title": "Vue",
      "description": "A flexible approach that's easy to integrate",
      "businessImpact": "Good for gradually enhancing existing websites",
      "timeImpact": "Quick to get started, moderate learning curve",
      "costImpact": "Free to use, growing developer community",
      "riskLevel": "low"
    },
    {
      "id": "custom",
      "title": "Custom Choice",
      "description": "Your own preferred solution",
      "businessImpact": "Tailored to your specific requirements",
      "timeImpact": "Depends on your specific choice",
      "costImpact": "Varies based on implementation",
      "riskLevel": "medium",
      "isCustom": true
    }
  ],
  "recommendedOption": "react"
}
```

### `guidant_make_decision`

Records a user's decision with business rationale.

**Parameters:**
- `decisionId`: The ID of the decision being made
- `choiceId`: The ID of the chosen option
- `rationale`: The user's business rationale for the decision
- `customDetails`: (Optional) Additional details for custom choices
- `projectRoot`: (Optional) The root directory of the project

**Response:**
```json
{
  "recordedDecision": {
    "context": "framework_selection",
    "title": "Choose a Frontend Framework",
    "selectedOption": {
      "id": "react",
      "title": "React",
      "description": "A modern approach for interactive websites",
      "businessImpact": "Great for complex, interactive applications"
    },
    "rationale": "We need a solution that handles complex user interactions",
    "timestamp": "2025-01-28T12:34:56.789Z"
  },
  "nextSteps": [
    "Your choice of \"React\" has been recorded.",
    "Next, you'll need to choose a design approach."
  ]
}
```

### `guidant_create_custom_decision`

Creates a custom decision template for technologies not in the predefined list.

### `guidant_add_custom_term_mapping`

Adds a custom mapping from a technical term to a business-friendly term.

## Usage Examples

### Basic Decision Flow

1. Present options to the user:

```javascript
const decisionOptions = await getDecisionOptions({
  context: 'framework_selection',
  userExpertiseLevel: 'novice',
  projectRoot: '/path/to/project'
});

// Present options to the user through the AI agent
console.log(`Let's choose a frontend framework for your project.`);
console.log(`${decisionOptions.description}`);

decisionOptions.options.forEach(option => {
  console.log(`Option: ${option.title}`);
  console.log(`Description: ${option.description}`);
  console.log(`Business Impact: ${option.businessImpact}`);
  console.log(`Time Impact: ${option.timeImpact}`);
  console.log(`Cost Impact: ${option.costImpact}`);
  console.log(`Risk Level: ${option.riskLevel}`);
  console.log('---');
});

if (decisionOptions.recommendedOption) {
  const recommended = decisionOptions.options.find(
    opt => opt.id === decisionOptions.recommendedOption
  );
  console.log(`Recommendation: ${recommended.title}`);
}
```

2. Record the user's decision:

```javascript
const result = await recordDecision({
  decisionId: decisionOptions.decisionId,
  choiceId: 'react', // The user chose React
  rationale: 'We need a solution that handles complex user interactions',
  projectRoot: '/path/to/project'
});

// Confirm the decision to the user
console.log(`Your choice of ${result.recordedDecision.selectedOption.title} has been recorded.`);

// Provide next steps
result.nextSteps.forEach(step => {
  console.log(`- ${step}`);
});
```

### Custom Choices

For custom choices not in the predefined options:

```javascript
const result = await recordDecision({
  decisionId: decisionOptions.decisionId,
  choiceId: 'custom', // The user chose a custom option
  rationale: 'We need to use our company's internal framework',
  customDetails: {
    frameworkName: 'CompanyUI',
    version: '3.2.1',
    specialFeatures: ['Company design system integration', 'SSO support']
  },
  projectRoot: '/path/to/project'
});
```

## Extending the System

### Adding New Decision Templates

Add new templates to the `decision-templates.js` file following the existing pattern:

```javascript
export const DECISION_TEMPLATES = {
  // ... existing templates
  
  new_decision_type: {
    title: 'Decision Title',
    description: 'Description of the decision',
    options: [
      {
        id: 'option1',
        title: 'Option 1',
        description: 'Technical description of option 1',
        businessImpact: 'Business impact description',
        timeImpact: 'Time impact description',
        costImpact: 'Cost impact description',
        riskLevel: 'low'
      },
      // ... more options
    ],
    recommendedOption: 'option1'
  }
};
```

### Adding Technical Term Mappings

Add new term mappings to the `decision-translator.js` file:

```javascript
export const TECHNICAL_TERM_MAPPING = {
  // ... existing mappings
  
  'new technical term': {
    novice: 'simple business term',
    intermediate: 'slightly technical term',
    advanced: 'technical term with business context'
  }
};
```

## AI-Powered Features

The system includes AI capabilities for:

1. **Dynamic Term Translation**: Translates unknown technical terms to business language
2. **Business Impact Generation**: Creates business-focused impact assessments
3. **Custom Template Creation**: Generates templates for new decision types
4. **Adaptive Language**: Adjusts language complexity based on user expertise

## Decision Storage

Decisions are stored in the project's `.guidant/context/decisions.json` file, with separate sections for pending and completed decisions.
