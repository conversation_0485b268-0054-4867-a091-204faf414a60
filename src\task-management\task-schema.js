/**
 * Rich Task Schema for Guidant
 * Defines the structure for all task types within the system.
 * Based on specifications from TASK-003.
 */

export const TASK_STATUSES = {
  PENDING: 'pending',
  IN_PROGRESS: 'in-progress',
  DONE: 'done',
  BLOCKED: 'blocked',
  DEFERRED: 'deferred'
};

export const TASK_PRIORITIES = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low'
};

export const TASK_TYPES = {
  ENHANCEMENT: 'enhancement',
  FEATURE: 'feature',
  BUG: 'bug',
  RESEARCH: 'research',
  USER_TASK: 'user_task', // For tasks created by users
  AI_GENERATED: 'ai_generated' // For tasks generated by AI agents
};

export const TASK_COMPLEXITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high'
};

/**
 * Core fields required for all task types.
 */
export const CoreTaskFieldsSchema = {
  id: { type: 'string', required: true, description: 'Unique identifier for the task (e.g., UUID or sequential ID).' },
  title: { type: 'string', required: true, description: 'Clear, concise title for the task.' },
  status: { type: 'string', enum: Object.values(TASK_STATUSES), required: true, default: TASK_STATUSES.PENDING, description: 'Current status of the task.' },
  createdAt: { type: 'string', format: 'date-time', required: true, description: 'Timestamp of when the task was created.' },
  updatedAt: { type: 'string', format: 'date-time', required: true, description: 'Timestamp of when the task was last updated.' }
};

/**
 * Basic optional fields commonly used for user and AI tasks.
 */
export const BasicOptionalTaskFieldsSchema = {
  description: { type: 'string', description: 'One to two sentence description of the task.' },
  priority: { type: 'string', enum: Object.values(TASK_PRIORITIES), default: TASK_PRIORITIES.MEDIUM, description: 'Priority level of the task.' },
  estimatedHours: { type: 'number', minimum: 0, description: 'Estimated time in hours to complete the task.' }
};

/**
 * Relationship fields for task interconnections.
 */
export const RelationshipTaskFieldsSchema = {
  dependencies: { type: 'array', items: { type: 'string' }, default: [], description: 'Array of task IDs that must be completed before this task can start.' },
  subtasks: { 
    type: 'array', 
    items: { type: 'object' }, // Ideally, this would reference a SubTask schema
    default: [], 
    description: 'Array of subtasks for hierarchical task breakdown.' 
  },
  parentTask: { type: 'string', description: 'ID of the parent task, if this is a subtask.' }
};

/**
 * Context fields, often enhanced by AI or user input.
 */
export const ContextTaskFieldsSchema = {
  businessContext: { 
    type: 'object', 
    properties: {
      projectContext: { type: 'string' }, // General project context
      userPreferences: { type: 'object' }, // Relevant user preferences
      businessImpact: { type: 'string' }   // Potential business impact
    },
    description: 'Business-related context for the task.' 
  },
  complexityScore: { type: 'number', minimum: 1, maximum: 10, description: 'AI-calculated difficulty rating (1-10).' },
  assignedRole: { type: 'string', description: 'AI agent role or team assigned to the task.' }
};

/**
 * Implementation-specific fields for development tasks.
 */
export const ImplementationTaskFieldsSchema = {
  type: { type: 'string', enum: Object.values(TASK_TYPES), description: 'Type of the implementation task.' },
  phase: { type: 'string', description: 'Development phase this task belongs to (e.g., "task_management_revolution").' },
  complexity: { type: 'string', enum: Object.values(TASK_COMPLEXITY), description: 'Subjective complexity assessment (low, medium, high).' },
  technicalSpecs: { type: 'object', description: 'Detailed technical specifications for implementation.' },
  acceptanceCriteria: { type: 'array', items: { type: 'string' }, default: [], description: 'List of criteria to validate task completion.' },
  testStrategy: { type: 'string', description: 'Approach for testing and verifying the task.' }
};

/**
 * Fields for tasks created through natural language interaction.
 */
export const ConversationTaskFieldsSchema = {
  conversationContext: { 
    type: 'object',
    properties: {
      originalUserInput: { type: 'string' },
      creationMethod: { type: 'string', enum: ['user_command', 'ai_suggestion'] }
    },
    description: 'Context of how the task was created through conversation.' 
  },
  userNotes: { type: 'string', description: 'Free-form user annotations or notes related to the task.' }
};

/**
 * Combined Rich Task Schema
 * This schema represents the full structure of a task object.
 * Tasks can evolve, so not all fields are required initially.
 * Validation should be progressive based on the fields present.
 */
export const RichTaskSchema = {
  type: 'object',
  properties: {
    ...CoreTaskFieldsSchema,
    ...BasicOptionalTaskFieldsSchema,
    ...RelationshipTaskFieldsSchema,
    ...ContextTaskFieldsSchema,
    ...ImplementationTaskFieldsSchema,
    ...ConversationTaskFieldsSchema
  },
  required: ['id', 'title', 'status', 'createdAt', 'updatedAt']
};

/**
 * Represents a subtask, which is a simplified task structure.
 * For now, we'll keep it minimal. It could be expanded to a full RichTaskSchema.
 */
export const SubTaskSchema = {
  type: 'object',
  properties: {
    id: { type: 'string', required: true },
    title: { type: 'string', required: true },
    status: { type: 'string', enum: Object.values(TASK_STATUSES), required: true, default: TASK_STATUSES.PENDING },
    completed: { type: 'boolean', default: false }
  },
  required: ['id', 'title', 'status']
};

// Example of how to use:
// const myTask = {
//   id: 'task_123',
//   title: 'Implement User Login',
//   status: TASK_STATUSES.PENDING,
//   createdAt: new Date().toISOString(),
//   updatedAt: new Date().toISOString(),
//   description: 'Allow users to log in using email and password.',
//   priority: TASK_PRIORITIES.HIGH,
//   dependencies: ['task_120', 'task_121'],
//   type: TASK_TYPES.FEATURE,
//   acceptanceCriteria: [
//     'User can enter email and password.',
//     'Successful login redirects to dashboard.',
//     'Error message shown for invalid credentials.'
//   ]
// };