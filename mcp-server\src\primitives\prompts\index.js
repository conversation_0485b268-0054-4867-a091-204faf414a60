/**
 * MCP Prompts Registry
 * Central registry for all MCP Prompt templates and capabilities
 */

import { AnalysisPromptHandler } from './analysis-prompts.js';
import { WorkflowPromptHandler } from './workflow-prompts.js';
import { OnboardingPromptHandler } from './onboarding-prompts.js';
import { DecisionPromptHandler } from './decision-prompts.js';
import { MCPMessageHandler } from '../../utils/mcp-message-handler.js';

/**
 * MCP Prompt Registry
 * Manages all prompt handlers and provides unified prompt access
 */
export class MCPPromptRegistry {
  constructor() {
    this.handlers = new Map();
    this.messageHandler = new MCPMessageHandler();
    this.initialized = false;
  }

  /**
   * Initialize the prompt registry
   * @param {object} existingInfrastructure - Existing infrastructure
   */
  async initialize(existingInfrastructure = {}) {
    if (this.initialized) return;

    // Initialize prompt handlers
    const analysisHandler = new AnalysisPromptHandler(existingInfrastructure);
    const workflowHandler = new WorkflowPromptHandler(existingInfrastructure);
    const onboardingHandler = new OnboardingPromptHandler(existingInfrastructure);
    const decisionHandler = new DecisionPromptHandler(existingInfrastructure);

    // Register handlers by category
    this.handlers.set('analysis', analysisHandler);
    this.handlers.set('workflow', workflowHandler);
    this.handlers.set('onboarding', onboardingHandler);
    this.handlers.set('decision', decisionHandler);

    this.initialized = true;
    console.log(`📝 MCP Prompt Registry initialized with ${this.handlers.size} handlers`);
  }

  /**
   * Register MCP prompt capabilities with the server
   * @param {object} server - FastMCP server instance
   */
  registerCapabilities(server) {
    // FastMCP handles capabilities automatically when prompts are added
    // Register each prompt template with the server using FastMCP API

    try {
      const templates = this.getAllPromptTemplates();

      // Register each prompt template with the server
      for (const template of templates) {
        if (server.addPrompt) {
          server.addPrompt(template);
        }
      }

      console.log(`📝 MCP Prompt capabilities registered (${templates.length} prompts)`);
    } catch (error) {
      console.warn('⚠️ Prompt registration skipped - FastMCP API not available:', error.message);
      console.log('📝 MCP Prompt registry initialized (capabilities will be handled by router)');
    }
  }

  /**
   * Get all available prompt templates
   * @returns {Array} Array of all prompt templates
   */
  getAllPromptTemplates() {
    const templates = [];
    
    for (const handler of this.handlers.values()) {
      if (handler.getPromptTemplates) {
        templates.push(...handler.getPromptTemplates());
      }
    }

    return templates;
  }

  /**
   * Handle prompt get request
   * @param {string} name - Prompt name
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt response
   */
  async handlePromptGet(name, args = {}) {
    try {
      const handler = this.getHandlerForPrompt(name);
      
      if (!handler) {
        throw new Error(`No handler found for prompt: ${name}`);
      }

      const prompt = await handler.handlePromptGet(name, args);
      
      return {
        description: prompt.description,
        messages: prompt.messages
      };
    } catch (error) {
      console.error(`Prompt get error for ${name}:`, error);
      
      // Return error as prompt response (MCP pattern)
      return {
        description: `Error generating prompt: ${error.message}`,
        messages: [{
          role: "user",
          content: {
            type: "text",
            text: `Error: Unable to generate prompt '${name}'. ${error.message}`
          }
        }]
      };
    }
  }

  /**
   * Get appropriate handler for a prompt
   * @param {string} name - Prompt name
   * @returns {object|null} Handler instance or null
   */
  getHandlerForPrompt(name) {
    // Extract category from prompt name (e.g., "analyze-project-phase" -> "analysis")
    const category = this.extractCategoryFromPromptName(name);
    return this.handlers.get(category) || null;
  }

  /**
   * Extract category from prompt name
   * @param {string} name - Prompt name
   * @returns {string} Category name
   */
  extractCategoryFromPromptName(name) {
    if (name.startsWith('analyze-') || name.includes('-analysis')) return 'analysis';
    if (name.startsWith('workflow-') || name.includes('-workflow')) return 'workflow';
    if (name.startsWith('onboard-') || name.includes('-onboarding')) return 'onboarding';
    if (name.startsWith('decision-') || name.includes('-decision')) return 'decision';
    return 'general';
  }

  /**
   * Add a new prompt handler
   * @param {string} category - Prompt category
   * @param {object} handler - Handler instance
   */
  addHandler(category, handler) {
    this.handlers.set(category, handler);
    console.log(`📝 Added prompt handler for category: ${category}`);
  }

  /**
   * Remove a prompt handler
   * @param {string} category - Prompt category
   */
  removeHandler(category) {
    const removed = this.handlers.delete(category);
    if (removed) {
      console.log(`📝 Removed prompt handler for category: ${category}`);
    }
    return removed;
  }

  /**
   * Get prompt statistics
   * @returns {object} Prompt registry statistics
   */
  getStatistics() {
    const stats = {
      handlers: this.handlers.size,
      templates: this.getAllPromptTemplates().length,
      categories: Array.from(this.handlers.keys())
    };

    return stats;
  }

  /**
   * Validate prompt template
   * @param {string} name - Prompt name
   * @param {object} args - Prompt arguments
   * @returns {object} Validation result
   */
  validatePrompt(name, args) {
    const handler = this.getHandlerForPrompt(name);
    
    if (!handler) {
      return {
        valid: false,
        error: `No handler for prompt: ${name}`
      };
    }

    if (handler.validatePrompt) {
      return handler.validatePrompt(name, args);
    }

    return {
      valid: true,
      message: 'Basic validation passed'
    };
  }
}

/**
 * Register MCP Prompt capabilities with FastMCP server
 * @param {object} server - FastMCP server instance
 * @param {object} existingInfrastructure - Existing infrastructure
 * @returns {MCPPromptRegistry} Initialized prompt registry
 */
export async function registerPromptCapabilities(server, existingInfrastructure = {}) {
  const registry = new MCPPromptRegistry();
  
  // Initialize with existing infrastructure
  await registry.initialize(existingInfrastructure);
  
  // Register capabilities with server
  registry.registerCapabilities(server);
  
  return registry;
}

/**
 * Create mock prompt infrastructure for testing
 * @returns {object} Mock infrastructure
 */
export function createMockPromptInfrastructure() {
  return {
    projectManager: {
      async getProjectContext(projectId) {
        return {
          id: projectId,
          name: 'Mock Project',
          phase: 'development',
          progress: 65,
          team_size: 5,
          methodology: 'agile'
        };
      }
    },
    analyticsManager: {
      async getAnalysisContext(target) {
        return {
          target,
          metrics: ['quality', 'performance', 'productivity'],
          timeframe: '30d',
          baseline: 'industry_standard'
        };
      }
    },
    workflowManager: {
      async getWorkflowContext(workflowId) {
        return {
          id: workflowId,
          type: 'development',
          steps: ['plan', 'implement', 'test', 'deploy'],
          current_step: 'implement'
        };
      }
    },
    userManager: {
      async getUserContext(userId) {
        return {
          id: userId,
          experience_level: 'intermediate',
          preferences: { learning_style: 'hands-on' },
          role: 'developer'
        };
      }
    }
  };
}

export default MCPPromptRegistry;
