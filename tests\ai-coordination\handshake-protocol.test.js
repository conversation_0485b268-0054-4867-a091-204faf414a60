import { test, expect, describe, beforeEach, afterEach, spyOn } from 'bun:test';
import { HandshakeProtocol, performHandshakeWithCurrentAgent, generateGapReport } from '../../src/ai-coordination/capability-discovery.js';
import { AgentDiscovery } from '../../src/agent-registry/agent-discovery.js';
import { GapAnalyzer } from '../../src/agent-registry/gap-analysis.js';
import { ToolConfigurator } from '../../src/agent-registry/tool-configurator.js';

describe('HandshakeProtocol - WLR-002 Tests', () => {
  let handshakeProtocol;
  let mockAgentCard;
  let mockGapAnalysis;
  let mockConfigRecommendations;

  beforeEach(() => {
    handshakeProtocol = new HandshakeProtocol();
    
    // Mock agent card
    mockAgentCard = {
      id: 'test_agent_123',
      status: 'discovered',
      tools: ['read_file', 'edit_file', 'web_search', 'unknown_tool'],
      normalizedTools: ['read_file', 'edit_file', 'web_search'],
      identity: {
        name: 'Test Agent',
        type: 'ai_assistant'
      },
      roleAnalysis: {
        research: { canFulfill: true, confidence: 0.7 },
        development: { canFulfill: false, confidence: 0.4 }
      }
    };
    
    // Mock gap analysis
    mockGapAnalysis = {
      agentId: 'test_agent_123',
      totalGapScore: 0.6,
      roleGaps: {
        research: {
          canCurrentlyFulfill: true,
          currentConfidence: 0.7,
          missingEssentialCategories: [],
          missingImportantCategories: ['ai_integration']
        },
        development: {
          canCurrentlyFulfill: false,
          currentConfidence: 0.4,
          missingEssentialCategories: ['version_control'],
          missingImportantCategories: ['debugging']
        },
        design: {
          canCurrentlyFulfill: false,
          currentConfidence: 0.3,
          missingEssentialCategories: ['visualization'],
          missingImportantCategories: []
        }
      },
      prioritizedRecommendations: [
        { name: 'git', category: 'version_control', priority: 'essential', confidenceBoost: 0.5 },
        { name: 'mermaid', category: 'visualization', priority: 'essential', confidenceBoost: 0.5 }
      ]
    };
    
    // Mock config recommendations
    mockConfigRecommendations = {
      agentId: 'test_agent_123',
      gapScore: 0.6,
      recommendations: [
        { 
          tool: 'git', 
          priority: 'essential',
          reasoning: 'Improves development capabilities',
          alternatives: ['github', 'gitlab']
        },
        { 
          tool: 'mermaid', 
          priority: 'essential',
          reasoning: 'Improves design capabilities',
          alternatives: ['plantuml', 'draw_io']
        }
      ],
      implementationPlan: {
        steps: [
          { step: 1, action: 'Install git', difficulty: 'medium' },
          { step: 2, action: 'Configure mermaid', difficulty: 'easy' }
        ]
      }
    };
    
    // Spy on the component classes
    spyOn(handshakeProtocol.agentDiscovery, 'discoverAgent').mockResolvedValue(mockAgentCard);
    spyOn(handshakeProtocol.gapAnalyzer, 'analyzeCapabilityGaps').mockResolvedValue(mockGapAnalysis);
    spyOn(handshakeProtocol.toolConfigurator, 'suggestOptimalConfiguration').mockResolvedValue(mockConfigRecommendations);
  });

  describe('Handshake Protocol Integration', () => {
    test('should perform complete handshake process successfully', async () => {
      const result = await handshakeProtocol.performHandshake({ type: 'direct' }, 'test_agent_123');
      
      expect(result.success).toBe(true);
      expect(result.agentCard).toBe(mockAgentCard);
      expect(result.gapAnalysis).toBe(mockGapAnalysis);
      expect(result.configRecommendations).toBe(mockConfigRecommendations);
      
      // Verify all components were called with correct parameters
      expect(handshakeProtocol.agentDiscovery.discoverAgent).toHaveBeenCalledWith({ type: 'direct' }, 'test_agent_123');
      expect(handshakeProtocol.gapAnalyzer.analyzeCapabilityGaps).toHaveBeenCalledWith(mockAgentCard, []);
      expect(handshakeProtocol.toolConfigurator.suggestOptimalConfiguration).toHaveBeenCalledWith(mockAgentCard, null);
    });
    
    test('should include project context in analysis when provided', async () => {
      const projectContext = { name: 'test_project', phases: ['design', 'development'] };
      
      await handshakeProtocol.performHandshake({ type: 'direct' }, 'test_agent_123', projectContext);
      
      expect(handshakeProtocol.gapAnalyzer.analyzeCapabilityGaps).toHaveBeenCalledWith(mockAgentCard, [projectContext]);
      expect(handshakeProtocol.toolConfigurator.suggestOptimalConfiguration).toHaveBeenCalledWith(mockAgentCard, projectContext);
    });
    
    test('should handle agent discovery failure', async () => {
      const failedAgentCard = {
        id: 'test_agent_123',
        status: 'discovery_failed',
        error: 'Connection error'
      };
      
      handshakeProtocol.agentDiscovery.discoverAgent.mockResolvedValue(failedAgentCard);
      
      const result = await handshakeProtocol.performHandshake({ type: 'direct' }, 'test_agent_123');
      
      expect(result.success).toBe(false);
      expect(result.stage).toBe('discovery');
      expect(result.error).toContain('Connection error');
      expect(result.agentCard).toBe(failedAgentCard);
      
      // Verify that gap analysis and tool configuration were not called
      expect(handshakeProtocol.gapAnalyzer.analyzeCapabilityGaps).not.toHaveBeenCalled();
      expect(handshakeProtocol.toolConfigurator.suggestOptimalConfiguration).not.toHaveBeenCalled();
    });
  });

  describe('Handshake Summary Generation', () => {
    test('should generate comprehensive summary from handshake results', () => {
      const summary = handshakeProtocol.generateHandshakeSummary(mockAgentCard, mockGapAnalysis, mockConfigRecommendations);
      
      expect(summary.agentName).toBe('Test Agent');
      expect(summary.discoveredTools).toBe(4);
      expect(summary.normalizedTools).toBe(3);
      expect(summary.unknownTools).toBe(1);
      expect(summary.gapScore).toBe(0.6);
      
      // Verify capable roles identification
      expect(summary.capableRoles.length).toBe(1);
      expect(summary.capableRoles[0].role).toBe('research');
      expect(summary.capableRoles[0].confidence).toBe(0.7);
      
      // Verify critical gaps identification
      expect(summary.criticalGaps.length).toBe(2);
      expect(summary.criticalGaps[0].role).toBe('development');
      expect(summary.criticalGaps[0].missingCategories).toContain('version_control');
      expect(summary.criticalGaps[1].role).toBe('design');
      expect(summary.criticalGaps[1].missingCategories).toContain('visualization');
      
      // Verify top recommendations
      expect(summary.topRecommendations.length).toBe(2);
      expect(summary.topRecommendations[0].tool).toBe('git');
      expect(summary.topRecommendations[1].tool).toBe('mermaid');
    });
  });

  describe('Gap Report Generation', () => {
    test('should generate user-friendly gap report', () => {
      const handshakeResult = {
        success: true,
        agentCard: mockAgentCard,
        gapAnalysis: mockGapAnalysis,
        configRecommendations: mockConfigRecommendations,
        summary: handshakeProtocol.generateHandshakeSummary(mockAgentCard, mockGapAnalysis, mockConfigRecommendations)
      };
      
      const report = generateGapReport(handshakeResult);
      
      expect(report.title).toContain('Test Agent');
      expect(report.overview.discoveredCapabilities).toBe(4);
      expect(report.overview.gapScore).toBe(0.6);
      expect(report.overview.capableRoles).toContain('research');
      expect(report.overview.criticalGaps).toBe(2);
      
      // Verify role analysis
      expect(report.roleAnalysis.length).toBe(3);
      const developmentAnalysis = report.roleAnalysis.find(r => r.role === 'development');
      expect(developmentAnalysis.canFulfill).toBe(false);
      expect(developmentAnalysis.missingEssentials).toContain('version_control');
      
      // Verify recommendations
      expect(report.recommendations.length).toBe(2);
      expect(report.recommendations[0].tool).toBe('git');
      expect(report.recommendations[0].alternatives).toContain('github');
      
      // Verify implementation plan
      expect(report.implementationPlan.steps.length).toBe(2);
    });
    
    test('should handle failed handshake in report generation', () => {
      const failedHandshakeResult = {
        success: false,
        stage: 'discovery',
        error: 'Agent connection failed'
      };
      
      const report = generateGapReport(failedHandshakeResult);
      
      expect(report.title).toContain('Failed');
      expect(report.error).toBe('Agent connection failed');
      expect(report.recommendations).toBeDefined();
      expect(report.recommendations.length).toBeGreaterThan(0);
    });
  });
}); 