/**
 * Autonomous Research Orchestrator
 * Implements background research processing during conversation continuation,
 * intelligent caching, progressive research refinement, and research routing
 */

import { EventEmitter } from 'events';
import { ResearchServicesManager } from '../research-integration/research-services-unified.js';

/**
 * Research queue manager for background processing
 */
export class ResearchQueueManager {
  constructor(config = {}) {
    this.config = config;
    this.queue = [];
    this.processing = false;
    this.maxConcurrent = config.maxConcurrent || 3;
    this.activeJobs = new Map();
    this.completedJobs = new Map();
    this.failedJobs = new Map();
  }

  /**
   * Add research request to queue
   * @param {object} researchRequest - Research request
   * @returns {string} Job ID
   */
  enqueue(researchRequest) {
    const jobId = this.generateJobId();
    const job = {
      id: jobId,
      request: researchRequest,
      priority: researchRequest.priority || 'medium',
      enqueuedAt: new Date().toISOString(),
      status: 'queued',
      retries: 0,
      maxRetries: 3
    };

    // Insert based on priority
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    const insertIndex = this.queue.findIndex(queuedJob => 
      priorityOrder[queuedJob.priority] > priorityOrder[job.priority]
    );

    if (insertIndex === -1) {
      this.queue.push(job);
    } else {
      this.queue.splice(insertIndex, 0, job);
    }

    return jobId;
  }

  /**
   * Get next job from queue
   * @returns {object|null} Next job or null if queue empty
   */
  dequeue() {
    return this.queue.shift() || null;
  }

  /**
   * Get job status
   * @param {string} jobId - Job ID
   * @returns {object|null} Job status
   */
  getJobStatus(jobId) {
    // Check active jobs
    if (this.activeJobs.has(jobId)) {
      return { status: 'processing', ...this.activeJobs.get(jobId) };
    }

    // Check completed jobs
    if (this.completedJobs.has(jobId)) {
      return { status: 'completed', ...this.completedJobs.get(jobId) };
    }

    // Check failed jobs
    if (this.failedJobs.has(jobId)) {
      return { status: 'failed', ...this.failedJobs.get(jobId) };
    }

    // Check queued jobs
    const queuedJob = this.queue.find(job => job.id === jobId);
    if (queuedJob) {
      return { status: 'queued', ...queuedJob };
    }

    return null;
  }

  /**
   * Mark job as processing
   * @param {string} jobId - Job ID
   * @param {object} job - Job details
   */
  markProcessing(jobId, job) {
    this.activeJobs.set(jobId, {
      ...job,
      status: 'processing',
      startedAt: new Date().toISOString()
    });
  }

  /**
   * Mark job as completed
   * @param {string} jobId - Job ID
   * @param {object} result - Job result
   */
  markCompleted(jobId, result) {
    const job = this.activeJobs.get(jobId);
    this.activeJobs.delete(jobId);
    this.completedJobs.set(jobId, {
      ...job,
      status: 'completed',
      completedAt: new Date().toISOString(),
      result
    });
  }

  /**
   * Mark job as failed
   * @param {string} jobId - Job ID
   * @param {Error} error - Error details
   */
  markFailed(jobId, error) {
    const job = this.activeJobs.get(jobId);
    this.activeJobs.delete(jobId);
    this.failedJobs.set(jobId, {
      ...job,
      status: 'failed',
      failedAt: new Date().toISOString(),
      error: error.message
    });
  }

  /**
   * Get queue statistics
   * @returns {object} Queue statistics
   */
  getStats() {
    return {
      queued: this.queue.length,
      processing: this.activeJobs.size,
      completed: this.completedJobs.size,
      failed: this.failedJobs.size,
      total: this.queue.length + this.activeJobs.size + this.completedJobs.size + this.failedJobs.size
    };
  }

  generateJobId() {
    return `research_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

/**
 * Intelligent research cache with progressive refinement
 */
export class IntelligentResearchCache {
  constructor(config = {}) {
    this.config = config;
    this.cache = new Map();
    this.ttl = config.ttl || 3600000; // 1 hour default
    this.maxSize = config.maxSize || 1000;
    this.refinementThreshold = config.refinementThreshold || 0.7;
  }

  /**
   * Get cached research result
   * @param {string} cacheKey - Cache key
   * @returns {object|null} Cached result or null
   */
  get(cacheKey) {
    const entry = this.cache.get(cacheKey);
    if (!entry) return null;

    // Check TTL
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(cacheKey);
      return null;
    }

    // Update access time and count
    entry.lastAccessed = Date.now();
    entry.accessCount = (entry.accessCount || 0) + 1;

    return entry.data;
  }

  /**
   * Set cached research result
   * @param {string} cacheKey - Cache key
   * @param {object} data - Research data
   * @param {object} metadata - Cache metadata
   */
  set(cacheKey, data, metadata = {}) {
    // Enforce cache size limit
    if (this.cache.size >= this.maxSize) {
      this.evictOldest();
    }

    this.cache.set(cacheKey, {
      data,
      metadata,
      timestamp: Date.now(),
      lastAccessed: Date.now(),
      accessCount: 1
    });
  }

  /**
   * Check if research needs refinement
   * @param {string} cacheKey - Cache key
   * @param {object} newContext - New research context
   * @returns {boolean} Whether refinement is needed
   */
  needsRefinement(cacheKey, newContext) {
    const entry = this.cache.get(cacheKey);
    if (!entry) return true;

    // Check if context has significantly changed
    const contextSimilarity = this.calculateContextSimilarity(
      entry.metadata.context || {},
      newContext
    );

    return contextSimilarity < this.refinementThreshold;
  }

  /**
   * Calculate similarity between research contexts
   * @param {object} oldContext - Previous context
   * @param {object} newContext - New context
   * @returns {number} Similarity score (0-1)
   */
  calculateContextSimilarity(oldContext, newContext) {
    // Simplified similarity calculation
    const oldKeys = Object.keys(oldContext);
    const newKeys = Object.keys(newContext);
    const allKeys = new Set([...oldKeys, ...newKeys]);

    if (allKeys.size === 0) return 1.0;

    let matches = 0;
    for (const key of allKeys) {
      if (oldContext[key] === newContext[key]) {
        matches++;
      }
    }

    return matches / allKeys.size;
  }

  /**
   * Evict oldest cache entry
   */
  evictOldest() {
    let oldestKey = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Generate cache key for research request
   * @param {object} researchRequest - Research request
   * @returns {string} Cache key
   */
  generateCacheKey(researchRequest) {
    const keyData = {
      type: researchRequest.type,
      query: researchRequest.query,
      domain: researchRequest.context?.domain || 'general'
    };
    return `research_${JSON.stringify(keyData)}`;
  }

  /**
   * Clear expired cache entries
   */
  clearExpired() {
    const now = Date.now();
    for (const [key, entry] of this.cache) {
      if (now - entry.timestamp > this.ttl) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get cache statistics
   * @returns {object} Cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.calculateHitRate(),
      averageAge: this.calculateAverageAge()
    };
  }

  calculateHitRate() {
    // Simplified hit rate calculation
    let totalAccess = 0;
    for (const entry of this.cache.values()) {
      totalAccess += entry.accessCount || 0;
    }
    return totalAccess > 0 ? this.cache.size / totalAccess : 0;
  }

  calculateAverageAge() {
    if (this.cache.size === 0) return 0;
    
    const now = Date.now();
    let totalAge = 0;
    for (const entry of this.cache.values()) {
      totalAge += now - entry.timestamp;
    }
    return totalAge / this.cache.size;
  }
}

/**
 * Research routing engine for intelligent provider selection
 */
export class ResearchRoutingEngine {
  constructor(config = {}) {
    this.config = config;
    this.routingRules = this.initializeRoutingRules();
    this.providerPerformance = new Map();
  }

  /**
   * Route research request to optimal providers
   * @param {object} researchRequest - Research request
   * @param {object} context - Research context
   * @returns {Array} Routing plan
   */
  routeResearch(researchRequest, context = {}) {
    const baseRouting = this.routingRules.get(researchRequest.type) || ['tavily'];
    
    // Apply context-based routing optimizations
    const optimizedRouting = this.optimizeRouting(baseRouting, researchRequest, context);
    
    // Apply performance-based adjustments
    const performanceAdjusted = this.adjustForPerformance(optimizedRouting);
    
    return performanceAdjusted.map(provider => ({
      provider,
      priority: this.calculateProviderPriority(provider, researchRequest),
      estimatedTime: this.estimateProviderTime(provider, researchRequest)
    }));
  }

  /**
   * Initialize routing rules
   * @returns {Map} Routing rules map
   */
  initializeRoutingRules() {
    return new Map([
      ['market_research', ['tavily']],
      ['competitive_analysis', ['tavily', 'firecrawl']],
      ['technical_documentation', ['context7']],
      ['library_research', ['context7']],
      ['web_content_extraction', ['firecrawl']],
      ['news_monitoring', ['tavily']],
      ['fact_verification', ['tavily']],
      ['api_documentation', ['context7']],
      ['website_analysis', ['firecrawl']],
      ['general_search', ['tavily']]
    ]);
  }

  /**
   * Optimize routing based on context
   * @param {Array} baseRouting - Base provider routing
   * @param {object} researchRequest - Research request
   * @param {object} context - Research context
   * @returns {Array} Optimized routing
   */
  optimizeRouting(baseRouting, researchRequest, context) {
    const optimized = [...baseRouting];

    // Add Firecrawl for specific domains that benefit from deep content extraction
    if (context.domain && ['ecommerce', 'news', 'content'].includes(context.domain)) {
      if (!optimized.includes('firecrawl')) {
        optimized.push('firecrawl');
      }
    }

    // Add Context7 for technical queries
    if (researchRequest.query && this.isTechnicalQuery(researchRequest.query)) {
      if (!optimized.includes('context7')) {
        optimized.push('context7');
      }
    }

    return optimized;
  }

  /**
   * Adjust routing based on provider performance
   * @param {Array} routing - Current routing
   * @returns {Array} Performance-adjusted routing
   */
  adjustForPerformance(routing) {
    return routing.filter(provider => {
      const performance = this.providerPerformance.get(provider);
      if (!performance) return true; // Include if no performance data
      
      // Exclude providers with poor performance
      return performance.successRate > 0.5 && performance.averageResponseTime < 30000;
    });
  }

  /**
   * Calculate provider priority
   * @param {string} provider - Provider name
   * @param {object} researchRequest - Research request
   * @returns {number} Priority score
   */
  calculateProviderPriority(provider, researchRequest) {
    let priority = 1.0;

    // Provider-specific bonuses
    if (provider === 'tavily' && researchRequest.type.includes('market')) {
      priority += 0.3;
    }
    if (provider === 'context7' && researchRequest.type.includes('technical')) {
      priority += 0.3;
    }
    if (provider === 'firecrawl' && researchRequest.type.includes('content')) {
      priority += 0.3;
    }

    // Performance-based adjustments
    const performance = this.providerPerformance.get(provider);
    if (performance) {
      priority *= performance.successRate;
    }

    return priority;
  }

  /**
   * Estimate provider response time
   * @param {string} provider - Provider name
   * @param {object} researchRequest - Research request
   * @returns {number} Estimated time in milliseconds
   */
  estimateProviderTime(provider, researchRequest) {
    const baseTimes = {
      tavily: 5000,
      context7: 8000,
      firecrawl: 15000
    };

    let estimatedTime = baseTimes[provider] || 10000;

    // Adjust based on query complexity
    if (researchRequest.query && researchRequest.query.length > 100) {
      estimatedTime *= 1.5;
    }

    // Adjust based on historical performance
    const performance = this.providerPerformance.get(provider);
    if (performance) {
      estimatedTime = performance.averageResponseTime;
    }

    return estimatedTime;
  }

  /**
   * Update provider performance metrics
   * @param {string} provider - Provider name
   * @param {boolean} success - Whether request was successful
   * @param {number} responseTime - Response time in milliseconds
   */
  updateProviderPerformance(provider, success, responseTime) {
    const current = this.providerPerformance.get(provider) || {
      totalRequests: 0,
      successfulRequests: 0,
      totalResponseTime: 0,
      successRate: 1.0,
      averageResponseTime: 10000
    };

    current.totalRequests++;
    if (success) {
      current.successfulRequests++;
    }
    current.totalResponseTime += responseTime;

    current.successRate = current.successfulRequests / current.totalRequests;
    current.averageResponseTime = current.totalResponseTime / current.totalRequests;

    this.providerPerformance.set(provider, current);
  }

  /**
   * Check if query is technical in nature
   * @param {string} query - Research query
   * @returns {boolean} Whether query is technical
   */
  isTechnicalQuery(query) {
    const technicalKeywords = [
      'api', 'framework', 'library', 'documentation', 'code', 'programming',
      'development', 'architecture', 'database', 'integration', 'deployment'
    ];

    const queryLower = query.toLowerCase();
    return technicalKeywords.some(keyword => queryLower.includes(keyword));
  }
}

/**
 * Autonomous Research Orchestrator
 * Main orchestrator for background research processing
 */
export class AutonomousResearchOrchestrator extends EventEmitter {
  constructor(config = {}) {
    super();
    this.config = config;
    this.researchManager = new ResearchServicesManager(config.research || {});
    this.queueManager = new ResearchQueueManager(config.queue || {});
    this.cache = new IntelligentResearchCache(config.cache || {});
    this.routingEngine = new ResearchRoutingEngine(config.routing || {});
    
    this.isProcessing = false;
    this.processingInterval = null;
    this.stats = {
      totalRequests: 0,
      completedRequests: 0,
      failedRequests: 0,
      cacheHits: 0
    };
  }

  /**
   * Start autonomous research processing
   */
  start() {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, 1000); // Process every second

    this.emit('started');
  }

  /**
   * Stop autonomous research processing
   */
  stop() {
    if (!this.isProcessing) return;
    
    this.isProcessing = false;
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }

    this.emit('stopped');
  }

  /**
   * Submit research request for autonomous processing
   * @param {object} researchRequest - Research request
   * @returns {string} Job ID
   */
  submitResearch(researchRequest) {
    this.stats.totalRequests++;
    
    // Check cache first
    const cacheKey = this.cache.generateCacheKey(researchRequest);
    const cachedResult = this.cache.get(cacheKey);
    
    if (cachedResult && !this.cache.needsRefinement(cacheKey, researchRequest.context)) {
      this.stats.cacheHits++;
      this.emit('researchCompleted', {
        jobId: `cached_${Date.now()}`,
        result: cachedResult,
        fromCache: true
      });
      return `cached_${Date.now()}`;
    }

    // Add to queue for processing
    const jobId = this.queueManager.enqueue(researchRequest);
    this.emit('researchQueued', { jobId, request: researchRequest });
    
    return jobId;
  }

  /**
   * Process research queue
   */
  async processQueue() {
    if (!this.isProcessing) return;

    const activeJobs = this.queueManager.activeJobs.size;
    const maxConcurrent = this.queueManager.maxConcurrent;

    if (activeJobs >= maxConcurrent) return;

    const job = this.queueManager.dequeue();
    if (!job) return;

    try {
      this.queueManager.markProcessing(job.id, job);
      this.emit('researchStarted', { jobId: job.id, request: job.request });

      const result = await this.executeResearch(job.request);
      
      this.queueManager.markCompleted(job.id, result);
      this.stats.completedRequests++;

      // Cache the result
      const cacheKey = this.cache.generateCacheKey(job.request);
      this.cache.set(cacheKey, result, { context: job.request.context });

      this.emit('researchCompleted', { jobId: job.id, result });
    } catch (error) {
      this.queueManager.markFailed(job.id, error);
      this.stats.failedRequests++;
      this.emit('researchFailed', { jobId: job.id, error });

      // Retry logic
      if (job.retries < job.maxRetries) {
        job.retries++;
        job.status = 'queued';
        this.queueManager.enqueue(job);
      }
    }
  }

  /**
   * Execute research request
   * @param {object} researchRequest - Research request
   * @returns {Promise<object>} Research result
   */
  async executeResearch(researchRequest) {
    const startTime = Date.now();
    
    try {
      // Route research to optimal providers
      const routing = this.routingEngine.routeResearch(researchRequest, researchRequest.context);
      
      // Execute research
      const result = await this.researchManager.conductResearch(researchRequest);
      
      // Update provider performance metrics
      const responseTime = Date.now() - startTime;
      routing.forEach(route => {
        this.routingEngine.updateProviderPerformance(route.provider, true, responseTime);
      });

      return result;
    } catch (error) {
      // Update provider performance for failures
      const responseTime = Date.now() - startTime;
      const routing = this.routingEngine.routeResearch(researchRequest, researchRequest.context);
      routing.forEach(route => {
        this.routingEngine.updateProviderPerformance(route.provider, false, responseTime);
      });

      throw error;
    }
  }

  /**
   * Get research job status
   * @param {string} jobId - Job ID
   * @returns {object|null} Job status
   */
  getJobStatus(jobId) {
    return this.queueManager.getJobStatus(jobId);
  }

  /**
   * Get orchestrator statistics
   * @returns {object} Statistics
   */
  getStats() {
    return {
      ...this.stats,
      queue: this.queueManager.getStats(),
      cache: this.cache.getStats(),
      successRate: this.stats.totalRequests > 0 
        ? (this.stats.completedRequests / this.stats.totalRequests) * 100 
        : 0,
      cacheHitRate: this.stats.totalRequests > 0
        ? (this.stats.cacheHits / this.stats.totalRequests) * 100
        : 0
    };
  }

  /**
   * Clear expired cache entries
   */
  clearExpiredCache() {
    this.cache.clearExpired();
  }
}

export default AutonomousResearchOrchestrator;
