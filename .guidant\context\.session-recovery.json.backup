{"version": "1.0", "activeSessions": [{"sessionId": "test-session-1749936135485", "startTime": "2025-06-14T21:22:15.486Z", "lastActive": "2025-06-14T21:22:15.551Z", "agentId": "", "projectName": "Test Project"}, {"sessionId": "test-session-1749936289492", "startTime": "2025-06-14T21:24:49.493Z", "lastActive": "2025-06-14T21:24:49.534Z", "agentId": "", "projectName": "Test Project"}, {"sessionId": "test-session-1749936349172", "startTime": "2025-06-14T21:25:49.172Z", "lastActive": "2025-06-14T21:25:49.200Z", "agentId": "", "projectName": "Test Project"}, {"sessionId": "mcp-test-session-1749936598799", "startTime": "2025-06-14T21:29:58.799Z", "lastActive": "2025-06-14T21:29:58.891Z", "agentId": "", "projectName": "MCP Test Project"}], "sessionHistory": [{"sessionId": "mcp-test-session-1749936307238", "startTime": "2025-06-14T21:25:07.239Z", "lastActive": "2025-06-14T21:25:07.307Z", "agentId": "", "projectName": "MCP Test Project", "endTime": "2025-06-14T21:25:07.447Z", "status": "completed"}, {"sessionId": "mcp-test-session-1749936475064", "startTime": "2025-06-14T21:27:55.064Z", "lastActive": "2025-06-14T21:27:55.097Z", "agentId": "", "projectName": "MCP Test Project", "endTime": "2025-06-14T21:27:55.214Z", "status": "completed"}, {"sessionId": "test-session-1749936536770", "startTime": "2025-06-14T21:28:56.770Z", "lastActive": "2025-06-14T21:28:57.313Z", "agentId": "", "projectName": "Test Project", "endTime": "2025-06-14T21:28:57.367Z", "status": "completed"}, {"sessionId": "test-session-1749936564018", "startTime": "2025-06-14T21:29:24.018Z", "lastActive": "2025-06-14T21:29:24.151Z", "agentId": "", "projectName": "Test Project", "endTime": "2025-06-14T21:29:24.194Z", "status": "completed"}, {"sessionId": "mcp-test-session-1749936594585", "startTime": "2025-06-14T21:29:54.585Z", "lastActive": "2025-06-14T21:29:54.664Z", "agentId": "", "projectName": "MCP Test Project", "endTime": "2025-06-14T21:29:54.774Z", "status": "completed"}], "lastRecovery": null}