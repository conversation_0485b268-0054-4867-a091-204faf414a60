{"concept": {"required": ["market_analysis", "user_personas"], "optional": ["competitor_research"], "description": "Comprehensive research and validation", "status": "pending", "completed": []}, "requirements": {"required": ["prd_complete", "user_stories"], "optional": ["feature_specifications"], "description": "Detailed requirements and specifications", "status": "pending", "completed": []}, "design": {"required": ["wireframes", "user_flows"], "optional": ["component_specifications"], "description": "Complete design and user experience", "status": "pending", "completed": []}, "architecture": {"required": ["basic_completion"], "optional": [], "description": "Complete architecture phase deliverables", "status": "pending", "completed": []}, "implementation": {"required": ["core_features", "testing_suite"], "optional": ["documentation"], "description": "Full implementation with testing", "status": "pending", "completed": []}, "deployment": {"required": ["production_environment"], "optional": ["monitoring_setup"], "description": "Production deployment", "status": "pending", "completed": []}}