/**
 * User Flow Mapping Engine
 * Extracts user journeys from user stories and functional requirements,
 * identifying decision points, process nodes, and error handling paths
 */

import { MermaidSyntaxGenerator } from './mermaid-syntax-generator.js';

/**
 * User Flow Mapping Engine
 */
export class UserFlowMappingEngine {
  constructor(config = {}) {
    this.config = {
      enableIntelligentMapping: config.enableIntelligentMapping !== false,
      includeErrorPaths: config.includeErrorPaths !== false,
      includeDecisionPoints: config.includeDecisionPoints !== false,
      maxFlowComplexity: config.maxFlowComplexity || 20,
      enablePersonaMapping: config.enablePersonaMapping !== false,
      generateMultipleDiagramTypes: config.generateMultipleDiagramTypes || false,
      ...config
    };

    // Initialize Mermaid generator
    this.mermaidGenerator = new MermaidSyntaxGenerator({
      includeErrorPaths: this.config.includeErrorPaths,
      includeDecisionPoints: this.config.includeDecisionPoints,
      enableStyling: true
    });

    // Flow mapping patterns
    this.flowPatterns = this.initializeFlowPatterns();
    this.decisionPatterns = this.initializeDecisionPatterns();
    this.errorPatterns = this.initializeErrorPatterns();
    this.personaPatterns = this.initializePersonaPatterns();

    this.mappingStats = {
      totalFlowsMapped: 0,
      decisionPointsIdentified: 0,
      errorPathsGenerated: 0,
      personasExtracted: 0
    };
  }

  /**
   * Map user stories and functional requirements to user flows
   */
  async mapUserFlows(userStories, functionalRequirements = [], context = {}) {
    const mappedFlows = [];
    
    try {
      // Process user stories
      for (const story of userStories) {
        const flow = await this.mapUserStoryToFlow(story, context);
        if (flow) {
          mappedFlows.push(flow);
          this.mappingStats.totalFlowsMapped++;
        }
      }

      // Process functional requirements
      for (const requirement of functionalRequirements) {
        const flow = await this.mapRequirementToFlow(requirement, context);
        if (flow) {
          mappedFlows.push(flow);
          this.mappingStats.totalFlowsMapped++;
        }
      }

      // Enhance flows with intelligent mapping
      if (this.config.enableIntelligentMapping) {
        await this.enhanceFlowsWithIntelligence(mappedFlows, context);
      }

      // Generate Mermaid diagrams for flows
      const flowsWithDiagrams = await this.generateDiagramsForFlows(mappedFlows);

      return {
        flows: flowsWithDiagrams,
        metadata: {
          totalFlows: mappedFlows.length,
          mappingStats: this.mappingStats,
          generatedAt: new Date().toISOString(),
          context
        }
      };

    } catch (error) {
      console.error('User flow mapping failed:', error.message);
      throw new Error(`Flow mapping failed: ${error.message}`);
    }
  }

  /**
   * Map individual user story to flow
   */
  async mapUserStoryToFlow(story, context) {
    const storyText = typeof story === 'object' ? 
      (story.story || story.description || story.title || '') : story;
    
    if (!storyText.trim()) return null;

    // Extract flow components
    const persona = this.extractPersona(story);
    const trigger = this.extractTrigger(storyText);
    const steps = this.extractSteps(storyText);
    const decisionPoints = this.extractDecisionPoints(storyText);
    const errorPaths = this.extractErrorPaths(storyText);
    const exitPoints = this.extractExitPoints(storyText);

    // Create flow object
    const flow = {
      id: story.id || `flow_${Date.now()}`,
      title: this.generateFlowTitle(storyText, persona),
      source: 'user_story',
      sourceData: story,
      persona,
      trigger,
      steps,
      decision_points: decisionPoints,
      error_handling: errorPaths,
      exit_points: exitPoints,
      complexity: this.calculateComplexity(steps, decisionPoints, errorPaths),
      priority: story.priority || 'medium',
      metadata: {
        extractedAt: new Date().toISOString(),
        sourceType: 'user_story',
        mappingEngine: 'UserFlowMappingEngine'
      }
    };

    return flow;
  }

  /**
   * Map functional requirement to flow
   */
  async mapRequirementToFlow(requirement, context) {
    const reqText = typeof requirement === 'object' ? 
      (requirement.description || requirement.title || requirement.name || '') : requirement;
    
    if (!reqText.trim()) return null;

    // Extract flow components from requirement
    const steps = this.extractStepsFromRequirement(reqText);
    const decisionPoints = this.extractDecisionPointsFromRequirement(reqText);
    const errorPaths = this.extractErrorPathsFromRequirement(reqText);

    // Only create flow if we have meaningful steps
    if (steps.length < 2) return null;

    const flow = {
      id: requirement.id || `req_flow_${Date.now()}`,
      title: this.generateFlowTitleFromRequirement(reqText),
      source: 'functional_requirement',
      sourceData: requirement,
      persona: 'User', // Default persona for requirements
      trigger: this.extractTriggerFromRequirement(reqText),
      steps,
      decision_points: decisionPoints,
      error_handling: errorPaths,
      exit_points: ['Success', 'Error'],
      complexity: this.calculateComplexity(steps, decisionPoints, errorPaths),
      priority: requirement.priority || 'medium',
      metadata: {
        extractedAt: new Date().toISOString(),
        sourceType: 'functional_requirement',
        mappingEngine: 'UserFlowMappingEngine'
      }
    };

    return flow;
  }

  /**
   * Extract persona from user story
   */
  extractPersona(story) {
    if (typeof story === 'object' && story.persona) {
      this.mappingStats.personasExtracted++;
      return story.persona;
    }

    const storyText = typeof story === 'object' ? story.story : story;
    const storyLower = storyText.toLowerCase();

    // Look for persona patterns
    for (const [pattern, persona] of Object.entries(this.personaPatterns)) {
      if (storyLower.includes(pattern)) {
        this.mappingStats.personasExtracted++;
        return persona;
      }
    }

    return 'User'; // Default persona
  }

  /**
   * Extract trigger from story text
   */
  extractTrigger(storyText) {
    const triggerPatterns = [
      /so that I can (.+)/i,
      /in order to (.+)/i,
      /to (.+)/i,
      /wants to (.+)/i
    ];

    for (const pattern of triggerPatterns) {
      const match = storyText.match(pattern);
      if (match) {
        return `User wants to ${match[1].trim()}`;
      }
    }

    return 'User initiates action';
  }

  /**
   * Extract steps from story text
   */
  extractSteps(storyText) {
    const steps = ['User enters system'];
    const storyLower = storyText.toLowerCase();

    // Common step patterns
    const stepPatterns = {
      'login': ['User provides credentials', 'System validates credentials'],
      'register': ['User provides registration details', 'System creates account'],
      'search': ['User enters search criteria', 'System returns results'],
      'form': ['User fills out form', 'User submits form', 'System processes form'],
      'upload': ['User selects file', 'User uploads file', 'System processes file'],
      'payment': ['User enters payment details', 'System processes payment'],
      'view': ['User requests data', 'System displays data'],
      'edit': ['User modifies data', 'System saves changes'],
      'delete': ['User confirms deletion', 'System removes data']
    };

    // Add relevant steps based on content
    for (const [pattern, patternSteps] of Object.entries(stepPatterns)) {
      if (storyLower.includes(pattern)) {
        steps.push(...patternSteps);
      }
    }

    // Add completion steps
    steps.push('User completes action', 'System provides feedback');

    return [...new Set(steps)]; // Remove duplicates
  }

  /**
   * Extract decision points from story text
   */
  extractDecisionPoints(storyText) {
    const decisions = [];
    const storyLower = storyText.toLowerCase();

    // Decision point patterns
    const decisionPatterns = {
      'login': ['Valid credentials?', 'User authorized?'],
      'payment': ['Payment valid?', 'Sufficient funds?'],
      'form': ['Valid input?', 'Required fields completed?'],
      'upload': ['Valid file type?', 'File size acceptable?'],
      'access': ['User has permission?', 'Resource available?'],
      'search': ['Results found?', 'Search criteria valid?']
    };

    for (const [pattern, patternDecisions] of Object.entries(decisionPatterns)) {
      if (storyLower.includes(pattern)) {
        decisions.push(...patternDecisions);
        this.mappingStats.decisionPointsIdentified += patternDecisions.length;
      }
    }

    // Always add a general success decision
    decisions.push('Action successful?');
    this.mappingStats.decisionPointsIdentified++;

    return [...new Set(decisions)];
  }

  /**
   * Extract error paths from story text
   */
  extractErrorPaths(storyText) {
    const errorPaths = [];
    const storyLower = storyText.toLowerCase();

    // Error handling patterns
    const errorPatterns = {
      'login': ['Invalid credentials error', 'Account locked error'],
      'payment': ['Payment failed error', 'Insufficient funds error'],
      'form': ['Validation error', 'Required field error'],
      'upload': ['File type error', 'File size error'],
      'network': ['Connection error', 'Timeout error'],
      'server': ['Server error', 'Service unavailable error']
    };

    for (const [pattern, patternErrors] of Object.entries(errorPatterns)) {
      if (storyLower.includes(pattern)) {
        errorPaths.push(...patternErrors);
        this.mappingStats.errorPathsGenerated += patternErrors.length;
      }
    }

    // Add general error handling
    errorPaths.push('Display error message', 'Provide recovery options');
    this.mappingStats.errorPathsGenerated += 2;

    return [...new Set(errorPaths)];
  }

  /**
   * Extract exit points from story text
   */
  extractExitPoints(storyText) {
    const exitPoints = ['Success'];
    const storyLower = storyText.toLowerCase();

    // Add context-specific exit points
    if (storyLower.includes('cancel')) exitPoints.push('Cancel');
    if (storyLower.includes('timeout')) exitPoints.push('Timeout');
    if (storyLower.includes('error')) exitPoints.push('Error');
    if (storyLower.includes('abort')) exitPoints.push('Abort');

    // Always include error as fallback
    if (!exitPoints.includes('Error')) exitPoints.push('Error');

    return exitPoints;
  }

  /**
   * Extract steps from functional requirement
   */
  extractStepsFromRequirement(reqText) {
    const steps = [];
    const reqLower = reqText.toLowerCase();

    // Requirement-specific step patterns
    if (reqLower.includes('shall') || reqLower.includes('must')) {
      steps.push('System receives request');
      
      if (reqLower.includes('validate')) steps.push('System validates input');
      if (reqLower.includes('process')) steps.push('System processes request');
      if (reqLower.includes('store') || reqLower.includes('save')) steps.push('System stores data');
      if (reqLower.includes('notify') || reqLower.includes('alert')) steps.push('System sends notification');
      if (reqLower.includes('return') || reqLower.includes('respond')) steps.push('System returns response');
      
      steps.push('System completes operation');
    }

    return steps.length > 0 ? steps : ['System performs operation', 'System completes task'];
  }

  /**
   * Extract decision points from functional requirement
   */
  extractDecisionPointsFromRequirement(reqText) {
    const decisions = [];
    const reqLower = reqText.toLowerCase();

    if (reqLower.includes('if') || reqLower.includes('when')) decisions.push('Condition met?');
    if (reqLower.includes('valid')) decisions.push('Input valid?');
    if (reqLower.includes('authorized') || reqLower.includes('permission')) decisions.push('User authorized?');
    if (reqLower.includes('available')) decisions.push('Resource available?');

    return decisions;
  }

  /**
   * Extract error paths from functional requirement
   */
  extractErrorPathsFromRequirement(reqText) {
    const errorPaths = ['Log error', 'Return error response'];
    const reqLower = reqText.toLowerCase();

    if (reqLower.includes('timeout')) errorPaths.push('Handle timeout');
    if (reqLower.includes('fail')) errorPaths.push('Handle failure');
    if (reqLower.includes('invalid')) errorPaths.push('Handle invalid input');

    return errorPaths;
  }

  /**
   * Calculate flow complexity
   */
  calculateComplexity(steps, decisionPoints, errorPaths) {
    const stepWeight = steps.length * 1;
    const decisionWeight = decisionPoints.length * 2;
    const errorWeight = errorPaths.length * 1.5;
    
    const totalComplexity = stepWeight + decisionWeight + errorWeight;
    
    if (totalComplexity <= 5) return 'low';
    if (totalComplexity <= 15) return 'medium';
    return 'high';
  }

  /**
   * Generate flow title
   */
  generateFlowTitle(storyText, persona) {
    // Extract action from user story
    const actionMatch = storyText.match(/I want to (.+?) so that/i) || 
                       storyText.match(/I want to (.+)/i);
    
    if (actionMatch) {
      const action = actionMatch[1].trim();
      return `${persona} ${action}`;
    }
    
    return `${persona} Flow`;
  }

  /**
   * Generate flow title from requirement
   */
  generateFlowTitleFromRequirement(reqText) {
    // Extract main action from requirement
    const words = reqText.split(' ').slice(0, 6);
    return words.join(' ').replace(/[^\w\s]/g, '') + ' Flow';
  }

  /**
   * Extract trigger from requirement
   */
  extractTriggerFromRequirement(reqText) {
    if (reqText.toLowerCase().includes('when')) {
      const match = reqText.match(/when (.+)/i);
      if (match) return match[1].trim();
    }
    
    return 'System requirement triggered';
  }

  /**
   * Enhance flows with intelligent mapping
   */
  async enhanceFlowsWithIntelligence(flows, context) {
    for (const flow of flows) {
      // Add flow relationships
      flow.relationships = this.identifyFlowRelationships(flow, flows);
      
      // Optimize flow structure
      flow.optimizedSteps = this.optimizeFlowSteps(flow.steps);
      
      // Add context-specific enhancements
      if (context.industry) {
        flow.industrySpecific = this.addIndustrySpecificElements(flow, context.industry);
      }
    }
  }

  /**
   * Generate Mermaid diagrams for flows
   */
  async generateDiagramsForFlows(flows) {
    const flowsWithDiagrams = [];

    for (const flow of flows) {
      try {
        if (this.config.generateMultipleDiagramTypes) {
          // Generate multiple diagram types
          const diagrams = this.mermaidGenerator.generateMultipleDiagrams(flow);
          flow.mermaidDiagrams = diagrams;
        } else {
          // Generate default flowchart
          const flowchart = this.mermaidGenerator.generateFromUserFlow(flow, 'flowchart');
          flow.mermaidDiagram = flowchart;
        }

        flowsWithDiagrams.push(flow);
      } catch (error) {
        console.warn(`Failed to generate diagram for flow ${flow.id}:`, error.message);
        flowsWithDiagrams.push(flow); // Include flow without diagram
      }
    }

    return flowsWithDiagrams;
  }

  // Helper methods and initialization
  initializeFlowPatterns() {
    return {
      authentication: ['login', 'signin', 'authenticate'],
      registration: ['register', 'signup', 'create account'],
      search: ['search', 'find', 'lookup'],
      payment: ['pay', 'purchase', 'checkout'],
      upload: ['upload', 'attach', 'import'],
      download: ['download', 'export', 'save'],
      edit: ['edit', 'modify', 'update'],
      delete: ['delete', 'remove', 'cancel']
    };
  }

  initializeDecisionPatterns() {
    return {
      validation: ['valid', 'correct', 'acceptable'],
      authorization: ['authorized', 'permitted', 'allowed'],
      availability: ['available', 'exists', 'found'],
      completion: ['complete', 'finished', 'done']
    };
  }

  initializeErrorPatterns() {
    return {
      validation: ['invalid', 'incorrect', 'malformed'],
      authorization: ['unauthorized', 'forbidden', 'denied'],
      availability: ['unavailable', 'not found', 'missing'],
      system: ['error', 'failure', 'exception']
    };
  }

  initializePersonaPatterns() {
    return {
      'admin': 'Administrator',
      'administrator': 'Administrator',
      'manager': 'Manager',
      'customer': 'Customer',
      'client': 'Client',
      'visitor': 'Visitor',
      'guest': 'Guest',
      'employee': 'Employee',
      'staff': 'Staff Member',
      'developer': 'Developer',
      'user': 'User'
    };
  }

  identifyFlowRelationships(flow, allFlows) {
    // Simple relationship identification
    const relationships = [];
    
    for (const otherFlow of allFlows) {
      if (otherFlow.id !== flow.id) {
        // Check for shared steps or personas
        const sharedSteps = flow.steps.filter(step => 
          otherFlow.steps.some(otherStep => 
            step.toLowerCase().includes(otherStep.toLowerCase().split(' ')[0])
          )
        );
        
        if (sharedSteps.length > 0 || flow.persona === otherFlow.persona) {
          relationships.push({
            flowId: otherFlow.id,
            type: 'related',
            reason: sharedSteps.length > 0 ? 'shared_steps' : 'same_persona'
          });
        }
      }
    }
    
    return relationships;
  }

  optimizeFlowSteps(steps) {
    // Remove duplicate steps and optimize order
    const uniqueSteps = [...new Set(steps)];
    
    // Sort by logical order (start -> process -> end)
    const startSteps = uniqueSteps.filter(step => 
      step.toLowerCase().includes('enter') || step.toLowerCase().includes('start')
    );
    const processSteps = uniqueSteps.filter(step => 
      !step.toLowerCase().includes('enter') && 
      !step.toLowerCase().includes('complete') &&
      !step.toLowerCase().includes('feedback')
    );
    const endSteps = uniqueSteps.filter(step => 
      step.toLowerCase().includes('complete') || step.toLowerCase().includes('feedback')
    );
    
    return [...startSteps, ...processSteps, ...endSteps];
  }

  addIndustrySpecificElements(flow, industry) {
    const industryElements = {
      'healthcare': {
        compliance: ['HIPAA compliance check', 'Patient consent verification'],
        security: ['Medical data encryption', 'Audit trail logging']
      },
      'finance': {
        compliance: ['KYC verification', 'AML screening'],
        security: ['PCI DSS compliance', 'Fraud detection']
      },
      'ecommerce': {
        features: ['Inventory check', 'Price calculation', 'Shipping options'],
        security: ['Payment security', 'Customer data protection']
      }
    };
    
    return industryElements[industry.toLowerCase()] || {};
  }

  /**
   * Get mapping statistics
   */
  getMappingStats() {
    return {
      ...this.mappingStats,
      averageComplexity: this.mappingStats.totalFlowsMapped > 0 ?
        (this.mappingStats.decisionPointsIdentified + this.mappingStats.errorPathsGenerated) /
        this.mappingStats.totalFlowsMapped : 0
    };
  }

  /**
   * Generate flow comparison summary
   */
  generateFlowComparison(flows) {
    let comparison = '# User Flow Comparison\n\n';

    for (let i = 0; i < flows.length; i++) {
      const flow = flows[i];
      comparison += `## ${i + 1}. ${flow.title}\n`;
      comparison += `- **Persona**: ${flow.persona}\n`;
      comparison += `- **Complexity**: ${flow.complexity}\n`;
      comparison += `- **Steps**: ${flow.steps.length}\n`;
      comparison += `- **Decision Points**: ${flow.decision_points.length}\n`;
      comparison += `- **Error Paths**: ${flow.error_handling.length}\n`;
      comparison += `- **Source**: ${flow.source}\n\n`;

      if (flow.mermaidDiagram) {
        comparison += '### Flow Diagram\n```mermaid\n';
        comparison += flow.mermaidDiagram;
        comparison += '\n```\n\n';
      }
    }

    return comparison;
  }
}
