/**
 * @file option-presenter.js
 * @description Presents decision options with clear business implications and trade-offs
 */

import { DecisionTranslator } from './decision-translator.js';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs/promises';
import { translateTechnicalToBusinessTerms } from './decision-translator.js';
import { getDecisionTemplate, generateCustomTemplate } from './templates/decision-templates.js';
import { AITranslationService } from './ai-translation-service.js';

/**
 * OptionPresenter class responsible for formatting and presenting business decisions
 * to users in a clear, non-technical way
 */
export class OptionPresenter {
  /**
   * Create a new OptionPresenter instance
   * @param {string} projectRoot - The project root directory
   */
  constructor(projectRoot = '.') {
    this.projectRoot = projectRoot;
    this.translator = new DecisionTranslator(projectRoot);
  }

  /**
   * Get decision options for a specific context, formatted for presentation
   * @async
   * @param {string} context - The decision context (e.g., 'framework_selection')
   * @returns {Object} Formatted decision options ready for presentation
   */
  async getDecisionOptions(context) {
    try {
      // Get translated options from the translator
      const translatedOptions = await this.translator.translateDecisionOptions(context);
      
      // Format the options for presentation
      return this._formatForPresentation(translatedOptions);
    } catch (error) {
      console.error('Error getting decision options:', error);
      throw error;
    }
  }

  /**
   * Format decision options for presentation
   * @private
   * @param {Object} translatedOptions - The translated options from DecisionTranslator
   * @returns {Object} Formatted options for presentation
   */
  _formatForPresentation(translatedOptions) {
    const { decisionId, context, title, description, options, isCustomTemplate } = translatedOptions;
    
    // Format each option with clear labels
    const formattedOptions = options.map((option, index) => {
      const label = String.fromCharCode(65 + index); // A, B, C, etc.
      
      return {
        id: option.id,
        label,
        title: option.title,
        description: option.description,
        implications: {
          business: this._formatImplication('Business Impact', option.businessImpact),
          time: this._formatImplication('Time', option.timeImplication),
          cost: this._formatImplication('Cost', option.costImplication),
          risk: this._formatImplication('Risk', option.riskAssessment)
        },
        technicalDetails: option.technicalDetails,
        isRecommended: option.isRecommended,
        isCustom: option.isCustom || false
      };
    });
    
    // Find the recommended option if any
    const recommendedOption = formattedOptions.find(o => o.isRecommended);
    
    return {
      decisionId,
      context,
      title,
      description,
      options: formattedOptions,
      recommendedOption: recommendedOption ? {
        label: recommendedOption.label,
        id: recommendedOption.id,
        title: recommendedOption.title,
        reasoning: "Based on your project context and preferences"
      } : null,
      isCustomTemplate: isCustomTemplate || false
    };
  }

  /**
   * Format a specific implication for presentation
   * @private
   * @param {string} type - The type of implication (Business, Time, Cost, Risk)
   * @param {Object|string} implication - The implication data
   * @returns {Object} Formatted implication
   */
  _formatImplication(type, implication) {
    if (!implication) {
      return { summary: 'No information available' };
    }
    
    // If implication is just a string
    if (typeof implication === 'string') {
      return { summary: implication };
    }
    
    // If implication is an object with level and description
    if (implication.level && implication.description) {
      const emoji = this._getEmojiForLevel(type, implication.level);
      return {
        level: implication.level,
        summary: `${emoji} ${implication.description}`,
        details: implication.details || null
      };
    }
    
    return { summary: JSON.stringify(implication) };
  }

  /**
   * Get an appropriate emoji for an implication level
   * @private
   * @param {string} type - The type of implication
   * @param {string} level - The level (low, medium, high)
   * @returns {string} An emoji representing the level
   */
  _getEmojiForLevel(type, level) {
    const emojiMap = {
      'Business Impact': {
        low: '🔷',
        medium: '🔶',
        high: '⭐'
      },
      'Time': {
        low: '⏱️',
        medium: '⏰',
        high: '⌛'
      },
      'Cost': {
        low: '💰',
        medium: '💰💰',
        high: '💰💰💰'
      },
      'Risk': {
        low: '✅',
        medium: '⚠️',
        high: '🚨'
      }
    };
    
    return (emojiMap[type] && emojiMap[type][level]) || '';
  }

  /**
   * Present a decision with options in a format suitable for AI agents to display
   * @async
   * @param {string} context - The decision context
   * @returns {Object} Presentation-ready decision
   */
  async presentDecision(context) {
    const decision = await this.getDecisionOptions(context);
    
    // Format the decision for AI agent presentation
    const presentation = {
      title: `Decision: ${decision.title}`,
      description: decision.description,
      options: decision.options.map(option => {
        return {
          id: option.id,
          label: `Option ${option.label}: ${option.title}`,
          description: option.description,
          implications: [
            `${option.implications.business.summary}`,
            `${option.implications.time.summary}`,
            `${option.implications.cost.summary}`,
            `${option.implications.risk.summary}`
          ],
          technicalDetails: option.technicalDetails || null,
          isCustom: option.isCustom || false
        };
      }),
      recommendation: decision.recommendedOption ? {
        message: `Recommendation: Option ${decision.recommendedOption.label} (${decision.recommendedOption.title})`,
        reasoning: decision.recommendedOption.reasoning
      } : null,
      decisionId: decision.decisionId,
      context: decision.context,
      isCustomTemplate: decision.isCustomTemplate || false,
      customChoiceAvailable: decision.options.some(option => option.isCustom)
    };
    
    return presentation;
  }

  /**
   * Record a user's decision
   * @async
   * @param {string} decisionId - The decision ID
   * @param {string} choiceId - The chosen option ID
   * @param {string} rationale - The user's rationale for the choice
   * @param {Object} customDetails - Optional custom details for custom choices
   * @returns {Object} The recorded decision
   */
  async recordDecision(decisionId, choiceId, rationale, customDetails = null) {
    return await this.translator.recordDecision(decisionId, choiceId, rationale, customDetails);
  }

  /**
   * Create a custom decision template
   * @async
   * @param {string} context - The decision context
   * @param {string} title - The title for the decision
   * @param {string} description - The description of the decision
   * @param {Array} options - Array of technology options
   * @returns {Object} The created decision template
   */
  createCustomDecisionTemplate(context, title, description, options) {
    return this.translator.createCustomDecisionTemplate(context, title, description, options);
  }

  /**
   * Add a custom technology term to the translation mapping
   * @param {string} technicalTerm - The technical term to add
   * @param {string} businessTerm - The business-friendly translation
   * @returns {Object} The updated mapping
   */
  addCustomTermMapping(technicalTerm, businessTerm) {
    return this.translator.addCustomTermMapping(technicalTerm, businessTerm);
  }
}

export default OptionPresenter;

/**
 * Get business-friendly decision options for a technical context
 * 
 * @param {Object} params - Parameters
 * @param {string} params.context - The technical context/decision type (e.g., 'framework_selection')
 * @param {string} [params.userExpertiseLevel='novice'] - User's technical expertise level
 * @param {Object} [params.additionalContext={}] - Additional context for the decision
 * @param {string} params.projectRoot - Project root directory
 * @param {Object} [params.session] - Session information
 * @returns {Promise<Object>} Decision options with business-friendly descriptions
 */
export async function getDecisionOptions({
  context,
  userExpertiseLevel = 'novice',
  additionalContext = {},
  projectRoot,
  session
}) {
  try {
    // Generate a unique decision ID
    const decisionId = `${context}_${uuidv4()}`;
    
    // Get the decision template
    let template = getDecisionTemplate(context);
    
    // If no template exists, generate a custom one
    if (!template) {
      template = await generateCustomTemplate(context, additionalContext);
    }
    
    // Process each option to add business-friendly descriptions
    const processedOptions = await Promise.all(template.options.map(async (option) => {
      // Translate technical terms to business language
      const businessDescription = await translateTechnicalToBusinessTerms(
        option.description,
        userExpertiseLevel
      );
      
      // Generate business impact if not provided
      let businessImpact = option.businessImpact;
      if (!businessImpact) {
        businessImpact = await AITranslationService.generateBusinessImpact(
          context,
          option.id,
          option.description,
          additionalContext
        );
      }
      
      return {
        id: option.id,
        title: option.title,
        description: businessDescription,
        businessImpact,
        timeImpact: option.timeImpact || 'No significant time impact',
        costImpact: option.costImpact || 'No significant cost impact',
        riskLevel: option.riskLevel || 'low',
        isCustom: option.isCustom || false
      };
    }));
    
    // Store the decision in the project context for later reference
    await storeDecisionContext(decisionId, {
      context,
      title: template.title,
      description: template.description,
      options: processedOptions,
      recommendedOption: template.recommendedOption,
      timestamp: new Date().toISOString()
    }, projectRoot);
    
    return {
      decisionId,
      title: template.title,
      description: template.description,
      options: processedOptions,
      recommendedOption: template.recommendedOption
    };
  } catch (error) {
    throw new Error(`Failed to get decision options: ${error.message}`);
  }
}

/**
 * Store decision context in the project for later reference
 * 
 * @param {string} decisionId - Unique decision ID
 * @param {Object} decisionContext - Decision context data
 * @param {string} projectRoot - Project root directory
 * @returns {Promise<void>}
 */
async function storeDecisionContext(decisionId, decisionContext, projectRoot) {
  try {
    const guidantDir = path.join(projectRoot, '.guidant');
    const contextDir = path.join(guidantDir, 'context');
    const decisionsFile = path.join(contextDir, 'decisions.json');
    
    // Create directories if they don't exist
    await fs.mkdir(contextDir, { recursive: true });
    
    // Read existing decisions or create new file
    let decisions = {};
    try {
      const fileContent = await fs.readFile(decisionsFile, 'utf8');
      decisions = JSON.parse(fileContent);
    } catch (error) {
      // File doesn't exist or is invalid, create new
      decisions = { pendingDecisions: {}, completedDecisions: {} };
    }
    
    // Store the pending decision
    decisions.pendingDecisions[decisionId] = decisionContext;
    
    // Write back to file
    await fs.writeFile(decisionsFile, JSON.stringify(decisions, null, 2), 'utf8');
  } catch (error) {
    console.error(`Failed to store decision context: ${error.message}`);
    // Non-fatal error, continue execution
  }
} 