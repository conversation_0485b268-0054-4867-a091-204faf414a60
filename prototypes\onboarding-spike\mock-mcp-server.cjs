/**
 * <PERSON>ck MCP Server for WLR-000: Conversational Onboarding Spike
 *
 * This server simulates the Guidant back-end for the interactive onboarding process.
 * It's designed to be lightweight and stateless, storing the "session" in memory
 * to validate the conversational flow.
 */

const http = require('http');

// --- In-Memory State & Mock Data ---

// This simulates the project-specific question flows.
const questionFlows = {
  restaurant_app: [
    { id: 1, question: "What is your restaurant's name?", key: 'projectName' },
    { id: 2, question: 'What type of cuisine do you serve? (e.g., Italian, Mexican, etc.)', key: 'cuisineType' },
    { id: 3, question: 'What are the main features you want? (e.g., Online Ordering, Reservations, Reviews)', key: 'features', type: 'tags' },
    { id: 4, question: 'Share a link to a restaurant app you like for inspiration.', key: 'inspirationUrl', type: 'url' },
    { id: 5, question: 'Is this a high-end restaurant or more casual?', key: 'style' }
  ],
  default: [
    { id: 1, question: "What is the name of your project?", key: 'projectName' },
    { id: 2, question: "Briefly describe the main goal of your project.", key: 'description' }
  ]
};

// In-memory session storage. In the real system, this would be in a persistent store.
const sessions = {};

// --- Mock MCP Tool Implementations ---

/**
 * Mocks the `guidant_init_project` tool.
 * Starts a new onboarding session.
 */
function guidant_init_project(args) {
  const sessionId = `session_${Date.now()}`;
  const idea = args.idea || 'default';
  
  // Simple classification logic for the spike
  const flowType = idea.toLowerCase().includes('restaurant') ? 'restaurant_app' : 'default';
  const flow = questionFlows[flowType];

  sessions[sessionId] = {
    flowType,
    currentQuestionIndex: 0,
    answers: {},
  };

  const currentQuestion = flow[0];

  return {
    success: true,
    data: {
      sessionId,
      currentQuestion: currentQuestion.question,
      progress: `1/${flow.length}`,
    },
  };
}

/**
 * Mocks the `guidant_answer_question` tool.
 * Records an answer and returns the next question.
 */
function guidant_answer_question(args) {
  const { sessionId, answer } = args;

  const session = sessions[sessionId];
  if (!session) {
    return { success: false, error: 'Invalid session ID.' };
  }

  const flow = questionFlows[session.flowType];
  const currentQuestion = flow[session.currentQuestionIndex];

  // Store the answer
  session.answers[currentQuestion.key] = answer;

  // Move to the next question
  session.currentQuestionIndex++;

  if (session.currentQuestionIndex >= flow.length) {
    // End of the flow
    return guidant_complete_onboarding({ sessionId });
  }

  const nextQuestion = flow[session.currentQuestionIndex];

  return {
    success: true,
    data: {
      nextQuestion: nextQuestion.question,
      progress: `${session.currentQuestionIndex + 1}/${flow.length}`,
    },
  };
}

/**
 * Mocks the `guidant_complete_onboarding` tool.
 * Finalizes the process and returns the collected data.
 */
function guidant_complete_onboarding(args) {
    const { sessionId } = args;
    const session = sessions[sessionId];
    if (!session) {
        return { success: false, error: 'Invalid session ID.' };
    }

    // In a real system, this would trigger project file creation, etc.
    const finalData = {
        summary: "Onboarding complete! Project context has been created.",
        collectedData: session.answers
    };
    
    // Clean up the session
    delete sessions[sessionId];

    return {
        success: true,
        data: finalData,
    };
}


// --- HTTP Server to expose the mock tools ---

const server = http.createServer((req, res) => {
  if (req.method === 'POST' && req.url === '/mcp') {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        const { tool, args } = JSON.parse(body);
        let result;

        switch (tool) {
          case 'guidant_init_project':
            result = guidant_init_project(args);
            break;
          case 'guidant_answer_question':
            result = guidant_answer_question(args);
            break;
          default:
            result = { success: false, error: 'Unknown tool' };
        }

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(result));
      } catch (error) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Invalid JSON request' }));
      }
    });
  } else {
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ success: false, error: 'Not Found' }));
  }
});

const PORT = 3000;
server.listen(PORT, () => {
  console.log(`Mock MCP Server running on http://localhost:${PORT}`);
  console.log('Ready to simulate conversational onboarding.');
});
