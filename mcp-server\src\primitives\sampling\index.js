/**
 * MCP Sampling Registry
 * Central registry for all MCP Sampling capabilities and intelligent decision making
 */

import { IntelligentDecisionSampler } from './intelligent-decisions.js';
import { AdaptiveResponseSampler } from './adaptive-responses.js';
import { MCPMessageHandler } from '../../utils/mcp-message-handler.js';

/**
 * MCP Sampling Registry
 * Manages all sampling handlers and provides unified LLM completion access
 */
export class MCPSamplingRegistry {
  constructor() {
    this.samplers = new Map();
    this.messageHandler = new MCPMessageHandler();
    this.initialized = false;
  }

  /**
   * Initialize the sampling registry
   * @param {object} existingInfrastructure - Existing infrastructure
   */
  async initialize(existingInfrastructure = {}) {
    if (this.initialized) return;

    // Initialize sampling handlers
    const decisionSampler = new IntelligentDecisionSampler(existingInfrastructure);
    const responseSampler = new AdaptiveResponseSampler(existingInfrastructure);

    // Register samplers by category
    this.samplers.set('decisions', decisionSampler);
    this.samplers.set('responses', responseSampler);

    this.initialized = true;
    console.log(`🧠 MCP Sampling Registry initialized with ${this.samplers.size} samplers`);
  }

  /**
   * Register MCP sampling capabilities with the server
   * @param {object} server - FastMCP server instance
   */
  registerCapabilities(server) {
    // FastMCP handles capabilities automatically
    // Note: Sampling is server-controlled, so no direct client handlers needed
    // The server will use these samplers internally for intelligent decisions

    console.log('🧠 MCP Sampling capabilities registered');
  }

  /**
   * Request LLM completion for intelligent decision making
   * @param {string} purpose - Purpose of the sampling request
   * @param {object} context - Context for the completion
   * @param {object} options - Sampling options
   * @returns {object} LLM completion result
   */
  async requestCompletion(purpose, context = {}, options = {}) {
    try {
      const sampler = this.getSamplerForPurpose(purpose);
      
      if (!sampler) {
        throw new Error(`No sampler found for purpose: ${purpose}`);
      }

      const completion = await sampler.requestCompletion(context, options);
      
      return {
        purpose,
        completion,
        timestamp: new Date().toISOString(),
        context_used: context,
        options_applied: options
      };
    } catch (error) {
      console.error(`Sampling error for ${purpose}:`, error);
      
      return {
        purpose,
        error: error.message,
        timestamp: new Date().toISOString(),
        fallback_used: true
      };
    }
  }

  /**
   * Get appropriate sampler for a purpose
   * @param {string} purpose - Sampling purpose
   * @returns {object|null} Sampler instance or null
   */
  getSamplerForPurpose(purpose) {
    // Map purposes to sampler categories
    if (purpose.includes('decision') || purpose.includes('choice') || purpose.includes('optimization')) {
      return this.samplers.get('decisions');
    }
    
    if (purpose.includes('response') || purpose.includes('adaptation') || purpose.includes('content')) {
      return this.samplers.get('responses');
    }
    
    // Default to decision sampler for unknown purposes
    return this.samplers.get('decisions');
  }

  /**
   * Add a new sampler
   * @param {string} category - Sampler category
   * @param {object} sampler - Sampler instance
   */
  addSampler(category, sampler) {
    this.samplers.set(category, sampler);
    console.log(`🧠 Added sampler for category: ${category}`);
  }

  /**
   * Remove a sampler
   * @param {string} category - Sampler category
   */
  removeSampler(category) {
    const removed = this.samplers.delete(category);
    if (removed) {
      console.log(`🧠 Removed sampler for category: ${category}`);
    }
    return removed;
  }

  /**
   * Get sampling statistics
   * @returns {object} Sampling registry statistics
   */
  getStatistics() {
    const stats = {
      samplers: this.samplers.size,
      categories: Array.from(this.samplers.keys()),
      capabilities: [
        'Intelligent decision making',
        'Adaptive response generation',
        'Context-aware completions',
        'Workflow optimization suggestions'
      ]
    };

    return stats;
  }

  /**
   * Test sampling capabilities
   * @returns {object} Test results
   */
  async testSamplingCapabilities() {
    const testResults = {
      timestamp: new Date().toISOString(),
      tests_run: 0,
      tests_passed: 0,
      test_details: []
    };

    // Test decision sampling
    try {
      const decisionTest = await this.requestCompletion(
        'workflow_optimization_decision',
        { workflow_type: 'development', current_performance: 75 },
        { max_tokens: 100, temperature: 0.7 }
      );
      
      testResults.tests_run++;
      if (decisionTest.completion || decisionTest.fallback_used) {
        testResults.tests_passed++;
      }
      
      testResults.test_details.push({
        test: 'decision_sampling',
        status: decisionTest.completion ? 'passed' : 'fallback',
        result: decisionTest
      });
    } catch (error) {
      testResults.test_details.push({
        test: 'decision_sampling',
        status: 'failed',
        error: error.message
      });
    }

    // Test response sampling
    try {
      const responseTest = await this.requestCompletion(
        'adaptive_content_generation',
        { content_type: 'documentation', audience: 'developers' },
        { max_tokens: 100, temperature: 0.5 }
      );
      
      testResults.tests_run++;
      if (responseTest.completion || responseTest.fallback_used) {
        testResults.tests_passed++;
      }
      
      testResults.test_details.push({
        test: 'response_sampling',
        status: responseTest.completion ? 'passed' : 'fallback',
        result: responseTest
      });
    } catch (error) {
      testResults.test_details.push({
        test: 'response_sampling',
        status: 'failed',
        error: error.message
      });
    }

    testResults.success_rate = testResults.tests_run > 0 ? 
      (testResults.tests_passed / testResults.tests_run) * 100 : 0;

    return testResults;
  }

  /**
   * Get available sampling purposes
   * @returns {Array} List of supported sampling purposes
   */
  getAvailablePurposes() {
    const purposes = [];
    
    for (const [category, sampler] of this.samplers) {
      if (sampler.getSupportedPurposes) {
        purposes.push(...sampler.getSupportedPurposes().map(purpose => ({
          category,
          purpose,
          description: sampler.getPurposeDescription ? sampler.getPurposeDescription(purpose) : purpose
        })));
      }
    }

    return purposes;
  }

  /**
   * Validate sampling request
   * @param {string} purpose - Sampling purpose
   * @param {object} context - Request context
   * @param {object} options - Sampling options
   * @returns {object} Validation result
   */
  validateSamplingRequest(purpose, context, options) {
    const sampler = this.getSamplerForPurpose(purpose);
    
    if (!sampler) {
      return {
        valid: false,
        error: `No sampler available for purpose: ${purpose}`
      };
    }

    if (sampler.validateRequest) {
      return sampler.validateRequest(purpose, context, options);
    }

    return {
      valid: true,
      message: 'Basic validation passed'
    };
  }
}

/**
 * Register MCP Sampling capabilities with FastMCP server
 * @param {object} server - FastMCP server instance
 * @param {object} existingInfrastructure - Existing infrastructure
 * @returns {MCPSamplingRegistry} Initialized sampling registry
 */
export async function registerSamplingCapabilities(server, existingInfrastructure = {}) {
  const registry = new MCPSamplingRegistry();
  
  // Initialize with existing infrastructure
  await registry.initialize(existingInfrastructure);
  
  // Register capabilities with server
  registry.registerCapabilities(server);
  
  return registry;
}

/**
 * Create mock sampling infrastructure for testing
 * @returns {object} Mock infrastructure
 */
export function createMockSamplingInfrastructure() {
  return {
    llmProvider: {
      async requestCompletion(prompt, options = {}) {
        // Mock LLM completion
        return {
          text: `Mock completion for: ${prompt.substring(0, 50)}...`,
          tokens_used: options.max_tokens || 100,
          model: 'mock-model',
          finish_reason: 'completed'
        };
      }
    },
    contextManager: {
      async getRelevantContext(purpose, context) {
        return {
          ...context,
          enhanced_context: 'Mock enhanced context',
          relevance_score: 0.85
        };
      }
    },
    decisionHistory: {
      async getRecentDecisions(category, limit = 5) {
        return [
          { decision: 'Mock decision 1', outcome: 'successful', timestamp: new Date().toISOString() },
          { decision: 'Mock decision 2', outcome: 'successful', timestamp: new Date().toISOString() }
        ];
      }
    }
  };
}

export default MCPSamplingRegistry;
