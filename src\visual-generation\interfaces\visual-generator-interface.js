/**
 * Base Visual Generator Interface
 * Defines common contract for all visual generators following Interface Segregation Principle
 */

/**
 * Core interface that all visual generators must implement
 * @interface IVisualGenerator
 */
export class IVisualGenerator {
  /**
   * Generate visual asset from requirements
   * @param {Object} requirements - Functional requirements and user stories
   * @param {Object} options - Generation options and preferences
   * @returns {Promise<VisualAsset>} Generated visual asset
   * @abstract
   */
  async generate(requirements, options = {}) {
    throw new Error('generate() method must be implemented by subclass');
  }

  /**
   * Validate generated visual asset
   * @param {VisualAsset} asset - Visual asset to validate
   * @returns {Promise<ValidationResult>} Validation result with errors/warnings
   * @abstract
   */
  async validate(asset) {
    throw new Error('validate() method must be implemented by subclass');
  }

  /**
   * Get metadata about the generator capabilities
   * @returns {GeneratorMetadata} Generator metadata and capabilities
   * @abstract
   */
  getMetadata() {
    throw new Error('getMetadata() method must be implemented by subclass');
  }
}

/**
 * Extended interface for generators that support research integration
 * @interface IResearchEnhancedGenerator
 * @extends IVisualGenerator
 */
export class IResearchEnhancedGenerator extends IVisualGenerator {
  /**
   * Enhance generation with research insights
   * @param {Object} requirements - Base requirements
   * @param {Object} researchInsights - Research data from Tavily/Context7/Firecrawl
   * @param {Object} options - Generation options
   * @returns {Promise<VisualAsset>} Research-enhanced visual asset
   * @abstract
   */
  async generateWithResearch(requirements, researchInsights, options = {}) {
    throw new Error('generateWithResearch() method must be implemented by subclass');
  }
}

/**
 * Interface for generators that support iterative refinement
 * @interface IIterativeGenerator
 * @extends IVisualGenerator
 */
export class IIterativeGenerator extends IVisualGenerator {
  /**
   * Refine existing visual asset based on feedback
   * @param {VisualAsset} existingAsset - Current visual asset
   * @param {Object} feedback - User feedback and modification requests
   * @param {Object} options - Refinement options
   * @returns {Promise<VisualAsset>} Refined visual asset
   * @abstract
   */
  async refine(existingAsset, feedback, options = {}) {
    throw new Error('refine() method must be implemented by subclass');
  }

  /**
   * Get refinement suggestions for an asset
   * @param {VisualAsset} asset - Visual asset to analyze
   * @returns {Promise<Array<RefinementSuggestion>>} Suggested improvements
   * @abstract
   */
  async getSuggestions(asset) {
    throw new Error('getSuggestions() method must be implemented by subclass');
  }
}

/**
 * Interface for generators that support responsive design
 * @interface IResponsiveGenerator
 * @extends IVisualGenerator
 */
export class IResponsiveGenerator extends IVisualGenerator {
  /**
   * Generate responsive variants for different breakpoints
   * @param {Object} requirements - Base requirements
   * @param {Array<string>} breakpoints - Target breakpoints ['mobile', 'tablet', 'desktop']
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} Map of breakpoint to visual asset
   * @abstract
   */
  async generateResponsive(requirements, breakpoints, options = {}) {
    throw new Error('generateResponsive() method must be implemented by subclass');
  }
}

/**
 * Interface for generators that support accessibility features
 * @interface IAccessibleGenerator
 * @extends IVisualGenerator
 */
export class IAccessibleGenerator extends IVisualGenerator {
  /**
   * Generate accessibility-enhanced visual asset
   * @param {Object} requirements - Base requirements
   * @param {Object} accessibilityOptions - Accessibility preferences and requirements
   * @param {Object} options - Generation options
   * @returns {Promise<VisualAsset>} Accessibility-enhanced visual asset
   * @abstract
   */
  async generateAccessible(requirements, accessibilityOptions, options = {}) {
    throw new Error('generateAccessible() method must be implemented by subclass');
  }

  /**
   * Validate accessibility compliance of visual asset
   * @param {VisualAsset} asset - Visual asset to validate
   * @param {Object} standards - Accessibility standards to check against
   * @returns {Promise<AccessibilityReport>} Accessibility compliance report
   * @abstract
   */
  async validateAccessibility(asset, standards = {}) {
    throw new Error('validateAccessibility() method must be implemented by subclass');
  }
}

/**
 * Factory interface for creating visual generators
 * @interface IVisualGeneratorFactory
 */
export class IVisualGeneratorFactory {
  /**
   * Create a visual generator of specified type
   * @param {string} type - Generator type ('ascii', 'mermaid', 'html')
   * @param {Object} config - Generator configuration
   * @returns {IVisualGenerator} Visual generator instance
   * @abstract
   */
  createGenerator(type, config = {}) {
    throw new Error('createGenerator() method must be implemented by subclass');
  }

  /**
   * Get available generator types
   * @returns {Array<string>} Available generator types
   * @abstract
   */
  getAvailableTypes() {
    throw new Error('getAvailableTypes() method must be implemented by subclass');
  }
}
