/**
 * Tavily Research Provider Unit Tests
 * 
 * Tests for the TavilyProvider class including search functionality,
 * rate limiting, error handling, and result ranking.
 */

import { describe, it, expect, beforeEach, afterEach, mock, spyOn } from 'bun:test';
import { TavilyProvider } from '../../src/research-providers/tavily-provider.js';

describe('TavilyProvider', () => {
  let provider;
  let mockConfig;

  beforeEach(() => {
    mockConfig = {
      apiKey: 'test-api-key',
      baseUrl: 'https://api.tavily.com',
      rateLimit: {
        requestsPerMinute: 60,
        burstLimit: 10
      },
      defaultOptions: {
        searchDepth: 'basic',
        maxResults: 10,
        includeImages: false
      }
    };
    provider = new TavilyProvider(mockConfig);
  });

  afterEach(() => {
    mock.restore();
  });

  describe('Constructor', () => {
    it('should initialize with correct configuration', () => {
      expect(provider.name).toBe('Tavily');
      expect(provider.baseUrl).toBe('https://api.tavily.com');
      expect(provider.apiKey).toBe('test-api-key');
    });

    it('should use default configuration when not provided', () => {
      const defaultProvider = new TavilyProvider();
      expect(defaultProvider.name).toBe('Tavily');
      expect(defaultProvider.baseUrl).toBe('https://api.tavily.com');
    });

    it('should initialize rate limiter with correct settings', () => {
      expect(provider.rateLimiter).toBeDefined();
      expect(provider.rateLimiter.requestsPerMinute).toBe(60);
    });
  });

  describe('Configuration Validation', () => {
    it('should validate API key requirement', async () => {
      const invalidProvider = new TavilyProvider({ apiKey: null });
      await expect(invalidProvider.validateConfig()).rejects.toThrow('Tavily API key is required');
    });

    it('should validate base URL requirement', async () => {
      const invalidProvider = new TavilyProvider({ apiKey: 'test', baseUrl: null });
      await expect(invalidProvider.validateConfig()).rejects.toThrow('Tavily base URL is required');
    });

    it('should pass validation with valid configuration', async () => {
      // Mock successful API call for validation
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ results: [] })
      });

      await expect(provider.validateConfig()).resolves.toBe(true);
      mockFetch.mockRestore();
    });
  });

  describe('Search Functionality', () => {
    it('should perform basic search successfully', async () => {
      const mockResponse = {
        results: [
          {
            title: 'Test Result 1',
            url: 'https://example.com/1',
            content: 'Test content 1',
            score: 0.9
          },
          {
            title: 'Test Result 2',
            url: 'https://example.com/2',
            content: 'Test content 2',
            score: 0.8
          }
        ]
      };

      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await provider.search('test query');

      expect(result.query).toBe('test query');
      expect(result.results).toHaveLength(2);
      expect(result.results[0].title).toBe('Test Result 1');
      expect(result.metadata.provider).toBe('Tavily');

      mockFetch.mockRestore();
    });

    it('should handle search with options', async () => {
      const mockResponse = { results: [] };
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      await provider.search('test query', {
        maxResults: 5,
        searchDepth: 'advanced',
        includeImages: true
      });

      const fetchCall = mockFetch.mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);

      expect(requestBody.max_results).toBe(5);
      expect(requestBody.search_depth).toBe('advanced');
      expect(requestBody.include_images).toBe(true);

      mockFetch.mockRestore();
    });

    it('should handle API errors gracefully', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      });

      await expect(provider.search('test query')).rejects.toThrow('HTTP 401: Unauthorized');
      mockFetch.mockRestore();
    });

    it('should handle network errors', async () => {
      const mockFetch = spyOn(global, 'fetch').mockRejectedValue(new Error('Network error'));

      await expect(provider.search('test query')).rejects.toThrow('Tavily search failed: Network error');
      mockFetch.mockRestore();
    });
  });

  describe('Result Ranking', () => {
    it('should rank results by relevance score', async () => {
      const mockResponse = {
        results: [
          { title: 'Low Score', score: 0.3, url: 'https://example.com/low' },
          { title: 'High Score', score: 0.9, url: 'https://example.com/high' },
          { title: 'Medium Score', score: 0.6, url: 'https://example.com/medium' }
        ]
      };

      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await provider.search('test query');

      // Results should be sorted by score (highest first)
      expect(result.results[0].title).toBe('High Score');
      expect(result.results[1].title).toBe('Medium Score');
      expect(result.results[2].title).toBe('Low Score');

      mockFetch.mockRestore();
    });

    it('should filter results below relevance threshold', async () => {
      const mockResponse = {
        results: [
          { title: 'High Score', score: 0.9, url: 'https://example.com/high' },
          { title: 'Low Score', score: 0.2, url: 'https://example.com/low' }
        ]
      };

      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      // Provider has relevance threshold of 0.7 by default
      const result = await provider.search('test query');

      // Only high score result should remain
      expect(result.results).toHaveLength(1);
      expect(result.results[0].title).toBe('High Score');

      mockFetch.mockRestore();
    });
  });

  describe('Rate Limiting', () => {
    it('should respect rate limits', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ results: [] })
      });

      // Mock rate limiter to simulate rate limit exceeded
      const mockCheckLimit = spyOn(provider.rateLimiter, 'checkLimit').mockResolvedValue(false);

      await expect(provider.search('test query')).rejects.toThrow('Rate limit exceeded');

      mockCheckLimit.mockRestore();
      mockFetch.mockRestore();
    });

    it('should track request counts', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ results: [] })
      });

      const mockRecordRequest = spyOn(provider.rateLimiter, 'recordRequest').mockResolvedValue();

      await provider.search('test query');

      expect(mockRecordRequest).toHaveBeenCalled();

      mockRecordRequest.mockRestore();
      mockFetch.mockRestore();
    });
  });

  describe('Caching', () => {
    it('should cache search results', async () => {
      const mockResponse = { results: [{ title: 'Cached Result' }] };
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      // First search
      await provider.search('test query');
      
      // Second search with same query
      const result = await provider.search('test query');

      // Should only make one API call due to caching
      expect(mockFetch).toHaveBeenCalledTimes(1);
      expect(result.results[0].title).toBe('Cached Result');

      mockFetch.mockRestore();
    });

    it('should respect cache TTL', async () => {
      const mockResponse = { results: [{ title: 'Fresh Result' }] };
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      // Mock cache to simulate expired entry
      const mockGet = spyOn(provider.cache, 'get').mockReturnValue(null);

      await provider.search('test query');

      expect(mockFetch).toHaveBeenCalled();

      mockGet.mockRestore();
      mockFetch.mockRestore();
    });
  });

  describe('Statistics', () => {
    it('should track search statistics', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ results: [] })
      });

      await provider.search('test query');

      const stats = provider.getStats();
      expect(stats.totalRequests).toBeGreaterThan(0);
      expect(stats.provider).toBe('Tavily');

      mockFetch.mockRestore();
    });
  });
});
