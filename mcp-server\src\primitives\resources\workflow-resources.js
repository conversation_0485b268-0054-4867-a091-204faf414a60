/**
 * Workflow Resources Handler
 * Handles MCP Resource requests for workflow-related data
 */

import { URITemplate<PERSON>arser, MCPResourceURI } from '../../utils/uri-template-parser.js';
import { RESOURCE_TEMPLATES } from '../../schemas/resource-schemas.js';

/**
 * Workflow Resource Handler
 * Converts existing workflow tools to MCP Resources
 */
export class WorkflowResourceHandler {
  constructor(existingWorkflowManager) {
    this.workflowManager = existingWorkflowManager;
    this.templates = {
      'workflow_definition': RESOURCE_TEMPLATES.workflow_definition,
      'workflow_execution': RESOURCE_TEMPLATES.workflow_execution,
      'workflow_metrics': RESOURCE_TEMPLATES.workflow_metrics
    };
  }

  /**
   * Get resource templates for this handler
   * @returns {Array} Array of resource templates
   */
  getResourceTemplates() {
    return Object.values(this.templates);
  }

  /**
   * Handle resource read request
   * @param {string} uri - Resource URI
   * @returns {object} Resource content
   */
  async handleResourceRead(uri) {
    const parsed = MCPResourceURI.parse(uri);
    if (!parsed || parsed.type !== 'workflow') {
      throw new Error(`Invalid workflow resource URI: ${uri}`);
    }

    const { id: workflowId, subpath } = parsed;

    try {
      switch (subpath) {
        case 'definition':
          return await this.getWorkflowDefinition(workflowId, uri);
        case 'metrics':
          return await this.getWorkflowMetrics(workflowId, uri);
        default:
          // Handle parameterized paths
          return await this.handleParameterizedPath(workflowId, subpath, uri);
      }
    } catch (error) {
      throw new Error(`Failed to read workflow resource: ${error.message}`);
    }
  }

  /**
   * Handle parameterized resource paths
   * @param {string} workflowId - Workflow ID
   * @param {string} subpath - Resource subpath
   * @param {string} uri - Full URI
   * @returns {object} Resource content
   */
  async handleParameterizedPath(workflowId, subpath, uri) {
    // Handle execution-specific resources
    const executionMatch = subpath.match(/^execution\/([^/]+)$/);
    if (executionMatch) {
      const executionId = decodeURIComponent(executionMatch[1]);
      return await this.getWorkflowExecution(workflowId, executionId, uri);
    }

    // Handle metrics with timeframe
    const metricsMatch = subpath.match(/^metrics\/([^/]+)$/);
    if (metricsMatch) {
      const timeframe = decodeURIComponent(metricsMatch[1]);
      return await this.getWorkflowMetrics(workflowId, uri, timeframe);
    }

    throw new Error(`Unknown workflow resource path: ${subpath}`);
  }

  /**
   * Get workflow definition data
   * @param {string} workflowId - Workflow ID
   * @param {string} uri - Resource URI
   * @returns {object} Workflow definition resource
   */
  async getWorkflowDefinition(workflowId, uri) {
    try {
      const definition = await this.workflowManager.getWorkflowDefinition({ 
        workflowId 
      });
      
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          workflowId,
          name: definition.name || 'Unknown Workflow',
          description: definition.description || '',
          version: definition.version || '1.0',
          type: definition.type || 'sequential',
          status: definition.status || 'active',
          steps: definition.steps || [],
          configuration: {
            timeout: definition.timeout || 3600,
            retryPolicy: definition.retryPolicy || 'exponential',
            maxRetries: definition.maxRetries || 3,
            parallelism: definition.parallelism || 1,
            ...definition.configuration
          },
          triggers: definition.triggers || [],
          dependencies: definition.dependencies || [],
          outputs: definition.outputs || [],
          metadata: {
            resourceType: 'workflow_definition',
            version: '1.0',
            created: definition.created || new Date().toISOString(),
            updated: definition.updated || new Date().toISOString(),
            author: definition.author || 'system',
            lastAccessed: new Date().toISOString(),
            ...definition.metadata
          }
        }, null, 2)
      };
    } catch (error) {
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          workflowId,
          name: 'Workflow Not Found',
          description: 'Workflow definition not available',
          version: '1.0',
          type: 'unknown',
          status: 'not_found',
          steps: [],
          configuration: {},
          triggers: [],
          dependencies: [],
          outputs: [],
          metadata: {
            resourceType: 'workflow_definition',
            version: '1.0',
            error: error.message,
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    }
  }

  /**
   * Get workflow execution data
   * @param {string} workflowId - Workflow ID
   * @param {string} executionId - Execution ID
   * @param {string} uri - Resource URI
   * @returns {object} Workflow execution resource
   */
  async getWorkflowExecution(workflowId, executionId, uri) {
    try {
      const execution = await this.workflowManager.getWorkflowExecution({ 
        workflowId,
        executionId 
      });
      
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          executionId,
          workflowId,
          status: execution.status || 'unknown',
          started: execution.started || new Date().toISOString(),
          completed: execution.completed,
          duration: execution.duration || 0,
          progress: {
            current: execution.currentStep || 0,
            total: execution.totalSteps || 0,
            percentage: execution.progress || 0
          },
          steps: execution.steps || [],
          results: execution.results || {},
          errors: execution.errors || [],
          logs: execution.logs || [],
          metrics: {
            executionTime: execution.executionTime || 0,
            resourceUsage: execution.resourceUsage || {},
            throughput: execution.throughput || 0,
            errorRate: execution.errorRate || 0
          },
          metadata: {
            resourceType: 'workflow_execution',
            version: '1.0',
            triggeredBy: execution.triggeredBy || 'system',
            environment: execution.environment || 'production',
            lastAccessed: new Date().toISOString(),
            ...execution.metadata
          }
        }, null, 2)
      };
    } catch (error) {
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          executionId,
          workflowId,
          status: 'error',
          started: new Date().toISOString(),
          completed: null,
          duration: 0,
          progress: { current: 0, total: 0, percentage: 0 },
          steps: [],
          results: {},
          errors: [{ message: error.message, timestamp: new Date().toISOString() }],
          logs: [],
          metrics: {},
          metadata: {
            resourceType: 'workflow_execution',
            version: '1.0',
            error: error.message,
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    }
  }

  /**
   * Get workflow metrics data
   * @param {string} workflowId - Workflow ID
   * @param {string} uri - Resource URI
   * @param {string|null} timeframe - Optional timeframe filter
   * @returns {object} Workflow metrics resource
   */
  async getWorkflowMetrics(workflowId, uri, timeframe = null) {
    try {
      const metrics = await this.workflowManager.getWorkflowMetrics({ 
        workflowId,
        timeframe: timeframe || 'all'
      });
      
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          workflowId,
          timeframe: timeframe || 'all',
          collected: new Date().toISOString(),
          performance: {
            totalExecutions: metrics.totalExecutions || 0,
            successRate: metrics.successRate || 0,
            averageDuration: metrics.averageDuration || 0,
            throughput: metrics.throughput || 0,
            errorRate: metrics.errorRate || 0
          },
          trends: {
            executions: metrics.executionTrends || [],
            performance: metrics.performanceTrends || [],
            errors: metrics.errorTrends || []
          },
          bottlenecks: metrics.bottlenecks || [],
          optimizations: metrics.optimizations || [],
          alerts: metrics.alerts || [],
          metadata: {
            resourceType: 'workflow_metrics',
            version: '1.0',
            collectionPeriod: metrics.collectionPeriod || '24h',
            lastAccessed: new Date().toISOString(),
            ...metrics.metadata
          }
        }, null, 2)
      };
    } catch (error) {
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          workflowId,
          timeframe: timeframe || 'all',
          collected: new Date().toISOString(),
          performance: {},
          trends: {},
          bottlenecks: [],
          optimizations: [],
          alerts: [],
          metadata: {
            resourceType: 'workflow_metrics',
            version: '1.0',
            error: error.message,
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    }
  }

  /**
   * Check if this handler can handle a given URI
   * @param {string} uri - URI to check
   * @returns {boolean} True if this handler can process the URI
   */
  canHandle(uri) {
    const parsed = MCPResourceURI.parse(uri);
    return parsed && parsed.type === 'workflow';
  }
}

export default WorkflowResourceHandler;
