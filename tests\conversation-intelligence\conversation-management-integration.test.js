/**
 * Tests for Conversation Management Integration (Task 3.4)
 * 
 * Tests the integration between conversation intelligence and WLR-004 conversation management
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

// Mock dependencies before importing the main module
vi.mock('../../src/conversation-intelligence/context-analysis-engine.js', () => ({
  ContextAnalysisEngine: vi.fn()
}));
vi.mock('../../src/conversation-intelligence/research-timing-patterns.js', () => ({
  ResearchTimingPatterns: vi.fn()
}));
vi.mock('../../src/conversation-intelligence/autonomous-research-orchestrator.js', () => ({
  AutonomousResearchOrchestrator: vi.fn()
}));
vi.mock('../../src/workflow-logic/session-recovery-manager.js', () => ({
  getSessionRecoveryManager: vi.fn()
}));
vi.mock('../../src/workflow-logic/context-manager.js', () => ({
  ContextManager: vi.fn()
}));

import { ConversationIntelligenceManager } from '../../src/conversation-intelligence/conversation-management-integration.js';
import { getSessionRecoveryManager } from '../../src/workflow-logic/session-recovery-manager.js';
import { ContextManager } from '../../src/workflow-logic/context-manager.js';

describe('ConversationIntelligenceManager', () => {
  let manager;
  let mockSessionManager;
  let mockContextManager;
  let mockContextAnalysisEngine;
  let mockResearchTimingPatterns;
  let mockAutonomousResearchOrchestrator;

  beforeEach(() => {
    // Setup mocks
    mockSessionManager = {
      getSession: vi.fn(),
      updateSession: vi.fn(),
      updateLastMessage: vi.fn()
    };

    mockContextManager = {
      updateOrientation: vi.fn()
    };

    mockContextAnalysisEngine = {
      analyzeContext: vi.fn()
    };

    mockResearchTimingPatterns = {
      analyzeTimingPatterns: vi.fn(),
      on: vi.fn(),
      emit: vi.fn()
    };

    mockAutonomousResearchOrchestrator = {
      start: vi.fn(),
      stop: vi.fn(),
      submitResearch: vi.fn(),
      getStats: vi.fn(),
      on: vi.fn(),
      emit: vi.fn()
    };

    // Mock factory functions
    getSessionRecoveryManager.mockReturnValue(mockSessionManager);
    ContextManager.mockImplementation(() => mockContextManager);

    // Create manager instance
    manager = new ConversationIntelligenceManager({
      projectRoot: '/test/project'
    });

    // Override mocked components
    manager.contextAnalysisEngine = mockContextAnalysisEngine;
    manager.researchTimingPatterns = mockResearchTimingPatterns;
    manager.autonomousResearchOrchestrator = mockAutonomousResearchOrchestrator;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('initializeConversationIntelligence', () => {
    it('should initialize conversation intelligence for a session', async () => {
      const sessionId = 'test-session-123';
      const mockSession = {
        sessionId,
        projectContext: { projectName: 'Test Project' },
        status: 'active'
      };

      mockSessionManager.getSession.mockResolvedValue(mockSession);

      const result = await manager.initializeConversationIntelligence(sessionId, {
        enableIntelligence: true,
        enableBackgroundResearch: true
      });

      expect(result).toBe(true);
      expect(mockSessionManager.getSession).toHaveBeenCalledWith(sessionId);
      expect(mockAutonomousResearchOrchestrator.start).toHaveBeenCalled();
      expect(manager.activeConversations.has(sessionId)).toBe(true);
    });

    it('should handle session not found error', async () => {
      const sessionId = 'nonexistent-session';
      mockSessionManager.getSession.mockResolvedValue(null);

      const result = await manager.initializeConversationIntelligence(sessionId);

      expect(result).toBe(false);
      expect(manager.activeConversations.has(sessionId)).toBe(false);
    });
  });

  describe('processConversationMessage', () => {
    beforeEach(async () => {
      const sessionId = 'test-session-123';
      const mockSession = { sessionId, status: 'active' };
      mockSessionManager.getSession.mockResolvedValue(mockSession);
      
      await manager.initializeConversationIntelligence(sessionId);
    });

    it('should process conversation message with intelligence', async () => {
      const sessionId = 'test-session-123';
      const message = {
        content: 'I want to build a restaurant app',
        sender: 'user',
        type: 'text'
      };

      const mockAnalysis = {
        contextAnalysis: {
          projectContext: {
            domain: 'restaurant',
            businessModel: 'marketplace',
            confidence: 0.8
          },
          informationGaps: []
        },
        timingAnalysis: {
          researchTriggers: [],
          milestonesDetected: 1
        },
        analyzedAt: new Date().toISOString()
      };

      mockContextAnalysisEngine.analyzeContext.mockResolvedValue(mockAnalysis.contextAnalysis);
      mockResearchTimingPatterns.analyzeTimingPatterns.mockReturnValue(mockAnalysis.timingAnalysis);

      const result = await manager.processConversationMessage(sessionId, message);

      expect(result.messageProcessed).toBe(true);
      expect(result.sessionId).toBe(sessionId);
      expect(result.intelligenceResults).toBeDefined();
      expect(mockSessionManager.updateLastMessage).toHaveBeenCalled();
    });

    it('should handle conversation intelligence not initialized', async () => {
      const sessionId = 'uninitialized-session';
      const message = { content: 'test message' };

      await expect(manager.processConversationMessage(sessionId, message))
        .rejects.toThrow('Conversation intelligence not initialized');
    });
  });

  describe('analyzeConversationIntelligence', () => {
    beforeEach(async () => {
      const sessionId = 'test-session-123';
      const mockSession = { sessionId, status: 'active' };
      mockSessionManager.getSession.mockResolvedValue(mockSession);
      
      await manager.initializeConversationIntelligence(sessionId);
    });

    it('should perform comprehensive conversation analysis', async () => {
      const sessionId = 'test-session-123';
      
      const mockContextAnalysis = {
        projectContext: {
          domain: 'ecommerce',
          businessModel: 'b2c',
          confidence: 0.9
        },
        informationGaps: [
          { type: 'market_research', priority: 1 }
        ]
      };

      const mockTimingAnalysis = {
        researchTriggers: [
          { id: 'trigger-1', type: 'market_research' }
        ],
        milestonesDetected: 2
      };

      mockContextAnalysisEngine.analyzeContext.mockResolvedValue(mockContextAnalysis);
      mockResearchTimingPatterns.analyzeTimingPatterns.mockReturnValue(mockTimingAnalysis);

      const result = await manager.analyzeConversationIntelligence(sessionId);

      expect(result.contextAnalysis).toEqual(mockContextAnalysis);
      expect(result.timingAnalysis).toEqual(mockTimingAnalysis);
      expect(result.analyzedAt).toBeDefined();
      expect(mockSessionManager.updateSession).toHaveBeenCalled();
    });
  });

  describe('checkResearchTriggers', () => {
    beforeEach(async () => {
      const sessionId = 'test-session-123';
      const mockSession = { sessionId, status: 'active' };
      mockSessionManager.getSession.mockResolvedValue(mockSession);
      
      await manager.initializeConversationIntelligence(sessionId);
      
      // Set up analysis results
      const conversationState = manager.activeConversations.get(sessionId);
      conversationState.lastAnalysis = {
        timingAnalysis: {
          researchTriggers: [
            {
              id: 'trigger-1',
              type: 'market_research',
              description: 'Market analysis needed',
              priority: 'high'
            }
          ]
        }
      };
    });

    it('should submit research requests for new triggers', async () => {
      const sessionId = 'test-session-123';
      const mockJobId = 'job-123';
      
      mockAutonomousResearchOrchestrator.submitResearch.mockReturnValue(mockJobId);

      const researchJobs = await manager.checkResearchTriggers(sessionId);

      expect(researchJobs).toHaveLength(1);
      expect(researchJobs[0].jobId).toBe(mockJobId);
      expect(researchJobs[0].triggerId).toBe('trigger-1');
      expect(mockAutonomousResearchOrchestrator.submitResearch).toHaveBeenCalled();
    });

    it('should not submit duplicate research requests', async () => {
      const sessionId = 'test-session-123';
      
      // First submission
      mockAutonomousResearchOrchestrator.submitResearch.mockReturnValue('job-123');
      await manager.checkResearchTriggers(sessionId);

      // Second submission should not create new job
      const researchJobs = await manager.checkResearchTriggers(sessionId);
      
      expect(researchJobs).toHaveLength(0);
      expect(mockAutonomousResearchOrchestrator.submitResearch).toHaveBeenCalledTimes(1);
    });
  });

  describe('maintainConversationContinuity', () => {
    beforeEach(async () => {
      const sessionId = 'test-session-123';
      const mockSession = { sessionId, status: 'active' };
      mockSessionManager.getSession.mockResolvedValue(mockSession);
      
      await manager.initializeConversationIntelligence(sessionId);
    });

    it('should maintain continuity with active background jobs', async () => {
      const sessionId = 'test-session-123';
      const conversationState = manager.activeConversations.get(sessionId);
      
      // Add active research job
      conversationState.activeResearchJobs.push({
        jobId: 'job-123',
        status: 'processing',
        trigger: { type: 'market_research' }
      });

      const result = await manager.maintainConversationContinuity(sessionId);

      expect(result.continuityMaintained).toBe(true);
      expect(result.activeBackgroundJobs).toBe(1);
      expect(mockSessionManager.updateLastMessage).toHaveBeenCalledWith(
        sessionId,
        'Background research in progress',
        '1 research jobs running in background'
      );
    });

    it('should handle no active background jobs', async () => {
      const sessionId = 'test-session-123';

      const result = await manager.maintainConversationContinuity(sessionId);

      expect(result.continuityMaintained).toBe(true);
      expect(result.activeBackgroundJobs).toBe(0);
      expect(result.message).toBe('No background activity');
    });
  });

  describe('getConversationIntelligenceStatus', () => {
    it('should return status for active conversation', async () => {
      const sessionId = 'test-session-123';
      const mockSession = { sessionId, status: 'active' };
      mockSessionManager.getSession.mockResolvedValue(mockSession);
      
      await manager.initializeConversationIntelligence(sessionId);

      const status = manager.getConversationIntelligenceStatus(sessionId);

      expect(status).toEqual({
        sessionId,
        intelligenceEnabled: true,
        backgroundResearchEnabled: true,
        conversationLength: 0,
        lastAnalysis: null,
        activeResearchJobs: 0,
        completedResearchJobs: 0,
        status: 'active'
      });
    });

    it('should return null for non-existent conversation', () => {
      const status = manager.getConversationIntelligenceStatus('nonexistent');
      expect(status).toBeNull();
    });
  });

  describe('cleanupConversationIntelligence', () => {
    it('should cleanup conversation state and cancel jobs', async () => {
      const sessionId = 'test-session-123';
      const mockSession = { sessionId, status: 'active' };
      mockSessionManager.getSession.mockResolvedValue(mockSession);
      
      await manager.initializeConversationIntelligence(sessionId);

      const result = await manager.cleanupConversationIntelligence(sessionId);

      expect(result).toBe(true);
      expect(manager.activeConversations.has(sessionId)).toBe(false);
      expect(manager.conversationHistory.has(sessionId)).toBe(false);
      expect(mockSessionManager.updateLastMessage).toHaveBeenCalledWith(
        sessionId,
        'Conversation intelligence cleanup completed',
        'Background research stopped and state cleaned up'
      );
    });
  });
});
