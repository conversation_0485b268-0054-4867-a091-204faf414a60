/**
 * Demo test for Research-Enhanced Visual Generation
 */

import { describe, it, expect } from 'bun:test';
import { ComponentMapper } from '../../src/visual-generation/generators/component-mapper.js';
import { ASCIILayoutEngine } from '../../src/visual-generation/generators/ascii-layout-engine.js';

describe('Research-Enhanced Wireframe Demo', () => {
  it('should generate research-enhanced wireframe from requirements', async () => {
    const componentMapper = new ComponentMapper({
      enableResearch: false, // Disabled for demo to avoid API calls
      intelligence: {
        enableResearch: false
      }
    });
    
    const layoutEngine = new ASCIILayoutEngine();

    // Healthcare application requirements
    const requirements = [
      'Patient portal with secure login using email and password',
      'Medical records table with patient data, search and filtering',
      'Appointment booking form with date picker and validation',
      'Dashboard showing patient metrics and health summaries'
    ];

    const context = {
      industry: 'healthcare',
      platform: 'web',
      accessibility: 'WCAG_AA',
      userTypes: ['patients', 'doctors', 'nurses']
    };

    // Map requirements to components with intelligence
    const components = await componentMapper.mapRequirementsToComponents(
      requirements,
      [],
      context
    );

    // Generate ASCII layout
    const layout = layoutEngine.generateLayout(components, {
      breakpoint: 'desktop',
      showLabels: true
    });

    console.log('\n🏥 HEALTHCARE PATIENT PORTAL WIREFRAME:');
    console.log('=' .repeat(80));
    console.log(layout.ascii);
    console.log('=' .repeat(80));

    // Verify components were created
    expect(components.length).toBeGreaterThan(0);
    expect(layout.ascii).toBeDefined();
    expect(layout.ascii.length).toBeGreaterThan(100);

    // Check for healthcare-specific components
    const hasLoginForm = components.some(c => c.type === 'form' && c.name.toLowerCase().includes('login'));
    const hasDataTable = components.some(c => c.type === 'table');
    const hasDashboard = components.some(c => c.type === 'dashboard');

    expect(hasLoginForm).toBe(true);
    expect(hasDataTable).toBe(true);
    expect(hasDashboard).toBe(true);

    // Show individual enhanced components
    console.log('\n📋 ENHANCED COMPONENTS WITH INTELLIGENCE:');
    console.log('-'.repeat(80));
    
    for (const component of components.slice(0, 3)) { // Show first 3 components
      console.log(`\n🔧 ${component.name} (${component.type})`);
      
      if (component.bestPractices) {
        console.log('✨ Best Practices Applied:');
        for (const practice of component.bestPractices.slice(0, 2)) {
          console.log(`   • ${practice.practice || practice}`);
        }
      }
      
      if (component.accessibility) {
        console.log('♿ Accessibility Features:');
        if (component.accessibility.ariaAttributes) {
          console.log(`   • ARIA: ${component.accessibility.ariaAttributes.slice(0, 3).join(', ')}`);
        }
        if (component.accessibility.keyboardNavigation) {
          console.log(`   • Keyboard: ${component.accessibility.keyboardNavigation.slice(0, 1).join('')}`);
        }
      }
      
      if (component.intelligence && component.intelligence.recommendations) {
        console.log('💡 Intelligence Recommendations:');
        for (const rec of component.intelligence.recommendations.slice(0, 2)) {
          console.log(`   • ${rec}`);
        }
      }
      
      console.log('-'.repeat(40));
    }

    // Test mobile responsive version
    const mobileLayout = layoutEngine.generateLayout(components, {
      breakpoint: 'mobile',
      showLabels: true
    });

    console.log('\n📱 MOBILE RESPONSIVE VERSION:');
    console.log('=' .repeat(50));
    console.log(mobileLayout.ascii);
    console.log('=' .repeat(50));

    expect(mobileLayout.ascii).toContain('WIREFRAME - MOBILE');
    expect(mobileLayout.metadata.breakpoint).toBe('mobile');
  });

  it('should demonstrate different industry contexts', async () => {
    const componentMapper = new ComponentMapper({
      enableResearch: false,
      intelligence: { enableResearch: false }
    });

    // E-commerce requirements
    const ecommerceRequirements = [
      'Product catalog with search and filtering',
      'Shopping cart with checkout process',
      'User account management with order history'
    ];

    const ecommerceComponents = await componentMapper.mapRequirementsToComponents(
      ecommerceRequirements,
      [],
      { industry: 'ecommerce', platform: 'web' }
    );

    // Financial services requirements  
    const financeRequirements = [
      'Secure login with two-factor authentication',
      'Account dashboard with transaction history',
      'Money transfer form with validation'
    ];

    const financeComponents = await componentMapper.mapRequirementsToComponents(
      financeRequirements,
      [],
      { industry: 'finance', platform: 'web' }
    );

    console.log('\n🛒 E-COMMERCE COMPONENTS:');
    ecommerceComponents.forEach(c => {
      console.log(`   • ${c.name} (${c.type})`);
      if (c.intelligence && c.intelligence.recommendations) {
        console.log(`     💡 ${c.intelligence.recommendations[0] || 'Standard e-commerce practices'}`);
      }
    });

    console.log('\n💰 FINANCIAL SERVICES COMPONENTS:');
    financeComponents.forEach(c => {
      console.log(`   • ${c.name} (${c.type})`);
      if (c.intelligence && c.intelligence.recommendations) {
        console.log(`     💡 ${c.intelligence.recommendations[0] || 'Financial security practices'}`);
      }
    });

    expect(ecommerceComponents.length).toBeGreaterThan(0);
    expect(financeComponents.length).toBeGreaterThan(0);
  });
});
