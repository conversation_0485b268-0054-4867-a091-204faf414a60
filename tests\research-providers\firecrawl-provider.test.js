/**
 * Firecrawl Research Provider Unit Tests
 * 
 * Tests for the FirecrawlProvider class including web scraping,
 * content extraction, and bulk processing capabilities.
 */

import { describe, it, expect, beforeEach, afterEach, mock, spyOn } from 'bun:test';
import { FirecrawlProvider } from '../../src/research-providers/firecrawl-provider.js';

describe('FirecrawlProvider', () => {
  let provider;
  let mockConfig;

  beforeEach(() => {
    mockConfig = {
      apiKey: 'test-api-key',
      baseUrl: 'https://api.firecrawl.dev',
      rateLimit: {
        requestsPerMinute: 100,
        concurrentBrowsers: 5
      },
      defaultOptions: {
        formats: ['markdown', 'html'],
        onlyMainContent: true,
        timeout: 30000
      }
    };
    provider = new FirecrawlProvider(mockConfig);
  });

  afterEach(() => {
    mock.restore();
  });

  describe('Constructor', () => {
    it('should initialize with correct configuration', () => {
      expect(provider.name).toBe('Firecrawl');
      expect(provider.baseUrl).toBe('https://api.firecrawl.dev');
      expect(provider.apiKey).toBe('test-api-key');
    });

    it('should use default configuration when not provided', () => {
      const defaultProvider = new FirecrawlProvider();
      expect(defaultProvider.name).toBe('Firecrawl');
      expect(defaultProvider.baseUrl).toBe('https://api.firecrawl.dev');
    });

    it('should initialize content processor', () => {
      expect(provider.processor).toBeDefined();
      expect(provider.processor.maxContentLength).toBe(100000);
    });
  });

  describe('Configuration Validation', () => {
    it('should validate API key requirement', async () => {
      const invalidProvider = new FirecrawlProvider({ apiKey: null });
      await expect(invalidProvider.validateConfig()).rejects.toThrow('Firecrawl API key is required');
    });

    it('should validate base URL requirement', async () => {
      const invalidProvider = new FirecrawlProvider({ apiKey: 'test', baseUrl: null });
      await expect(invalidProvider.validateConfig()).rejects.toThrow('Firecrawl base URL is required');
    });

    it('should pass validation with successful scrape test', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { content: 'Test content' }
        })
      });

      await expect(provider.validateConfig()).resolves.toBe(true);
      mockFetch.mockRestore();
    });
  });

  describe('Web Scraping', () => {
    it('should scrape single URL successfully', async () => {
      const mockResponse = {
        success: true,
        data: {
          content: '# Test Page\n\nThis is test content.',
          metadata: {
            title: 'Test Page',
            description: 'A test page',
            url: 'https://example.com'
          }
        }
      };

      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await provider.scrapeUrl('https://example.com');

      expect(result.url).toBe('https://example.com');
      expect(result.content).toBe('# Test Page\n\nThis is test content.');
      expect(result.metadata.title).toBe('Test Page');
      expect(result.metadata.provider).toBe('Firecrawl');

      mockFetch.mockRestore();
    });

    it('should handle scraping options', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { content: 'Content' }
        })
      });

      await provider.scrapeUrl('https://example.com', {
        formats: ['markdown'],
        onlyMainContent: false,
        waitFor: 5000
      });

      const fetchCall = mockFetch.mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);

      expect(requestBody.formats).toEqual(['markdown']);
      expect(requestBody.onlyMainContent).toBe(false);
      expect(requestBody.waitFor).toBe(5000);

      mockFetch.mockRestore();
    });

    it('should handle scraping errors', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: false,
        status: 400,
        statusText: 'Bad Request'
      });

      await expect(provider.scrapeUrl('https://invalid-url')).rejects.toThrow('HTTP 400: Bad Request');
      mockFetch.mockRestore();
    });

    it('should handle API error responses', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: false,
          error: 'URL not accessible'
        })
      });

      await expect(provider.scrapeUrl('https://blocked-url.com')).rejects.toThrow('Firecrawl scrape failed: URL not accessible');
      mockFetch.mockRestore();
    });
  });

  describe('Search Functionality', () => {
    it('should search by scraping URL', async () => {
      const mockResponse = {
        success: true,
        data: {
          content: '# Search Results\n\nFound relevant content.',
          metadata: { title: 'Search Results' }
        }
      };

      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const result = await provider.search('https://example.com/search');

      expect(result.query).toBe('https://example.com/search');
      expect(result.results).toHaveLength(1);
      expect(result.results[0].content).toBe('# Search Results\n\nFound relevant content.');
      expect(result.metadata.type).toBe('web_content_extraction');

      mockFetch.mockRestore();
    });

    it('should handle search failures', async () => {
      const mockFetch = spyOn(global, 'fetch').mockRejectedValue(new Error('Network error'));

      const result = await provider.search('https://unreachable.com');

      expect(result.query).toBe('https://unreachable.com');
      expect(result.results).toHaveLength(0);
      expect(result.error).toContain('Network error');

      mockFetch.mockRestore();
    });
  });

  describe('Bulk Processing', () => {
    it('should process multiple URLs', async () => {
      const mockResponse = {
        success: true,
        data: {
          content: 'Page content',
          metadata: { title: 'Page Title' }
        }
      };

      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const urls = ['https://example.com/1', 'https://example.com/2'];
      const results = await provider.bulkScrape(urls);

      expect(results).toHaveLength(2);
      expect(results[0].url).toBe('https://example.com/1');
      expect(results[1].url).toBe('https://example.com/2');
      expect(mockFetch).toHaveBeenCalledTimes(2);

      mockFetch.mockRestore();
    });

    it('should handle partial failures in bulk processing', async () => {
      const mockFetch = spyOn(global, 'fetch')
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { content: 'Success 1' }
          })
        })
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            success: true,
            data: { content: 'Success 2' }
          })
        });

      const urls = ['https://example.com/1', 'https://fail.com', 'https://example.com/2'];
      const results = await provider.bulkScrape(urls);

      // Should have 2 successful results and 1 error
      const successful = results.filter(r => !r.error);
      const failed = results.filter(r => r.error);

      expect(successful).toHaveLength(2);
      expect(failed).toHaveLength(1);

      mockFetch.mockRestore();
    });

    it('should respect concurrency limits', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { content: 'Content' }
        })
      });

      const urls = Array.from({ length: 10 }, (_, i) => `https://example.com/${i}`);
      
      const startTime = Date.now();
      await provider.bulkScrape(urls, { concurrency: 2 });
      const endTime = Date.now();

      // With concurrency limit of 2, should take longer than if all were parallel
      expect(mockFetch).toHaveBeenCalledTimes(10);
      
      mockFetch.mockRestore();
    });
  });

  describe('Content Processing', () => {
    it('should process and filter content', () => {
      const rawContent = `
        <html>
          <head><title>Test</title></head>
          <body>
            <nav>Navigation</nav>
            <main>
              <h1>Main Content</h1>
              <p>This is the main content.</p>
            </main>
            <footer>Footer</footer>
          </body>
        </html>
      `;

      const processed = provider.processor.processContent(rawContent, {
        removeBoilerplate: true,
        extractMainContent: true
      });

      expect(processed.content).toContain('Main Content');
      expect(processed.content).not.toContain('Navigation');
      expect(processed.content).not.toContain('Footer');
    });

    it('should extract structured data', () => {
      const htmlContent = `
        <article>
          <h1>Article Title</h1>
          <p>Article content</p>
          <table>
            <tr><th>Header</th></tr>
            <tr><td>Data</td></tr>
          </table>
        </article>
      `;

      const structured = provider.processor.extractStructuredData(htmlContent);

      expect(structured.headers).toContain('Article Title');
      expect(structured.tables).toHaveLength(1);
      expect(structured.paragraphs).toContain('Article content');
    });
  });

  describe('Rate Limiting and Performance', () => {
    it('should respect rate limits', async () => {
      const mockCheckLimit = spyOn(provider.rateLimiter, 'checkLimit').mockResolvedValue(false);

      await expect(provider.scrapeUrl('https://example.com')).rejects.toThrow('Rate limit exceeded');

      mockCheckLimit.mockRestore();
    });

    it('should track performance metrics', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          success: true,
          data: { content: 'Content' }
        })
      });

      await provider.scrapeUrl('https://example.com');

      const stats = provider.getStats();
      expect(stats.provider).toBe('Firecrawl');
      expect(stats.totalRequests).toBeGreaterThan(0);
      expect(stats.averageResponseTime).toBeGreaterThan(0);

      mockFetch.mockRestore();
    });
  });

  describe('Error Handling', () => {
    it('should handle timeout errors', async () => {
      const mockFetch = spyOn(global, 'fetch').mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      await expect(provider.scrapeUrl('https://slow-site.com')).rejects.toThrow('Request timeout');

      mockFetch.mockRestore();
    });

    it('should emit error events', async () => {
      const mockFetch = spyOn(global, 'fetch').mockRejectedValue(new Error('Test error'));
      
      let errorEmitted = false;
      provider.on('scrapingError', () => {
        errorEmitted = true;
      });

      try {
        await provider.scrapeUrl('https://error.com');
      } catch (error) {
        // Expected to throw
      }

      expect(errorEmitted).toBe(true);

      mockFetch.mockRestore();
    });
  });
});
