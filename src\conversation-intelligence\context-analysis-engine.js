/**
 * Context Analysis Engine
 * Extracts project domain, business model, feature priorities from conversation
 * Identifies information gaps requiring research and determines optimal research timing
 */

import { performAIOperation } from '../ai-integration/ai-services-unified.js';

/**
 * Project context extractor for identifying key business information
 */
export class ProjectContextExtractor {
  constructor(config = {}) {
    this.config = config;
    this.aiModel = config.aiModel || 'analysis';
    this.confidenceThreshold = config.confidenceThreshold || 0.7;
  }

  /**
   * Extract project domain and business model from conversation
   * @param {Array} conversationHistory - Array of conversation messages
   * @returns {Promise<object>} Extracted project context
   */
  async extractProjectContext(conversationHistory) {
    const contextPrompt = this.createContextExtractionPrompt(conversationHistory);
    
    try {
      const aiResponse = await performAIOperation(this.aiModel, contextPrompt, {
        temperature: 0.2
      });

      const context = this.parseContextResponse(aiResponse.text);
      return this.validateAndEnrichContext(context, conversationHistory);
    } catch (error) {
      console.error('Context extraction failed:', error);
      return this.fallbackContextExtraction(conversationHistory);
    }
  }

  /**
   * Create prompt for AI-powered context extraction
   * @param {Array} conversationHistory - Conversation messages
   * @returns {string} Context extraction prompt
   */
  createContextExtractionPrompt(conversationHistory) {
    const recentMessages = conversationHistory.slice(-20); // Last 20 messages
    const conversationText = recentMessages.map(msg => 
      `${msg.role}: ${msg.content}`
    ).join('\n');

    return `
Analyze the following conversation and extract key project information:

CONVERSATION:
${conversationText}

Extract and return a JSON object with the following structure:
{
  "projectDomain": "string - the industry/domain (e.g., 'restaurant', 'ecommerce', 'healthcare')",
  "businessModel": "string - the business model (e.g., 'B2B SaaS', 'marketplace', 'direct-to-consumer')",
  "projectType": "string - type of project (e.g., 'mobile app', 'web platform', 'API service')",
  "targetAudience": "string - primary users/customers",
  "keyFeatures": ["array of main features mentioned"],
  "businessGoals": ["array of business objectives"],
  "technicalRequirements": ["array of technical needs mentioned"],
  "constraints": {
    "budget": "string - budget constraints if mentioned",
    "timeline": "string - timeline constraints if mentioned",
    "technical": ["array of technical constraints"]
  },
  "maturityLevel": "string - project stage (e.g., 'idea', 'planning', 'development', 'launch')",
  "confidence": "number - confidence in extraction (0-1)"
}

Focus on explicit information mentioned in the conversation. If information is not clearly stated, use null or empty arrays.
`;
  }

  /**
   * Parse AI response into structured context
   * @param {string|object} aiResponse - AI response
   * @returns {object} Parsed context
   */
  parseContextResponse(aiResponse) {
    try {
      if (typeof aiResponse === 'string') {
        // Try to extract JSON from string response
        const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[0]);
        }
      } else if (typeof aiResponse === 'object') {
        return aiResponse;
      }
    } catch (error) {
      console.error('Failed to parse context response:', error);
    }
    
    return this.getDefaultContext();
  }

  /**
   * Validate and enrich extracted context
   * @param {object} context - Extracted context
   * @param {Array} conversationHistory - Original conversation
   * @returns {object} Validated and enriched context
   */
  validateAndEnrichContext(context, conversationHistory) {
    const enriched = {
      ...this.getDefaultContext(),
      ...context,
      extractedAt: new Date().toISOString(),
      conversationLength: conversationHistory.length,
      lastUpdated: new Date().toISOString()
    };

    // Validate confidence
    if (!enriched.confidence || enriched.confidence < 0 || enriched.confidence > 1) {
      enriched.confidence = this.calculateConfidence(enriched, conversationHistory);
    }

    // Enrich with derived insights
    enriched.insights = this.generateInsights(enriched);
    enriched.researchNeeds = this.identifyResearchNeeds(enriched);

    return enriched;
  }

  /**
   * Calculate confidence score for extracted context
   * @param {object} context - Extracted context
   * @param {Array} conversationHistory - Conversation history
   * @returns {number} Confidence score (0-1)
   */
  calculateConfidence(context, conversationHistory) {
    let confidence = 0.3; // Base confidence

    // Domain specificity
    if (context.projectDomain && context.projectDomain !== 'unknown') {
      confidence += 0.2;
    }

    // Business model clarity
    if (context.businessModel && context.businessModel !== 'unknown') {
      confidence += 0.15;
    }

    // Feature details
    if (context.keyFeatures && context.keyFeatures.length > 0) {
      confidence += Math.min(context.keyFeatures.length * 0.05, 0.2);
    }

    // Technical requirements
    if (context.technicalRequirements && context.technicalRequirements.length > 0) {
      confidence += Math.min(context.technicalRequirements.length * 0.03, 0.15);
    }

    // Conversation depth
    if (conversationHistory.length > 10) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * Generate insights from extracted context
   * @param {object} context - Project context
   * @returns {object} Generated insights
   */
  generateInsights(context) {
    const insights = {
      complexity: this.assessComplexity(context),
      marketPosition: this.assessMarketPosition(context),
      technicalChallenges: this.identifyTechnicalChallenges(context),
      businessRisks: this.identifyBusinessRisks(context)
    };

    return insights;
  }

  /**
   * Identify research needs based on context
   * @param {object} context - Project context
   * @returns {Array} Research needs
   */
  identifyResearchNeeds(context) {
    const needs = [];

    // Market research needs
    if (context.projectDomain && context.projectDomain !== 'unknown') {
      needs.push({
        type: 'market_research',
        priority: 'high',
        query: `${context.projectDomain} market analysis trends 2024`,
        reason: 'Understanding market landscape for project domain'
      });
    }

    // Competitive analysis needs
    if (context.businessModel && context.keyFeatures?.length > 0) {
      needs.push({
        type: 'competitive_analysis',
        priority: 'medium',
        query: `${context.businessModel} ${context.projectDomain} competitors`,
        reason: 'Identifying competitive landscape and differentiation opportunities'
      });
    }

    // Technical research needs
    if (context.technicalRequirements?.length > 0) {
      context.technicalRequirements.forEach(requirement => {
        needs.push({
          type: 'technical_documentation',
          priority: 'medium',
          query: requirement,
          reason: `Technical guidance for ${requirement}`
        });
      });
    }

    // Validation research needs
    if (context.maturityLevel === 'idea' || context.maturityLevel === 'planning') {
      needs.push({
        type: 'fact_verification',
        priority: 'low',
        query: `${context.projectDomain} business model validation`,
        reason: 'Validating business assumptions and market viability'
      });
    }

    return needs;
  }

  /**
   * Fallback context extraction using simple pattern matching
   * @param {Array} conversationHistory - Conversation history
   * @returns {object} Basic extracted context
   */
  fallbackContextExtraction(conversationHistory) {
    const conversationText = conversationHistory.map(msg => msg.content).join(' ').toLowerCase();
    
    const context = this.getDefaultContext();
    
    // Simple domain detection
    const domains = ['restaurant', 'ecommerce', 'healthcare', 'fintech', 'education', 'gaming'];
    context.projectDomain = domains.find(domain => conversationText.includes(domain)) || 'unknown';
    
    // Simple project type detection
    const projectTypes = ['mobile app', 'web app', 'website', 'api', 'platform'];
    context.projectType = projectTypes.find(type => conversationText.includes(type)) || 'unknown';
    
    context.confidence = 0.3; // Low confidence for fallback
    context.extractedAt = new Date().toISOString();
    
    return context;
  }

  /**
   * Get default context structure
   * @returns {object} Default context
   */
  getDefaultContext() {
    return {
      projectDomain: 'unknown',
      businessModel: 'unknown',
      projectType: 'unknown',
      targetAudience: 'unknown',
      keyFeatures: [],
      businessGoals: [],
      technicalRequirements: [],
      constraints: {
        budget: null,
        timeline: null,
        technical: []
      },
      maturityLevel: 'unknown',
      confidence: 0.0
    };
  }

  // Helper methods for insight generation
  assessComplexity(context) {
    let complexity = 'low';
    
    if (context.technicalRequirements?.length > 5) complexity = 'high';
    else if (context.technicalRequirements?.length > 2) complexity = 'medium';
    
    if (context.keyFeatures?.length > 10) complexity = 'high';
    else if (context.keyFeatures?.length > 5) complexity = 'medium';
    
    return complexity;
  }

  assessMarketPosition(context) {
    // Simplified market position assessment
    if (context.businessModel?.includes('innovative') || context.businessModel?.includes('new')) {
      return 'innovator';
    }
    return 'follower';
  }

  identifyTechnicalChallenges(context) {
    const challenges = [];
    
    if (context.technicalRequirements?.some(req => req.includes('scale'))) {
      challenges.push('scalability');
    }
    
    if (context.technicalRequirements?.some(req => req.includes('security'))) {
      challenges.push('security');
    }
    
    if (context.technicalRequirements?.some(req => req.includes('integration'))) {
      challenges.push('integration');
    }
    
    return challenges;
  }

  identifyBusinessRisks(context) {
    const risks = [];
    
    if (context.maturityLevel === 'idea') {
      risks.push('market_validation');
    }
    
    if (context.constraints?.budget?.includes('limited')) {
      risks.push('budget_constraints');
    }
    
    if (context.constraints?.timeline?.includes('tight')) {
      risks.push('timeline_pressure');
    }
    
    return risks;
  }
}

/**
 * Information gap analyzer for identifying research opportunities
 */
export class InformationGapAnalyzer {
  constructor(config = {}) {
    this.config = config;
    this.gapThreshold = config.gapThreshold || 0.6;
  }

  /**
   * Analyze conversation for information gaps
   * @param {object} projectContext - Extracted project context
   * @param {Array} conversationHistory - Conversation history
   * @returns {Array} Identified information gaps
   */
  analyzeInformationGaps(projectContext, conversationHistory) {
    const gaps = [];
    
    // Market knowledge gaps
    if (this.hasMarketKnowledgeGap(projectContext)) {
      gaps.push({
        type: 'market_knowledge',
        severity: 'high',
        description: 'Limited market understanding',
        suggestedResearch: 'market_research',
        priority: 1
      });
    }
    
    // Technical knowledge gaps
    if (this.hasTechnicalKnowledgeGap(projectContext)) {
      gaps.push({
        type: 'technical_knowledge',
        severity: 'medium',
        description: 'Technical implementation details needed',
        suggestedResearch: 'technical_documentation',
        priority: 2
      });
    }
    
    // Competitive intelligence gaps
    if (this.hasCompetitiveIntelligenceGap(projectContext)) {
      gaps.push({
        type: 'competitive_intelligence',
        severity: 'medium',
        description: 'Competitive landscape unclear',
        suggestedResearch: 'competitive_analysis',
        priority: 2
      });
    }
    
    // Business model validation gaps
    if (this.hasBusinessModelGap(projectContext)) {
      gaps.push({
        type: 'business_validation',
        severity: 'low',
        description: 'Business model assumptions need validation',
        suggestedResearch: 'fact_verification',
        priority: 3
      });
    }
    
    return gaps.sort((a, b) => a.priority - b.priority);
  }

  hasMarketKnowledgeGap(context) {
    return context.projectDomain === 'unknown' || 
           context.targetAudience === 'unknown' ||
           context.businessGoals.length === 0;
  }

  hasTechnicalKnowledgeGap(context) {
    return context.technicalRequirements.length === 0 ||
           context.projectType === 'unknown';
  }

  hasCompetitiveIntelligenceGap(context) {
    return context.businessModel !== 'unknown' && 
           context.projectDomain !== 'unknown' &&
           context.keyFeatures.length > 0;
  }

  hasBusinessModelGap(context) {
    return context.maturityLevel === 'idea' ||
           context.businessModel === 'unknown';
  }
}

/**
 * User expertise assessor for determining research depth
 */
export class UserExpertiseAssessor {
  constructor(config = {}) {
    this.config = config;
  }

  /**
   * Assess user expertise level from conversation
   * @param {Array} conversationHistory - Conversation history
   * @param {object} projectContext - Project context
   * @returns {object} Expertise assessment
   */
  assessExpertiseLevel(conversationHistory, projectContext) {
    const assessment = {
      technical: this.assessTechnicalExpertise(conversationHistory),
      business: this.assessBusinessExpertise(conversationHistory),
      domain: this.assessDomainExpertise(conversationHistory, projectContext),
      overall: 'beginner'
    };

    // Calculate overall expertise
    const scores = [assessment.technical, assessment.business, assessment.domain];
    const avgScore = scores.reduce((sum, score) => sum + this.scoreToNumber(score), 0) / scores.length;
    
    if (avgScore >= 2.5) assessment.overall = 'expert';
    else if (avgScore >= 1.5) assessment.overall = 'intermediate';
    else assessment.overall = 'beginner';

    return assessment;
  }

  assessTechnicalExpertise(conversationHistory) {
    const conversationText = conversationHistory.map(msg => msg.content).join(' ').toLowerCase();
    
    const expertTerms = ['api', 'database', 'architecture', 'scalability', 'microservices', 'devops'];
    const intermediateTerms = ['framework', 'library', 'integration', 'deployment'];
    const beginnerTerms = ['website', 'app', 'simple', 'basic'];
    
    const expertCount = expertTerms.filter(term => conversationText.includes(term)).length;
    const intermediateCount = intermediateTerms.filter(term => conversationText.includes(term)).length;
    const beginnerCount = beginnerTerms.filter(term => conversationText.includes(term)).length;
    
    if (expertCount >= 3) return 'expert';
    if (intermediateCount >= 2 || expertCount >= 1) return 'intermediate';
    return 'beginner';
  }

  assessBusinessExpertise(conversationHistory) {
    const conversationText = conversationHistory.map(msg => msg.content).join(' ').toLowerCase();
    
    const expertTerms = ['revenue model', 'market analysis', 'competitive advantage', 'kpi', 'roi'];
    const intermediateTerms = ['business model', 'target market', 'pricing', 'customers'];
    
    const expertCount = expertTerms.filter(term => conversationText.includes(term)).length;
    const intermediateCount = intermediateTerms.filter(term => conversationText.includes(term)).length;
    
    if (expertCount >= 2) return 'expert';
    if (intermediateCount >= 2 || expertCount >= 1) return 'intermediate';
    return 'beginner';
  }

  assessDomainExpertise(conversationHistory, projectContext) {
    // Domain expertise is harder to assess automatically
    // Use project context confidence as a proxy
    if (projectContext.confidence >= 0.8) return 'expert';
    if (projectContext.confidence >= 0.5) return 'intermediate';
    return 'beginner';
  }

  scoreToNumber(level) {
    switch (level) {
      case 'expert': return 3;
      case 'intermediate': return 2;
      case 'beginner': return 1;
      default: return 1;
    }
  }
}

/**
 * Main Context Analysis Engine
 * Orchestrates all context analysis components
 */
export class ContextAnalysisEngine {
  constructor(config = {}) {
    this.config = config;
    this.contextExtractor = new ProjectContextExtractor(config.extraction || {});
    this.gapAnalyzer = new InformationGapAnalyzer(config.gapAnalysis || {});
    this.expertiseAssessor = new UserExpertiseAssessor(config.expertise || {});
  }

  /**
   * Perform comprehensive context analysis
   * @param {Array} conversationHistory - Conversation history
   * @returns {Promise<object>} Complete context analysis
   */
  async analyzeContext(conversationHistory) {
    try {
      // Extract project context
      const projectContext = await this.contextExtractor.extractProjectContext(conversationHistory);
      
      // Analyze information gaps
      const informationGaps = this.gapAnalyzer.analyzeInformationGaps(projectContext, conversationHistory);
      
      // Assess user expertise
      const userExpertise = this.expertiseAssessor.assessExpertiseLevel(conversationHistory, projectContext);
      
      // Determine optimal research timing
      const researchTiming = this.determineResearchTiming(projectContext, informationGaps, userExpertise);
      
      return {
        projectContext,
        informationGaps,
        userExpertise,
        researchTiming,
        metadata: {
          analyzedAt: new Date().toISOString(),
          conversationLength: conversationHistory.length,
          analysisVersion: '1.0'
        }
      };
    } catch (error) {
      console.error('Context analysis failed:', error);
      throw new Error(`Context analysis failed: ${error.message}`);
    }
  }

  /**
   * Determine optimal research timing based on analysis
   * @param {object} projectContext - Project context
   * @param {Array} informationGaps - Information gaps
   * @param {object} userExpertise - User expertise assessment
   * @returns {object} Research timing recommendations
   */
  determineResearchTiming(projectContext, informationGaps, userExpertise) {
    const timing = {
      immediate: [],
      soon: [],
      later: [],
      recommendations: []
    };

    // High priority gaps for immediate research
    informationGaps.filter(gap => gap.priority === 1).forEach(gap => {
      timing.immediate.push({
        type: gap.suggestedResearch,
        reason: gap.description,
        urgency: 'high'
      });
    });

    // Medium priority gaps for soon
    informationGaps.filter(gap => gap.priority === 2).forEach(gap => {
      timing.soon.push({
        type: gap.suggestedResearch,
        reason: gap.description,
        urgency: 'medium'
      });
    });

    // Low priority gaps for later
    informationGaps.filter(gap => gap.priority === 3).forEach(gap => {
      timing.later.push({
        type: gap.suggestedResearch,
        reason: gap.description,
        urgency: 'low'
      });
    });

    // Generate recommendations based on expertise level
    if (userExpertise.overall === 'beginner') {
      timing.recommendations.push('Start with market research to understand the landscape');
      timing.recommendations.push('Focus on business fundamentals before technical details');
    } else if (userExpertise.overall === 'intermediate') {
      timing.recommendations.push('Balance market and technical research');
      timing.recommendations.push('Consider competitive analysis for differentiation');
    } else {
      timing.recommendations.push('Focus on advanced technical and market insights');
      timing.recommendations.push('Prioritize competitive intelligence and validation');
    }

    return timing;
  }
}

export default ContextAnalysisEngine;
