/**
 * Test Client for WLR-000: Conversational Onboarding Spike
 *
 * This script simulates the AI Agent (e.g., <PERSON>, GPT-4 in an environment like Cursor)
 * interacting with the mock Guidant MCP server. It runs a sample conversation
 * to validate the flow.
 */

const http = require('http');
const readline = require('readline');

const MCP_SERVER_URL = 'http://localhost:3000/mcp';

// --- Helper for command-line input ---
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const askUser = (question) => {
  return new Promise(resolve => {
    rl.question(`\n> User Input for "${question}": `, answer => {
      resolve(answer);
    });
  });
};

// --- Helper to call the mock MCP server ---

async function callMcpTool(tool, args) {
  return new Promise((resolve, reject) => {
    const payload = JSON.stringify({ tool, args });

    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(payload),
      },
    };

    const req = http.request(MCP_SERVER_URL, options, (res) => {
      let data = '';
      res.on('data', (chunk) => (data += chunk));
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          reject(new Error('Failed to parse JSON response from server.'));
        }
      });
    });

    req.on('error', (e) => reject(e));
    req.write(payload);
    req.end();
  });
}


// --- Main Conversation Simulation Logic ---

async function runOnboardingConversation() {
  console.log('--- Starting Conversational Onboarding Test ---');
  console.log("Simulating an AI Agent initiating a project.");

  try {
    // 1. Initialize the project
    console.log("\n🤖 AI Agent: I'm starting a new project. My user wants to build a restaurant app.");
    let response = await callMcpTool('guidant_init_project', { idea: 'restaurant app' });
    
    if (!response.success) {
      throw new Error(`Failed to initialize project: ${response.error}`);
    }

    let sessionId = response.data.sessionId;
    let currentQuestion = response.data.currentQuestion;
    console.log(`✅ Guidant Server: Started session ${sessionId}.`);
    console.log(`❓ Guidant Server asks: "${currentQuestion}"`);


    // 2. Loop through the question flow
    while (currentQuestion) {
      const userAnswer = await askUser(currentQuestion);
      
      console.log(`\n🤖 AI Agent: My user answered "${userAnswer}". Sending to Guidant...`);
      
      response = await callMcpTool('guidant_answer_question', {
        sessionId,
        answer: userAnswer,
      });

      if (!response.success) {
        throw new Error(`Error processing answer: ${response.error}`);
      }

      if (response.data.nextQuestion) {
        currentQuestion = response.data.nextQuestion;
        console.log(`✅ Guidant Server: Answer received. Progress: ${response.data.progress}.`);
        console.log(`❓ Guidant Server asks: "${currentQuestion}"`);
      } else {
        // Conversation is over
        currentQuestion = null;
        console.log('\n--- Onboarding Complete ---');
        console.log(JSON.stringify(response.data, null, 2));
      }
    }
  } catch (error) {
    console.error('\n--- CONVERSATION FAILED ---');
    console.error(error.message);
  } finally {
    rl.close();
  }
}

// Start the simulation
runOnboardingConversation();
