import { describe, it, expect, beforeEach, mock, spyOn } from 'bun:test';
import { 
    buildTaskGraph,
    addDependency,
    removeDependency,
    detectCycleInGraph,
    getCriticalPath,
    getNextTasks,
    getTaskGraphWithStatuses
} from '../../src/central-coordination-engine/task-manager/dependency-manager.js';
import { TaskPersistenceService } from '../../src/task-management/task-persistence-service.js';
import { TASK_STATUSES, TASK_PRIORITIES } from '../../src/task-management/task-schema.js';
import { v4 as uuidv4 } from 'uuid';

// Define shared mock functions that our mocked service instance will use
let mockedGetAllTasksImpl = async () => [];
let mockedGetTaskByIdImpl = async (id) => null;
let mockedUpdateTaskImpl = async (id, updates) => ({ id, ...updates });
let mockedCreateTaskImpl = async (data) => ({...data, id: data.id || uuidv4()});
let mockedDeleteTaskImpl = async (id) => true;

// Mock TaskPersistenceService module
mock.module('../../src/task-management/task-persistence-service.js', () => {
  return {
    TaskPersistenceService: mock(function() { // This is the mocked constructor
      // The instance returned by the mocked constructor will use our shared mock functions
      this.getAllTasks = mock(async (...args) => mockedGetAllTasksImpl(...args));
      this.getTaskById = mock(async (...args) => mockedGetTaskByIdImpl(...args));
      this.updateTask = mock(async (...args) => mockedUpdateTaskImpl(...args));
      this.createTask = mock(async (...args) => mockedCreateTaskImpl(...args));
      this.deleteTask = mock(async (...args) => mockedDeleteTaskImpl(...args));
      return this;
    })
  };
});

// We don't need MockTaskPersistenceService directly anymore for method assignment.
// We will control the behavior by re-assigning our `mockedGetAllTasksImpl`, etc.

// Helper to create a task (used for setting up test data)
const createTaskObject = (id, title, status = TASK_STATUSES.PENDING, dependencies = [], estimatedHours = 1, priority = TASK_PRIORITIES.MEDIUM, businessImpact = 5, complexityScore = 5, createdAt = new Date().toISOString()) => ({
    id,
    title,
    status,
    dependencies,
    estimatedHours,
    priority,
    businessContext: { businessImpact },
    complexityScore,
    createdAt,
    updatedAt: createdAt,
});

describe('DependencyManager', () => {
  beforeEach(() => {
    // Reset implementations of our shared mock functions before each test
    mockedGetAllTasksImpl = async () => [];
    mockedGetTaskByIdImpl = async (id) => null;
    mockedUpdateTaskImpl = async (id, updates) => {
        // Find task in a temporary store if needed for more complex update mocks,
        // or just return a simple object. For these tests, often just ensuring
        // it's called correctly is enough, and the return value can be simple.
        const currentTasks = await mockedGetAllTasksImpl();
        const taskToUpdate = currentTasks.find(t => t.id === id);
        return { ...(taskToUpdate || {id}), ...updates };
    };
    mockedCreateTaskImpl = async (data) => ({...data, id: data.id || uuidv4()});
    mockedDeleteTaskImpl = async (id) => true;

    // If you need to check calls on the mock functions themselves (e.g. toHaveBeenCalledTimes)
    // you would need to ensure they are indeed `mock()` instances from `bun:test`.
    // The module mock above already wraps them with `mock()`.
  });

  describe('buildTaskGraph', () => {
    it('should build an empty graph if no tasks exist', async () => {
      mockedGetAllTasksImpl = async () => [];
      const graph = await buildTaskGraph();
      expect(graph.size).toBe(0);
    });

    it('should build a graph with nodes and correct durations', async () => {
      const tasks = [
        createTaskObject('t1', 'Task 1', TASK_STATUSES.PENDING, [], 5),
        createTaskObject('t2', 'Task 2', TASK_STATUSES.PENDING, ['t1'], 3),
      ];
      mockedGetAllTasksImpl = async () => tasks;
      const graph = await buildTaskGraph();
      expect(graph.size).toBe(2);
      expect(graph.get('t1').task.title).toBe('Task 1');
      expect(graph.get('t1').duration).toBe(5);
      expect(graph.get('t2').duration).toBe(3);
    });

    it('should correctly link predecessors and successors', async () => {
      const tasks = [
        createTaskObject('t1', 'Task 1'),
        createTaskObject('t2', 'Task 2', TASK_STATUSES.PENDING, ['t1']),
        createTaskObject('t3', 'Task 3', TASK_STATUSES.PENDING, ['t1', 't2']),
      ];
      mockedGetAllTasksImpl = async () => tasks; // Corrected this line
      const graph = await buildTaskGraph();
      
      expect(graph.get('t1').successors).toContain('t2');
      expect(graph.get('t1').successors).toContain('t3');
      expect(graph.get('t2').predecessors).toContain('t1');
      expect(graph.get('t2').successors).toContain('t3');
      expect(graph.get('t3').predecessors).toContain('t1');
      expect(graph.get('t3').predecessors).toContain('t2');
    });

    it('should use default duration of 1 if estimatedHours is invalid or missing', async () => {
        const tasks = [
            createTaskObject('t1', 'Task 1', TASK_STATUSES.PENDING, [], 0), // invalid
            createTaskObject('t2', 'Task 2', TASK_STATUSES.PENDING, [], -2), // invalid
            createTaskObject('t3', 'Task 3', TASK_STATUSES.PENDING, [], 'abc'), // invalid
            createTaskObject('t4', 'Task 4'), // missing
        ];
        mockedGetAllTasksImpl = async () => tasks;
        const graph = await buildTaskGraph();
        expect(graph.get('t1').duration).toBe(1);
        expect(graph.get('t2').duration).toBe(1);
        expect(graph.get('t3').duration).toBe(1);
        expect(graph.get('t4').duration).toBe(1);
    });
  });

  describe('addDependency', () => {
    let task1, task2, task3;
    beforeEach(async () => {
        task1 = createTaskObject('t1', 'Task 1');
        task2 = createTaskObject('t2', 'Task 2');
        task3 = createTaskObject('t3', 'Task 3', TASK_STATUSES.PENDING, ['t1']);
        
        mockedGetTaskByIdImpl = async (id) => {
            if (id === 't1') return {...task1};
            if (id === 't2') return {...task2};
            if (id === 't3') return {...task3};
            return null;
        };
        mockedGetAllTasksImpl = async () => [task1, task2, task3]; // For buildTaskGraph in cycle check
        
        // Need to ensure our mockUpdateTask can be spied upon for toHaveBeenCalledWith
        const updateTaskSpy = mock (async (id, updates) => {
            if (id === 't3') {
                task3 = { ...task3, ...updates };
                return task3;
            }
            return null;
        });
        mockedUpdateTaskImpl = updateTaskSpy;
    });

    it('should add a dependency to a task', async () => {
      const updatedTask = await addDependency('t3', 't2');
      expect(updatedTask.dependencies).toContain('t2');
      // To check if the underlying mockedUpdateTaskImpl (which is updateTaskSpy) was called:
      expect(mockedUpdateTaskImpl).toHaveBeenCalledWith('t3', { dependencies: ['t1', 't2'] });
    });

    it('should throw error for self-dependency', async () => {
      expect(async () => await addDependency('t1', 't1')).toThrow('Task t1 cannot depend on itself.');
    });

    it('should throw error if task not found', async () => {
      mockedGetTaskByIdImpl = async (id) => (id === 't1' ? task1 : null); // only t1 exists
      expect(async () => await addDependency('nonexistent', 't1')).toThrow('Task with ID nonexistent not found.');
    });

    it('should throw error if dependency task not found', async () => {
       mockedGetTaskByIdImpl = async (id) => (id === 't1' ? task1 : null); // only t1 exists
      expect(async () => await addDependency('t1', 'nonexistent')).toThrow('Dependency task with ID nonexistent not found.');
    });

    it('should throw error if adding dependency creates a cycle', async () => {
      // t2 -> t1, if we add t1 -> t2, it creates a cycle
      task2.dependencies = ['t3']; // t3 -> t1, so t2 -> t3 -> t1
      mockedGetTaskByIdImpl = async (id) => {
        if (id === 't1') return {...task1};
        if (id === 't2') return {...task2}; // t2 now depends on t3
        if (id === 't3') return {...task3}; // t3 depends on t1
        return null;
      };
      mockedGetAllTasksImpl = async () => [task1, task2, task3];

      // Try to make t1 depend on t2 (t1 -> t2 -> t3 -> t1)
      expect(async () => await addDependency('t1', 't2'))
        .toThrow('Adding dependency t2 to task t1 would create a circular dependency.');
    });

    it('should not add duplicate dependency', async () => {
        const originalUpdateTaskImpl = mockedUpdateTaskImpl;
        const updateTaskSpy = mock(originalUpdateTaskImpl); // Spy on the current impl
        mockedUpdateTaskImpl = updateTaskSpy;

        await addDependency('t3', 't1'); // t1 is already a dependency
        expect(updateTaskSpy).not.toHaveBeenCalled();
    });
  });
  
  describe('removeDependency', () => {
    let taskWithDeps;
    beforeEach(() => {
        taskWithDeps = createTaskObject('t_deps', 'Task With Deps', TASK_STATUSES.PENDING, ['d1', 'd2']);
        mockedGetTaskByIdImpl = async (id) => (id === 't_deps' ? {...taskWithDeps} : null);
        
        const updateTaskSpy = mock(async (id, updates) => {
            if (id === 't_deps') {
                taskWithDeps = { ...taskWithDeps, ...updates };
                return taskWithDeps;
            }
            return null;
        });
        mockedUpdateTaskImpl = updateTaskSpy;
    });

    it('should remove an existing dependency', async () => {
        const updated = await removeDependency('t_deps', 'd1');
        expect(updated.dependencies).toEqual(['d2']);
        expect(mockedUpdateTaskImpl).toHaveBeenCalledWith('t_deps', { dependencies: ['d2'] });
    });

    it('should not change dependencies if dependency to remove is not found', async () => {
        const updateTaskSpy = mockedUpdateTaskImpl; // already a mock
        await removeDependency('t_deps', 'd3');
        expect(taskWithDeps.dependencies).toEqual(['d1', 'd2']); // Check original task obj
        expect(updateTaskSpy).not.toHaveBeenCalled();
    });
    
    it('should throw error if task not found', async () => {
        mockedGetTaskByIdImpl = async (id) => null;
        expect(async () => await removeDependency('nonexistent', 'd1')).toThrow('Task with ID nonexistent not found.');
    });
  });

  describe('detectCycleInGraph', () => {
    it('should detect a simple direct cycle', () => {
      const graph = new Map();
      graph.set('a', { task: {id: 'a'}, successors: new Set(['b']), predecessors: new Set(['b'])});
      graph.set('b', { task: {id: 'b'}, successors: new Set(['a']), predecessors: new Set(['a'])});
      expect(detectCycleInGraph(graph)).toBe(true);
    });

    it('should detect a longer cycle', () => {
      const graph = new Map();
      graph.set('a', { task: {id: 'a'}, successors: new Set(['b']), predecessors: new Set() });
      graph.set('b', { task: {id: 'b'}, successors: new Set(['c']), predecessors: new Set(['a']) });
      graph.set('c', { task: {id: 'c'}, successors: new Set(['a']), predecessors: new Set(['b']) });
      expect(detectCycleInGraph(graph)).toBe(true);
    });

    it('should return false for a DAG', () => {
      const graph = new Map();
      graph.set('a', { task: {id: 'a'}, successors: new Set(['b']), predecessors: new Set() });
      graph.set('b', { task: {id: 'b'}, successors: new Set(), predecessors: new Set(['a']) });
      expect(detectCycleInGraph(graph)).toBe(false);
    });
  });

  describe('getCriticalPath', () => {
    it('should calculate critical path correctly', async () => {
        const tasks = [
            createTaskObject('s', 'Start', TASK_STATUSES.PENDING, [], 0), // Start node
            createTaskObject('a', 'A', TASK_STATUSES.PENDING, ['s'], 2),
            createTaskObject('b', 'B', TASK_STATUSES.PENDING, ['s'], 3),
            createTaskObject('c', 'C', TASK_STATUSES.PENDING, ['a'], 4),
            createTaskObject('d', 'D', TASK_STATUSES.PENDING, ['b'], 2),
            createTaskObject('e', 'E', TASK_STATUSES.PENDING, ['c', 'd'], 3),
            createTaskObject('f', 'Finish', TASK_STATUSES.PENDING, ['e'], 0), // End node
        ];
        mockedGetAllTasksImpl = async () => tasks;
        // Path s-a-c-e-f: 0+2+4+3+0 = 9
        // Path s-b-d-e-f: 0+3+2+3+0 = 8
        const criticalPath = await getCriticalPath();
        expect(criticalPath).toEqual(['s', 'a', 'c', 'e', 'f']);
    });
     it('should return empty array for empty graph', async () => {
        mockedGetAllTasksImpl = async () => [];
        expect(await getCriticalPath()).toEqual([]);
    });
    it('should return empty array if cycle detected', async () => {
        const tasks = [
            createTaskObject('a', 'A', TASK_STATUSES.PENDING, ['b'], 1),
            createTaskObject('b', 'B', TASK_STATUSES.PENDING, ['a'], 1),
        ];
        mockedGetAllTasksImpl = async () => tasks;
        const consoleWarnSpy = spyOn(console, 'warn').mockImplementation(() => {});
        expect(await getCriticalPath()).toEqual([]);
        expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringMatching(/Cannot calculate critical path: Cycle detected/));
        consoleWarnSpy.mockRestore();
    });
  });

  describe('getNextTasks', () => {
    it('should return tasks with no unmet dependencies, sorted', async () => {
        const t1 = createTaskObject('t1', 'Task 1', TASK_STATUSES.DONE, [], 2, TASK_PRIORITIES.HIGH, 10, 1); // Done
        const t2 = createTaskObject('t2', 'Task 2', TASK_STATUSES.PENDING, ['t1'], 3, TASK_PRIORITIES.MEDIUM, 8, 3); // Ready
        const t3 = createTaskObject('t3', 'Task 3', TASK_STATUSES.PENDING, [], 1, TASK_PRIORITIES.HIGH, 7, 2, '2023-01-01T08:00:00Z'); // Ready, higher prio than t4, older than t4_prime
        const t4 = createTaskObject('t4', 'Task 4', TASK_STATUSES.PENDING, ['t5'], 4, TASK_PRIORITIES.LOW, 9, 4); // Blocked
        const t5 = createTaskObject('t5', 'Task 5', TASK_STATUSES.IN_PROGRESS, [], 2); // Not done, so t4 is blocked
        const t6 = createTaskObject('t6', 'Task 6', TASK_STATUSES.DEFERRED, [], 1); // Deferred, should be ignored
        const t4_prime = createTaskObject('t4_prime', 'Task 4 Prime', TASK_STATUSES.PENDING, [], 1, TASK_PRIORITIES.HIGH, 7, 2, '2023-01-01T09:00:00Z'); // Ready, same as t3 but newer

        mockedGetAllTasksImpl = async () => [t1, t2, t3, t4, t5, t4_prime, t6];
        const nextTasks = await getNextTasks();
        // Expected order: t3 (High, BI 7, CS 2, older), t4_prime (High, BI 7, CS 2, newer), t2 (Medium, BI 8, CS 3)
        expect(nextTasks.map(t => t.id)).toEqual(['t3', 't4_prime', 't2']);
    });
     it('should return empty array if all tasks are done or blocked', async () => {
        const t1 = createTaskObject('t1', 'Task 1', TASK_STATUSES.DONE);
        const t2 = createTaskObject('t2', 'Task 2', TASK_STATUSES.PENDING, ['t3']); // Blocked by non-existent t3
        mockedGetAllTasksImpl = async () => [t1, t2];
        expect(await getNextTasks()).toEqual([]);
    });
  });

  describe('getTaskGraphWithStatuses', () => {
    it('should correctly assign graph statuses', async () => {
        const t1 = createTaskObject('t1', 'Task 1', TASK_STATUSES.DONE);
        const t2 = createTaskObject('t2', 'Task 2', TASK_STATUSES.PENDING, ['t1']); // Should be ready-to-start
        const t3 = createTaskObject('t3', 'Task 3', TASK_STATUSES.IN_PROGRESS, ['t1']); // Should be in-progress
        const t4 = createTaskObject('t4', 'Task 4', TASK_STATUSES.PENDING, ['t2']); // Should be blocked
        const t5 = createTaskObject('t5', 'Task 5', TASK_STATUSES.DEFERRED); // Should be deferred
        mockedGetAllTasksImpl = async () => [t1, t2, t3, t4, t5];

        const graphWithStatuses = await getTaskGraphWithStatuses();
        
        const getStatus = id => graphWithStatuses.find(t => t.id === id)?.graphStatus;

        expect(getStatus('t1')).toBe(TASK_STATUSES.DONE);
        expect(getStatus('t2')).toBe('ready-to-start');
        expect(getStatus('t3')).toBe(TASK_STATUSES.IN_PROGRESS);
        expect(getStatus('t4')).toBe('blocked');
        expect(getStatus('t5')).toBe(TASK_STATUSES.DEFERRED);
    });
  });
});