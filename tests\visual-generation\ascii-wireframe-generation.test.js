/**
 * Tests for ASCII Wireframe Generation
 */

import { describe, it, expect, beforeEach } from 'bun:test';
import { ASCIILayoutEngine } from '../../src/visual-generation/generators/ascii-layout-engine.js';
import { ComponentMapper } from '../../src/visual-generation/generators/component-mapper.js';

describe('ASCII Wireframe Generation', () => {
  let layoutEngine;
  let componentMapper;

  beforeEach(() => {
    layoutEngine = new ASCIILayoutEngine();
    componentMapper = new ComponentMapper();
  });

  describe('ComponentMapper', () => {
    it('should map functional requirements to components', () => {
      const requirements = [
        'User login functionality with username and password',
        'Display list of products in a table format',
        'Search functionality with filters'
      ];

      const components = componentMapper.mapRequirementsToComponents(requirements);
      
      expect(components.length).toBeGreaterThan(0);
      expect(components.some(c => c.type === 'form')).toBe(true);
      expect(components.some(c => c.type === 'table')).toBe(true);
      expect(components.some(c => c.type === 'search')).toBe(true);
    });

    it('should create form component with correct fields', () => {
      const requirements = ['User registration form with email, password, and name fields'];
      const components = componentMapper.mapRequirementsToComponents(requirements);
      
      const formComponent = components.find(c => c.type === 'form');
      expect(formComponent).toBeDefined();
      expect(formComponent.properties.fields).toBeDefined();
      expect(formComponent.properties.fields.length).toBeGreaterThan(0);
    });

    it('should generate ASCII representation for components', () => {
      const component = componentMapper.createComponent('button', 'Submit Button');
      
      expect(component.asciiRepresentation).toBeDefined();
      expect(component.asciiRepresentation).toContain('Submit Button');
      expect(component.asciiRepresentation).toContain('┌');
      expect(component.asciiRepresentation).toContain('└');
    });

    it('should create form ASCII with fields and actions', () => {
      const component = componentMapper.createComponent('form', 'Login Form', {
        fields: [
          { name: 'username', type: 'text' },
          { name: 'password', type: 'password' }
        ],
        actions: ['login', 'forgot_password']
      });

      const ascii = component.asciiRepresentation;
      expect(ascii).toContain('Login Form');
      expect(ascii).toContain('username');
      expect(ascii).toContain('password');
      expect(ascii).toContain('login');
    });

    it('should create table ASCII with columns', () => {
      const component = componentMapper.createComponent('table', 'Product List', {
        columns: ['Name', 'Price', 'Category'],
        dataSource: 'products'
      });

      const ascii = component.asciiRepresentation;
      expect(ascii).toContain('Product List');
      expect(ascii).toContain('Name');
      expect(ascii).toContain('Price');
      expect(ascii).toContain('Category');
    });
  });

  describe('ASCIILayoutEngine', () => {
    it('should initialize grid correctly', () => {
      const grid = layoutEngine.initializeGrid({ 
        breakpoint: 'desktop', 
        width: 1200, 
        height: 600 
      });
      
      expect(grid.rows).toBeGreaterThan(0);
      expect(grid.cols).toBeGreaterThan(0);
      expect(grid.cells).toBeDefined();
      expect(grid.cells.length).toBe(grid.rows);
      expect(grid.cells[0].length).toBe(grid.cols);
    });

    it('should position components without overlap', () => {
      const components = [
        { type: 'header', name: 'Header' },
        { type: 'navigation', name: 'Nav' },
        { type: 'content', name: 'Main Content' }
      ];

      const positioned = layoutEngine.positionComponents(components, { breakpoint: 'desktop' });
      
      expect(positioned.length).toBe(components.length);
      
      // Check that components have positions
      for (const component of positioned) {
        expect(component.position).toBeDefined();
        expect(component.gridSpan).toBeDefined();
        expect(component.position.row).toBeGreaterThanOrEqual(0);
        expect(component.position.col).toBeGreaterThanOrEqual(0);
      }
    });

    it('should generate complete ASCII layout', () => {
      const components = [
        { type: 'header', name: 'Site Header' },
        { type: 'navigation', name: 'Main Nav' },
        { type: 'content', name: 'Content Area' },
        { type: 'footer', name: 'Footer' }
      ];

      const layout = layoutEngine.generateLayout(components, {
        breakpoint: 'desktop',
        showLabels: true
      });

      expect(layout.ascii).toBeDefined();
      expect(layout.ascii.length).toBeGreaterThan(0);
      expect(layout.components).toBeDefined();
      expect(layout.metadata).toBeDefined();

      // Check that ASCII contains component content (updated navigation)
      expect(layout.ascii).toContain('Site Header');
      expect(layout.ascii).toContain('[Home] [Products] [About] [Services] [Contact] [Support]'); // Updated navigation
      expect(layout.ascii).toContain('Content Area');
      expect(layout.ascii).toContain('© 2024 Company Name'); // Footer content
    });

    it('should generate responsive layouts for different breakpoints', () => {
      const components = [
        { type: 'header', name: 'Header' },
        { type: 'content', name: 'Content' }
      ];

      const mobileLayout = layoutEngine.generateLayout(components, { breakpoint: 'mobile' });
      const desktopLayout = layoutEngine.generateLayout(components, { breakpoint: 'desktop' });

      expect(mobileLayout).toBeDefined();
      expect(desktopLayout).toBeDefined();

      // Both should contain the wireframe title with breakpoint
      expect(mobileLayout.ascii).toContain('WIREFRAME - MOBILE');
      expect(desktopLayout.ascii).toContain('WIREFRAME - DESKTOP');

      // Both should contain component content
      expect(mobileLayout.ascii).toContain('Header');
      expect(desktopLayout.ascii).toContain('Header');
    });

    it('should handle multiple components in layout', () => {
      const components = [
        { type: 'content', name: 'Content 1' },
        { type: 'content', name: 'Content 2' },
        { type: 'content', name: 'Content 3' }
      ];

      const layout = layoutEngine.generateLayout(components, { breakpoint: 'desktop' });

      // Should generate layout with all components
      expect(layout.components.length).toBe(3);
      expect(layout.ascii).toContain('Content 1');
      expect(layout.ascii).toContain('Content 2');
      expect(layout.ascii).toContain('Content 3');

      // Should have proper box structure
      expect(layout.ascii).toMatch(/┌.*┐/);
      expect(layout.ascii).toMatch(/└.*┘/);
    });

    it('should generate proper ASCII border characters', () => {
      const components = [
        { type: 'button', name: 'Test Button' }
      ];

      const layout = layoutEngine.generateLayout(components, { 
        breakpoint: 'desktop',
        showLabels: true 
      });
      
      // Should contain proper box drawing characters
      expect(layout.ascii).toMatch(/[┌┐└┘─│]/);
    });
  });

  describe('Integration Tests', () => {
    it('should create complete wireframe from requirements', () => {
      const requirements = [
        'User authentication with login form',
        'Product catalog displayed in table format',
        'Shopping cart functionality'
      ];

      // Map requirements to components
      const components = componentMapper.mapRequirementsToComponents(requirements);
      
      // Generate ASCII layout
      const layout = layoutEngine.generateLayout(components, {
        breakpoint: 'desktop',
        showLabels: true
      });
      
      expect(layout.ascii).toBeDefined();
      expect(layout.ascii.length).toBeGreaterThan(100); // Should be substantial
      expect(layout.components.length).toBeGreaterThan(0);
      
      // Should contain structural elements
      expect(layout.ascii).toMatch(/[┌┐└┘─│]/);
      
      console.log('Generated ASCII Wireframe:');
      console.log(layout.ascii);
    });

    it('should handle complex requirements with multiple components', () => {
      const requirements = [
        'Admin dashboard with user management table',
        'Search functionality with advanced filters',
        'File upload for document management',
        'User profile form with validation'
      ];

      const components = componentMapper.mapRequirementsToComponents(requirements);
      const layout = layoutEngine.generateLayout(components, {
        breakpoint: 'desktop',
        showLabels: true
      });
      
      expect(components.length).toBeGreaterThan(4); // Should include structural components
      expect(layout.ascii).toBeDefined();
      
      // Should contain various component types
      const componentTypes = components.map(c => c.type);
      expect(componentTypes).toContain('table');
      expect(componentTypes).toContain('search');
      expect(componentTypes).toContain('upload');
      expect(componentTypes).toContain('form');
    });

    it('should generate mobile-responsive wireframe', () => {
      const requirements = [
        'Mobile-friendly navigation menu',
        'Product listing with images',
        'Contact form'
      ];

      const components = componentMapper.mapRequirementsToComponents(requirements);
      const mobileLayout = layoutEngine.generateLayout(components, {
        breakpoint: 'mobile',
        showLabels: true
      });
      
      expect(mobileLayout.metadata.breakpoint).toBe('mobile');
      expect(mobileLayout.ascii).toBeDefined();
      expect(mobileLayout.ascii).toContain('WIREFRAME - MOBILE');
      
      console.log('Mobile Wireframe:');
      console.log(mobileLayout.ascii);
    });
  });
});
