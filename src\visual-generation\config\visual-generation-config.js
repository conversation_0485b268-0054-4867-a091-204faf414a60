/**
 * Visual Generation Configuration Schema
 * Defines configuration structure for visual generation settings
 */

/**
 * Default visual generation configuration
 */
export const DEFAULT_VISUAL_CONFIG = {
  // ASCII Wireframe Generation Settings
  wireframes: {
    enabled: true,
    gridSystem: {
      columns: 12,
      gutters: 16,
      maxWidth: 1200
    },
    layout: {
      headerHeight: 80,
      footerHeight: 60,
      sidebarWidth: 250,
      contentPadding: 20
    },
    components: {
      defaultSpacing: 16,
      componentPadding: 12,
      borderStyle: 'solid',
      labelPosition: 'top'
    },
    responsiveBreakpoints: {
      mobile: { width: 375, columns: 4 },
      tablet: { width: 768, columns: 8 },
      desktop: { width: 1200, columns: 12 }
    },
    asciiCharacters: {
      horizontal: '─',
      vertical: '│',
      topLeft: '┌',
      topRight: '┐',
      bottomLeft: '└',
      bottomRight: '┘',
      cross: '┼',
      teeDown: '┬',
      teeUp: '┴',
      teeRight: '├',
      teeLeft: '┤'
    }
  },

  // Mermaid Diagram Generation Settings
  diagrams: {
    enabled: true,
    defaultType: 'flowchart',
    flowchart: {
      direction: 'TD', // Top Down
      nodeShape: 'rect',
      edgeStyle: 'solid',
      theme: 'default'
    },
    sequence: {
      theme: 'default',
      actorMargin: 50,
      width: 150,
      height: 65
    },
    state: {
      theme: 'default',
      scale: 1,
      useMaxWidth: true
    },
    styling: {
      primaryColor: '#007bff',
      primaryTextColor: '#ffffff',
      primaryBorderColor: '#0056b3',
      lineColor: '#333333',
      sectionBkgColor: '#f8f9fa',
      altSectionBkgColor: '#e9ecef'
    },
    interactivity: {
      enableClick: true,
      enableTooltips: true,
      enableZoom: true
    }
  },

  // HTML Prototype Generation Settings
  prototypes: {
    enabled: true,
    framework: 'vanilla', // vanilla, react, vue
    styling: {
      framework: 'tailwind', // tailwind, bootstrap, custom
      customCSS: true,
      responsiveDesign: true
    },
    interactions: {
      level: 'basic', // static, basic, advanced
      formValidation: true,
      modalDialogs: true,
      animations: false
    },
    accessibility: {
      ariaLabels: true,
      keyboardNavigation: true,
      screenReaderSupport: true,
      highContrastMode: true,
      focusManagement: true
    },
    designSystem: {
      useSystemTokens: true,
      colorPalette: 'default',
      typography: 'system',
      spacing: 'consistent'
    }
  },

  // Research Integration Settings
  research: {
    enabled: true,
    providers: {
      tavily: {
        enabled: true,
        maxResults: 5,
        searchDepth: 'basic'
      },
      context7: {
        enabled: true,
        maxTokens: 5000,
        focusAreas: ['ui-patterns', 'accessibility', 'best-practices']
      },
      firecrawl: {
        enabled: true,
        maxPages: 3,
        extractionDepth: 'basic'
      }
    },
    intelligence: {
      cacheResults: true,
      cacheDuration: 3600, // 1 hour in seconds
      enhanceWithResearch: true,
      researchWeight: 0.3 // 30% research influence
    }
  },

  // Asset Storage and Organization
  storage: {
    baseDirectory: '.guidant/deliverables/wireframes',
    subdirectories: {
      wireframes: 'ascii-wireframes',
      diagrams: 'mermaid-diagrams',
      prototypes: 'html-prototypes',
      components: 'component-previews'
    },
    fileNaming: {
      includeTimestamp: true,
      includeVersion: true,
      format: '{type}_{title}_{version}_{timestamp}'
    },
    metadata: {
      trackUsage: true,
      trackPerformance: true,
      trackUserFeedback: true
    },
    versioning: {
      enabled: true,
      maxVersions: 10,
      autoCleanup: true
    }
  },

  // Quality and Validation Settings
  quality: {
    validation: {
      enabled: true,
      strictMode: false,
      autoFix: true
    },
    accessibility: {
      wcagLevel: 'AA',
      checkContrast: true,
      checkKeyboardNav: true,
      checkScreenReader: true
    },
    performance: {
      maxGenerationTime: 30000, // 30 seconds
      maxAssetSize: 1048576, // 1MB
      optimizeOutput: true
    }
  },

  // Business Intelligence Settings
  businessIntelligence: {
    enabled: true,
    tracking: {
      usageMetrics: true,
      performanceMetrics: true,
      userSatisfaction: true,
      businessValue: true
    },
    reporting: {
      generateReports: true,
      reportFrequency: 'weekly',
      includeROI: true
    }
  }
};

/**
 * Visual Generation Configuration Manager
 */
export class VisualGenerationConfig {
  constructor(customConfig = {}) {
    this.config = this.mergeConfigs(DEFAULT_VISUAL_CONFIG, customConfig);
  }

  /**
   * Deep merge configuration objects
   */
  mergeConfigs(defaultConfig, customConfig) {
    const merged = { ...defaultConfig };
    
    for (const key in customConfig) {
      if (customConfig[key] && typeof customConfig[key] === 'object' && !Array.isArray(customConfig[key])) {
        merged[key] = this.mergeConfigs(merged[key] || {}, customConfig[key]);
      } else {
        merged[key] = customConfig[key];
      }
    }
    
    return merged;
  }

  /**
   * Get configuration for specific generator type
   */
  getGeneratorConfig(type) {
    const configs = {
      wireframe: this.config.wireframes,
      diagram: this.config.diagrams,
      prototype: this.config.prototypes
    };
    
    return configs[type] || {};
  }

  /**
   * Get research configuration
   */
  getResearchConfig() {
    return this.config.research;
  }

  /**
   * Get storage configuration
   */
  getStorageConfig() {
    return this.config.storage;
  }

  /**
   * Get quality configuration
   */
  getQualityConfig() {
    return this.config.quality;
  }

  /**
   * Update configuration
   */
  updateConfig(updates) {
    this.config = this.mergeConfigs(this.config, updates);
  }

  /**
   * Validate configuration
   */
  validate() {
    const errors = [];
    
    // Validate required settings
    if (!this.config.storage.baseDirectory) {
      errors.push('Storage base directory is required');
    }
    
    // Validate breakpoints
    const breakpoints = this.config.wireframes.responsiveBreakpoints;
    if (breakpoints.mobile.width >= breakpoints.tablet.width) {
      errors.push('Mobile breakpoint must be smaller than tablet');
    }
    if (breakpoints.tablet.width >= breakpoints.desktop.width) {
      errors.push('Tablet breakpoint must be smaller than desktop');
    }
    
    // Validate research providers
    const research = this.config.research;
    if (research.enabled && !Object.values(research.providers).some(p => p.enabled)) {
      errors.push('At least one research provider must be enabled when research is enabled');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Export configuration to JSON
   */
  toJSON() {
    return this.config;
  }

  /**
   * Create configuration from JSON
   */
  static fromJSON(json) {
    return new VisualGenerationConfig(json);
  }
}

/**
 * Integration with Guidant's main config.json
 */
export function integrateWithGuidantConfig(guidantConfig) {
  if (!guidantConfig.visualGeneration) {
    guidantConfig.visualGeneration = DEFAULT_VISUAL_CONFIG;
  } else {
    const visualConfig = new VisualGenerationConfig(guidantConfig.visualGeneration);
    guidantConfig.visualGeneration = visualConfig.toJSON();
  }
  
  return guidantConfig;
}
