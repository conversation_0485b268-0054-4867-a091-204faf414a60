/**
 * Research-Enhanced Decision Translator (Task 4.2)
 * 
 * Enhances WLR-005 business decision translation system with research synthesis capabilities.
 * Ensures research findings inform business decisions and maintains business-friendly language
 * for technical recommendations.
 */

import { DecisionTranslator } from './decision-translator.js';
import { ResearchSynthesisEngine } from '../research-synthesis/research-synthesis-engine.js';
import { performResearchOperation } from '../ai-integration/ai-services-unified.js';
import { AITranslationService } from './ai-translation-service.js';

/**
 * Research-Enhanced Decision Translator
 * Extends the existing DecisionTranslator with research synthesis capabilities
 */
export class ResearchEnhancedDecisionTranslator extends DecisionTranslator {
  constructor(projectRoot = '.') {
    super(projectRoot);
    this.researchSynthesisEngine = new ResearchSynthesisEngine({
      projectRoot,
      aiModel: 'research'
    });
    this.researchCache = new Map();
    this.researchTimeout = 30000; // 30 seconds timeout for research
  }

  /**
   * Translate decision options with research backing
   * @param {string} decisionContext - Decision context
   * @param {object} projectContext - Project context for research
   * @param {object} options - Translation options
   * @returns {Promise<object>} Research-enhanced decision options
   */
  async translateDecisionOptionsWithResearch(decisionContext, projectContext = {}, options = {}) {
    try {
      console.log(`🔍 Enhancing decision translation with research for: ${decisionContext}`);
      
      // Step 1: Get base decision options from existing system
      const baseDecision = await super.translateDecisionOptions(decisionContext);
      
      // Step 2: Conduct relevant research if enabled
      let researchResults = null;
      if (options.enableResearch !== false) {
        researchResults = await this.conductDecisionResearch(decisionContext, projectContext, options);
      }
      
      // Step 3: Enhance options with research findings
      const enhancedOptions = await this.enhanceOptionsWithResearch(
        baseDecision.options,
        researchResults,
        projectContext
      );
      
      // Step 4: Generate research-backed recommendations
      const recommendations = await this.generateResearchBackedRecommendations(
        enhancedOptions,
        researchResults,
        projectContext
      );
      
      return {
        ...baseDecision,
        options: enhancedOptions,
        researchBacking: researchResults ? {
          summary: researchResults.synthesis?.summary || 'Research conducted',
          confidence: researchResults.metadata?.confidenceLevel || 0.7,
          sources: researchResults.synthesis?.sources?.length || 0,
          timestamp: new Date().toISOString()
        } : null,
        recommendations,
        metadata: {
          ...baseDecision.metadata,
          researchEnhanced: true,
          researchConducted: !!researchResults,
          enhancementTimestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      console.error('Research-enhanced decision translation failed:', error);
      // Fallback to base decision translation
      return await super.translateDecisionOptions(decisionContext);
    }
  }

  /**
   * Conduct research relevant to the decision context
   * @param {string} decisionContext - Decision context
   * @param {object} projectContext - Project context
   * @param {object} options - Research options
   * @returns {Promise<object|null>} Research results
   */
  async conductDecisionResearch(decisionContext, projectContext, options = {}) {
    try {
      // Check cache first
      const cacheKey = this.generateResearchCacheKey(decisionContext, projectContext);
      if (this.researchCache.has(cacheKey)) {
        console.log('📋 Using cached research results');
        return this.researchCache.get(cacheKey);
      }

      // Generate research query based on decision context
      const researchQuery = this.generateResearchQuery(decisionContext, projectContext);
      
      // Conduct research with timeout
      const researchPromise = performResearchOperation({
        type: this.mapDecisionToResearchType(decisionContext),
        query: researchQuery,
        context: {
          domain: projectContext.domain || 'general',
          businessModel: projectContext.businessModel || 'unknown',
          decisionContext,
          ...projectContext
        },
        providers: options.researchProviders || ['tavily'],
        maxResults: options.maxResults || 5
      });

      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Research timeout')), this.researchTimeout)
      );

      const researchResults = await Promise.race([researchPromise, timeoutPromise]);
      
      // Synthesize research with business context
      const synthesizedResults = await this.researchSynthesisEngine.synthesizeResearchWithContext(
        researchResults,
        projectContext,
        { templateType: this.mapDecisionToSynthesisTemplate(decisionContext) }
      );

      // Cache results
      this.researchCache.set(cacheKey, synthesizedResults);
      
      console.log(`✅ Research completed for ${decisionContext}`);
      return synthesizedResults;
    } catch (error) {
      console.warn(`Research failed for ${decisionContext}:`, error.message);
      return null;
    }
  }

  /**
   * Enhance decision options with research findings
   * @param {Array} baseOptions - Base decision options
   * @param {object} researchResults - Research results
   * @param {object} projectContext - Project context
   * @returns {Promise<Array>} Enhanced options
   */
  async enhanceOptionsWithResearch(baseOptions, researchResults, projectContext) {
    if (!researchResults || !researchResults.recommendations) {
      return baseOptions;
    }

    const enhancedOptions = await Promise.all(baseOptions.map(async (option) => {
      // Find relevant research recommendations for this option
      const relevantRecommendations = this.findRelevantRecommendations(
        option,
        researchResults.recommendations
      );

      // Enhance option with research insights
      const enhanced = { ...option };

      if (relevantRecommendations.length > 0) {
        // Add research backing to business impact
        enhanced.businessImpact = await this.enhanceBusinessImpact(
          option.businessImpact,
          relevantRecommendations,
          projectContext
        );

        // Add research-backed risk assessment
        enhanced.riskAssessment = await this.enhanceRiskAssessment(
          option.riskAssessment,
          relevantRecommendations,
          projectContext
        );

        // Add research insights
        enhanced.researchInsights = this.extractResearchInsights(relevantRecommendations);

        // Update confidence based on research backing
        enhanced.confidence = this.calculateResearchBackedConfidence(
          option,
          relevantRecommendations
        );

        // Add research-backed tags
        enhanced.tags = [
          ...(option.tags || []),
          'research_backed',
          ...this.generateResearchTags(relevantRecommendations)
        ];
      }

      return enhanced;
    }));

    return enhancedOptions;
  }

  /**
   * Generate research-backed recommendations
   * @param {Array} enhancedOptions - Enhanced options
   * @param {object} researchResults - Research results
   * @param {object} projectContext - Project context
   * @returns {Promise<object>} Recommendations
   */
  async generateResearchBackedRecommendations(enhancedOptions, researchResults, projectContext) {
    const recommendations = {
      primary: null,
      alternatives: [],
      reasoning: '',
      researchBacking: '',
      businessJustification: ''
    };

    if (!researchResults || !enhancedOptions.length) {
      return recommendations;
    }

    // Find the option with highest research confidence
    const sortedOptions = enhancedOptions
      .filter(opt => opt.confidence)
      .sort((a, b) => b.confidence - a.confidence);

    if (sortedOptions.length > 0) {
      recommendations.primary = sortedOptions[0];
      recommendations.alternatives = sortedOptions.slice(1, 3); // Top 2 alternatives

      // Generate reasoning based on research
      recommendations.reasoning = await this.generateResearchBasedReasoning(
        recommendations.primary,
        researchResults,
        projectContext
      );

      // Extract research backing
      recommendations.researchBacking = this.extractResearchBacking(
        recommendations.primary,
        researchResults
      );

      // Generate business justification
      recommendations.businessJustification = await this.generateBusinessJustification(
        recommendations.primary,
        projectContext,
        researchResults
      );
    }

    return recommendations;
  }

  /**
   * Generate research query based on decision context
   * @param {string} decisionContext - Decision context
   * @param {object} projectContext - Project context
   * @returns {string} Research query
   */
  generateResearchQuery(decisionContext, projectContext) {
    const contextQueries = {
      framework_selection: `best web development frameworks ${projectContext.domain || ''} 2024`,
      database_selection: `database comparison ${projectContext.businessModel || ''} scalability`,
      hosting_selection: `cloud hosting providers comparison ${projectContext.budget || ''} budget`,
      architecture_selection: `software architecture patterns ${projectContext.domain || ''} best practices`,
      deployment_selection: `deployment strategies ${projectContext.timeline || ''} timeline`
    };

    const baseQuery = contextQueries[decisionContext] || `${decisionContext.replace(/_/g, ' ')} best practices`;
    
    // Add project-specific context
    if (projectContext.domain) {
      return `${baseQuery} ${projectContext.domain} industry`;
    }
    
    return baseQuery;
  }

  /**
   * Map decision context to research type
   * @param {string} decisionContext - Decision context
   * @returns {string} Research type
   */
  mapDecisionToResearchType(decisionContext) {
    const mappings = {
      framework_selection: 'technical_research',
      database_selection: 'technical_research',
      hosting_selection: 'technical_research',
      architecture_selection: 'technical_research',
      market_strategy: 'market_research',
      competitive_analysis: 'competitive_analysis',
      business_model: 'market_research'
    };

    return mappings[decisionContext] || 'technical_research';
  }

  /**
   * Map decision context to synthesis template
   * @param {string} decisionContext - Decision context
   * @returns {string} Template type
   */
  mapDecisionToSynthesisTemplate(decisionContext) {
    const mappings = {
      framework_selection: 'technical_research',
      database_selection: 'technical_research',
      hosting_selection: 'technical_research',
      market_strategy: 'market_research',
      competitive_analysis: 'competitive_analysis'
    };

    return mappings[decisionContext] || 'general';
  }

  /**
   * Generate cache key for research results
   * @param {string} decisionContext - Decision context
   * @param {object} projectContext - Project context
   * @returns {string} Cache key
   */
  generateResearchCacheKey(decisionContext, projectContext) {
    const contextHash = JSON.stringify({
      domain: projectContext.domain,
      businessModel: projectContext.businessModel,
      budget: projectContext.budget?.category,
      timeline: projectContext.timeline?.category
    });
    
    return `${decisionContext}_${Buffer.from(contextHash).toString('base64').slice(0, 10)}`;
  }

  /**
   * Find relevant recommendations for an option
   * @param {object} option - Decision option
   * @param {Array} recommendations - Research recommendations
   * @returns {Array} Relevant recommendations
   */
  findRelevantRecommendations(option, recommendations) {
    const optionText = (option.title + ' ' + option.description).toLowerCase();
    
    return recommendations.filter(rec => {
      const recText = (rec.title + ' ' + rec.description).toLowerCase();
      return this.calculateTextSimilarity(optionText, recText) > 0.3;
    });
  }

  /**
   * Calculate text similarity between two strings
   * @param {string} text1 - First text
   * @param {string} text2 - Second text
   * @returns {number} Similarity score (0-1)
   */
  calculateTextSimilarity(text1, text2) {
    const words1 = text1.split(' ').filter(w => w.length > 3);
    const words2 = text2.split(' ').filter(w => w.length > 3);

    const commonWords = words1.filter(word => words2.includes(word));
    const totalWords = new Set([...words1, ...words2]).size;

    return totalWords > 0 ? commonWords.length / totalWords : 0;
  }

  /**
   * Enhance business impact with research insights
   * @param {string} originalImpact - Original business impact
   * @param {Array} recommendations - Relevant recommendations
   * @param {object} projectContext - Project context
   * @returns {Promise<string>} Enhanced business impact
   */
  async enhanceBusinessImpact(originalImpact, recommendations, projectContext) {
    if (!recommendations.length) return originalImpact;

    const researchInsights = recommendations
      .map(rec => rec.businessImpact?.revenue || rec.businessImpact?.efficiency || '')
      .filter(insight => insight)
      .join('. ');

    if (!researchInsights) return originalImpact;

    // Use AI to merge original impact with research insights
    try {
      const enhancedImpact = await this.aiTranslator.translateTerm(
        `${originalImpact}. Research shows: ${researchInsights}`,
        'business_impact_enhancement',
        'novice'
      );
      return enhancedImpact;
    } catch (error) {
      return `${originalImpact}. Research insights: ${researchInsights.substring(0, 100)}...`;
    }
  }

  /**
   * Enhance risk assessment with research insights
   * @param {string} originalRisk - Original risk assessment
   * @param {Array} recommendations - Relevant recommendations
   * @param {object} projectContext - Project context
   * @returns {Promise<string>} Enhanced risk assessment
   */
  async enhanceRiskAssessment(originalRisk, recommendations, projectContext) {
    if (!recommendations.length) return originalRisk;

    const riskInsights = recommendations
      .flatMap(rec => rec.implementation?.risks || [])
      .filter(risk => risk)
      .slice(0, 3)
      .join(', ');

    if (!riskInsights) return originalRisk;

    return `${originalRisk}. Research-identified risks: ${riskInsights}`;
  }

  /**
   * Extract research insights from recommendations
   * @param {Array} recommendations - Recommendations
   * @returns {Array} Research insights
   */
  extractResearchInsights(recommendations) {
    return recommendations.map(rec => ({
      title: rec.title,
      insight: rec.researchBacking || rec.businessRationale || 'Research-backed recommendation',
      confidence: rec.confidence || 0.7,
      source: 'research_synthesis'
    }));
  }

  /**
   * Calculate research-backed confidence
   * @param {object} option - Decision option
   * @param {Array} recommendations - Relevant recommendations
   * @returns {number} Confidence score (0-1)
   */
  calculateResearchBackedConfidence(option, recommendations) {
    if (!recommendations.length) return 0.5; // Default confidence

    const avgConfidence = recommendations.reduce((sum, rec) => sum + (rec.confidence || 0.7), 0) / recommendations.length;
    const researchBoost = Math.min(0.3, recommendations.length * 0.1); // Up to 30% boost

    return Math.min(1.0, avgConfidence + researchBoost);
  }

  /**
   * Generate research tags
   * @param {Array} recommendations - Recommendations
   * @returns {Array} Research tags
   */
  generateResearchTags(recommendations) {
    const tags = new Set();

    recommendations.forEach(rec => {
      if (rec.priority === 'high') tags.add('high_priority_research');
      if (rec.confidence > 0.8) tags.add('high_confidence_research');
      if (rec.implementation?.effort === 'low') tags.add('low_effort_research');
      if (rec.businessImpact?.revenue) tags.add('revenue_impact');
    });

    return Array.from(tags);
  }

  /**
   * Generate research-based reasoning
   * @param {object} primaryOption - Primary recommended option
   * @param {object} researchResults - Research results
   * @param {object} projectContext - Project context
   * @returns {Promise<string>} Research-based reasoning
   */
  async generateResearchBasedReasoning(primaryOption, researchResults, projectContext) {
    const prompt = `Based on research findings, explain why "${primaryOption.title}" is the recommended choice for a ${projectContext.domain || 'general'} project with ${projectContext.budget?.category || 'unknown'} budget and ${projectContext.timeline?.category || 'unknown'} timeline. Keep explanation business-friendly and under 200 words.`;

    try {
      const reasoning = await this.aiTranslator.translateTerm(prompt, 'reasoning_generation', 'novice');
      return reasoning;
    } catch (error) {
      return `${primaryOption.title} is recommended based on research analysis of current market trends and technical best practices for your project requirements.`;
    }
  }

  /**
   * Extract research backing
   * @param {object} option - Decision option
   * @param {object} researchResults - Research results
   * @returns {string} Research backing summary
   */
  extractResearchBacking(option, researchResults) {
    if (!researchResults.synthesis) return 'Research-informed recommendation';

    const sources = researchResults.synthesis.sources?.length || 0;
    const confidence = researchResults.metadata?.confidenceLevel || 0.7;

    return `Based on analysis of ${sources} research sources with ${Math.round(confidence * 100)}% confidence. ${researchResults.synthesis.summary?.substring(0, 150) || 'Research synthesis available.'}...`;
  }

  /**
   * Generate business justification
   * @param {object} option - Decision option
   * @param {object} projectContext - Project context
   * @param {object} researchResults - Research results
   * @returns {Promise<string>} Business justification
   */
  async generateBusinessJustification(option, projectContext, researchResults) {
    const businessFactors = [
      projectContext.budget?.category && `${projectContext.budget.category} budget`,
      projectContext.timeline?.category && `${projectContext.timeline.category} timeline`,
      projectContext.expertise?.category && `${projectContext.expertise.category} expertise level`
    ].filter(Boolean).join(', ');

    const justification = `This choice aligns with your ${businessFactors} and is supported by current market research showing strong adoption and business value in similar projects.`;

    return justification;
  }

  /**
   * Create research-enhanced custom decision template
   * @param {string} context - Decision context
   * @param {string} title - Decision title
   * @param {string} description - Decision description
   * @param {Array} options - Decision options
   * @param {object} projectContext - Project context
   * @returns {Promise<object>} Enhanced custom template
   */
  async createResearchEnhancedCustomTemplate(context, title, description, options, projectContext = {}) {
    // Create base custom template
    const baseTemplate = await super.createCustomDecisionTemplate(context, title, description, options);

    // Enhance with research if enabled
    try {
      const researchResults = await this.conductDecisionResearch(context, projectContext, {
        maxResults: 3,
        researchProviders: ['tavily']
      });

      if (researchResults) {
        // Enhance options with research insights
        baseTemplate.options = await this.enhanceOptionsWithResearch(
          baseTemplate.options,
          researchResults,
          projectContext
        );

        // Add research metadata
        baseTemplate.researchEnhanced = true;
        baseTemplate.researchSummary = researchResults.synthesis?.summary?.substring(0, 200) || 'Research conducted';
        baseTemplate.researchConfidence = researchResults.metadata?.confidenceLevel || 0.7;
      }
    } catch (error) {
      console.warn('Failed to enhance custom template with research:', error.message);
    }

    return baseTemplate;
  }

  /**
   * Clear research cache
   */
  clearResearchCache() {
    this.researchCache.clear();
  }

  /**
   * Get research cache statistics
   * @returns {object} Cache statistics
   */
  getResearchCacheStats() {
    return {
      size: this.researchCache.size,
      keys: Array.from(this.researchCache.keys())
    };
  }
}

/**
 * Factory function to create research-enhanced decision translator
 * @param {string} projectRoot - Project root path
 * @returns {ResearchEnhancedDecisionTranslator} Enhanced translator instance
 */
export function createResearchEnhancedDecisionTranslator(projectRoot = '.') {
  return new ResearchEnhancedDecisionTranslator(projectRoot);
}

export default ResearchEnhancedDecisionTranslator;
