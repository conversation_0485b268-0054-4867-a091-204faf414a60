# TASK-001 Implementation Plan: MCP-Native Tool Architecture Overhaul

## Executive Summary

Based on comprehensive analysis of MCP documentation, this plan restructures Guidant's 48 tools around the 4 core MCP primitives (Resources, Tools, Prompts, Sampling) rather than arbitrary consolidation. This creates a standards-compliant, maintainable architecture aligned with MCP's design philosophy.

## MCP Architecture Foundation

### The 4 Core MCP Primitives

1. **Resources** (Application-Controlled) - Data exposure through URI templates
2. **Tools** (Model-Controlled) - Executable functionality with operation parameters  
3. **Prompts** (User-Controlled) - Reusable templates with dynamic arguments
4. **Sampling** (Server-Controlled) - LLM completion requests for intelligent decisions

### Control Paradigms
- **Application-Controlled**: Client decides when/how to use (Resources)
- **Model-Controlled**: AI can automatically invoke with approval (Tools)
- **User-Controlled**: User explicitly selects for use (Prompts)
- **Server-Controlled**: Server requests from LLM (Sampling)

## Current State Analysis

### Tool Inventory (48 Total Tools)
```
Categories:
├── core-project-management (3 tools)
├── workflow-control (3 tools)  
├── deliverable-analysis (3 tools)
├── agent-discovery (4 tools)
├── capability-analysis (4 tools)
├── adaptive-workflow (6 tools)
├── quality-validation (5 tools)
├── orchestration (8 tools)
├── analytics (4 tools)
├── context-management (3 tools)
├── interactive-onboarding (4 tools)
└── business-decisions (4 tools)
```

### Existing Infrastructure Assets
- ✅ Tool orchestration system (`tool-orchestrator.js`)
- ✅ Workflow DSL and validation (`workflow-dsl.js`)
- ✅ Circuit breaker and caching infrastructure
- ✅ Tool categorization and registry system
- ✅ JSON-RPC 2.0 compatible message handling

## Implementation Strategy

### Phase 1: MCP Primitive Mapping & Architecture Design (Week 1)

#### 1.1 Tool Primitive Audit
**Objective**: Map all 48 tools to appropriate MCP primitives

**Methodology**:
1. Analyze each tool's function and control paradigm
2. Categorize by MCP primitive type
3. Identify consolidation opportunities within primitives
4. Design URI templates for Resources
5. Plan operation-based parameters for Tools

**Expected Outcomes**:
- Complete primitive mapping document
- Resource URI template specifications
- Tool consolidation strategy
- Prompt template definitions

#### 1.2 Architecture Design
**New Directory Structure**:
```
mcp-server/src/
├── primitives/
│   ├── resources/
│   │   ├── project-resources.js
│   │   ├── deliverable-resources.js
│   │   ├── analytics-resources.js
│   │   └── workflow-resources.js
│   ├── tools/
│   │   ├── workflow-executor.js (consolidates 15+ workflow tools)
│   │   ├── project-manager.js (consolidates 8+ management tools)
│   │   ├── analytics-engine.js (consolidates 9+ analytics tools)
│   │   └── quality-validator.js (consolidates 5+ quality tools)
│   ├── prompts/
│   │   ├── analysis-prompts.js
│   │   ├── workflow-prompts.js
│   │   ├── onboarding-prompts.js
│   │   └── decision-prompts.js
│   └── sampling/
│       ├── intelligent-decisions.js
│       └── adaptive-responses.js
├── orchestration/ (enhanced existing)
│   ├── primitive-router.js (new - MCP-aware routing)
│   ├── mcp-compliance.js (new - standards validation)
│   └── tool-orchestrator.js (existing - enhanced)
├── schemas/
│   ├── resource-schemas.js
│   ├── tool-schemas.js
│   └── prompt-schemas.js
└── index.js (MCP-compliant registration)
```

### Phase 2: Resource Architecture Implementation (Week 2)

#### 2.1 Resource URI Templates (RFC 6570)
**Design Pattern**:
```javascript
// Project Resources
"guidant://project/{projectId}/state"
"guidant://project/{projectId}/phase/{phase}"
"guidant://project/{projectId}/deliverables"

// Analytics Resources  
"guidant://analytics/{projectId}/insights"
"guidant://analytics/{projectId}/metrics/{timeframe}"
"guidant://analytics/workflow/{workflowId}/performance"

// Workflow Resources
"guidant://workflow/{workflowId}/definition"
"guidant://workflow/{workflowId}/execution/{executionId}"
```

#### 2.2 Resource Implementation
**Tools Converting to Resources**:
- `guidant_get_project_state` → `guidant://project/{id}/state`
- `guidant_analyze_deliverable` → `guidant://deliverable/{id}/analysis`
- `guidant_extract_insights` → `guidant://analytics/{id}/insights`
- `guidant_get_workflow_metrics` → `guidant://workflow/{id}/metrics`

### Phase 3: Tool Consolidation with MCP Annotations (Week 3)

#### 3.1 Consolidated Tool Design
**Primary Consolidated Tools** (15 tools → 4-5 tools):

```javascript
// 1. Workflow Executor (consolidates 15+ workflow tools)
{
  name: "guidant_execute_workflow",
  description: "Execute workflow operations with intelligent routing",
  parameters: {
    operation: {
      enum: ["research", "onboarding", "development", "analysis", "quality"],
      description: "Type of workflow operation to execute"
    },
    workflow_template: { type: "string", optional: true },
    context: { type: "object" },
    parameters: { type: "object" }
  },
  annotations: {
    title: "Workflow Executor",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: true,
    openWorldHint: false
  }
}

// 2. Project Manager (consolidates 8+ management tools)
{
  name: "guidant_manage_project",
  description: "Project management operations with state control",
  parameters: {
    operation: {
      enum: ["init", "advance_phase", "save_deliverable", "update_state"],
      description: "Project management operation"
    },
    // ... operation-specific parameters
  }
}

// 3. Analytics Engine (consolidates 9+ analytics tools)
{
  name: "guidant_analyze_data",
  description: "Analytics and insight generation operations",
  parameters: {
    operation: {
      enum: ["performance", "quality", "insights", "metrics", "trends"],
      description: "Type of analysis to perform"
    }
  }
}
```

#### 3.2 Smart Routing Implementation
**MCP Annotation-Based Routing**:
- Use `readOnlyHint` for read-only operations
- Use `destructiveHint` for state-changing operations  
- Use `idempotentHint` for safe retry operations
- Use `openWorldHint` for external system interactions

### Phase 4: Prompts & Sampling Integration (Week 4)

#### 4.1 Prompt Templates
**Convert template-based tools to MCP Prompts**:
```javascript
{
  name: "analyze-project-phase",
  description: "Analyze project phase progress and provide recommendations",
  arguments: [
    { name: "phase", description: "Project phase to analyze", required: true },
    { name: "focus_area", description: "Specific area to focus on", required: false },
    { name: "timeframe", description: "Analysis timeframe", required: false }
  ]
}
```

#### 4.2 Sampling for Intelligent Decisions
**New Capability**: Enable Guidant to request LLM completions for:
- Adaptive workflow decisions
- Content generation
- Intelligent analysis
- Context-aware recommendations

## Success Metrics

### Quantitative Goals
- **Tool Count Reduction**: 48 → 15 total primitives (69% reduction)
- **MCP Compliance**: 100% adherence to MCP primitive patterns
- **Performance**: <5 seconds tool discovery time
- **Backward Compatibility**: 100% existing workflow support

### Qualitative Goals
- **Standards Compliance**: Full MCP protocol adherence
- **Maintainability**: Clear primitive-based organization
- **Extensibility**: Easy addition of new capabilities
- **Developer Experience**: Intuitive MCP-native patterns

## Risk Mitigation

### Technical Risks
1. **Breaking Changes**: Implement gradual migration with compatibility layer
2. **Performance Impact**: Benchmark before/after with optimization
3. **Complexity**: Start with one primitive, iterate incrementally

### Implementation Risks
1. **Timeline**: Phase-based approach with clear milestones
2. **Testing**: Comprehensive test coverage for each primitive
3. **Documentation**: Update all documentation during implementation

## Next Steps

1. **Immediate**: Begin Phase 1 primitive mapping audit
2. **Week 1**: Complete architecture design and tool categorization
3. **Week 2**: Implement Resource layer with URI templates
4. **Week 3**: Build consolidated Tools with MCP annotations
5. **Week 4**: Add Prompts and Sampling capabilities

## Dependencies

- MCP SDK compatibility verification
- Existing orchestration infrastructure enhancement
- JSON-RPC 2.0 message handling validation
- Tool registry system updates

---

**Implementation Lead**: AI Agent (Claude Sonnet 4)
**Timeline**: 4 weeks
**Priority**: Critical (blocks TASK-003 to TASK-007)
