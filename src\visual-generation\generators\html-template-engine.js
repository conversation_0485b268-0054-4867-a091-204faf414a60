/**
 * HTML Template Engine
 * Generates semantic HTML structure from component specifications with proper accessibility attributes,
 * responsive design classes, and component hierarchy
 */

import { IVisualGenerator, IResponsiveGenerator, IAccessibleGenerator } from '../interfaces/visual-generator-interface.js';
import { PrototypeAsset } from '../assets/visual-asset-models.js';
import { VisualIntelligenceCache } from '../research/visual-intelligence-cache.js';
import { TailwindCSSIntegration } from './tailwind-css-integration.js';

/**
 * HTML Template Engine
 * Generates semantic HTML prototypes from component specifications
 */
export class HTMLTemplateEngine extends IAccessibleGenerator {
  constructor(config = {}) {
    super();
    
    this.config = {
      enableAccessibility: config.enableAccessibility !== false,
      enableResponsive: config.enableResponsive !== false,
      enableCaching: config.enableCaching !== false,
      defaultBreakpoints: config.defaultBreakpoints || ['mobile', 'tablet', 'desktop'],
      semanticHTML: config.semanticHTML !== false,
      includeARIA: config.includeARIA !== false,
      generateCSS: config.generateCSS !== false,
      enableTailwind: config.enableTailwind !== false,
      tailwindMode: config.tailwindMode || 'cdn', // 'cdn', 'build', 'hybrid'
      ...config
    };

    // Initialize visual intelligence cache if enabled
    if (this.config.enableCaching) {
      this.visualCache = new VisualIntelligenceCache({
        maxCacheSize: config.maxCacheSize || 100,
        enableQualityScoring: true,
        enablePerformanceMonitoring: true
      });
    }

    this.generationStats = {
      totalGenerations: 0,
      cacheHits: 0,
      averageGenerationTime: 0,
      accessibilityEnhancements: 0,
      tailwindGenerations: 0
    };

    // Initialize Tailwind CSS integration if enabled
    if (this.config.enableTailwind) {
      this.tailwindIntegration = new TailwindCSSIntegration({
        enableDesignTokens: config.enableDesignTokens !== false,
        enableResponsiveUtilities: config.enableResponsiveUtilities !== false,
        enableCustomComponents: config.enableCustomComponents !== false,
        enableDarkMode: config.enableDarkMode || false,
        cdnMode: config.tailwindMode === 'cdn',
        customTheme: config.customTheme || {},
        enableCaching: config.enableCaching
      });
    }

    // HTML component templates
    this.componentTemplates = this.initializeComponentTemplates();
  }

  /**
   * Generate HTML prototype from requirements (implements IVisualGenerator)
   */
  async generate(requirements, options = {}) {
    const startTime = Date.now();
    this.generationStats.totalGenerations++;

    try {
      // Generate cache key
      const cacheKey = this.generateCacheKey(requirements, options);

      // Try cache first if enabled
      if (this.config.enableCaching && this.visualCache) {
        const cached = await this.visualCache.get(cacheKey, options);
        if (cached) {
          this.generationStats.cacheHits++;
          return cached;
        }
      }

      // Extract component specifications from requirements
      const componentSpecs = this.extractComponentSpecs(requirements);

      // Generate Tailwind data FIRST if enabled
      let cssContent = '';
      let tailwindData = null;

      if (this.config.enableTailwind && this.tailwindIntegration) {
        // Generate Tailwind classes and CSS
        tailwindData = await this.tailwindIntegration.generateTailwindClasses(
          componentSpecs,
          options.designSystem || {},
          options
        );
        cssContent = tailwindData.customCSS;
        this.generationStats.tailwindGenerations++;

        // Add Tailwind data to options so components can use it
        options.tailwindData = tailwindData;
      } else if (this.config.generateCSS) {
        // Fallback to basic CSS generation
        cssContent = await this.generateBasicCSS(componentSpecs, options);
      }

      // Generate HTML structure (now with Tailwind data available)
      const htmlContent = await this.generateHTMLStructure(componentSpecs, options);

      // Create prototype asset
      const prototypeAsset = new PrototypeAsset({
        title: requirements.title || 'Generated Prototype',
        description: requirements.description || 'HTML prototype generated from component specifications',
        htmlContent,
        cssContent,
        components: componentSpecs,
        designSystem: options.designSystem || {},
        responsiveBreakpoints: this.config.enableResponsive ?
          this.generateResponsiveBreakpoints(componentSpecs) : {},
        accessibilityFeatures: this.config.enableAccessibility ?
          this.generateAccessibilityFeatures(componentSpecs) : [],
        metadata: {
          sourceRequirements: requirements,
          generationMethod: 'html_template_engine',
          generatorVersion: '1.0.0',
          generatedAt: new Date().toISOString(),
          options: options,
          tailwindEnabled: this.config.enableTailwind,
          tailwindData: tailwindData
        }
      });

      // Apply accessibility enhancements if enabled
      if (this.config.enableAccessibility) {
        await this.enhanceAccessibility(prototypeAsset, options);
        this.generationStats.accessibilityEnhancements++;
      }

      // Update performance stats
      const generationTime = Date.now() - startTime;
      this.generationStats.averageGenerationTime = 
        (this.generationStats.averageGenerationTime * (this.generationStats.totalGenerations - 1) + generationTime) / 
        this.generationStats.totalGenerations;

      // Cache the result
      if (this.config.enableCaching && this.visualCache) {
        await this.visualCache.set(cacheKey, prototypeAsset, {
          requestType: 'html_generation',
          provider: 'html_template_engine',
          timestamp: Date.now()
        });
      }

      return prototypeAsset;

    } catch (error) {
      console.error('HTML template generation failed:', error);
      throw new Error(`HTML template generation failed: ${error.message}`);
    }
  }

  /**
   * Generate accessible HTML prototype (implements IAccessibleGenerator)
   */
  async generateAccessible(requirements, accessibilityOptions, options = {}) {
    const mergedOptions = {
      ...options,
      accessibility: {
        ...accessibilityOptions,
        enforceWCAG: true,
        includeARIA: true,
        semanticHTML: true
      }
    };

    return await this.generate(requirements, mergedOptions);
  }

  /**
   * Generate responsive HTML variants (implements IResponsiveGenerator)
   */
  async generateResponsive(requirements, breakpoints, options = {}) {
    const responsiveVariants = {};

    for (const breakpoint of breakpoints) {
      const breakpointOptions = {
        ...options,
        targetBreakpoint: breakpoint,
        responsive: true
      };

      const variant = await this.generate(requirements, breakpointOptions);
      responsiveVariants[breakpoint] = variant;
    }

    return responsiveVariants;
  }

  /**
   * Validate generated HTML (implements IVisualGenerator)
   */
  async validate(asset, options = {}) {
    const validationResults = {
      isValid: true,
      errors: [],
      warnings: [],
      accessibility: {
        score: 0,
        issues: []
      },
      semantic: {
        score: 0,
        issues: []
      }
    };

    try {
      // Validate HTML structure
      const htmlValidation = this.validateHTMLStructure(asset.htmlContent);
      validationResults.errors.push(...htmlValidation.errors);
      validationResults.warnings.push(...htmlValidation.warnings);

      // Validate accessibility if enabled
      if (this.config.enableAccessibility) {
        const accessibilityValidation = this.validateAccessibility(asset);
        validationResults.accessibility = accessibilityValidation;
      }

      // Validate semantic HTML
      if (this.config.semanticHTML) {
        const semanticValidation = this.validateSemanticHTML(asset.htmlContent);
        validationResults.semantic = semanticValidation;
      }

      validationResults.isValid = validationResults.errors.length === 0;

    } catch (error) {
      validationResults.isValid = false;
      validationResults.errors.push(`Validation error: ${error.message}`);
    }

    return validationResults;
  }

  /**
   * Get metadata about the generator (implements IVisualGenerator)
   */
  getMetadata() {
    return {
      name: 'HTML Template Engine',
      version: '1.0.0',
      description: 'Generates semantic HTML prototypes from component specifications',
      capabilities: [
        'semantic_html',
        'accessibility_features',
        'responsive_design',
        'component_hierarchy',
        'basic_css_generation'
      ],
      supportedFormats: ['html', 'css'],
      configuration: this.config,
      statistics: this.getGenerationStats()
    };
  }

  /**
   * Extract component specifications from requirements
   */
  extractComponentSpecs(requirements) {
    const specs = [];

    // Extract from componentSpecs if available
    if (requirements.componentSpecs) {
      specs.push(...requirements.componentSpecs);
    }

    // Extract from wireframes
    if (requirements.wireframes) {
      for (const wireframe of requirements.wireframes) {
        if (wireframe.components) {
          specs.push(...wireframe.components.map(comp => ({
            ...comp,
            source: 'wireframe',
            wireframeTitle: wireframe.title
          })));
        }
      }
    }

    // Extract from functional requirements
    if (requirements.functionalRequirements) {
      for (const req of requirements.functionalRequirements) {
        if (req.components) {
          specs.push(...req.components.map(comp => ({
            ...comp,
            source: 'functional_requirement',
            requirementId: req.id
          })));
        }
      }
    }

    return this.normalizeComponentSpecs(specs);
  }

  /**
   * Normalize component specifications to consistent format
   */
  normalizeComponentSpecs(specs) {
    return specs.map((spec, index) => ({
      id: spec.id || `component-${index}`,
      name: spec.name || spec.title || `Component ${index + 1}`,
      type: this.inferComponentType(spec),
      description: spec.description || '',
      priority: spec.priority || index + 1,
      accessibility: spec.accessibility || {},
      responsive: spec.responsive || {},
      interactions: spec.interactions || [],
      content: spec.content || '',
      attributes: spec.attributes || {},
      source: spec.source || 'unknown'
    }));
  }

  /**
   * Infer component type from specification
   */
  inferComponentType(spec) {
    const name = (spec.name || spec.title || '').toLowerCase();
    const description = (spec.description || '').toLowerCase();
    const text = `${name} ${description}`;

    if (text.includes('header') || text.includes('top')) return 'header';
    if (text.includes('nav') || text.includes('menu')) return 'navigation';
    if (text.includes('form') || text.includes('input')) return 'form';
    if (text.includes('table') || text.includes('list') || text.includes('data')) return 'table';
    if (text.includes('search') || text.includes('filter')) return 'search';
    if (text.includes('button') || text.includes('action')) return 'button';
    if (text.includes('sidebar') || text.includes('aside')) return 'sidebar';
    if (text.includes('footer') || text.includes('bottom')) return 'footer';
    if (text.includes('card') || text.includes('item')) return 'card';
    if (text.includes('modal') || text.includes('dialog')) return 'modal';

    return 'section'; // Default semantic container
  }

  /**
   * Initialize component templates
   */
  initializeComponentTemplates() {
    return {
      header: (spec, options) => this.generateHeaderTemplate(spec, options),
      navigation: (spec, options) => this.generateNavigationTemplate(spec, options),
      form: (spec, options) => this.generateFormTemplate(spec, options),
      table: (spec, options) => this.generateTableTemplate(spec, options),
      search: (spec, options) => this.generateSearchTemplate(spec, options),
      button: (spec, options) => this.generateButtonTemplate(spec, options),
      sidebar: (spec, options) => this.generateSidebarTemplate(spec, options),
      footer: (spec, options) => this.generateFooterTemplate(spec, options),
      card: (spec, options) => this.generateCardTemplate(spec, options),
      modal: (spec, options) => this.generateModalTemplate(spec, options),
      section: (spec, options) => this.generateSectionTemplate(spec, options)
    };
  }

  /**
   * Generate HTML structure from component specifications
   */
  async generateHTMLStructure(componentSpecs, options = {}) {
    const sortedSpecs = componentSpecs.sort((a, b) => (a.priority || 0) - (b.priority || 0));

    let htmlContent = '';

    // Generate document structure
    if (options.includeDocumentStructure !== false) {
      htmlContent += this.generateDocumentStart(options);
    }

    // Generate main container
    htmlContent += this.generateMainContainer(sortedSpecs, options);

    if (options.includeDocumentStructure !== false) {
      htmlContent += this.generateDocumentEnd(options);
    }

    return htmlContent;
  }

  /**
   * Generate document start
   */
  generateDocumentStart(options) {
    const lang = options.language || 'en';
    const title = options.title || 'Generated Prototype';

    let headContent = `<!DOCTYPE html>
<html lang="${lang}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
  ${this.config.enableResponsive ? this.generateResponsiveMeta() : ''}
  ${this.config.enableAccessibility ? this.generateAccessibilityMeta() : ''}`;

    // Add Tailwind CDN if enabled
    if (this.config.enableTailwind && this.config.tailwindMode === 'cdn' && this.tailwindIntegration) {
      const tailwindCDN = this.tailwindIntegration.generateTailwindCDNLink();
      if (tailwindCDN) {
        headContent += `\n  ${tailwindCDN}`;
      }
    }

    headContent += `
</head>
<body>
`;

    return headContent;
  }

  /**
   * Generate document end
   */
  generateDocumentEnd(options) {
    return `</body>
</html>`;
  }

  /**
   * Generate main container with components
   */
  generateMainContainer(componentSpecs, options) {
    let content = '';

    // Add main landmark for accessibility
    if (this.config.enableAccessibility) {
      content += '<main role="main" aria-label="Main content">\n';
    } else {
      content += '<main>\n';
    }

    // Generate each component
    for (const spec of componentSpecs) {
      const template = this.componentTemplates[spec.type];
      if (template) {
        content += template(spec, options) + '\n';
      } else {
        content += this.generateGenericComponent(spec, options) + '\n';
      }
    }

    content += '</main>\n';
    return content;
  }

  /**
   * Generate header template
   */
  generateHeaderTemplate(spec, options) {
    const ariaLabel = this.config.includeARIA ? ` aria-label="${spec.name}"` : '';
    const role = this.config.includeARIA ? ' role="banner"' : '';

    // Get Tailwind classes if enabled
    const headerClasses = this.getTailwindClasses(spec, 'header', options);
    const h1Classes = this.config.enableTailwind ? 'text-3xl font-bold text-gray-900' : '';
    const descClasses = this.config.enableTailwind ? 'text-gray-600 mt-2' : 'header-description';

    return `  <header${role}${ariaLabel}${headerClasses ? ` class="${headerClasses}"` : ''}>
    <h1${h1Classes ? ` class="${h1Classes}"` : ''}>${spec.name}</h1>
    ${spec.description ? `<p class="${descClasses}">${spec.description}</p>` : ''}
  </header>`;
  }

  /**
   * Generate navigation template
   */
  generateNavigationTemplate(spec, options) {
    const ariaLabel = this.config.includeARIA ? ` aria-label="${spec.name}"` : '';
    const role = this.config.includeARIA ? ' role="navigation"' : '';

    // Get Tailwind classes if enabled
    const navClasses = this.getTailwindClasses(spec, 'navigation', options);
    const ulClasses = this.config.enableTailwind ? 'flex space-x-4' : '';
    const linkClasses = this.config.enableTailwind ? 'text-gray-600 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200' : '';

    const navItems = spec.items || ['Home', 'About', 'Services', 'Contact'];
    const navList = navItems.map(item =>
      `      <li><a href="#${item.toLowerCase()}"${linkClasses ? ` class="${linkClasses}"` : ''}>${item}</a></li>`
    ).join('\n');

    return `  <nav${role}${ariaLabel}${navClasses ? ` class="${navClasses}"` : ''}>
    <ul${ulClasses ? ` class="${ulClasses}"` : ''}>
${navList}
    </ul>
  </nav>`;
  }

  /**
   * Generate form template
   */
  generateFormTemplate(spec, options) {
    const ariaLabel = this.config.includeARIA ? ` aria-label="${spec.name}"` : '';
    const fields = spec.fields || [
      { name: 'name', type: 'text', label: 'Name', required: true },
      { name: 'email', type: 'email', label: 'Email', required: true }
    ];

    const fieldHTML = fields.map(field => this.generateFormField(field)).join('\n');

    return `  <form${ariaLabel}>
    <fieldset>
      <legend>${spec.name}</legend>
${fieldHTML}
      <button type="submit">Submit</button>
    </fieldset>
  </form>`;
  }

  /**
   * Generate form field
   */
  generateFormField(field) {
    const required = field.required ? ' required' : '';
    const ariaRequired = field.required && this.config.includeARIA ? ' aria-required="true"' : '';
    const id = `field-${field.name}`;

    return `      <div class="form-field">
        <label for="${id}">${field.label}${field.required ? ' *' : ''}</label>
        <input type="${field.type}" id="${id}" name="${field.name}"${required}${ariaRequired}>
      </div>`;
  }

  /**
   * Generate table template
   */
  generateTableTemplate(spec, options) {
    const ariaLabel = this.config.includeARIA ? ` aria-label="${spec.name}"` : '';
    const caption = spec.description ? `    <caption>${spec.description}</caption>\n` : '';

    const headers = spec.headers || ['Column 1', 'Column 2', 'Column 3'];
    const headerHTML = headers.map(header => `        <th scope="col">${header}</th>`).join('\n');

    const rows = spec.rows || [
      ['Data 1', 'Data 2', 'Data 3'],
      ['Data 4', 'Data 5', 'Data 6']
    ];
    const rowsHTML = rows.map(row =>
      `      <tr>\n${row.map(cell => `        <td>${cell}</td>`).join('\n')}\n      </tr>`
    ).join('\n');

    return `  <table${ariaLabel}>
${caption}    <thead>
      <tr>
${headerHTML}
      </tr>
    </thead>
    <tbody>
${rowsHTML}
    </tbody>
  </table>`;
  }

  /**
   * Generate search template
   */
  generateSearchTemplate(spec, options) {
    const ariaLabel = this.config.includeARIA ? ` aria-label="${spec.name}"` : '';
    const role = this.config.includeARIA ? ' role="search"' : '';

    return `  <div${role}${ariaLabel}>
    <form class="search-form">
      <label for="search-input">${spec.name}</label>
      <input type="search" id="search-input" name="search" placeholder="${spec.placeholder || 'Search...'}">
      <button type="submit">Search</button>
    </form>
  </div>`;
  }

  /**
   * Generate button template
   */
  generateButtonTemplate(spec, options) {
    const ariaLabel = this.config.includeARIA && spec.description ? ` aria-label="${spec.description}"` : '';
    const type = spec.buttonType || 'button';

    // Get Tailwind classes if enabled
    const buttonClasses = this.getTailwindClasses(spec, 'button', options) ||
      (this.config.enableTailwind ?
        'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2' :
        (spec.variant ? `btn btn-${spec.variant}` : 'btn'));

    return `  <button type="${type}" class="${buttonClasses}"${ariaLabel}>${spec.name}</button>`;
  }

  /**
   * Generate sidebar template
   */
  generateSidebarTemplate(spec, options) {
    const ariaLabel = this.config.includeARIA ? ` aria-label="${spec.name}"` : '';
    const role = this.config.includeARIA ? ' role="complementary"' : '';

    const items = spec.items || ['Menu Item 1', 'Menu Item 2', 'Menu Item 3'];
    const itemsHTML = items.map(item => `      <li><a href="#${item.toLowerCase().replace(/\s+/g, '-')}">${item}</a></li>`).join('\n');

    return `  <aside${role}${ariaLabel}>
    <h2>${spec.name}</h2>
    <ul>
${itemsHTML}
    </ul>
  </aside>`;
  }

  /**
   * Generate footer template
   */
  generateFooterTemplate(spec, options) {
    const ariaLabel = this.config.includeARIA ? ` aria-label="${spec.name}"` : '';
    const role = this.config.includeARIA ? ' role="contentinfo"' : '';

    return `  <footer${role}${ariaLabel}>
    <p>${spec.content || '© 2024 Generated Prototype. All rights reserved.'}</p>
    ${spec.links ? this.generateFooterLinks(spec.links) : ''}
  </footer>`;
  }

  /**
   * Generate footer links
   */
  generateFooterLinks(links) {
    const linksHTML = links.map(link => `      <li><a href="${link.url || '#'}">${link.text}</a></li>`).join('\n');
    return `    <ul class="footer-links">
${linksHTML}
    </ul>`;
  }

  /**
   * Generate card template
   */
  generateCardTemplate(spec, options) {
    const ariaLabel = this.config.includeARIA ? ` aria-label="${spec.name}"` : '';

    // Get Tailwind classes if enabled
    const cardClasses = this.getTailwindClasses(spec, 'card', options);
    const h3Classes = this.config.enableTailwind ? 'text-xl font-semibold text-gray-900 mb-2' : '';
    const descClasses = this.config.enableTailwind ? 'text-gray-600 mb-4' : '';
    const contentClasses = this.config.enableTailwind ? 'text-gray-700 mb-4' : 'card-content';

    return `  <article${cardClasses ? ` class="${cardClasses}"` : ' class="card"'}${ariaLabel}>
    <h3${h3Classes ? ` class="${h3Classes}"` : ''}>${spec.name}</h3>
    ${spec.description ? `<p${descClasses ? ` class="${descClasses}"` : ''}>${spec.description}</p>` : ''}
    ${spec.content ? `<div${contentClasses ? ` class="${contentClasses}"` : ' class="card-content"'}>${spec.content}</div>` : ''}
    ${spec.actions ? this.generateCardActions(spec.actions) : ''}
  </article>`;
  }

  /**
   * Generate card actions
   */
  generateCardActions(actions) {
    const buttonClasses = this.config.enableTailwind ?
      'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2' :
      'card-action';
    const containerClasses = this.config.enableTailwind ? 'mt-4 flex gap-2' : 'card-actions';

    const actionsHTML = actions.map(action =>
      `      <button type="button" class="${buttonClasses}">${action.text || action}</button>`
    ).join('\n');
    return `    <div class="${containerClasses}">
${actionsHTML}
    </div>`;
  }

  /**
   * Generate modal template
   */
  generateModalTemplate(spec, options) {
    const ariaLabel = this.config.includeARIA ? ` aria-label="${spec.name}"` : '';
    const role = this.config.includeARIA ? ' role="dialog" aria-modal="true"' : '';

    return `  <div class="modal" id="${spec.id || 'modal'}"${role}${ariaLabel}>
    <div class="modal-content">
      <header class="modal-header">
        <h2>${spec.name}</h2>
        <button type="button" class="modal-close" aria-label="Close modal">&times;</button>
      </header>
      <div class="modal-body">
        ${spec.content || '<p>Modal content goes here.</p>'}
      </div>
      ${spec.actions ? this.generateModalActions(spec.actions) : ''}
    </div>
  </div>`;
  }

  /**
   * Generate modal actions
   */
  generateModalActions(actions) {
    const actionsHTML = actions.map(action =>
      `        <button type="button" class="modal-action">${action.text || action}</button>`
    ).join('\n');
    return `      <footer class="modal-footer">
${actionsHTML}
      </footer>`;
  }

  /**
   * Generate section template (generic container)
   */
  generateSectionTemplate(spec, options) {
    const ariaLabel = this.config.includeARIA && spec.description ? ` aria-label="${spec.description}"` : '';

    return `  <section${ariaLabel}>
    <h2>${spec.name}</h2>
    ${spec.description ? `<p>${spec.description}</p>` : ''}
    ${spec.content ? `<div class="section-content">${spec.content}</div>` : ''}
  </section>`;
  }

  /**
   * Generate generic component fallback
   */
  generateGenericComponent(spec, options) {
    const ariaLabel = this.config.includeARIA && spec.description ? ` aria-label="${spec.description}"` : '';

    return `  <div class="component component-${spec.type}"${ariaLabel}>
    <h3>${spec.name}</h3>
    ${spec.description ? `<p>${spec.description}</p>` : ''}
    ${spec.content ? `<div class="component-content">${spec.content}</div>` : ''}
  </div>`;
  }

  /**
   * Generate basic CSS for components
   */
  async generateBasicCSS(componentSpecs, options = {}) {
    let css = `/* Generated CSS for HTML Prototype */\n\n`;

    // Base styles
    css += this.generateBaseCSSStyles();

    // Component-specific styles
    for (const spec of componentSpecs) {
      css += this.generateComponentCSS(spec);
    }

    // Responsive styles if enabled
    if (this.config.enableResponsive) {
      css += this.generateResponsiveCSS(componentSpecs);
    }

    return css;
  }

  /**
   * Generate base CSS styles
   */
  generateBaseCSSStyles() {
    return `/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  margin: 0;
  padding: 0;
  color: #333;
}

main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

`;
  }

  /**
   * Generate CSS for specific component
   */
  generateComponentCSS(spec) {
    const componentCSS = {
      header: () => `/* Header Styles */
header {
  background: #f8f9fa;
  padding: 2rem 1rem;
  text-align: center;
  border-bottom: 1px solid #dee2e6;
}

header h1 {
  margin: 0 0 0.5rem 0;
  color: #212529;
}

`,
      navigation: () => `/* Navigation Styles */
nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  gap: 1rem;
}

nav a {
  text-decoration: none;
  color: #007bff;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

nav a:hover {
  background-color: #e9ecef;
}

`,
      form: () => `/* Form Styles */
form {
  max-width: 500px;
  margin: 2rem 0;
}

fieldset {
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  padding: 1rem;
}

legend {
  font-weight: bold;
  padding: 0 0.5rem;
}

.form-field {
  margin-bottom: 1rem;
}

label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 500;
}

input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

button {
  background: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}

`,
      table: () => `/* Table Styles */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

th, td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

th {
  background-color: #f8f9fa;
  font-weight: 600;
}

`,
      card: () => `/* Card Styles */
.card {
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  padding: 1rem;
  margin: 1rem 0;
  background: white;
}

.card h3 {
  margin-top: 0;
}

.card-actions {
  margin-top: 1rem;
  display: flex;
  gap: 0.5rem;
}

`,
      modal: () => `/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.modal-content {
  background: white;
  margin: 5% auto;
  padding: 0;
  border-radius: 0.25rem;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body {
  padding: 1rem;
}

.modal-footer {
  padding: 1rem;
  border-top: 1px solid #dee2e6;
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

`
    };

    const generator = componentCSS[spec.type];
    return generator ? generator() : '';
  }

  /**
   * Generate responsive CSS
   */
  generateResponsiveCSS(componentSpecs) {
    return `
/* Responsive Styles */
@media (max-width: 768px) {
  main {
    padding: 0.5rem;
  }

  nav ul {
    flex-direction: column;
  }

  .modal-content {
    margin: 2% auto;
    max-width: 95%;
  }
}

@media (max-width: 480px) {
  .card-actions {
    flex-direction: column;
  }

  .modal-footer {
    flex-direction: column;
  }
}
`;
  }

  /**
   * Generate responsive meta tags
   */
  generateResponsiveMeta() {
    return `  <meta name="viewport" content="width=device-width, initial-scale=1.0">`;
  }

  /**
   * Generate accessibility meta tags
   */
  generateAccessibilityMeta() {
    return `  <meta name="color-scheme" content="light dark">`;
  }

  /**
   * Generate responsive breakpoints configuration
   */
  generateResponsiveBreakpoints(componentSpecs) {
    return {
      mobile: { maxWidth: '480px', components: componentSpecs.length },
      tablet: { maxWidth: '768px', components: componentSpecs.length },
      desktop: { minWidth: '769px', components: componentSpecs.length }
    };
  }

  /**
   * Generate accessibility features list
   */
  generateAccessibilityFeatures(componentSpecs) {
    const features = [];

    if (this.config.semanticHTML) {
      features.push('semantic_html');
    }

    if (this.config.includeARIA) {
      features.push('aria_labels', 'aria_roles');
    }

    // Check for form accessibility
    if (componentSpecs.some(spec => spec.type === 'form')) {
      features.push('form_labels', 'required_indicators');
    }

    // Check for table accessibility
    if (componentSpecs.some(spec => spec.type === 'table')) {
      features.push('table_headers', 'table_captions');
    }

    // Check for navigation accessibility
    if (componentSpecs.some(spec => spec.type === 'navigation')) {
      features.push('navigation_landmarks');
    }

    return features;
  }

  /**
   * Enhance accessibility of prototype asset
   */
  async enhanceAccessibility(prototypeAsset, options) {
    // Add skip links
    if (options.accessibility?.includeSkipLinks !== false) {
      prototypeAsset.htmlContent = this.addSkipLinks(prototypeAsset.htmlContent);
    }

    // Add focus management
    if (options.accessibility?.includeFocusManagement !== false) {
      prototypeAsset.jsContent += this.generateFocusManagementJS();
    }

    // Add keyboard navigation
    if (options.accessibility?.includeKeyboardNav !== false) {
      prototypeAsset.jsContent += this.generateKeyboardNavigationJS();
    }
  }

  /**
   * Add skip links to HTML content
   */
  addSkipLinks(htmlContent) {
    const skipLinks = `  <div class="skip-links">
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <a href="#navigation" class="skip-link">Skip to navigation</a>
  </div>
`;

    return htmlContent.replace('<main', skipLinks + '<main id="main-content"');
  }

  /**
   * Generate focus management JavaScript
   */
  generateFocusManagementJS() {
    return `
// Focus management
document.addEventListener('DOMContentLoaded', function() {
  // Trap focus in modals
  const modals = document.querySelectorAll('.modal');
  modals.forEach(modal => {
    const focusableElements = modal.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    modal.addEventListener('keydown', function(e) {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            lastElement.focus();
            e.preventDefault();
          }
        } else {
          if (document.activeElement === lastElement) {
            firstElement.focus();
            e.preventDefault();
          }
        }
      }
    });
  });
});
`;
  }

  /**
   * Generate keyboard navigation JavaScript
   */
  generateKeyboardNavigationJS() {
    return `
// Keyboard navigation
document.addEventListener('keydown', function(e) {
  // Close modal on Escape
  if (e.key === 'Escape') {
    const openModal = document.querySelector('.modal[style*="block"]');
    if (openModal) {
      openModal.style.display = 'none';
    }
  }
});
`;
  }

  /**
   * Validate HTML structure
   */
  validateHTMLStructure(htmlContent) {
    const errors = [];
    const warnings = [];

    // Basic HTML validation
    if (!htmlContent.includes('<!DOCTYPE html>')) {
      warnings.push('Missing DOCTYPE declaration');
    }

    if (!htmlContent.includes('<html')) {
      errors.push('Missing html element');
    }

    if (!htmlContent.includes('<head>')) {
      errors.push('Missing head element');
    }

    if (!htmlContent.includes('<body>')) {
      errors.push('Missing body element');
    }

    // Check for unclosed tags (basic check)
    const openTags = htmlContent.match(/<[^/][^>]*>/g) || [];
    const closeTags = htmlContent.match(/<\/[^>]*>/g) || [];

    if (openTags.length !== closeTags.length) {
      warnings.push('Potential unclosed HTML tags detected');
    }

    return { errors, warnings };
  }

  /**
   * Validate accessibility features
   */
  validateAccessibility(asset) {
    const issues = [];
    let score = 100;

    // Check for alt attributes on images
    const imgTags = asset.htmlContent.match(/<img[^>]*>/g) || [];
    const imgsWithoutAlt = imgTags.filter(img => !img.includes('alt='));
    if (imgsWithoutAlt.length > 0) {
      issues.push(`${imgsWithoutAlt.length} images missing alt attributes`);
      score -= 20;
    }

    // Check for form labels
    const inputTags = asset.htmlContent.match(/<input[^>]*>/g) || [];
    const inputsWithoutLabels = inputTags.filter(input => {
      const id = input.match(/id="([^"]*)"/) || input.match(/id='([^']*)'/);
      if (!id) return true;
      return !asset.htmlContent.includes(`for="${id[1]}"`);
    });
    if (inputsWithoutLabels.length > 0) {
      issues.push(`${inputsWithoutLabels.length} form inputs missing labels`);
      score -= 15;
    }

    // Check for heading hierarchy
    const headings = asset.htmlContent.match(/<h[1-6][^>]*>/g) || [];
    if (headings.length === 0) {
      issues.push('No headings found - poor document structure');
      score -= 10;
    }

    return { score: Math.max(0, score), issues };
  }

  /**
   * Validate semantic HTML
   */
  validateSemanticHTML(htmlContent) {
    const issues = [];
    let score = 100;

    // Check for semantic elements
    const semanticElements = ['header', 'nav', 'main', 'section', 'article', 'aside', 'footer'];
    const foundElements = semanticElements.filter(element =>
      htmlContent.includes(`<${element}`)
    );

    if (foundElements.length < 3) {
      issues.push('Limited use of semantic HTML elements');
      score -= 20;
    }

    // Check for proper heading structure
    const h1Count = (htmlContent.match(/<h1[^>]*>/g) || []).length;
    if (h1Count === 0) {
      issues.push('Missing h1 element');
      score -= 15;
    } else if (h1Count > 1) {
      issues.push('Multiple h1 elements found');
      score -= 10;
    }

    return { score: Math.max(0, score), issues };
  }

  /**
   * Generate cache key for requirements
   */
  generateCacheKey(requirements, options) {
    const keyData = {
      requirementsHash: this.hashObject(requirements),
      optionsHash: this.hashObject(options),
      configHash: this.hashObject(this.config)
    };

    return `html_template_${JSON.stringify(keyData)}`.replace(/[^a-zA-Z0-9_]/g, '_');
  }

  /**
   * Simple hash function for objects
   */
  hashObject(obj) {
    return JSON.stringify(obj).split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
  }

  /**
   * Get generation statistics
   */
  getGenerationStats() {
    return {
      ...this.generationStats,
      cacheHitRate: this.generationStats.totalGenerations > 0 ?
        this.generationStats.cacheHits / this.generationStats.totalGenerations : 0,
      accessibilityRate: this.generationStats.totalGenerations > 0 ?
        this.generationStats.accessibilityEnhancements / this.generationStats.totalGenerations : 0,
      tailwindUsageRate: this.generationStats.totalGenerations > 0 ?
        this.generationStats.tailwindGenerations / this.generationStats.totalGenerations : 0
    };
  }

  /**
   * Get Tailwind classes for component
   */
  getTailwindClasses(spec, componentType, options) {
    if (!this.config.enableTailwind || !this.tailwindIntegration) {
      return '';
    }

    // Get cached Tailwind data from options if available
    const tailwindData = options.tailwindData ||
      (options.metadata && options.metadata.tailwindData);

    if (tailwindData && tailwindData.componentClasses && tailwindData.componentClasses[spec.id]) {
      return tailwindData.componentClasses[spec.id].final;
    }

    // Fallback to component class mappings
    const componentClasses = this.tailwindIntegration.componentClasses;
    return componentClasses[componentType] || componentClasses.default || '';
  }

  /**
   * Enhanced generateHTML method with Tailwind support
   */
  generateHTML() {
    // Check if htmlContent is already a complete document
    if (this.htmlContent.includes('<!DOCTYPE html>')) {
      // Already a complete document, just enhance it if needed
      let html = this.htmlContent;

      // If Tailwind is enabled and we have Tailwind data, enhance the HTML
      if (this.config.enableTailwind && this.metadata?.tailwindData) {
        const tailwindData = this.metadata.tailwindData;

        // Add Tailwind config script if needed
        if (tailwindData.tailwindConfig && this.config.tailwindMode === 'cdn') {
          const configScript = `
<script>
  tailwind.config = ${JSON.stringify(tailwindData.tailwindConfig, null, 2)};
</script>`;
          html = html.replace('</head>', `${configScript}\n</head>`);
        }
      }

      return html;
    } else {
      // Content only, wrap in complete document
      return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${this.title}</title>
  ${this.config.enableTailwind && this.config.tailwindMode === 'cdn' ?
    '<script src="https://cdn.tailwindcss.com"></script>' : ''}
  <style>${this.cssContent}</style>
</head>
<body>
  ${this.htmlContent}
  <script>${this.jsContent || ''}</script>
</body>
</html>`;
    }
  }
}
