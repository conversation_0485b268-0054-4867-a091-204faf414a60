/**
 * Research-Enhanced Decision Translation Example (Task 4.2)
 * 
 * Demonstrates the integration of research synthesis with WLR-005 business decision translation.
 * Shows how research findings inform business decisions while maintaining business-friendly language.
 */

import { ResearchEnhancedDecisionTranslator } from '../src/business-decisions/research-enhanced-decision-translator.js';
import { DecisionTranslator } from '../src/business-decisions/decision-translator.js';

/**
 * Example: Framework Selection with Research Enhancement
 */
async function frameworkSelectionWithResearchExample() {
  console.log('🔧 Framework Selection with Research Enhancement Example...\n');

  const projectRoot = process.cwd();
  const enhancedTranslator = new ResearchEnhancedDecisionTranslator(projectRoot);
  const baseTranslator = new DecisionTranslator(projectRoot);

  // Project context for a restaurant discovery app
  const projectContext = {
    domain: 'restaurant_discovery',
    businessModel: 'marketplace',
    targetAudience: 'food_enthusiasts',
    budget: {
      amount: 50000,
      category: 'small'
    },
    timeline: {
      duration: 6,
      unit: 'month',
      category: 'standard'
    },
    expertise: {
      category: 'intermediate',
      overall: 0.6
    }
  };

  try {
    // 1. Get base decision options (without research)
    console.log('1. Getting base framework selection options...');
    const baseDecision = await baseTranslator.translateDecisionOptions('framework_selection');
    console.log(`   📋 Base options: ${baseDecision.options.length} choices`);
    console.log(`   🎯 Recommended: ${baseDecision.options.find(opt => opt.isRecommended)?.title || 'None'}`);

    // 2. Get research-enhanced decision options
    console.log('\n2. Getting research-enhanced framework selection options...');
    const enhancedDecision = await enhancedTranslator.translateDecisionOptionsWithResearch(
      'framework_selection',
      projectContext,
      {
        enableResearch: true,
        researchProviders: ['tavily'],
        maxResults: 5
      }
    );

    console.log(`   📋 Enhanced options: ${enhancedDecision.options.length} choices`);
    console.log(`   🔍 Research conducted: ${enhancedDecision.researchBacking ? 'Yes' : 'No'}`);
    
    if (enhancedDecision.researchBacking) {
      console.log(`   📊 Research confidence: ${Math.round(enhancedDecision.researchBacking.confidence * 100)}%`);
      console.log(`   📚 Sources analyzed: ${enhancedDecision.researchBacking.sources}`);
    }

    // 3. Compare base vs enhanced options
    console.log('\n3. Comparing base vs enhanced options...');
    
    const reactOptionBase = baseDecision.options.find(opt => opt.id === 'react');
    const reactOptionEnhanced = enhancedDecision.options.find(opt => opt.id === 'react');

    if (reactOptionBase && reactOptionEnhanced) {
      console.log('\n   📊 React Option Comparison:');
      console.log(`   Base Business Impact: ${reactOptionBase.businessImpact?.description || reactOptionBase.businessImpact}`);
      console.log(`   Enhanced Business Impact: ${reactOptionEnhanced.businessImpact?.description || reactOptionEnhanced.businessImpact}`);
      
      if (reactOptionEnhanced.researchInsights) {
        console.log(`   🔬 Research Insights: ${reactOptionEnhanced.researchInsights.length} insights`);
        reactOptionEnhanced.researchInsights.forEach((insight, index) => {
          console.log(`      ${index + 1}. ${insight.insight.substring(0, 100)}...`);
        });
      }

      if (reactOptionEnhanced.confidence) {
        console.log(`   📈 Research-backed confidence: ${Math.round(reactOptionEnhanced.confidence * 100)}%`);
      }
    }

    // 4. Show recommendations
    if (enhancedDecision.recommendations) {
      console.log('\n4. Research-backed recommendations:');
      console.log(`   🎯 Primary: ${enhancedDecision.recommendations.primary?.title || 'None'}`);
      console.log(`   📝 Reasoning: ${enhancedDecision.recommendations.reasoning?.substring(0, 200) || 'Not available'}...`);
      console.log(`   💼 Business Justification: ${enhancedDecision.recommendations.businessJustification?.substring(0, 200) || 'Not available'}...`);
      
      if (enhancedDecision.recommendations.alternatives?.length > 0) {
        console.log(`   🔄 Alternatives: ${enhancedDecision.recommendations.alternatives.map(alt => alt.title).join(', ')}`);
      }
    }

    console.log('\n✅ Framework selection with research enhancement completed!');

  } catch (error) {
    console.error('❌ Framework selection example failed:', error);
  }
}

/**
 * Example: Custom Decision Template with Research Enhancement
 */
async function customDecisionWithResearchExample() {
  console.log('\n🛠️ Custom Decision Template with Research Enhancement...\n');

  const projectRoot = process.cwd();
  const enhancedTranslator = new ResearchEnhancedDecisionTranslator(projectRoot);

  // Project context for an e-commerce platform
  const projectContext = {
    domain: 'ecommerce',
    businessModel: 'b2c',
    targetAudience: 'online_shoppers',
    budget: {
      amount: 100000,
      category: 'medium'
    },
    timeline: {
      duration: 4,
      unit: 'month',
      category: 'fast'
    },
    expertise: {
      category: 'beginner',
      overall: 0.3
    }
  };

  try {
    // Create custom decision for payment processing
    console.log('1. Creating custom payment processing decision...');
    
    const customOptions = [
      {
        id: 'stripe',
        title: 'Stripe',
        description: 'Popular payment processing service',
        technicalDetails: 'RESTful API, extensive documentation, supports multiple payment methods'
      },
      {
        id: 'paypal',
        title: 'PayPal',
        description: 'Widely recognized payment platform',
        technicalDetails: 'PayPal API, buyer protection, global reach'
      },
      {
        id: 'square',
        title: 'Square',
        description: 'Comprehensive payment solution',
        technicalDetails: 'Square API, point-of-sale integration, analytics'
      }
    ];

    const enhancedTemplate = await enhancedTranslator.createResearchEnhancedCustomTemplate(
      'payment_processing',
      'Choose Your Payment Processing Solution',
      'Select the best payment processing service for your e-commerce platform',
      customOptions,
      projectContext
    );

    console.log(`   📋 Template created: ${enhancedTemplate.title}`);
    console.log(`   🔍 Research enhanced: ${enhancedTemplate.researchEnhanced ? 'Yes' : 'No'}`);
    
    if (enhancedTemplate.researchEnhanced) {
      console.log(`   📊 Research confidence: ${Math.round(enhancedTemplate.researchConfidence * 100)}%`);
      console.log(`   📝 Research summary: ${enhancedTemplate.researchSummary}`);
    }

    // 2. Show enhanced options
    console.log('\n2. Enhanced payment processing options:');
    enhancedTemplate.options.forEach((option, index) => {
      console.log(`\n   ${index + 1}. ${option.title}`);
      console.log(`      Description: ${option.description}`);
      
      if (option.businessImpact) {
        console.log(`      Business Impact: ${typeof option.businessImpact === 'string' ? option.businessImpact : option.businessImpact.description}`);
      }
      
      if (option.confidence) {
        console.log(`      Confidence: ${Math.round(option.confidence * 100)}%`);
      }
      
      if (option.tags && option.tags.length > 0) {
        console.log(`      Tags: ${option.tags.join(', ')}`);
      }
      
      if (option.researchInsights && option.researchInsights.length > 0) {
        console.log(`      Research Insights: ${option.researchInsights.length} insights available`);
      }
    });

    console.log('\n✅ Custom decision with research enhancement completed!');

  } catch (error) {
    console.error('❌ Custom decision example failed:', error);
  }
}

/**
 * Example: Business Decision Translation Comparison
 */
async function businessTranslationComparisonExample() {
  console.log('\n📊 Business Decision Translation Comparison...\n');

  const projectRoot = process.cwd();
  const baseTranslator = new DecisionTranslator(projectRoot);
  const enhancedTranslator = new ResearchEnhancedDecisionTranslator(projectRoot);

  const projectContext = {
    domain: 'fintech',
    businessModel: 'saas',
    budget: { category: 'large' },
    timeline: { category: 'urgent' },
    expertise: { category: 'advanced' }
  };

  try {
    console.log('1. Testing database selection decision...');
    
    // Base translation
    const baseDecision = await baseTranslator.translateDecisionOptions('database_selection');
    
    // Enhanced translation
    const enhancedDecision = await enhancedTranslator.translateDecisionOptionsWithResearch(
      'database_selection',
      projectContext,
      { enableResearch: true }
    );

    console.log('\n2. Translation comparison results:');
    console.log(`   📋 Base options: ${baseDecision.options.length}`);
    console.log(`   📋 Enhanced options: ${enhancedDecision.options.length}`);
    console.log(`   🔍 Research backing: ${enhancedDecision.researchBacking ? 'Available' : 'Not available'}`);
    console.log(`   💡 Recommendations: ${enhancedDecision.recommendations ? 'Generated' : 'Not generated'}`);

    // Show business-friendly language preservation
    console.log('\n3. Business-friendly language preservation:');
    const postgresBase = baseDecision.options.find(opt => opt.title.toLowerCase().includes('postgres'));
    const postgresEnhanced = enhancedDecision.options.find(opt => opt.title.toLowerCase().includes('postgres'));

    if (postgresBase && postgresEnhanced) {
      console.log('\n   PostgreSQL Option:');
      console.log(`   Base: ${postgresBase.description}`);
      console.log(`   Enhanced: ${postgresEnhanced.description}`);
      console.log(`   ✅ Business language maintained: ${!postgresEnhanced.description.includes('SQL') || postgresEnhanced.description.includes('database')}`);
    }

    // Show research cache statistics
    console.log('\n4. Research cache statistics:');
    const cacheStats = enhancedTranslator.getResearchCacheStats();
    console.log(`   📦 Cache size: ${cacheStats.size} entries`);
    console.log(`   🔑 Cache keys: ${cacheStats.keys.join(', ')}`);

    console.log('\n✅ Business decision translation comparison completed!');

  } catch (error) {
    console.error('❌ Translation comparison failed:', error);
  }
}

/**
 * Run all research-enhanced decision examples
 */
async function runResearchEnhancedDecisionExamples() {
  try {
    await frameworkSelectionWithResearchExample();
    await customDecisionWithResearchExample();
    await businessTranslationComparisonExample();
    
    console.log('\n🎉 All research-enhanced decision translation examples completed successfully!');
  } catch (error) {
    console.error('❌ Research-enhanced decision examples failed:', error);
    process.exit(1);
  }
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runResearchEnhancedDecisionExamples();
}

export {
  frameworkSelectionWithResearchExample,
  customDecisionWithResearchExample,
  businessTranslationComparisonExample,
  runResearchEnhancedDecisionExamples
};
