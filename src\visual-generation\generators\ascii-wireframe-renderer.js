/**
 * ASCII Wireframe Renderer Integration
 * Integrates ASCII wireframe generation with requirements-to-design transformer
 * while maintaining backward compatibility
 */

import { ASCIILayoutEngine } from './ascii-layout-engine.js';
import { ComponentMapper } from './component-mapper.js';
import { VisualIntelligenceCache } from '../research/visual-intelligence-cache.js';

/**
 * ASCII Wireframe Renderer
 * Bridges the gap between requirements analysis and visual ASCII wireframe generation
 */
export class ASCIIWireframeRenderer {
  constructor(config = {}) {
    this.config = {
      enableVisualGeneration: config.enableVisualGeneration !== false,
      enableCaching: config.enableCaching !== false,
      defaultBreakpoint: config.defaultBreakpoint || 'desktop',
      generateMultipleBreakpoints: config.generateMultipleBreakpoints || false,
      enhanceWithIntelligence: config.enhanceWithIntelligence !== false,
      ...config
    };

    // Initialize components
    this.layoutEngine = new ASCIILayoutEngine(config.layout || {});
    this.componentMapper = new ComponentMapper(config.componentMapping || {});
    
    // Initialize visual intelligence cache if enabled
    if (this.config.enableCaching) {
      this.visualCache = new VisualIntelligenceCache({
        maxCacheSize: config.maxCacheSize || 200,
        enableQualityScoring: true,
        enablePerformanceMonitoring: true
      });
    }

    this.renderingStats = {
      totalRenders: 0,
      cacheHits: 0,
      enhancedRenders: 0,
      averageRenderTime: 0
    };
  }

  /**
   * Enhanced wireframe generation that integrates with existing generateWireframes method
   */
  async enhanceWireframes(wireframes, context = {}) {
    if (!this.config.enableVisualGeneration) {
      return wireframes; // Return original wireframes if visual generation disabled
    }

    const startTime = Date.now();
    this.renderingStats.totalRenders++;

    try {
      const enhancedWireframes = [];

      for (const wireframe of wireframes) {
        const enhanced = await this.enhanceIndividualWireframe(wireframe, context);
        enhancedWireframes.push(enhanced);
      }

      // Update performance stats
      const renderTime = Date.now() - startTime;
      this.renderingStats.averageRenderTime = 
        (this.renderingStats.averageRenderTime * (this.renderingStats.totalRenders - 1) + renderTime) / 
        this.renderingStats.totalRenders;

      return enhancedWireframes;

    } catch (error) {
      console.warn('ASCII wireframe enhancement failed, returning original wireframes:', error.message);
      return wireframes; // Graceful fallback
    }
  }

  /**
   * Enhance individual wireframe with ASCII visual representation
   */
  async enhanceIndividualWireframe(wireframe, context) {
    // Generate cache key for this wireframe
    const cacheKey = this.generateWireframeCacheKey(wireframe, context);

    // Try cache first if enabled
    if (this.config.enableCaching && this.visualCache) {
      const cached = await this.visualCache.get(cacheKey, context);
      if (cached) {
        this.renderingStats.cacheHits++;
        return cached;
      }
    }

    // Map wireframe components to visual components
    const visualComponents = this.mapWireframeToComponents(wireframe);

    // Generate ASCII representation
    const asciiRepresentation = await this.generateASCIIRepresentation(
      visualComponents, 
      wireframe, 
      context
    );

    // Create enhanced wireframe
    const enhancedWireframe = {
      ...wireframe,
      // Maintain backward compatibility - all original fields preserved
      visualRepresentation: {
        ascii: asciiRepresentation,
        components: visualComponents,
        metadata: {
          generatedAt: new Date().toISOString(),
          renderingEngine: 'ASCIIWireframeRenderer',
          breakpoint: context.breakpoint || this.config.defaultBreakpoint,
          enhanced: true
        }
      },
      // Add visual metadata without breaking existing structure
      metadata: {
        ...wireframe.metadata,
        hasVisualRepresentation: true,
        visualGenerationEnabled: true,
        renderingStats: {
          componentCount: visualComponents.length,
          renderTime: Date.now()
        }
      }
    };

    // Apply intelligence enhancements if enabled
    if (this.config.enhanceWithIntelligence) {
      await this.applyIntelligenceEnhancements(enhancedWireframe, context);
      this.renderingStats.enhancedRenders++;
    }

    // Cache the enhanced wireframe
    if (this.config.enableCaching && this.visualCache) {
      await this.visualCache.set(cacheKey, enhancedWireframe, {
        requestType: 'wireframe_enhancement',
        provider: 'ascii_wireframe_renderer',
        timestamp: Date.now()
      });
    }

    return enhancedWireframe;
  }

  /**
   * Map wireframe specification to visual components
   */
  mapWireframeToComponents(wireframe) {
    const components = [];

    // Extract components from wireframe specification
    const wireframeComponents = wireframe.components || [];
    const wireframeTitle = wireframe.title || 'Wireframe';

    // Always add header for structure
    components.push({
      id: 'header',
      name: wireframeTitle,
      type: 'header',
      priority: 1
    });

    // Map wireframe components to visual components
    for (let i = 0; i < wireframeComponents.length; i++) {
      const component = wireframeComponents[i];
      const mappedComponent = this.componentMapper.mapComponent(component, {
        index: i,
        total: wireframeComponents.length,
        wireframeContext: wireframe
      });

      if (mappedComponent) {
        components.push({
          id: `component-${i}`,
          name: mappedComponent.name || component,
          type: mappedComponent.type || this.inferComponentType(component),
          priority: mappedComponent.priority || (i + 2),
          ...mappedComponent
        });
      }
    }

    // Add footer for completeness
    components.push({
      id: 'footer',
      name: 'Footer',
      type: 'footer',
      priority: 99
    });

    return components;
  }

  /**
   * Generate ASCII representation using layout engine
   */
  async generateASCIIRepresentation(components, wireframe, context) {
    const layoutOptions = {
      breakpoint: context.breakpoint || this.config.defaultBreakpoint,
      width: context.width || 80,
      showLabels: context.showLabels !== false,
      wireframeTitle: wireframe.title,
      wireframeDescription: wireframe.description
    };

    // Generate layout using ASCII layout engine
    const layout = this.layoutEngine.generateLayout(components, layoutOptions);

    // If multiple breakpoints requested, generate for all
    if (context.generateMultipleBreakpoints) {
      const breakpoints = ['mobile', 'tablet', 'desktop'];
      const multiBreakpointLayouts = {};

      for (const breakpoint of breakpoints) {
        const breakpointOptions = { ...layoutOptions, breakpoint };
        const breakpointLayout = this.layoutEngine.generateLayout(components, breakpointOptions);
        multiBreakpointLayouts[breakpoint] = breakpointLayout.ascii;
      }

      return {
        primary: layout.ascii,
        breakpoints: multiBreakpointLayouts,
        metadata: layout.metadata
      };
    }

    return layout.ascii;
  }

  /**
   * Apply intelligence enhancements to wireframe
   */
  async applyIntelligenceEnhancements(wireframe, context) {
    try {
      // Add accessibility indicators
      if (wireframe.accessibility) {
        wireframe.visualRepresentation.accessibility = {
          indicators: wireframe.accessibility,
          compliant: wireframe.accessibility.length > 0
        };
      }

      // Add interaction indicators
      if (wireframe.interactions) {
        wireframe.visualRepresentation.interactions = {
          types: wireframe.interactions,
          count: wireframe.interactions.length
        };
      }

      // Add responsive indicators
      if (wireframe.responsiveBreakpoints) {
        wireframe.visualRepresentation.responsive = {
          breakpoints: wireframe.responsiveBreakpoints,
          adaptive: true
        };
      }

      // Add priority-based visual indicators
      wireframe.visualRepresentation.metadata.priority = wireframe.priority || 'medium';
      wireframe.visualRepresentation.metadata.enhanced = true;

    } catch (error) {
      console.warn('Intelligence enhancement failed:', error.message);
    }
  }

  /**
   * Infer component type from component name/description
   */
  inferComponentType(component) {
    const componentText = (typeof component === 'string' ? component : component.name || '').toLowerCase();

    if (componentText.includes('form') || componentText.includes('input')) return 'form';
    if (componentText.includes('table') || componentText.includes('list') || componentText.includes('data')) return 'table';
    if (componentText.includes('nav') || componentText.includes('menu')) return 'navigation';
    if (componentText.includes('search') || componentText.includes('filter')) return 'search';
    if (componentText.includes('button') || componentText.includes('action')) return 'button';
    if (componentText.includes('upload') || componentText.includes('file')) return 'upload';
    if (componentText.includes('dashboard') || componentText.includes('chart')) return 'dashboard';
    if (componentText.includes('sidebar') || componentText.includes('aside')) return 'sidebar';
    if (componentText.includes('header') || componentText.includes('top')) return 'header';
    if (componentText.includes('footer') || componentText.includes('bottom')) return 'footer';

    return 'content'; // Default fallback
  }

  /**
   * Generate cache key for wireframe
   */
  generateWireframeCacheKey(wireframe, context) {
    const keyData = {
      wireframeId: wireframe.id,
      title: wireframe.title,
      components: wireframe.components?.length || 0,
      breakpoint: context.breakpoint || this.config.defaultBreakpoint,
      enhanced: this.config.enhanceWithIntelligence
    };

    return `wireframe_${JSON.stringify(keyData)}`.replace(/[^a-zA-Z0-9_]/g, '_');
  }

  /**
   * Get rendering statistics
   */
  getRenderingStats() {
    return {
      ...this.renderingStats,
      cacheHitRate: this.renderingStats.totalRenders > 0 ? 
        this.renderingStats.cacheHits / this.renderingStats.totalRenders : 0,
      enhancementRate: this.renderingStats.totalRenders > 0 ? 
        this.renderingStats.enhancedRenders / this.renderingStats.totalRenders : 0
    };
  }

  /**
   * Create comparison view of multiple wireframes
   */
  async generateWireframeComparison(wireframes, context = {}) {
    const enhancedWireframes = await this.enhanceWireframes(wireframes, context);
    
    let comparison = '┌' + '─'.repeat(78) + '┐\n';
    comparison += '│' + ' '.repeat(25) + 'WIREFRAME COMPARISON' + ' '.repeat(25) + '│\n';
    comparison += '└' + '─'.repeat(78) + '┘\n\n';

    for (let i = 0; i < enhancedWireframes.length; i++) {
      const wireframe = enhancedWireframes[i];
      comparison += `${i + 1}. ${wireframe.title}\n`;
      comparison += '─'.repeat(wireframe.title.length + 3) + '\n';
      
      if (wireframe.visualRepresentation?.ascii) {
        const ascii = typeof wireframe.visualRepresentation.ascii === 'string' ? 
          wireframe.visualRepresentation.ascii : 
          wireframe.visualRepresentation.ascii.primary || 'No visual representation';
        
        comparison += ascii + '\n\n';
      } else {
        comparison += '(No visual representation available)\n\n';
      }
    }

    return comparison;
  }
}
