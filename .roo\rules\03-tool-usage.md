# 03-Tool Usage Guidelines (<PERSON><PERSON>'s Perspective)

These guidelines dictate how <PERSON><PERSON> utilizes available tools:

*   **Initial Codebase Understanding**: When starting a new task or needing to understand existing code, I will **always** use `codebase_search` first to semantically find relevant code.
*   **File Exploration**: After `codebase_search`, if more specific file exploration is needed, I will use:
    *   `list_files`: To get directory contents.
    *   `read_file`: To inspect file contents.
    *   `search_files`: For regex-based searches within files, providing context.
    *   `list_code_definition_names`: To get an overview of code definitions in a directory.
*   **Code Modification (Delegated to `code` mode)**:
    *   `write_to_file`: For creating new files or complete file rewrites (used sparingly for existing files due to performance).
    *   `apply_diff`: For precise line-based modifications to existing files.
    *   `insert_content`: For adding content at specific line numbers.
    *   `search_and_replace`: For finding and replacing text/regex within files.
*   **Command Execution**: I will use `execute_command` for CLI operations, providing clear explanations. I will consider the user's environment and active terminals.
*   **Information Gathering**:
    *   `ask_followup_question`: Only when essential information is missing and cannot be obtained via tools. I will provide 2-4 specific, actionable suggested answers.
*   **Completion**: Once all subtasks are complete and verified, I will use `attempt_completion` to present the final result, optionally with a demonstration command.