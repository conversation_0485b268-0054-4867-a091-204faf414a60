/**
 * Asset Reference & Linking System
 * Creates linkable references between visual assets and requirements for traceability
 */

import { writeJSONF<PERSON>, readJSONFile } from '../../file-management/reliable-file-manager.js';
import path from 'path';
import fs from 'fs/promises';

/**
 * Asset Reference Manager
 */
export class AssetReferenceManager {
  constructor(projectRoot = process.cwd()) {
    this.projectRoot = projectRoot;
    this.referencesDir = path.join(projectRoot, '.guidant/deliverables/wireframes/.references');
  }

  /**
   * Initialize reference system
   */
  async initialize() {
    await fs.mkdir(this.referencesDir, { recursive: true });
    
    // Initialize reference files
    const referenceFiles = [
      'asset-references.json',
      'requirement-links.json',
      'cross-references.json',
      'dependency-graph.json'
    ];
    
    for (const file of referenceFiles) {
      const filePath = path.join(this.referencesDir, file);
      try {
        await fs.access(filePath);
      } catch {
        await this.initializeReferenceFile(file);
      }
    }
    
    return {
      initialized: true,
      referencesDirectory: this.referencesDir
    };
  }

  /**
   * Create reference between visual asset and source requirements
   */
  async createAssetReference(assetId, sourceData) {
    await this.initialize();
    
    const reference = {
      id: this.generateReferenceId(),
      assetId,
      timestamp: new Date().toISOString(),
      sourceType: sourceData.type || 'requirement', // requirement, user_story, feature_spec
      sourceId: sourceData.id || null,
      sourceContent: sourceData.content || '',
      sourceMetadata: sourceData.metadata || {},
      linkType: sourceData.linkType || 'generated_from', // generated_from, inspired_by, implements, validates
      confidence: sourceData.confidence || 1.0, // 0.0 to 1.0
      context: {
        phase: sourceData.phase || 'design',
        transformer: sourceData.transformer || 'requirements-to-design',
        generationMethod: sourceData.generationMethod || 'automatic'
      },
      traceability: {
        parentRequirements: sourceData.parentRequirements || [],
        childAssets: [],
        relatedAssets: [],
        impactedBy: [],
        impacts: []
      }
    };
    
    // Save to asset references
    const referencesPath = path.join(this.referencesDir, 'asset-references.json');
    const references = await readJSONFile(referencesPath);
    
    references.references.push(reference);
    references.totalReferences++;
    references.lastUpdated = new Date().toISOString();
    
    // Update statistics
    if (!references.statistics.bySourceType[reference.sourceType]) {
      references.statistics.bySourceType[reference.sourceType] = 0;
    }
    references.statistics.bySourceType[reference.sourceType]++;
    
    if (!references.statistics.byLinkType[reference.linkType]) {
      references.statistics.byLinkType[reference.linkType] = 0;
    }
    references.statistics.byLinkType[reference.linkType]++;
    
    await writeJSONFile(referencesPath, references);
    
    // Update requirement links
    await this.updateRequirementLinks(reference);
    
    // Update dependency graph
    await this.updateDependencyGraph(reference);
    
    return reference.id;
  }

  /**
   * Create cross-reference between visual assets
   */
  async createCrossReference(sourceAssetId, targetAssetId, relationshipData) {
    await this.initialize();
    
    const crossReference = {
      id: this.generateReferenceId(),
      sourceAssetId,
      targetAssetId,
      timestamp: new Date().toISOString(),
      relationshipType: relationshipData.type || 'related', // related, derived_from, contains, part_of, implements
      strength: relationshipData.strength || 0.5, // 0.0 to 1.0
      direction: relationshipData.direction || 'bidirectional', // unidirectional, bidirectional
      description: relationshipData.description || '',
      context: {
        createdBy: relationshipData.createdBy || 'system',
        reason: relationshipData.reason || 'automatic_detection',
        confidence: relationshipData.confidence || 0.8
      },
      metadata: relationshipData.metadata || {}
    };
    
    const crossRefsPath = path.join(this.referencesDir, 'cross-references.json');
    const crossRefs = await readJSONFile(crossRefsPath);
    
    // Check for existing cross-reference
    const existingRef = crossRefs.references.find(ref => 
      (ref.sourceAssetId === sourceAssetId && ref.targetAssetId === targetAssetId) ||
      (ref.direction === 'bidirectional' && 
       ref.sourceAssetId === targetAssetId && ref.targetAssetId === sourceAssetId)
    );
    
    if (existingRef) {
      // Update existing reference
      Object.assign(existingRef, crossReference);
      existingRef.lastUpdated = new Date().toISOString();
    } else {
      // Add new reference
      crossRefs.references.push(crossReference);
      crossRefs.totalReferences++;
    }
    
    crossRefs.lastUpdated = new Date().toISOString();
    
    // Update statistics
    if (!crossRefs.statistics.byType[crossReference.relationshipType]) {
      crossRefs.statistics.byType[crossReference.relationshipType] = 0;
    }
    crossRefs.statistics.byType[crossReference.relationshipType]++;
    
    await writeJSONFile(crossRefsPath, crossRefs);
    
    return crossReference.id;
  }

  /**
   * Get all references for an asset
   */
  async getAssetReferences(assetId, options = {}) {
    const [assetRefs, crossRefs] = await Promise.all([
      this.getAssetSourceReferences(assetId),
      this.getAssetCrossReferences(assetId)
    ]);
    
    const references = {
      assetId,
      sourceReferences: assetRefs,
      crossReferences: crossRefs,
      totalReferences: assetRefs.length + crossRefs.length
    };
    
    // Apply filters
    if (options.includeTraceability) {
      references.traceabilityChain = await this.buildTraceabilityChain(assetId);
    }
    
    if (options.includeImpactAnalysis) {
      references.impactAnalysis = await this.analyzeImpact(assetId);
    }
    
    return references;
  }

  /**
   * Get requirement links for PRD generation bridge
   */
  async getRequirementLinks(requirementId) {
    const linksPath = path.join(this.referencesDir, 'requirement-links.json');
    const links = await readJSONFile(linksPath);
    
    return links.links.filter(link => 
      link.requirementId === requirementId ||
      link.parentRequirements.includes(requirementId)
    );
  }

  /**
   * Build traceability chain from requirements to assets
   */
  async buildTraceabilityChain(assetId) {
    const assetRefs = await this.getAssetSourceReferences(assetId);
    const chain = {
      assetId,
      levels: [],
      totalLevels: 0
    };
    
    // Level 0: Direct source requirements
    const directSources = assetRefs.map(ref => ({
      type: ref.sourceType,
      id: ref.sourceId,
      content: ref.sourceContent,
      linkType: ref.linkType,
      confidence: ref.confidence
    }));
    
    if (directSources.length > 0) {
      chain.levels.push({
        level: 0,
        description: 'Direct source requirements',
        items: directSources
      });
    }
    
    // Level 1: Parent requirements (if available)
    const parentRequirements = [];
    for (const ref of assetRefs) {
      if (ref.traceability.parentRequirements) {
        parentRequirements.push(...ref.traceability.parentRequirements);
      }
    }
    
    if (parentRequirements.length > 0) {
      chain.levels.push({
        level: 1,
        description: 'Parent requirements',
        items: [...new Set(parentRequirements)] // Remove duplicates
      });
    }
    
    // Level 2: Related assets
    const crossRefs = await this.getAssetCrossReferences(assetId);
    if (crossRefs.length > 0) {
      chain.levels.push({
        level: 2,
        description: 'Related visual assets',
        items: crossRefs.map(ref => ({
          type: 'visual_asset',
          id: ref.sourceAssetId === assetId ? ref.targetAssetId : ref.sourceAssetId,
          relationshipType: ref.relationshipType,
          strength: ref.strength
        }))
      });
    }
    
    chain.totalLevels = chain.levels.length;
    return chain;
  }

  /**
   * Analyze impact of changes to an asset
   */
  async analyzeImpact(assetId) {
    const crossRefs = await this.getAssetCrossReferences(assetId);
    const dependencyGraph = await this.getDependencyGraph();
    
    const impact = {
      assetId,
      directImpact: [],
      indirectImpact: [],
      riskLevel: 'low', // low, medium, high, critical
      affectedAssets: 0,
      affectedRequirements: 0
    };
    
    // Direct impact: assets directly referencing this one
    const directlyAffected = crossRefs.filter(ref => 
      ref.targetAssetId === assetId || 
      (ref.direction === 'bidirectional' && ref.sourceAssetId !== assetId)
    );
    
    impact.directImpact = directlyAffected.map(ref => ({
      assetId: ref.sourceAssetId === assetId ? ref.targetAssetId : ref.sourceAssetId,
      relationshipType: ref.relationshipType,
      strength: ref.strength,
      riskContribution: this.calculateRiskContribution(ref)
    }));
    
    // Indirect impact: traverse dependency graph
    const visited = new Set([assetId]);
    const queue = [...impact.directImpact.map(item => item.assetId)];
    
    while (queue.length > 0) {
      const currentAssetId = queue.shift();
      if (visited.has(currentAssetId)) continue;
      
      visited.add(currentAssetId);
      const currentRefs = await this.getAssetCrossReferences(currentAssetId);
      
      const indirectlyAffected = currentRefs.filter(ref => 
        !visited.has(ref.sourceAssetId === currentAssetId ? ref.targetAssetId : ref.sourceAssetId)
      );
      
      impact.indirectImpact.push(...indirectlyAffected.map(ref => ({
        assetId: ref.sourceAssetId === currentAssetId ? ref.targetAssetId : ref.sourceAssetId,
        relationshipType: ref.relationshipType,
        strength: ref.strength * 0.5, // Reduce strength for indirect impact
        riskContribution: this.calculateRiskContribution(ref) * 0.5
      })));
      
      // Add to queue for further traversal (limit depth to prevent infinite loops)
      if (impact.indirectImpact.length < 50) {
        queue.push(...indirectlyAffected.map(ref => 
          ref.sourceAssetId === currentAssetId ? ref.targetAssetId : ref.sourceAssetId
        ));
      }
    }
    
    impact.affectedAssets = impact.directImpact.length + impact.indirectImpact.length;
    
    // Calculate risk level
    const totalRisk = [...impact.directImpact, ...impact.indirectImpact]
      .reduce((sum, item) => sum + item.riskContribution, 0);
    
    if (totalRisk > 0.8) impact.riskLevel = 'critical';
    else if (totalRisk > 0.6) impact.riskLevel = 'high';
    else if (totalRisk > 0.3) impact.riskLevel = 'medium';
    else impact.riskLevel = 'low';
    
    return impact;
  }

  /**
   * Get dependency graph for visualization
   */
  async getDependencyGraph() {
    const graphPath = path.join(this.referencesDir, 'dependency-graph.json');
    return await readJSONFile(graphPath);
  }

  /**
   * Update dependency graph with new reference
   */
  async updateDependencyGraph(reference) {
    const graphPath = path.join(this.referencesDir, 'dependency-graph.json');
    const graph = await readJSONFile(graphPath);
    
    // Add node if not exists
    if (!graph.nodes.find(node => node.id === reference.assetId)) {
      graph.nodes.push({
        id: reference.assetId,
        type: 'visual_asset',
        label: `Asset ${reference.assetId}`,
        metadata: {
          createdAt: reference.timestamp,
          sourceType: reference.sourceType
        }
      });
    }
    
    // Add source node if not exists
    if (reference.sourceId && !graph.nodes.find(node => node.id === reference.sourceId)) {
      graph.nodes.push({
        id: reference.sourceId,
        type: reference.sourceType,
        label: `${reference.sourceType} ${reference.sourceId}`,
        metadata: {
          content: reference.sourceContent.substring(0, 100) + '...'
        }
      });
    }
    
    // Add edge
    if (reference.sourceId) {
      graph.edges.push({
        id: reference.id,
        source: reference.sourceId,
        target: reference.assetId,
        type: reference.linkType,
        weight: reference.confidence,
        metadata: {
          createdAt: reference.timestamp,
          context: reference.context
        }
      });
    }
    
    graph.lastUpdated = new Date().toISOString();
    graph.totalNodes = graph.nodes.length;
    graph.totalEdges = graph.edges.length;
    
    await writeJSONFile(graphPath, graph);
  }

  // Helper methods
  generateReferenceId() {
    return `ref_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async initializeReferenceFile(filename) {
    const filePath = path.join(this.referencesDir, filename);
    const initialData = this.getInitialDataForReferenceFile(filename);
    await writeJSONFile(filePath, initialData);
  }

  getInitialDataForReferenceFile(filename) {
    const baseStructure = {
      version: '1.0.0',
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    switch (filename) {
      case 'asset-references.json':
        return {
          ...baseStructure,
          references: [],
          totalReferences: 0,
          statistics: {
            bySourceType: {},
            byLinkType: {}
          }
        };
      
      case 'requirement-links.json':
        return {
          ...baseStructure,
          links: [],
          totalLinks: 0,
          statistics: {
            byPhase: {},
            byTransformer: {}
          }
        };
      
      case 'cross-references.json':
        return {
          ...baseStructure,
          references: [],
          totalReferences: 0,
          statistics: {
            byType: {}
          }
        };
      
      case 'dependency-graph.json':
        return {
          ...baseStructure,
          nodes: [],
          edges: [],
          totalNodes: 0,
          totalEdges: 0,
          metadata: {
            graphType: 'directed',
            layout: 'hierarchical'
          }
        };
      
      default:
        return baseStructure;
    }
  }

  async getAssetSourceReferences(assetId) {
    const referencesPath = path.join(this.referencesDir, 'asset-references.json');
    const references = await readJSONFile(referencesPath);
    return references.references.filter(ref => ref.assetId === assetId);
  }

  async getAssetCrossReferences(assetId) {
    const crossRefsPath = path.join(this.referencesDir, 'cross-references.json');
    const crossRefs = await readJSONFile(crossRefsPath);
    return crossRefs.references.filter(ref => 
      ref.sourceAssetId === assetId || ref.targetAssetId === assetId
    );
  }

  async updateRequirementLinks(reference) {
    const linksPath = path.join(this.referencesDir, 'requirement-links.json');
    const links = await readJSONFile(linksPath);
    
    const link = {
      id: this.generateReferenceId(),
      requirementId: reference.sourceId,
      assetId: reference.assetId,
      linkType: reference.linkType,
      confidence: reference.confidence,
      timestamp: reference.timestamp,
      phase: reference.context.phase,
      transformer: reference.context.transformer,
      parentRequirements: reference.traceability.parentRequirements
    };
    
    links.links.push(link);
    links.totalLinks++;
    links.lastUpdated = new Date().toISOString();
    
    // Update statistics
    if (!links.statistics.byPhase[link.phase]) {
      links.statistics.byPhase[link.phase] = 0;
    }
    links.statistics.byPhase[link.phase]++;
    
    if (!links.statistics.byTransformer[link.transformer]) {
      links.statistics.byTransformer[link.transformer] = 0;
    }
    links.statistics.byTransformer[link.transformer]++;
    
    await writeJSONFile(linksPath, links);
  }

  calculateRiskContribution(reference) {
    // Simple risk calculation based on relationship strength and type
    const typeWeights = {
      'derived_from': 0.9,
      'implements': 0.8,
      'contains': 0.7,
      'part_of': 0.6,
      'related': 0.3
    };
    
    const typeWeight = typeWeights[reference.relationshipType] || 0.5;
    return reference.strength * typeWeight;
  }
}
