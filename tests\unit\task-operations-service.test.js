import { describe, it, expect, beforeEach, mock, spyOn } from 'bun:test'; // Using spyOn from bun:test
import { TaskOperationsService } from '../../src/task-management/task-operations-service.js';
import { TaskPersistenceService } from '../../src/task-management/task-persistence-service.js';
import { TASK_PRIORITIES } from '../../src/task-management/task-schema.js'; // Import TASK_PRIORITIES

// Mock TaskPersistenceService
mock.module('../../src/task-management/task-persistence-service.js', () => {
  return {
    TaskPersistenceService: mock(() => ({
      getTaskById: mock(async () => {}),
      updateTask: mock(async () => {}),
    })),
  };
});

// Get the mocked constructor and its methods for more specific mocking per test
const MockTaskPersistenceService = TaskPersistenceService;
let mockGetTaskById;
let mockUpdateTask;

describe('TaskOperationsService', () => {
  let taskOpsService;

  beforeEach(() => {
    // Reset the implementation of mocks for each test
    // This is important because the mock constructor is called once per import.
    // We need to access the mocked methods from the instance it creates.
    // A common pattern is to have the mock constructor store its method mocks
    // so they can be updated. Or, re-mock implementations if needed.

    // For simplicity here, we'll assume the mock constructor above
    // gives us fresh mocks or we re-assign them.
    // A more robust way would be:
    // const mockInstance = new MockTaskPersistenceService();
    // mockGetTaskById = mockInstance.getTaskById;
    // mockUpdateTask = mockInstance.updateTask;
    // taskOpsService = new TaskOperationsService('mock_project_root');
    // taskOpsService.persistenceService = mockInstance; // Inject mock

    // Simpler approach for now, assuming the mock setup is basic:
    taskOpsService = new TaskOperationsService('mock_project_root');
    // Access the mocked methods from the instance created by the service
    mockGetTaskById = taskOpsService.persistenceService.getTaskById;
    mockUpdateTask = taskOpsService.persistenceService.updateTask;

    mockGetTaskById.mockClear();
    mockUpdateTask.mockClear();
  });

  describe('calculateComplexityScore', () => {
    it('should calculate score correctly with valid factors', () => {
      const factors = {
        technicalDifficulty: 7,
        dependenciesComplexity: 5,
        businessImpact: 8,
        timeSensitivity: 6,
      };
      // (7*0.4) + (5*0.3) + (8*0.2) + (6*0.1) = 2.8 + 1.5 + 1.6 + 0.6 = 6.5
      expect(taskOpsService.calculateComplexityScore(factors)).toBe(6.5);
    });

    it('should use default values for missing factors', () => {
      const factors = { technicalDifficulty: 10 }; // Others default to 5
      // (10*0.4) + (5*0.3) + (5*0.2) + (5*0.1) = 4 + 1.5 + 1 + 0.5 = 7.0
      expect(taskOpsService.calculateComplexityScore(factors)).toBe(7.0);
    });

    it('should handle all default values', () => {
      // (5*0.4) + (5*0.3) + (5*0.2) + (5*0.1) = 2 + 1.5 + 1 + 0.5 = 5.0
      expect(taskOpsService.calculateComplexityScore({})).toBe(5.0);
    });

    it('should return -1 for invalid technicalDifficulty (too low) and log error', () => {
      const factors = { technicalDifficulty: 0 };
      const consoleErrorSpy = spyOn(console, 'error').mockImplementation(() => {});
      expect(taskOpsService.calculateComplexityScore(factors)).toBe(-1);
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error calculating complexity score:', 'Invalid technicalDifficulty factor: 0. Must be a number between 1 and 10.');
      consoleErrorSpy.mockRestore();
    });

    it('should return -1 for invalid technicalDifficulty (too high) and log error', () => {
      const factors = { technicalDifficulty: 11 };
      const consoleErrorSpy = spyOn(console, 'error').mockImplementation(() => {});
      expect(taskOpsService.calculateComplexityScore(factors)).toBe(-1);
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error calculating complexity score:', 'Invalid technicalDifficulty factor: 11. Must be a number between 1 and 10.');
      consoleErrorSpy.mockRestore();
    });

    it('should return -1 for invalid dependenciesComplexity (non-numeric) and log error', () => {
      const factors = { dependenciesComplexity: 'abc' };
      const consoleErrorSpy = spyOn(console, 'error').mockImplementation(() => {});
      expect(taskOpsService.calculateComplexityScore(factors)).toBe(-1);
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error calculating complexity score:', 'Invalid dependenciesComplexity factor: abc. Must be a number between 1 and 10.');
      consoleErrorSpy.mockRestore();
    });
     it('should return score rounded to one decimal place', () => {
      const factors = {
        technicalDifficulty: 1,
        dependenciesComplexity: 1,
        businessImpact: 1,
        timeSensitivity: 1,
      };
      // (1*0.4) + (1*0.3) + (1*0.2) + (1*0.1) = 0.4 + 0.3 + 0.2 + 0.1 = 1.0
      expect(taskOpsService.calculateComplexityScore(factors)).toBe(1.0);

      const factors2 = {
        technicalDifficulty: 3,
        dependenciesComplexity: 4,
        businessImpact: 2,
        timeSensitivity: 5,
      };
      // (3*0.4) + (4*0.3) + (2*0.2) + (5*0.1) = 1.2 + 1.2 + 0.4 + 0.5 = 3.3
      expect(taskOpsService.calculateComplexityScore(factors2)).toBe(3.3);
    });
  });

  describe('scoreTaskComplexity', () => {
    const taskId = 'task123';
    const mockTask = { id: taskId, title: 'Test Task', complexityScore: null };
    const validFactors = {
      technicalDifficulty: 6,
      dependenciesComplexity: 4,
      businessImpact: 7,
      timeSensitivity: 5,
    }; // Score: (6*0.4) + (4*0.3) + (7*0.2) + (5*0.1) = 2.4 + 1.2 + 1.4 + 0.5 = 5.5

    it('should update task with calculated complexity score', async () => {
      mockGetTaskById.mockResolvedValue({ ...mockTask });
      const updatedTaskData = { ...mockTask, complexityScore: 5.5 };
      mockUpdateTask.mockResolvedValue(updatedTaskData);

      const result = await taskOpsService.scoreTaskComplexity(taskId, validFactors);
      
      expect(mockGetTaskById).toHaveBeenCalledWith(taskId);
      expect(mockUpdateTask).toHaveBeenCalledWith(taskId, { complexityScore: 5.5 });
      expect(result).toEqual(updatedTaskData);
    });

    it('should throw error if task is not found', async () => {
      mockGetTaskById.mockResolvedValue(null);
      
      expect(async () => await taskOpsService.scoreTaskComplexity('nonexistent_id', validFactors))
        .toThrow('Task with ID nonexistent_id not found for complexity scoring.');
      expect(mockUpdateTask).not.toHaveBeenCalled();
    });

    it('should throw error if scoring factors are invalid', async () => {
      mockGetTaskById.mockResolvedValue({ ...mockTask });
      const invalidFactors = { technicalDifficulty: 0 }; // Invalid
      const consoleErrorSpy = spyOn(console, 'error').mockImplementation(() => {});
      
      expect(async () => await taskOpsService.scoreTaskComplexity(taskId, invalidFactors))
        .toThrow('Could not calculate complexity for task task123 due to invalid factors.');
      expect(mockUpdateTask).not.toHaveBeenCalled();
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error calculating complexity score:', 'Invalid technicalDifficulty factor: 0. Must be a number between 1 and 10.');
      consoleErrorSpy.mockRestore();
    });

    it('should propagate error from persistenceService.updateTask if it fails', async () => {
      mockGetTaskById.mockResolvedValue({ ...mockTask });
      mockUpdateTask.mockRejectedValue(new Error('DB update failed'));

      // scoreTaskComplexity does not have a try-catch for persistenceService.updateTask,
      // so the original error from the mock should propagate.
      // Using Bun's recommended way for async throws if the direct toThrow isn't working as expected.
      let errorThrown = null;
      try {
        await taskOpsService.scoreTaskComplexity(taskId, validFactors);
      } catch (e) {
        errorThrown = e;
      }
      expect(errorThrown).toBeInstanceOf(Error);
      expect(errorThrown?.message).toBe('DB update failed');
    });
  });

  describe('prioritizeTasks', () => {
    const task1 = { id: 't1', title: 'Task 1', priority: TASK_PRIORITIES.HIGH, businessContext: { businessImpact: 8 }, complexityScore: 3, createdAt: '2023-01-01T10:00:00Z' };
    const task2 = { id: 't2', title: 'Task 2', priority: TASK_PRIORITIES.MEDIUM, businessContext: { businessImpact: 9 }, complexityScore: 2, createdAt: '2023-01-02T10:00:00Z' };
    const task3 = { id: 't3', title: 'Task 3', priority: TASK_PRIORITIES.HIGH, businessContext: { businessImpact: 8 }, complexityScore: 5, createdAt: '2023-01-03T10:00:00Z' };
    const task4 = { id: 't4', title: 'Task 4', priority: TASK_PRIORITIES.LOW, businessContext: { businessImpact: 10 }, complexityScore: 1, createdAt: '2023-01-04T10:00:00Z' };
    const task5 = { id: 't5', title: 'Task 5', priority: TASK_PRIORITIES.HIGH, businessContext: { businessImpact: 9 }, complexityScore: 3, createdAt: '2023-01-05T10:00:00Z' }; // Highest impact in HIGH priority
    const task6 = { id: 't6', title: 'Task 6', priority: TASK_PRIORITIES.HIGH, businessContext: { businessImpact: 8 }, complexityScore: 3, createdAt: '2023-01-01T09:00:00Z' }; // Same BI & CS as task1, but older

    const tasks = [task2, task1, task4, task3, task5, task6];

    it('should return an empty array if no tasks are provided', () => {
      expect(taskOpsService.prioritizeTasks([])).toEqual([]);
      expect(taskOpsService.prioritizeTasks()).toEqual([]);
    });

    it('should sort tasks correctly based on priority, business impact, complexity, and creation date', () => {
      const sortedTasks = taskOpsService.prioritizeTasks(tasks);
      // Expected order:
      // 1. task5 (High, BI 9, CS 3)
      // 2. task6 (High, BI 8, CS 3, Older)
      // 3. task1 (High, BI 8, CS 3, Newer)
      // 4. task3 (High, BI 8, CS 5)
      // 5. task2 (Medium, BI 9, CS 2)
      // 6. task4 (Low, BI 10, CS 1)
      expect(sortedTasks.map(t => t.id)).toEqual(['t5', 't6', 't1', 't3', 't2', 't4']);
    });

    it('should handle tasks with missing optional sorting fields gracefully', () => {
      const taskWithMissing = [
        { id: 'm1', title: 'Missing All', createdAt: '2023-01-01T00:00:00Z' }, // Defaults: P:low, BI:0, CS:Infinity
        { id: 'm2', title: 'Missing Prio', businessContext: { businessImpact: 5 }, complexityScore: 5, createdAt: '2023-01-02T00:00:00Z' }, // P:low
        { id: 'm3', title: 'High Prio Only', priority: TASK_PRIORITIES.HIGH, createdAt: '2023-01-03T00:00:00Z' }, // BI:0, CS:Infinity
      ];
      const sorted = taskOpsService.prioritizeTasks(taskWithMissing);
      // Expected: m3 (High), then m2 (Low, BI 5), then m1 (Low, BI 0)
      expect(sorted.map(t => t.id)).toEqual(['m3', 'm2', 'm1']);
    });
  });
});