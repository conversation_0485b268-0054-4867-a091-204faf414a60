/**
 * @file ai-translation-service.js
 * @description AI-powered service for translating technical terms to business language
 */

import path from 'path';
import { readJSONFile } from '../utils/file-utils.js';

/**
 * AI Translation Service for converting technical terms to business language
 */
export class AITranslationService {
  /**
   * Create a new AITranslationService instance
   * @param {string} projectRoot - The project root directory
   */
  constructor(projectRoot = '.') {
    this.projectRoot = projectRoot;
    this.configPath = path.join(projectRoot, '.guidant', 'config.json');
    this.mockMode = false; // Add a flag to enable mock mode for testing
  }

  /**
   * Enable or disable mock mode for testing
   * @param {boolean} enabled - Whether mock mode should be enabled
   */
  setMockMode(enabled) {
    this.mockMode = enabled;
  }

  /**
   * Get the AI service configuration
   * @private
   * @async
   * @returns {Object} AI service configuration
   */
  async _getConfig() {
    try {
      const config = await readJSONFile(this.configPath);
      return {
        mainModel: config.models?.main || 'anthropic/claude-3-sonnet',
        temperature: 0.2,
        maxTokens: 500
      };
    } catch (error) {
      console.error('Error reading AI config:', error);
      // Default configuration if config file can't be read
      return {
        mainModel: 'anthropic/claude-3-sonnet',
        temperature: 0.2,
        maxTokens: 500
      };
    }
  }

  /**
   * Translate a technical term to business language using AI
   * @async
   * @param {string} technicalTerm - The technical term to translate
   * @param {string} context - Optional context about the term's usage
   * @param {string} expertiseLevel - User expertise level (novice, intermediate, expert)
   * @returns {Promise<string>} The business-friendly translation
   */
  async translateTerm(technicalTerm, context = '', expertiseLevel = 'novice') {
    // If in mock mode, return a mock response immediately
    if (this.mockMode) {
      return 'AI-translated business description';
    }
    
    try {
      // Import the AI service dynamically to avoid circular dependencies
      const aiServices = await import('../ai-services/unified-service.js');
      const { generateTextService } = aiServices;
      
      const config = await this._getConfig();
      
      // Create prompt based on expertise level
      let prompt = '';
      
      if (expertiseLevel === 'novice') {
        prompt = `Translate the technical term "${technicalTerm}" into simple, non-technical business language that a non-technical person would understand.`;
      } else if (expertiseLevel === 'intermediate') {
        prompt = `Translate the technical term "${technicalTerm}" into business-oriented language. The user has some technical knowledge, so you can keep some technical accuracy while focusing on business implications.`;
      } else {
        prompt = `Provide a business-oriented description of "${technicalTerm}" that maintains technical accuracy but emphasizes business value and implications.`;
      }
      
      // Add context if provided
      if (context) {
        prompt += `\n\nContext: ${context}`;
      }
      
      prompt += `\n\nProvide ONLY the translated term/phrase with no additional explanation or context. Keep it concise (max 10 words).`;
      
      // Call the AI service
      const result = await generateTextService({
        prompt,
        model: config.mainModel,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        commandName: 'translate_term',
        outputType: 'internal'
      });
      
      // Extract the translation from the result
      const translation = result.mainResult.text.trim();
      
      return translation;
    } catch (error) {
      console.error('Error translating term with AI:', error);
      // Fallback to a simple transformation if AI fails
      return `${technicalTerm} (business-oriented)`;
    }
  }

  /**
   * Generate business impact description for a technology
   * @async
   * @param {string} technology - The technology name
   * @param {string} technicalDetails - Technical details about the technology
   * @param {string} impactLevel - Impact level (low, medium, high)
   * @returns {Promise<string>} Business impact description
   */
  async generateBusinessImpact(technology, technicalDetails, impactLevel = 'medium') {
    // If in mock mode, return a mock response immediately
    if (this.mockMode) {
      return 'AI-generated business impact description';
    }
    
    try {
      // Import the AI service dynamically to avoid circular dependencies
      const aiServices = await import('../ai-services/unified-service.js');
      const { generateTextService } = aiServices;
      
      const config = await this._getConfig();
      
      const prompt = `
Describe the business impact of using "${technology}" in a software project.
Technical details: ${technicalDetails}
Impact level: ${impactLevel}

Provide a concise (15-20 words max) business-oriented description focusing on value, risk, and strategic implications.
Respond with ONLY the description, no additional text.
`;
      
      // Call the AI service
      const result = await generateTextService({
        prompt,
        model: config.mainModel,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        commandName: 'generate_business_impact',
        outputType: 'internal'
      });
      
      // Extract the description from the result
      const description = result.mainResult.text.trim();
      
      return description;
    } catch (error) {
      console.error('Error generating business impact with AI:', error);
      // Fallback descriptions based on impact level
      const fallbacks = {
        low: `Limited business impact with minimal changes to operations`,
        medium: `Moderate business impact affecting some operational aspects`,
        high: `Significant business impact with substantial operational benefits`
      };
      return fallbacks[impactLevel] || fallbacks.medium;
    }
  }

  /**
   * Generate a complete business-friendly decision template from technical details
   * @async
   * @param {string} technology - The technology name
   * @param {string} description - Technical description
   * @param {string} details - Detailed technical information
   * @returns {Promise<Object>} Business-friendly template content
   */
  async generateDecisionTemplate(technology, description, details) {
    // If in mock mode, return a mock response immediately
    if (this.mockMode) {
      return {
        title: `${technology} Solution`,
        description: `A business-oriented approach using ${technology}`,
        businessImpact: {
          level: "medium",
          description: "Moderate impact on business operations"
        },
        timeImplication: {
          level: "medium",
          description: "Standard implementation timeline"
        },
        costImplication: {
          level: "medium",
          description: "Average cost implications"
        },
        riskAssessment: {
          level: "medium",
          description: "Balanced risk profile"
        }
      };
    }
    
    try {
      // Import the AI service dynamically to avoid circular dependencies
      const aiServices = await import('../ai-services/unified-service.js');
      const { generateObjectService } = aiServices;
      
      const config = await this._getConfig();
      
      const prompt = `
Create a business-friendly decision template for the technology "${technology}".

Technical description: ${description}
Technical details: ${details}

Generate a complete business-oriented decision template with the following:
1. A business-friendly title
2. A non-technical description focusing on business value
3. Business impact description (high/medium/low with explanation)
4. Time implication description (high/medium/low with explanation)
5. Cost implication description (high/medium/low with explanation)
6. Risk assessment description (high/medium/low with explanation)

Format the response as a structured object.
`;
      
      // Call the AI service
      const result = await generateObjectService({
        prompt,
        outputFormat: {
          title: "string",
          description: "string",
          businessImpact: {
            level: "string", // high, medium, low
            description: "string"
          },
          timeImplication: {
            level: "string", // high, medium, low
            description: "string"
          },
          costImplication: {
            level: "string", // high, medium, low
            description: "string"
          },
          riskAssessment: {
            level: "string", // high, medium, low
            description: "string"
          }
        },
        model: config.mainModel,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        commandName: 'generate_decision_template',
        outputType: 'internal'
      });
      
      // Extract the template from the result
      const template = result.mainResult.object;
      
      return template;
    } catch (error) {
      console.error('Error generating decision template with AI:', error);
      // Return a fallback template
      return {
        title: `${technology} Solution`,
        description: `A business-oriented approach using ${technology}`,
        businessImpact: {
          level: "medium",
          description: "Moderate impact on business operations"
        },
        timeImplication: {
          level: "medium",
          description: "Standard implementation timeline"
        },
        costImplication: {
          level: "medium",
          description: "Average cost implications"
        },
        riskAssessment: {
          level: "medium",
          description: "Balanced risk profile"
        }
      };
    }
  }
}

export default AITranslationService; 