/**
 * Research-Informed Choice Presentation Example (Task 4.3)
 * 
 * Demonstrates how to present research-backed options in business terms with 
 * appropriate rationale for different user expertise levels.
 */

import { ResearchInformedChoicePresenter, getResearchInformedDecisionOptions } from '../src/business-decisions/research-informed-choice-presenter.js';
import { OptionPresenter } from '../src/business-decisions/option-presenter.js';

/**
 * Example: Framework Selection for Different Expertise Levels
 */
async function frameworkSelectionExpertiseLevelsExample() {
  console.log('🎯 Framework Selection for Different Expertise Levels...\n');

  const projectRoot = process.cwd();
  const presenter = new ResearchInformedChoicePresenter(projectRoot);

  // Base project context for a restaurant app
  const baseProjectContext = {
    domain: 'restaurant_discovery',
    businessModel: 'marketplace',
    targetAudience: 'food_enthusiasts',
    budget: { amount: 50000, category: 'small' },
    timeline: { duration: 6, unit: 'month', category: 'standard' }
  };

  const expertiseLevels = ['novice', 'intermediate', 'advanced'];

  for (const expertiseLevel of expertiseLevels) {
    console.log(`\n📊 ${expertiseLevel.toUpperCase()} User Presentation:`);
    console.log('='.repeat(50));

    try {
      const projectContext = {
        ...baseProjectContext,
        expertise: { category: expertiseLevel, overall: expertiseLevel === 'novice' ? 0.2 : expertiseLevel === 'intermediate' ? 0.6 : 0.9 }
      };

      const presentation = await presenter.getResearchInformedDecisionOptions(
        'framework_selection',
        projectContext,
        {
          enableResearch: true,
          includeCLIFormatting: false, // We'll format manually for demo
          includeVisualFormatting: true
        }
      );

      // Show presentation style adaptation
      console.log(`🎨 Presentation Style: ${presentation.adaptations?.expertiseLevel || 'unknown'}`);
      console.log(`🎯 Focus Areas: ${presentation.adaptations?.focusAreas?.join(', ') || 'none'}`);
      console.log(`📊 Complexity Level: ${presentation.adaptations?.complexityLevel || 'unknown'}`);

      // Show research summary adaptation
      if (presentation.researchSummary) {
        console.log(`\n🔍 Research Summary (${expertiseLevel}):`);
        console.log(`   ${presentation.researchSummary}`);
      }

      // Show option adaptation
      const reactOption = presentation.options.find(opt => opt.title === 'React');
      if (reactOption) {
        console.log(`\n⚛️ React Option Adaptation:`);
        console.log(`   Description: ${reactOption.description.substring(0, 100)}...`);
        console.log(`   Focus Areas: ${reactOption.focusAreas?.join(', ') || 'none'}`);
        console.log(`   Confidence: ${reactOption.confidenceIndicator || 'not available'}`);
        
        if (reactOption.researchRationale) {
          console.log(`   Research: ${reactOption.researchRationale.substring(0, 100)}...`);
        }
        
        if (expertiseLevel === 'advanced' && reactOption.technicalDetails) {
          console.log(`   Technical: ${reactOption.technicalDetails.substring(0, 100)}...`);
        }
      }

      // Show quick summary
      if (presentation.quickSummary) {
        console.log(`\n⚡ Quick Summary:`);
        console.log(`   Recommendation: ${presentation.quickSummary.recommendation}`);
        console.log(`   Confidence: ${presentation.quickSummary.confidence}`);
        console.log(`   Key Benefits: ${presentation.quickSummary.keyBenefits?.join(', ') || 'none'}`);
        console.log(`   Reasoning: ${presentation.quickSummary.reasoning.substring(0, 150)}...`);
      }

    } catch (error) {
      console.error(`❌ Failed to generate ${expertiseLevel} presentation:`, error.message);
    }
  }

  console.log('\n✅ Expertise level adaptation example completed!');
}

/**
 * Example: Database Selection with Visual Presentation
 */
async function databaseSelectionVisualExample() {
  console.log('\n💾 Database Selection with Visual Presentation...\n');

  const projectContext = {
    domain: 'ecommerce',
    businessModel: 'b2c',
    expertise: { category: 'intermediate', overall: 0.6 },
    budget: { amount: 100000, category: 'medium' },
    timeline: { duration: 4, unit: 'month', category: 'fast' }
  };

  try {
    const presentation = await getResearchInformedDecisionOptions({
      context: 'database_selection',
      projectContext,
      projectRoot: process.cwd(),
      options: {
        enableResearch: true,
        includeVisualFormatting: true,
        includeCLIFormatting: true
      }
    });

    console.log('📊 Visual Presentation Structure:');
    if (presentation.visualPresentation) {
      presentation.visualPresentation.sections.forEach((section, index) => {
        console.log(`\n${index + 1}. ${section.title} (${section.type})`);
        console.log(`   Style: ${section.style}`);
        
        if (section.type === 'options') {
          console.log(`   Options: ${section.content.length} choices`);
          section.content.forEach((option, optIndex) => {
            console.log(`      ${optIndex + 1}. ${option.title}: ${option.confidence || 'No confidence data'}`);
          });
        } else if (section.type === 'recommendations') {
          console.log(`   Primary: ${section.content.primary || 'None'}`);
          console.log(`   Alternatives: ${section.content.alternatives?.join(', ') || 'None'}`);
        }
      });
    }

    console.log('\n🖥️ CLI Presentation Preview:');
    if (presentation.cliPresentation) {
      // Show first 500 characters of CLI presentation
      console.log(presentation.cliPresentation.substring(0, 500) + '...');
    }

    console.log('\n✅ Visual presentation example completed!');

  } catch (error) {
    console.error('❌ Database selection visual example failed:', error);
  }
}

/**
 * Example: Comparison with Base Option Presenter
 */
async function comparisonWithBasePresenterExample() {
  console.log('\n📊 Comparison: Research-Informed vs Base Presenter...\n');

  const projectRoot = process.cwd();
  const basePresenter = new OptionPresenter(projectRoot);
  const researchPresenter = new ResearchInformedChoicePresenter(projectRoot);

  const projectContext = {
    domain: 'fintech',
    businessModel: 'saas',
    expertise: { category: 'intermediate', overall: 0.6 }
  };

  try {
    console.log('1. Getting base presentation...');
    const basePresentation = await basePresenter.getDecisionOptions('hosting_selection');

    console.log('2. Getting research-informed presentation...');
    const researchPresentation = await researchPresenter.getResearchInformedDecisionOptions(
      'hosting_selection',
      projectContext,
      { enableResearch: true }
    );

    console.log('\n📋 Comparison Results:');
    console.log(`Base Options: ${basePresentation.options?.length || 0}`);
    console.log(`Research-Informed Options: ${researchPresentation.options?.length || 0}`);
    console.log(`Research Backing: ${researchPresentation.researchBacking ? 'Available' : 'Not available'}`);
    console.log(`Expertise Adaptation: ${researchPresentation.adaptations ? 'Yes' : 'No'}`);
    console.log(`Quick Summary: ${researchPresentation.quickSummary ? 'Generated' : 'Not generated'}`);

    // Compare first option details
    const baseOption = basePresentation.options?.[0];
    const researchOption = researchPresentation.options?.[0];

    if (baseOption && researchOption) {
      console.log('\n🔍 First Option Comparison:');
      console.log(`Base Description Length: ${baseOption.description?.length || 0} chars`);
      console.log(`Research Description Length: ${researchOption.description?.length || 0} chars`);
      console.log(`Base Confidence: ${baseOption.confidence || 'Not available'}`);
      console.log(`Research Confidence: ${researchOption.confidence || 'Not available'}`);
      console.log(`Research Insights: ${researchOption.researchInsights?.length || 0} insights`);
      console.log(`Focus Areas: ${researchOption.focusAreas?.join(', ') || 'None'}`);
    }

    // Show presentation statistics
    console.log('\n📈 Presentation Statistics:');
    const stats = researchPresenter.getPresentationStats();
    console.log(`Cache Size: ${stats.cacheStats.size} entries`);
    console.log(`Templates Available: ${stats.templatesAvailable}`);
    console.log(`Expertise Levels: ${stats.expertiseLevels.join(', ')}`);

    console.log('\n✅ Comparison example completed!');

  } catch (error) {
    console.error('❌ Comparison example failed:', error);
  }
}

/**
 * Example: Custom Decision with Research Enhancement
 */
async function customDecisionResearchExample() {
  console.log('\n🛠️ Custom Decision with Research Enhancement...\n');

  const projectContext = {
    domain: 'healthcare',
    businessModel: 'saas',
    expertise: { category: 'advanced', overall: 0.8 },
    budget: { amount: 200000, category: 'large' },
    timeline: { duration: 8, unit: 'month', category: 'standard' }
  };

  try {
    const presentation = await getResearchInformedDecisionOptions({
      context: 'security_framework_selection',
      projectContext,
      projectRoot: process.cwd(),
      options: {
        enableResearch: true,
        researchProviders: ['tavily'],
        maxResults: 3,
        includeVisualFormatting: true
      }
    });

    console.log('🔒 Security Framework Selection Results:');
    console.log(`Title: ${presentation.title}`);
    console.log(`Options Available: ${presentation.options?.length || 0}`);
    console.log(`Research Enhanced: ${presentation.metadata?.researchInformed ? 'Yes' : 'No'}`);
    console.log(`Presentation Style: ${presentation.presentationStyle || 'Unknown'}`);

    // Show advanced user presentation features
    if (presentation.options && presentation.options.length > 0) {
      console.log('\n🎯 Advanced User Features:');
      const firstOption = presentation.options[0];
      
      if (firstOption.technicalDetails) {
        console.log(`✅ Technical Details: Available (${firstOption.technicalDetails.length} chars)`);
      }
      
      if (firstOption.focusAreas?.includes('technical_details')) {
        console.log(`✅ Technical Focus: Enabled`);
      }
      
      if (firstOption.researchInsights) {
        console.log(`✅ Research Insights: ${firstOption.researchInsights.length} insights`);
      }
      
      if (firstOption.confidenceIndicator?.includes('evidence quality')) {
        console.log(`✅ Advanced Confidence Metrics: Available`);
      }
    }

    // Show research summary for advanced users
    if (presentation.researchSummary) {
      console.log('\n📊 Advanced Research Summary:');
      console.log(presentation.researchSummary.substring(0, 300) + '...');
    }

    console.log('\n✅ Custom decision research example completed!');

  } catch (error) {
    console.error('❌ Custom decision research example failed:', error);
  }
}

/**
 * Run all research-informed choice presentation examples
 */
async function runResearchInformedChoicePresentationExamples() {
  try {
    await frameworkSelectionExpertiseLevelsExample();
    await databaseSelectionVisualExample();
    await comparisonWithBasePresenterExample();
    await customDecisionResearchExample();
    
    console.log('\n🎉 All research-informed choice presentation examples completed successfully!');
  } catch (error) {
    console.error('❌ Research-informed choice presentation examples failed:', error);
    process.exit(1);
  }
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runResearchInformedChoicePresentationExamples();
}

export {
  frameworkSelectionExpertiseLevelsExample,
  databaseSelectionVisualExample,
  comparisonWithBasePresenterExample,
  customDecisionResearchExample,
  runResearchInformedChoicePresentationExamples
};
