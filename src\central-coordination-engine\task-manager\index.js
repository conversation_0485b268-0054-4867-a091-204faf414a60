import fs from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

const TASKS_DIR = path.resolve(process.cwd(), '.guidant', 'tasks');
const TASKS_FILE_PATH = path.join(TASKS_DIR, 'tasks.json');

/**
 * Ensures the tasks directory and file exist.
 * @returns {Promise<void>}
 */
async function ensureTasksFile() {
  try {
    await fs.mkdir(TASKS_DIR, { recursive: true });
    await fs.access(TASKS_FILE_PATH);
  } catch (error) {
    if (error.code === 'ENOENT') {
      await fs.writeFile(TASKS_FILE_PATH, JSON.stringify([]), 'utf-8');
    } else {
      console.error('Error ensuring tasks file:', error);
      throw error;
    }
  }
}

/**
 * Reads all tasks from the JSON file.
 * @returns {Promise<Array<Object>>} A promise that resolves to an array of task objects.
 */
async function getAllTasks() {
  await ensureTasksFile();
  try {
    const data = await fs.readFile(TASKS_FILE_PATH, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading tasks file:', error);
    if (error.code === 'ENOENT') {
      return []; // Return empty array if file doesn't exist after check (should not happen with ensureTasksFile)
    }
    throw error; // Re-throw other errors
  }
}

/**
 * Writes all tasks to the JSON file.
 * @param {Array<Object>} tasks - An array of task objects to write.
 * @returns {Promise<void>}
 */
async function writeAllTasks(tasks) {
  await ensureTasksFile();
  try {
    await fs.writeFile(TASKS_FILE_PATH, JSON.stringify(tasks, null, 2), 'utf-8');
  } catch (error) {
    console.error('Error writing tasks file:', error);
    throw error;
  }
}

/**
 * Creates a new task.
 * @param {Object} taskData - The data for the new task.
 *   Schema:
 *   - title: string (required)
 *   - description: string (required)
 *   - status: string (default: "pending")
 *   - priority: string (default: "medium")
 *   - dependencies: Array<string> (default: [])
 *   - assignedHats: Array<string> (default: [])
 *   - details: string (optional)
 *   - testStrategy: string (optional)
 *   - acceptanceCriteria: string (optional)
 *   - estimatedEffort: string (optional)
 *   - tags: Array<string> (default: [])
 *   - dueDate: string (ISO 8601, optional)
 *   - notes: string (optional)
 * @returns {Promise<Object>} The created task object.
 */
export async function createTask(taskData) {
  if (!taskData.title || !taskData.description) {
    throw new Error('Task title and description are required.');
  }

  const tasks = await getAllTasks();
  const now = new Date().toISOString();

  const newTask = {
    id: uuidv4(),
    title: taskData.title,
    description: taskData.description,
    status: taskData.status || 'pending',
    priority: taskData.priority || 'medium',
    dependencies: taskData.dependencies || [],
    assignedHats: taskData.assignedHats || [],
    complexityScore: null, // To be scored later
    subtasks: [],
    details: taskData.details || '',
    testStrategy: taskData.testStrategy || '',
    acceptanceCriteria: taskData.acceptanceCriteria || '',
    estimatedEffort: taskData.estimatedEffort || null,
    actualEffort: null,
    tags: taskData.tags || [],
    history: [{ timestamp: now, user: 'system', action: 'created', details: 'Task created' }],
    createdAt: now,
    updatedAt: now,
    dueDate: taskData.dueDate || null,
    blockedReason: null,
    notes: taskData.notes || '',
  };

  tasks.push(newTask);
  await writeAllTasks(tasks);
  return newTask;
}

/**
 * Reads a specific task by its ID.
 * @param {string} taskId - The ID of the task to retrieve.
 * @returns {Promise<Object|null>} The task object, or null if not found.
 */
export async function readTask(taskId) {
  const tasks = await getAllTasks();
  const task = tasks.find(t => t.id === taskId);
  // Consider searching in subtasks recursively if needed, for now top-level only
  return task || null;
}

/**
 * Updates an existing task.
 * @param {string} taskId - The ID of the task to update.
 * @param {Object} updates - An object containing the fields to update.
 * @returns {Promise<Object|null>} The updated task object, or null if not found.
 */
export async function updateTask(taskId, updates) {
  const tasks = await getAllTasks();
  const taskIndex = tasks.findIndex(t => t.id === taskId);

  if (taskIndex === -1) {
    return null; // Task not found
  }

  const originalTask = tasks[taskIndex];
  const now = new Date().toISOString();
  const updatedTask = { ...originalTask, ...updates, updatedAt: now };

  // Add to history
  const changes = Object.keys(updates)
    .filter(key => originalTask[key] !== updatedTask[key])
    .map(key => `${key}: from '${originalTask[key]}' to '${updatedTask[key]}'`)
    .join(', ');
  
  if (changes) {
    updatedTask.history = [
      ...(originalTask.history || []),
      { timestamp: now, user: 'system', action: 'updated', details: `Fields updated: ${changes}` }
    ];
  }


  tasks[taskIndex] = updatedTask;
  await writeAllTasks(tasks);
  return updatedTask;
}

/**
 * Deletes a task by its ID.
 * @param {string} taskId - The ID of the task to delete.
 * @returns {Promise<boolean>} True if the task was deleted, false otherwise.
 */
export async function deleteTask(taskId) {
  let tasks = await getAllTasks();
  const initialLength = tasks.length;
  tasks = tasks.filter(t => t.id !== taskId);

  if (tasks.length < initialLength) {
    await writeAllTasks(tasks);
    return true;
  }
  return false;
}

/**
 * Lists tasks, optionally filtered.
 * @param {Object} filters - An object containing filter criteria (e.g., { status: "pending", priority: "high" }).
 * @returns {Promise<Array<Object>>} An array of task objects matching the filters.
 */
export async function listTasks(filters = {}) {
  let tasks = await getAllTasks();
  if (Object.keys(filters).length === 0) {
    return tasks;
  }

  return tasks.filter(task => {
    for (const key in filters) {
      if (Object.prototype.hasOwnProperty.call(filters, key)) {
        if (Array.isArray(task[key]) && Array.isArray(filters[key])) {
          // For array fields like tags or assignedHats, check for intersection
          if (!filters[key].some(filterItem => task[key].includes(filterItem))) {
            return false;
          }
        } else if (task[key] !== filters[key]) {
          return false;
        }
      }
    }
    return true;
  });
}

/**
 * Placeholder for AI-driven task creation.
 * This function will eventually integrate with an AI service to generate task data
 * based on a high-level prompt, then use createTask to save it.
 * @param {string} prompt - The high-level prompt for task generation.
 * @param {Object} additionalContext - Any additional context for the AI.
 * @returns {Promise<Object>} The created task object.
 */
export async function addTaskWithAI(prompt, additionalContext = {}) {
  console.log(`[AI Task Creation Hook] Received prompt: "${prompt}"`);
  console.log('[AI Task Creation Hook] Additional context:', additionalContext);

  // --- AI Integration Placeholder ---
  // In a real implementation, this would involve:
  // 1. Formatting the prompt and context for an AI model.
  // 2. Calling the AI service (e.g., OpenAI, Anthropic).
  // 3. Parsing the AI's response to extract task details (title, description, etc.).
  // 4. Potentially performing validation or refinement of the AI-generated data.

  // For now, we'll create a mock task based on the prompt.
  const mockTaskData = {
    title: `AI Generated: ${prompt.substring(0, 50)}${prompt.length > 50 ? '...' : ''}`,
    description: `Task generated by AI based on prompt: "${prompt}". Additional context: ${JSON.stringify(additionalContext)}`,
    details: "Further details to be elaborated by AI or user.",
    testStrategy: "Test strategy to be defined by AI or user.",
    // Potentially extract priority, dependencies, etc. from additionalContext or AI
  };
  // --- End AI Integration Placeholder ---

  console.log('[AI Task Creation Hook] Mock task data generated:', mockTaskData);
  const newTask = await createTask(mockTaskData);
  console.log('[AI Task Creation Hook] Task created with ID:', newTask.id);
  return newTask;
}

/**
 * Placeholder for task complexity scoring.
 * This function will eventually use AI to assess the complexity of a given task
 * and assign a score.
 * @param {string | Object} taskOrId - The task object or its ID.
 * @returns {Promise<number|null>} The complexity score (e.g., 1-10), or null if scoring fails.
 */
export async function scoreTaskComplexity(taskOrId) {
  let taskData;
  if (typeof taskOrId === 'string') {
    taskData = await readTask(taskOrId);
    if (!taskData) {
      console.error(`[Complexity Scoring Hook] Task with ID "${taskOrId}" not found.`);
      return null;
    }
  } else {
    taskData = taskOrId;
  }

  console.log(`[Complexity Scoring Hook] Scoring task: "${taskData.title}" (ID: ${taskData.id})`);

  // --- AI Integration Placeholder for Complexity Scoring ---
  // In a real implementation, this would involve:
  // 1. Preparing task data (title, description, details, etc.) for an AI model.
  // 2. Calling an AI service with a prompt asking for a complexity score (e.g., on a scale of 1-10).
  // 3. Parsing the AI's response to get the score.

  // For now, a simple heuristic or a mock score.
  let mockScore = 5; // Default mock score
  if (taskData.description && taskData.description.length > 100) mockScore += 1;
  if (taskData.details && taskData.details.length > 200) mockScore += 1;
  if (taskData.dependencies && taskData.dependencies.length > 2) mockScore += 1;
  if (taskData.subtasks && taskData.subtasks.length > 3) mockScore +=1;
  mockScore = Math.min(10, Math.max(1, mockScore)); // Clamp between 1 and 10
  // --- End AI Integration Placeholder ---

  console.log(`[Complexity Scoring Hook] Mock complexity score for task ${taskData.id}: ${mockScore}`);
  
  // Update the task with the new score
  await updateTask(taskData.id, { complexityScore: mockScore });
  
  return mockScore;
}
