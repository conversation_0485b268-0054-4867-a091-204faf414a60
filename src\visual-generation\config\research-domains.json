{"domains": {"ui_practices": ["material.io", "developer.apple.com", "design.microsoft.com", "ant.design", "chakra-ui.com", "mui.com", "ui.shadcn.com", "tailwindui.com", "headlessui.com", "mantine.dev", "nextui.org", "daisyui.com", "flowbite.com", "primefaces.org", "semantic-ui.com"], "accessibility": ["w3.org", "webaim.org", "a11y.org", "accessibility.blog.gov.uk", "deque.com", "axe-core.org", "inclusive-components.design", "a11yproject.com"], "design_system": ["designsystems.com", "atomicdesign.bradfrost.com", "material.io", "spectrum.adobe.com", "polaris.shopify.com", "primer.style", "carbon.ibm.com", "atlassian.design", "lightning.design", "fluent2.microsoft.com"], "component_libraries": ["storybook.js.org", "bit.dev", "github.com", "npmjs.com", "unpkg.com"], "performance": ["web.dev", "developers.google.com", "lighthouse-metrics.com", "webpagetest.org"], "mobile_design": ["developer.android.com", "developer.apple.com", "flutter.dev", "reactnative.dev"]}, "libraries": {"react": ["react", "mui", "chakra-ui", "ant-design", "mantine", "react-bootstrap", "semantic-ui-react", "headlessui", "shadcn/ui", "nextui", "arco-design", "react-spectrum", "grommet", "evergreen", "rebass", "theme-ui", "<PERSON><PERSON>t"], "vue": ["vue", "vuetify", "quasar", "element-plus", "naive-ui", "ant-design-vue", "primevue", "vuestic-ui", "buefy", "oruga", "chakra-ui-vue"], "angular": ["angular", "angular-material", "ng-bootstrap", "primeng", "ng-zorro-antd", "clarity", "nebular", "taiga-ui", "ngx-admin"], "svelte": ["svelte", "svel<PERSON><PERSON>", "carbon-components-svelte", "smelte", "attractions", "svelte-material-ui", "skeleton"], "solid": ["solid-js", "hope-ui", "solid-ui", "kobalte", "solid-primitives"], "web_components": ["lit", "stencil", "fast", "shoelace", "lion", "hybrids", "haunted"], "css_frameworks": ["tailwindcss", "bootstrap", "bulma", "foundation", "uikit", "semantic-ui", "materialize", "spectre"]}, "industry_domains": {"healthcare": ["hl7.org", "himss.org", "healthit.gov", "epic.com", "cerner.com"], "finance": ["swift.com", "federalreserve.gov", "sec.gov", "finra.org", "bis.org"], "ecommerce": ["shopify.com", "magento.com", "woocommerce.com", "bigcommerce.com", "salesforce.com"], "education": ["educause.edu", "canvas.instructure.com", "blackboard.com", "moodle.org"], "government": ["usability.gov", "18f.gsa.gov", "designsystem.digital.gov", "gov.uk"]}, "research_priorities": {"ui_practices": {"weight": 0.3, "max_results": 5, "search_depth": "basic"}, "accessibility": {"weight": 0.4, "max_results": 3, "search_depth": "advanced"}, "design_system": {"weight": 0.2, "max_results": 4, "search_depth": "basic"}, "component_libraries": {"weight": 0.3, "max_results": 3, "search_depth": "basic"}}, "cache_settings": {"ui_practices": {"duration": 3600000, "max_entries": 100}, "accessibility": {"duration": 7200000, "max_entries": 50}, "design_system": {"duration": 3600000, "max_entries": 75}}}