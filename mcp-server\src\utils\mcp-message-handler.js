/**
 * MCP Message Handler
 * Handles JSON-RPC 2.0 message processing for MCP primitives
 */

/**
 * MCP-compliant message handler for JSON-RPC 2.0
 */
export class MCPMessageHandler {
  constructor() {
    this.requestId = 1;
  }

  /**
   * Create a JSON-RPC 2.0 request message
   * @param {string} method - Method name
   * @param {object} [params] - Request parameters
   * @param {string|number} [id] - Request ID (auto-generated if not provided)
   * @returns {object} JSON-RPC request
   */
  createRequest(method, params = {}, id = null) {
    const request = {
      jsonrpc: "2.0",
      method,
      id: id !== null ? id : this.requestId++
    };

    if (Object.keys(params).length > 0) {
      request.params = params;
    }

    return request;
  }

  /**
   * Create a JSON-RPC 2.0 success response
   * @param {string|number} id - Request ID
   * @param {any} result - Response result
   * @returns {object} JSON-RPC response
   */
  createSuccessResponse(id, result) {
    const response = {
      jsonrpc: "2.0",
      id
    };

    if (result !== undefined) {
      response.result = result;
    }

    return response;
  }

  /**
   * Create a JSON-RPC 2.0 error response
   * @param {string|number} id - Request ID
   * @param {number} code - Error code
   * @param {string} message - Error message
   * @param {any} [data] - Additional error data
   * @returns {object} JSON-RPC error response
   */
  createErrorResponse(id, code, message, data = undefined) {
    const response = {
      jsonrpc: "2.0",
      id,
      error: {
        code,
        message
      }
    };

    if (data !== undefined) {
      response.error.data = data;
    }

    return response;
  }

  /**
   * Create a JSON-RPC 2.0 notification
   * @param {string} method - Method name
   * @param {object} [params] - Notification parameters
   * @returns {object} JSON-RPC notification
   */
  createNotification(method, params = {}) {
    const notification = {
      jsonrpc: "2.0",
      method
    };

    if (Object.keys(params).length > 0) {
      notification.params = params;
    }

    return notification;
  }

  /**
   * Validate JSON-RPC 2.0 message format
   * @param {object} message - Message to validate
   * @returns {object} Validation result
   */
  validateMessage(message) {
    const errors = [];

    // Check required jsonrpc field
    if (message.jsonrpc !== "2.0") {
      errors.push("Missing or invalid 'jsonrpc' field (must be '2.0')");
    }

    // Check message type
    const hasId = message.hasOwnProperty('id');
    const hasMethod = message.hasOwnProperty('method');
    const hasResult = message.hasOwnProperty('result');
    const hasError = message.hasOwnProperty('error');

    if (hasMethod) {
      // Request or notification
      if (typeof message.method !== 'string') {
        errors.push("Method must be a string");
      }

      if (hasId) {
        // Request
        if (message.id === null || (typeof message.id !== 'string' && typeof message.id !== 'number')) {
          errors.push("Request ID must be a string or number");
        }
      }
      // Notification (no ID required)
    } else if (hasId) {
      // Response
      if (hasResult && hasError) {
        errors.push("Response cannot have both 'result' and 'error'");
      } else if (!hasResult && !hasError) {
        errors.push("Response must have either 'result' or 'error'");
      }

      if (hasError) {
        if (!message.error.code || typeof message.error.code !== 'number') {
          errors.push("Error code must be a number");
        }
        if (!message.error.message || typeof message.error.message !== 'string') {
          errors.push("Error message must be a string");
        }
      }
    } else {
      errors.push("Message must have either 'method' or 'id'");
    }

    return {
      valid: errors.length === 0,
      errors,
      type: this.getMessageType(message)
    };
  }

  /**
   * Determine message type
   * @param {object} message - JSON-RPC message
   * @returns {string} Message type
   */
  getMessageType(message) {
    if (message.hasOwnProperty('method')) {
      return message.hasOwnProperty('id') ? 'request' : 'notification';
    } else if (message.hasOwnProperty('id')) {
      return message.hasOwnProperty('error') ? 'error' : 'response';
    }
    return 'unknown';
  }

  /**
   * Standard JSON-RPC error codes
   */
  static get ErrorCodes() {
    return {
      PARSE_ERROR: -32700,
      INVALID_REQUEST: -32600,
      METHOD_NOT_FOUND: -32601,
      INVALID_PARAMS: -32602,
      INTERNAL_ERROR: -32603,
      // MCP-specific error codes (above -32000)
      RESOURCE_NOT_FOUND: -32001,
      TOOL_EXECUTION_ERROR: -32002,
      PROMPT_NOT_FOUND: -32003,
      SAMPLING_ERROR: -32004,
      CAPABILITY_NOT_SUPPORTED: -32005
    };
  }
}

/**
 * MCP Resource message utilities
 */
export class MCPResourceMessages {
  /**
   * Create a resources/list request
   * @param {string|number} [id] - Request ID
   * @returns {object} JSON-RPC request
   */
  static createListRequest(id = null) {
    return new MCPMessageHandler().createRequest('resources/list', {}, id);
  }

  /**
   * Create a resources/read request
   * @param {string} uri - Resource URI
   * @param {string|number} [id] - Request ID
   * @returns {object} JSON-RPC request
   */
  static createReadRequest(uri, id = null) {
    return new MCPMessageHandler().createRequest('resources/read', { uri }, id);
  }

  /**
   * Create a resources/subscribe request
   * @param {string} uri - Resource URI
   * @param {string|number} [id] - Request ID
   * @returns {object} JSON-RPC request
   */
  static createSubscribeRequest(uri, id = null) {
    return new MCPMessageHandler().createRequest('resources/subscribe', { uri }, id);
  }

  /**
   * Create a resources/list_changed notification
   * @returns {object} JSON-RPC notification
   */
  static createListChangedNotification() {
    return new MCPMessageHandler().createNotification('notifications/resources/list_changed');
  }

  /**
   * Create a resources/updated notification
   * @param {string} uri - Updated resource URI
   * @returns {object} JSON-RPC notification
   */
  static createUpdatedNotification(uri) {
    return new MCPMessageHandler().createNotification('notifications/resources/updated', { uri });
  }
}

/**
 * MCP Tool message utilities
 */
export class MCPToolMessages {
  /**
   * Create a tools/list request
   * @param {string|number} [id] - Request ID
   * @returns {object} JSON-RPC request
   */
  static createListRequest(id = null) {
    return new MCPMessageHandler().createRequest('tools/list', {}, id);
  }

  /**
   * Create a tools/call request
   * @param {string} name - Tool name
   * @param {object} [arguments_] - Tool arguments
   * @param {string|number} [id] - Request ID
   * @returns {object} JSON-RPC request
   */
  static createCallRequest(name, arguments_ = {}, id = null) {
    return new MCPMessageHandler().createRequest('tools/call', { 
      name, 
      arguments: arguments_ 
    }, id);
  }
}

/**
 * MCP Prompt message utilities
 */
export class MCPPromptMessages {
  /**
   * Create a prompts/list request
   * @param {string|number} [id] - Request ID
   * @returns {object} JSON-RPC request
   */
  static createListRequest(id = null) {
    return new MCPMessageHandler().createRequest('prompts/list', {}, id);
  }

  /**
   * Create a prompts/get request
   * @param {string} name - Prompt name
   * @param {object} [arguments_] - Prompt arguments
   * @param {string|number} [id] - Request ID
   * @returns {object} JSON-RPC request
   */
  static createGetRequest(name, arguments_ = {}, id = null) {
    return new MCPMessageHandler().createRequest('prompts/get', { 
      name, 
      arguments: arguments_ 
    }, id);
  }
}

/**
 * MCP Sampling message utilities
 */
export class MCPSamplingMessages {
  /**
   * Create a sampling/createMessage request
   * @param {Array} messages - Messages array
   * @param {object} [options] - Sampling options
   * @param {string|number} [id] - Request ID
   * @returns {object} JSON-RPC request
   */
  static createSamplingRequest(messages, options = {}, id = null) {
    const params = { messages, ...options };
    return new MCPMessageHandler().createRequest('sampling/createMessage', params, id);
  }
}

export default MCPMessageHandler;
