/**
 * Tests for Visual Asset Models
 */

import { describe, it, expect, beforeEach } from 'bun:test';
import { VisualAsset, WireframeAsset, DiagramAsset, PrototypeAsset } from '../../src/visual-generation/assets/visual-asset-models.js';

describe('Visual Asset Models', () => {
  describe('VisualAsset', () => {
    let asset;

    beforeEach(() => {
      asset = new VisualAsset({
        title: 'Test Asset',
        description: 'A test visual asset',
        content: 'Test content'
      });
    });

    it('should create a visual asset with default properties', () => {
      expect(asset.id).toBeDefined();
      expect(asset.type).toBe('unknown');
      expect(asset.title).toBe('Test Asset');
      expect(asset.description).toBe('A test visual asset');
      expect(asset.content).toBe('Test content');
      expect(asset.createdAt).toBeDefined();
      expect(asset.updatedAt).toBeDefined();
    });

    it('should generate unique IDs', () => {
      const asset1 = new VisualAsset({ title: 'Asset 1' });
      const asset2 = new VisualAsset({ title: 'Asset 2' });
      expect(asset1.id).not.toBe(asset2.id);
    });

    it('should validate asset structure', () => {
      const validation = asset.validate();
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should fail validation for missing required fields', () => {
      const invalidAsset = new VisualAsset({});
      const validation = invalidAsset.validate();
      expect(validation.isValid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });

    it('should update asset and increment version', () => {
      const originalVersion = asset.version.toString();
      const originalUpdatedAt = asset.updatedAt;
      
      asset.update({ title: 'Updated Title' });
      
      expect(asset.title).toBe('Updated Title');
      expect(asset.version.toString()).not.toBe(originalVersion);
      expect(asset.updatedAt).not.toBe(originalUpdatedAt);
    });

    it('should export to JSON correctly', () => {
      const json = asset.toJSON();
      expect(json.id).toBe(asset.id);
      expect(json.type).toBe(asset.type);
      expect(json.title).toBe(asset.title);
      expect(json.metadata).toBeDefined();
      expect(json.version).toBeDefined();
      expect(json.businessIntelligence).toBeDefined();
    });
  });

  describe('WireframeAsset', () => {
    let wireframe;

    beforeEach(() => {
      wireframe = new WireframeAsset({
        title: 'Test Wireframe',
        description: 'A test wireframe',
        content: 'Wireframe content'
      });
    });

    it('should create a wireframe asset with correct type', () => {
      expect(wireframe.type).toBe('wireframe');
      expect(wireframe.components).toEqual([]);
      expect(wireframe.interactions).toEqual([]);
      expect(wireframe.breakpoints).toEqual(['desktop']);
      expect(wireframe.gridSystem).toEqual({ columns: 12, gutters: 16 });
    });

    it('should add components correctly', () => {
      wireframe.addComponent({
        name: 'Header',
        type: 'navigation',
        position: { x: 0, y: 0 }
      });

      expect(wireframe.components).toHaveLength(1);
      expect(wireframe.components[0].name).toBe('Header');
      expect(wireframe.components[0].id).toBeDefined();
      expect(wireframe.components[0].addedAt).toBeDefined();
    });

    it('should export wireframe-specific properties', () => {
      const json = wireframe.toJSON();
      expect(json.type).toBe('wireframe');
      expect(json.layout).toBeDefined();
      expect(json.components).toBeDefined();
      expect(json.interactions).toBeDefined();
      expect(json.breakpoints).toBeDefined();
      expect(json.gridSystem).toBeDefined();
    });
  });

  describe('DiagramAsset', () => {
    let diagram;

    beforeEach(() => {
      diagram = new DiagramAsset({
        title: 'Test Diagram',
        description: 'A test diagram',
        content: 'Diagram content'
      });
    });

    it('should create a diagram asset with correct type', () => {
      expect(diagram.type).toBe('diagram');
      expect(diagram.diagramType).toBe('flowchart');
      expect(diagram.nodes).toEqual([]);
      expect(diagram.edges).toEqual([]);
      expect(diagram.decisionPoints).toEqual([]);
    });

    it('should add nodes correctly', () => {
      diagram.addNode({
        label: 'Start',
        shape: 'circle',
        position: { x: 100, y: 100 }
      });

      expect(diagram.nodes).toHaveLength(1);
      expect(diagram.nodes[0].label).toBe('Start');
      expect(diagram.nodes[0].id).toBeDefined();
    });

    it('should add edges correctly', () => {
      diagram.addNode({ label: 'Node 1' });
      diagram.addNode({ label: 'Node 2' });
      
      const node1Id = diagram.nodes[0].id;
      const node2Id = diagram.nodes[1].id;
      
      diagram.addEdge(node1Id, node2Id, 'connects to');

      expect(diagram.edges).toHaveLength(1);
      expect(diagram.edges[0].from).toBe(node1Id);
      expect(diagram.edges[0].to).toBe(node2Id);
      expect(diagram.edges[0].label).toBe('connects to');
    });
  });

  describe('PrototypeAsset', () => {
    let prototype;

    beforeEach(() => {
      prototype = new PrototypeAsset({
        title: 'Test Prototype',
        description: 'A test prototype',
        content: 'Prototype content'
      });
    });

    it('should create a prototype asset with correct type', () => {
      expect(prototype.type).toBe('prototype');
      expect(prototype.htmlContent).toBe('');
      expect(prototype.cssContent).toBe('');
      expect(prototype.jsContent).toBe('');
      expect(prototype.components).toEqual([]);
    });

    it('should generate complete HTML', () => {
      prototype.htmlContent = '<div>Test Content</div>';
      prototype.cssContent = 'body { margin: 0; }';
      prototype.jsContent = 'console.log("test");';

      const html = prototype.generateHTML();
      expect(html).toContain('<!DOCTYPE html>');
      expect(html).toContain('<div>Test Content</div>');
      expect(html).toContain('body { margin: 0; }');
      expect(html).toContain('console.log("test");');
    });

    it('should add components correctly', () => {
      prototype.addComponent({
        name: 'Button',
        type: 'interactive',
        props: { onClick: 'handleClick' }
      });

      expect(prototype.components).toHaveLength(1);
      expect(prototype.components[0].name).toBe('Button');
      expect(prototype.components[0].id).toBeDefined();
    });
  });
});
