import { describe, test, expect, beforeEach, mock } from "bun:test";
import { QuestionFlowEngine, QUESTION_TYPES } from "./question-flow-engine.js";
import fs from "fs";
import path from "path";

describe("QuestionFlowEngine", () => {
  let flowEngine;
  
  const testFlow = [
    {
      id: "project-name",
      type: QUESTION_TYPES.TEXT,
      text: "What is your project name?",
      required: true,
      minLength: 1,
      placeholder: "My Awesome Project"
    },
    {
      id: "project-description",
      type: QUESTION_TYPES.TEXT,
      text: "Describe your project briefly",
      required: false,
      minLength: 0,
      placeholder: "A project that does something amazing"
    },
    {
      id: "features",
      type: QUESTION_TYPES.LIST,
      text: "What features do you want to include?",
      required: true,
      placeholder: "Feature name"
    },
    {
      id: "project-type",
      type: QUESTION_TYPES.MULTIPLE_CHOICE,
      text: "What type of project is this?",
      required: true,
      options: [
        { value: "web", label: "Web Application" },
        { value: "mobile", label: "Mobile App" },
        { value: "desktop", label: "Desktop Application" }
      ]
    }
  ];
  
  beforeEach(() => {
    // Mock fs.existsSync and fs.readdirSync
    const originalExistsSync = fs.existsSync;
    const originalReaddirSync = fs.readdirSync;
    
    global.fs = {
      existsSync: mock(() => true),
      readdirSync: mock(() => [])
    };
    
    // Mock require function
    global.require = mock((modulePath) => {
      if (modulePath.includes('general-flow.js')) {
        return { default: [] };
      }
      return {};
    });
    
    flowEngine = new QuestionFlowEngine();
    
    // Override the questionFlows property to include our test flow
    flowEngine.questionFlows = { 'test-flow': testFlow };
    
    // Restore original fs functions
    global.fs.existsSync = originalExistsSync;
    global.fs.readdirSync = originalReaddirSync;
  });
  
  test("should get the question flow for a project type", () => {
    expect(flowEngine.getQuestionFlow("test-flow")).toEqual(testFlow);
  });
  
  test("should get the next question for a session", () => {
    const session = {
      projectType: "test-flow",
      answers: {},
      skippedQuestions: []
    };
    
    const nextQuestion = flowEngine.getNextQuestion(session);
    expect(nextQuestion).toEqual(testFlow[0]);
  });
  
  test("should validate a text answer correctly", () => {
    const question = testFlow[0];
    
    // Valid answer
    const validResult = flowEngine.validateAnswer(question, "My Project");
    expect(validResult.valid).toBe(true);
    expect(validResult.processed).toBe("My Project");
    
    // Invalid answer (empty for required question)
    const invalidResult = flowEngine.validateAnswer(question, "");
    expect(invalidResult.valid).toBe(false);
  });
  
  test("should validate a multiple_choice answer correctly", () => {
    const question = testFlow[3];
    
    // Valid answer
    const validResult = flowEngine.validateAnswer(question, "web");
    expect(validResult.valid).toBe(true);
    expect(validResult.processed).toBe("web");
    
    // Invalid answer (not in options)
    const invalidResult = flowEngine.validateAnswer(question, "invalid-option");
    expect(invalidResult.valid).toBe(false);
  });
  
  test("should process a list answer", () => {
    const question = testFlow[2];
    const session = {
      projectType: "test-flow",
      answers: {}
    };
    
    const result = flowEngine.processAnswer(question, "Feature 1", session);
    expect(result.valid).toBe(true);
    expect(result.listStarted).toBe(true);
    expect(result.processed).toEqual(["Feature 1"]);
  });
  
  test("should handle list continuation", () => {
    const question = {
      ...testFlow[2],
      isContinuation: true
    };
    
    const session = {
      projectType: "test-flow",
      answers: {},
      currentListQuestion: {
        ...testFlow[2],
        collectedItems: ["Feature 1"]
      }
    };
    
    const result = flowEngine.processListContinuation("Feature 2", session);
    expect(result.valid).toBe(true);
    expect(result.listContinued).toBe(true);
    expect(result.processed).toEqual(["Feature 1", "Feature 2"]);
  });
  
  test("should handle list completion", () => {
    const session = {
      projectType: "test-flow",
      answers: {},
      currentListQuestion: {
        ...testFlow[2],
        collectedItems: ["Feature 1", "Feature 2"]
      }
    };
    
    const result = flowEngine.processListContinuation("done", session);
    expect(result.valid).toBe(true);
    expect(result.listCompleted).toBe(true);
    expect(result.processed).toEqual(["Feature 1", "Feature 2"]);
  });
}); 