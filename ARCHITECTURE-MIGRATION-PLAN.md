# Guidant Architecture Migration Plan

**Status:** CRITICAL - Fundamental restructuring required  
**Timeline:** 8 weeks (4 phases × 2 weeks each)  
**Risk Level:** Medium effort, High benefit  

## 🚨 Executive Summary

The Guidant codebase has a **fundamental architectural mismatch** between documented clean architecture and actual feature-based implementation. This plan provides a systematic migration to proper clean architecture while preserving all existing functionality.

**Key Finding:** Documentation describes `src/domain/`, `src/application/`, `src/infrastructure/`, `src/interfaces/` structure that doesn't exist. Actual code has 25+ feature directories with tight coupling and SOLID violations.

## 📋 Migration Overview

| Phase | Duration | Focus | Risk |
|-------|----------|-------|------|
| 1 | Week 1-2 | Assessment & Stabilization | Low |
| 2 | Week 3-4 | Domain Extraction | Medium |
| 3 | Week 5-6 | Clean Architecture Implementation | Medium |
| 4 | Week 7-8 | Validation & Optimization | Low |

## 🚨 IMMEDIATE ACTIONS (Today)

### 1. Development Moratorium
- **STOP** all new feature development
- **FREEZE** current functionality 
- **DOCUMENT** critical bugs only

### 2. Create Migration Environment
```bash
# Create migration branch
git checkout -b architecture-migration
git push -u origin architecture-migration

# Create backup
cp -r . ../guidant-backup-$(date +%Y%m%d)
```

### 3. Preserve Current State
- Tag current version: `git tag v0.1.0-pre-migration`
- Document current functionality for regression testing

## 📊 PHASE 1: Assessment & Stabilization (Week 1-2)

### Objectives
- Create accurate documentation of current architecture
- Identify and preserve well-architected components
- Establish migration foundation with comprehensive testing

### Tasks

#### 1.1 Document Current Architecture
- **Map Dependencies**: Create visual diagram of actual component relationships
- **Identify Violations**: Document specific SOLID principle violations with file references
- **External Integrations**: Document all AI, research, and file system integrations

#### 1.2 Identify Preservation Targets

**✅ Keep These (Already Well-Architected):**
- `src/research-providers/` - Proper adapter pattern with BaseResearchProvider
- `src/file-management/reliable-file-manager.js` - Production-ready with atomic operations
- `src/ai-providers/BaseAIProvider.js` - Good inheritance hierarchy
- `.guidant/` workspace structure - Excellent organization and file management

**⚠️ Migrate These (Architectural Violations):**
- `src/workflow-logic/workflow-engine.js` - Business logic mixed with infrastructure
- `src/business-decisions/decision-translator.js` - Domain logic depends on file utilities
- `src/ai-integration/task-generator.js` - Scattered task generation logic

#### 1.3 Create Migration Test Suite
- **Integration Tests**: Cover all AI orchestration workflows end-to-end
- **Regression Tests**: Ensure migration preserves existing behavior
- **Performance Baselines**: Establish current performance metrics

### Success Criteria
- [ ] Complete dependency map with violation documentation
- [ ] Integration test suite with 90%+ coverage of core workflows
- [ ] Clear list of components to preserve vs. migrate
- [ ] Performance baseline established

### Deliverables
- `docs/current-architecture-analysis.md`
- `tests/migration/integration-test-suite.js`
- `docs/component-preservation-plan.md`

## 🏗️ PHASE 2: Domain Extraction (Week 3-4)

### Objectives
- Extract business logic into proper domain layer
- Create clean boundaries between concerns
- Establish foundation for clean architecture

### Tasks

#### 2.1 Create Clean Architecture Structure
```bash
# Create new directory structure
mkdir -p src/domain/{workflow,business-decisions,conversation,research}
mkdir -p src/application/{workflow,onboarding,task-generation,reporting}
mkdir -p src/infrastructure/{ai,research,file-system,external}
mkdir -p src/interfaces/{cli,api,ui}
```

#### 2.2 Extract Workflow Domain Logic
**Move & Refactor:**
- `src/workflow-logic/phase-definitions.js` → `src/domain/workflow/phase-definitions.js`
- Extract business rules from `workflow-engine.js` → `src/domain/workflow/workflow-rules.js`
- Create `src/domain/workflow/phase-transition-rules.js` (pure business logic)
- **Remove all infrastructure dependencies from domain files**

#### 2.3 Extract Business Decision Domain
**Move & Refactor:**
- `src/business-decisions/decision-translator.js` → `src/domain/business-decisions/decision-translator.js`
- Remove file system and AI service dependencies
- Create pure business logic for decision translation
- Extract decision rules into `src/domain/business-decisions/decision-rules.js`

#### 2.4 Create Application Services
- `src/application/workflow/workflow-orchestrator.js` - Coordinates workflow use cases
- `src/application/task-generation/task-service.js` - Orchestrates task generation
- `src/application/onboarding/onboarding-service.js` - Manages onboarding flow
- `src/application/reporting/report-service.js` - Handles report generation

#### 2.5 Preserve Infrastructure Adapters
- Keep `src/research-providers/` in new `src/infrastructure/research/`
- Keep `src/ai-providers/` in new `src/infrastructure/ai/`
- Enhance with proper interfaces

### Success Criteria
- [ ] Domain layer contains only business logic (zero infrastructure imports)
- [ ] Application layer properly orchestrates use cases
- [ ] All existing tests still pass
- [ ] No circular dependencies introduced

### Deliverables
- New clean architecture directory structure
- Migrated domain and application layers
- Updated import statements
- Validation that all tests pass

## 🔧 PHASE 3: Clean Architecture Implementation (Week 5-6)

### Objectives
- Implement proper dependency injection
- Create infrastructure abstractions
- Establish clean dependency flow: Interfaces → Application → Domain

### Tasks

#### 3.1 Create Infrastructure Abstractions
- `src/infrastructure/ai/ai-service-adapter.js` - Abstract all AI operations
- `src/infrastructure/file-system/file-repository.js` - Abstract file operations
- `src/infrastructure/research/research-gateway.js` - Abstract research operations
- `src/infrastructure/external/external-service-gateway.js` - Abstract external APIs

#### 3.2 Implement Dependency Injection Container
- Create `src/infrastructure/dependency-container.js`
- Configure all dependencies and their lifecycles
- Implement factory patterns for complex object creation
- Remove all direct imports between architectural layers

#### 3.3 Update Interface Layer
- Refactor `src/cli/` to depend only on application services
- Create proper command handlers with dependency injection
- Ensure UI components don't directly access domain or infrastructure
- Update MCP server integration to use application services

#### 3.4 Establish Clean Dependency Flow
**Enforce proper dependency direction:**
```
src/interfaces/ → src/application/ → src/domain/
src/infrastructure/ → src/application/ (via dependency injection only)
```

### Success Criteria
- [ ] Zero direct dependencies from domain to infrastructure
- [ ] All dependencies injected through container
- [ ] Clean dependency flow validated with tooling
- [ ] All functionality preserved

### Deliverables
- Dependency injection container
- Infrastructure abstractions
- Updated interface layer
- Dependency flow validation

## ✅ PHASE 4: Validation & Optimization (Week 7-8)

### Objectives
- Validate architectural integrity
- Optimize performance
- Update documentation to match reality

### Tasks

#### 4.1 Architectural Validation
- Run dependency analysis tools (madge, dependency-cruiser)
- Verify no circular dependencies exist
- Validate SOLID principles compliance
- Ensure clean architecture rules are enforced

#### 4.2 Performance Testing & Optimization
- Benchmark new vs. old architecture performance
- Identify and resolve any performance regressions
- Optimize dependency injection overhead
- Validate memory usage patterns

#### 4.3 Documentation & Knowledge Transfer
- Update `docs/AGENT.md` to reflect actual architecture
- Create architectural decision records (ADRs) for major decisions
- Update development guidelines and coding standards
- Create onboarding documentation for new developers

#### 4.4 Migration Completion
- Merge architecture-migration branch to main
- Deploy to production environment
- Monitor for issues and performance
- Create post-migration support plan

### Success Criteria
- [ ] All architectural principles validated with tooling
- [ ] Performance maintained or improved
- [ ] Documentation accurate and complete
- [ ] Successful production deployment

### Deliverables
- Updated documentation
- Performance validation report
- Production deployment
- Post-migration monitoring plan

## 🎯 Risk Mitigation Strategy

### Rollback Plan
- **Phase Checkpoints**: Git tag at end of each phase for easy rollback
- **Feature Flags**: Use feature flags for gradual rollout if needed
- **Parallel Branches**: Keep main branch available for critical fixes

### Functionality Preservation
- **Comprehensive Testing**: 90%+ test coverage before migration starts
- **Behavior Validation**: Integration tests verify identical behavior
- **User Acceptance**: Validate with stakeholders at each phase

### Performance Safety
- **Baseline Metrics**: Establish performance baselines before migration
- **Continuous Monitoring**: Monitor performance throughout migration
- **Optimization Budget**: Allocate time for performance optimization

## 📈 Success Metrics

### Technical Metrics
- Zero circular dependencies
- 90%+ test coverage maintained
- Performance within 5% of baseline
- Zero SOLID principle violations in domain layer

### Business Metrics
- All existing functionality preserved
- Development velocity improved post-migration
- Reduced bug reports related to architectural issues
- Faster onboarding for new developers

---

**Next Steps:** Review this plan and confirm approach before proceeding to detailed task breakdown and implementation.
