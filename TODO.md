# Guidant AI Agent Orchestration System - Consolidated Development Plan (v2)

This document outlines the remaining development tasks for the Guidant AI Agent Orchestration System, integrating the Central Coordination Engine (CCE) design with existing project requirements. It incorporates insights from the `@claude-task-master` project.

**Completed Foundational Tasks (Excluded from this list):**
*   TASK-001: MCP Tool Architecture Overhaul
*   TASK-002: Research Tools Integration
*   TASK-016: Visual Wireframe Generation Engine

## Central Coordination Engine (CCE) Overview
The CCE serves as the intelligent core for managing and coordinating a single AI agent wearing different "hats" (roles) to deliver working software. It orchestrates tasks, manages workflows, and ensures seamless collaboration, drawing inspiration from `@claude-task-master` for robust task management and AI integration patterns.

---

## Phase 1: Core Task & Intelligence Foundation

### 1.1 CCE Component: Task Manager (Foundation for Intelligent Task Orchestration)
*   **Goal:** Establish a robust system for defining, tracking, and managing granular development tasks and their relationships, inspired by `@claude-task-master`'s task structure and management logic.
*   **Key Tasks:**
    *   **TASK-003: Rich Task Infrastructure & General Task Management Foundation**
        *   Status: **Completed**
        *   Dependencies: TASK-001 (Completed), TASK-002 (Completed), WLR-003, WLR-005
        *   Objective: Create comprehensive rich task schema (see `claude-task-master/docs/task-structure.md` and Guidant's richer schema), persistent storage (`.guidant/tasks/tasks.json`), and core CRUD operations. Implement AI-driven task creation (`add-task.js` pattern) and complexity scoring.
        *   Inspired by: `claude-task-master/scripts/modules/task-manager.js`, `claude-task-master/mcp-server/src/tools/add-task.js`.
    *   **TASK-006: Task Dependency Engine**
    *   Status: **Completed**
    *   Dependencies: TASK-003, WLR-006
        *   Objective: Implement sophisticated dependency management (DAG, cycle detection, critical path), next-task recommendations.
        *   Inspired by: `claude-task-master/scripts/modules/dependency-manager.js`.
    *   **TASK-017: PRD Parser & Implementation Task Specialization**
        *   Status: Pending
        *   Dependencies: TASK-003, TASK-015, TASK-002 (Completed)
        *   Objective: Parse PRDs (from TASK-015) into structured implementation tasks using the rich task model from TASK-003 and generate TaskMaster-style task hierarchies.
        *   Inspired by: `claude-task-master/mcp-server/src/tools/parse-prd.js`.

### 1.2 CCE Component: Orchestration Logic Engine (Initial AI-Agentic Intelligence)
*   **Goal:** Implement the initial core decision-making unit for intelligent task assignment (to agent "hats"), basic workflow execution, and context-aware decision making.
*   **Key Tasks:**
    *   **TASK-008: Context-Aware Decision Making Enhancement**
        *   Status: Pending
        *   Dependencies: TASK-001 (Completed), WLR-003, WLR-005
        *   Objective: Enhance decision-making systems to consider project context (budget, timeline, user preferences from `.guidant/context/user-preferences.json`).
    *   **TASK-014: Guidant Intelligence Engine Implementation (Phase 1 - Core Orchestration Logic)**
        *   Status: Pending
        *   Dependencies: TASK-001 (Completed), TASK-002 (Completed), TASK-003 (for task data), TASK-008 (for context)
        *   Objective: Create the central AI-agentic intelligence coordinator. For this phase, focus on:
            *   Defining agent "hats" (roles/capabilities).
            *   Logic for selecting the appropriate "hat" for a task from `TaskManager`.
            *   Managing context switching for the agent based on the selected "hat".
            *   Basic workflow control: determining the next task (inspired by `claude-task-master/mcp-server/src/tools/next-task.js` and `claude-task-master/scripts/modules/task-manager/find-next-task.js`).
        *   Inspired by: `claude-task-master/scripts/modules/ai-services-unified.js` for managing AI interactions.

---

## Phase 2: Enhancing Interaction & Workflow

### 2.1 CCE Component: Communication Bus & Conversational Interface
*   **Goal:** Enable natural language interaction for task management and provide clear, business-friendly feedback.
*   **Key Tasks:**
    *   **TASK-005: Conversational Task Management MCP Tools**
        *   Status: Pending
        *   Dependencies: TASK-001 (Completed), TASK-003, WLR-004, WLR-005
        *   Objective: Extend MCP tools for natural language task operations (completion, queries, breakdown), leveraging the `TaskManager` from TASK-003.
    *   **TASK-004: Enhanced Dashboard Visualization**
        *   Status: Pending
        *   Dependencies: TASK-003, WLR-005
        *   Objective: Enhance the dashboard with TaskMaster-style visualizations (progress, dependencies) and business-friendly language, using data from `TaskManager`.

### 2.2 CCE Component: State Manager (Context & Learning Foundation)
*   **Goal:** Improve context preservation and begin tracking decisions for future learning.
*   **Key Tasks:**
    *   **TASK-011: Simple Decision Tracking and Learning**
        *   Status: Pending
        *   Dependencies: TASK-008, WLR-005
        *   Objective: Track major user decisions with rationale; implement basic pattern recognition for user preferences.
    *   **TASK-012: Enhanced Context Orchestrator Integration**
        *   Status: Pending
        *   Dependencies: TASK-008, Enhanced Context Orchestrator system (to be defined/integrated)
        *   Objective: Integrate the Enhanced Context Orchestrator for rich, business-aware context in AI task generation and decision-making.

---

## Phase 3: Advanced Intelligence & Automation

### 3.1 CCE Component: Orchestration Logic Engine (Advanced Capabilities)
*   **Goal:** Implement more sophisticated conflict resolution and further develop the Guidant Intelligence Engine.
*   **Key Tasks:**
    *   **TASK-009: Simple Requirement Conflict Resolution**
        *   Status: Pending
        *   Dependencies: TASK-002 (Completed), TASK-008
        *   Objective: Detect and resolve conflicting requirements from various sources with basic prioritization, guided by the Orchestration Logic Engine.
    *   **TASK-014: Guidant Intelligence Engine Implementation (Phase 2 - Advanced Features)**
        *   Status: Pending
        *   Dependencies: Phase 1 of TASK-014 completed.
        *   Objective: Expand GIE with adaptive flow management, decision synthesis, and context-aware triggers for autonomous workflow initiation.

### 3.2 Cross-Cutting & Supporting Tasks (Intelligence & Workflow Enhancements)
*   **TASK-013: AI-Enhanced Interactive Onboarding**
    *   Status: Pending
    *   Dependencies: TASK-002 (Completed), TASK-014, WLR-003, WLR-004, WLR-005
    *   Objective: Integrate Guidant Intelligence Engine (TASK-014) with onboarding for adaptive, AI-agentic conversation flows.
*   **TASK-015: Enhanced PRD Generation Bridge with Visual Integration**
    *   Status: Pending
    *   Dependencies: TASK-002 (Completed), TASK-016 (Completed), WLR-004, WLR-005
    *   Objective: Create a bridge to synthesize all deliverables (including visuals from TASK-016) into a comprehensive PRD for implementation task generation (feeding into TASK-017).
*   **TASK-018: Architecture Visualization Engine**
    *   Status: Pending
    *   Dependencies: TASK-002 (Completed), TASK-016 (Completed)
    *   Objective: Generate visual representations of system architecture (e.g., Mermaid diagrams) from technical specifications.

---

## Phase 4: Optimization & Refinement

### 4.1 CCE Component: Monitoring & Analytics (Performance)
*   **Goal:** Optimize system performance and provide actionable insights.
*   **Key Tasks:**
    *   **TASK-010: Basic Performance Optimization**
        *   Status: Pending
        *   Dependencies: TASK-003, TASK-004
        *   Objective: Address basic performance bottlenecks (caching, file I/O) and add simple monitoring to workflow logic.
    *   **TASK-007: Business-Aware Task Analytics (Full Implementation)**
        *   Status: Pending
        *   Dependencies: TASK-003, TASK-004, TASK-006, WLR-005
        *   Objective: Fully implement business-friendly progress reports, forecasts, and project health indicators based on rich task data and dependency analysis.

---

**Note:** WLR (Workflow Logic Revolution) tasks are referenced as dependencies and assumed to be either completed or their relevant functionalities incorporated into the listed TASK items.