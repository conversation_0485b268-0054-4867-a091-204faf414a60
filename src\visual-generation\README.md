# Visual Generation System

Enterprise-grade visual generation system that transforms functional requirements into ASCII wireframes, Mermaid diagrams, and HTML prototypes.

## Architecture

This module follows SOLID principles and integrates with Guidant's existing workflow system:

### Directory Structure

```
src/visual-generation/
├── generators/           # Visual generators (ASCII, Mermaid, HTML)
├── assets/              # Visual asset management
├── templates/           # Generation templates
├── config/              # Configuration schemas
├── interfaces/          # Common interfaces
└── utils/               # Utility functions
```

### Key Components

- **IVisualGenerator**: Base interface for all visual generators
- **ASCIIGenerator**: Grid-based ASCII wireframe generation
- **MermaidGenerator**: User flow diagram generation
- **HTMLGenerator**: Interactive prototype generation
- **VisualAssetManager**: Asset storage and organization
- **VisualIntelligence**: Research-enhanced generation

## Integration Points

- Enhances existing `requirements-to-design.js` transformer
- Integrates with Phase 3 Design workflow
- Stores assets in `.guidant/deliverables/wireframes/`
- Provides MCP tool operations for visual generation

## Usage

Visual generation is triggered automatically during Phase 3 Design transformation and can be invoked through MCP tools for on-demand generation.
