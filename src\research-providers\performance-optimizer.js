/**
 * Performance Optimizer for Research Providers
 * 
 * Optimizes research tool response times, implements caching strategies,
 * and ensures sub-5-second response times with quality scoring.
 */

import { EventEmitter } from 'events';

/**
 * Performance Monitor
 * Tracks and analyzes performance metrics
 */
export class PerformanceMonitor extends EventEmitter {
  constructor(config = {}) {
    super();
    this.targetResponseTime = config.targetResponseTime || 5000; // 5 seconds
    this.qualityThreshold = config.qualityThreshold || 0.8; // 80% relevance
    this.metrics = {
      requests: [],
      averageResponseTime: 0,
      successRate: 0,
      qualityScore: 0,
      cacheHitRate: 0
    };
    this.maxMetricsHistory = config.maxMetricsHistory || 1000;
  }

  /**
   * Record request metrics
   * @param {object} requestData - Request performance data
   */
  recordRequest(requestData) {
    const record = {
      timestamp: Date.now(),
      responseTime: requestData.responseTime,
      success: requestData.success,
      qualityScore: requestData.qualityScore || 0,
      cacheHit: requestData.cacheHit || false,
      provider: requestData.provider,
      operation: requestData.operation
    };

    this.metrics.requests.push(record);
    
    // Keep only recent metrics
    if (this.metrics.requests.length > this.maxMetricsHistory) {
      this.metrics.requests = this.metrics.requests.slice(-this.maxMetricsHistory);
    }

    this.updateAggregateMetrics();
    this.checkPerformanceThresholds(record);
  }

  /**
   * Update aggregate metrics
   */
  updateAggregateMetrics() {
    const requests = this.metrics.requests;
    if (requests.length === 0) return;

    // Calculate average response time
    this.metrics.averageResponseTime = requests.reduce((sum, req) => 
      sum + req.responseTime, 0) / requests.length;

    // Calculate success rate
    const successfulRequests = requests.filter(req => req.success).length;
    this.metrics.successRate = (successfulRequests / requests.length) * 100;

    // Calculate average quality score
    const qualityScores = requests.filter(req => req.qualityScore > 0);
    this.metrics.qualityScore = qualityScores.length > 0 
      ? qualityScores.reduce((sum, req) => sum + req.qualityScore, 0) / qualityScores.length
      : 0;

    // Calculate cache hit rate
    const cacheHits = requests.filter(req => req.cacheHit).length;
    this.metrics.cacheHitRate = (cacheHits / requests.length) * 100;
  }

  /**
   * Check if performance thresholds are met
   * @param {object} record - Latest request record
   */
  checkPerformanceThresholds(record) {
    // Check response time threshold
    if (record.responseTime > this.targetResponseTime) {
      this.emit('performanceWarning', {
        type: 'response_time',
        value: record.responseTime,
        threshold: this.targetResponseTime,
        record
      });
    }

    // Check quality threshold
    if (record.qualityScore > 0 && record.qualityScore < this.qualityThreshold) {
      this.emit('performanceWarning', {
        type: 'quality_score',
        value: record.qualityScore,
        threshold: this.qualityThreshold,
        record
      });
    }

    // Check overall success rate
    if (this.metrics.successRate < 99) { // <1% failure rate target
      this.emit('performanceWarning', {
        type: 'success_rate',
        value: this.metrics.successRate,
        threshold: 99,
        record
      });
    }
  }

  /**
   * Get performance metrics
   */
  getMetrics() {
    return {
      ...this.metrics,
      thresholds: {
        targetResponseTime: this.targetResponseTime,
        qualityThreshold: this.qualityThreshold,
        targetSuccessRate: 99
      },
      status: this.getPerformanceStatus()
    };
  }

  /**
   * Get overall performance status
   */
  getPerformanceStatus() {
    const responseTimeOk = this.metrics.averageResponseTime <= this.targetResponseTime;
    const successRateOk = this.metrics.successRate >= 99;
    const qualityOk = this.metrics.qualityScore >= this.qualityThreshold;

    if (responseTimeOk && successRateOk && qualityOk) {
      return 'excellent';
    } else if (responseTimeOk && successRateOk) {
      return 'good';
    } else if (successRateOk) {
      return 'acceptable';
    } else {
      return 'poor';
    }
  }
}

/**
 * Intelligent Cache Manager
 * Optimizes caching strategies for better performance
 */
export class IntelligentCacheManager {
  constructor(config = {}) {
    this.cache = new Map();
    this.accessPatterns = new Map();
    this.defaultTTL = config.defaultTTL || 3600000; // 1 hour
    this.maxCacheSize = config.maxCacheSize || 1000;
    this.adaptiveTTL = config.adaptiveTTL !== false;
  }

  /**
   * Get cached item with intelligent TTL
   * @param {string} key - Cache key
   * @returns {any} Cached value or null
   */
  get(key) {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const now = Date.now();
    const ttl = this.calculateAdaptiveTTL(key, entry);
    
    if (now - entry.timestamp > ttl) {
      this.cache.delete(key);
      return null;
    }

    // Update access pattern
    this.updateAccessPattern(key);
    
    return entry.data;
  }

  /**
   * Set cached item
   * @param {string} key - Cache key
   * @param {any} data - Data to cache
   * @param {object} metadata - Cache metadata
   */
  set(key, data, metadata = {}) {
    // Evict old entries if cache is full
    if (this.cache.size >= this.maxCacheSize) {
      this.evictLeastUsed();
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      metadata: {
        qualityScore: metadata.qualityScore || 0,
        provider: metadata.provider,
        ...metadata
      }
    });

    this.updateAccessPattern(key);
  }

  /**
   * Calculate adaptive TTL based on usage patterns
   * @param {string} key - Cache key
   * @param {object} entry - Cache entry
   * @returns {number} TTL in milliseconds
   */
  calculateAdaptiveTTL(key, entry) {
    if (!this.adaptiveTTL) return this.defaultTTL;

    const pattern = this.accessPatterns.get(key);
    if (!pattern) return this.defaultTTL;

    // Extend TTL for frequently accessed, high-quality content
    let multiplier = 1;
    
    if (pattern.accessCount > 5) multiplier *= 1.5;
    if (entry.metadata.qualityScore > 0.8) multiplier *= 1.3;
    if (pattern.recentAccesses > 3) multiplier *= 1.2;

    return Math.min(this.defaultTTL * multiplier, this.defaultTTL * 3);
  }

  /**
   * Update access pattern for key
   * @param {string} key - Cache key
   */
  updateAccessPattern(key) {
    const now = Date.now();
    const pattern = this.accessPatterns.get(key) || {
      accessCount: 0,
      lastAccess: now,
      recentAccesses: 0,
      accessHistory: []
    };

    pattern.accessCount++;
    pattern.lastAccess = now;
    pattern.accessHistory.push(now);

    // Count recent accesses (last hour)
    const oneHourAgo = now - 3600000;
    pattern.recentAccesses = pattern.accessHistory.filter(time => time > oneHourAgo).length;

    // Keep only recent history
    pattern.accessHistory = pattern.accessHistory.slice(-10);

    this.accessPatterns.set(key, pattern);
  }

  /**
   * Evict least used cache entries
   */
  evictLeastUsed() {
    let leastUsedKey = null;
    let leastUsedScore = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      const pattern = this.accessPatterns.get(key);
      const score = pattern ? pattern.accessCount + pattern.recentAccesses : 0;
      
      if (score < leastUsedScore) {
        leastUsedScore = score;
        leastUsedKey = key;
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
      this.accessPatterns.delete(leastUsedKey);
    }
  }

  /**
   * Get cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      utilizationRate: (this.cache.size / this.maxCacheSize) * 100,
      totalAccesses: Array.from(this.accessPatterns.values())
        .reduce((sum, pattern) => sum + pattern.accessCount, 0)
    };
  }
}

/**
 * Request Optimizer
 * Optimizes individual requests for better performance
 */
export class RequestOptimizer {
  constructor(config = {}) {
    this.concurrencyLimit = config.concurrencyLimit || 5;
    this.requestQueue = [];
    this.activeRequests = 0;
    this.requestTimeout = config.requestTimeout || 30000; // 30 seconds
  }

  /**
   * Execute request with optimization
   * @param {Function} requestFn - Request function
   * @param {object} options - Request options
   * @returns {Promise} Request result
   */
  async executeOptimizedRequest(requestFn, options = {}) {
    return new Promise((resolve, reject) => {
      const request = {
        fn: requestFn,
        options,
        resolve,
        reject,
        timestamp: Date.now()
      };

      this.requestQueue.push(request);
      this.processQueue();
    });
  }

  /**
   * Process request queue with concurrency control
   */
  async processQueue() {
    if (this.activeRequests >= this.concurrencyLimit || this.requestQueue.length === 0) {
      return;
    }

    const request = this.requestQueue.shift();
    this.activeRequests++;

    try {
      const startTime = Date.now();
      
      // Add timeout to request
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), this.requestTimeout);
      });

      const result = await Promise.race([
        request.fn(),
        timeoutPromise
      ]);

      const responseTime = Date.now() - startTime;
      
      request.resolve({
        ...result,
        metadata: {
          ...result.metadata,
          responseTime,
          optimized: true
        }
      });
    } catch (error) {
      request.reject(error);
    } finally {
      this.activeRequests--;
      // Process next request in queue
      setImmediate(() => this.processQueue());
    }
  }

  /**
   * Get optimizer statistics
   */
  getStats() {
    return {
      queueLength: this.requestQueue.length,
      activeRequests: this.activeRequests,
      concurrencyLimit: this.concurrencyLimit
    };
  }
}

/**
 * Performance Optimizer
 * Main class that coordinates all performance optimizations
 */
export class PerformanceOptimizer extends EventEmitter {
  constructor(config = {}) {
    super();
    this.monitor = new PerformanceMonitor(config.monitor);
    this.cacheManager = new IntelligentCacheManager(config.cache);
    this.requestOptimizer = new RequestOptimizer(config.request);

    // Forward performance warnings
    this.monitor.on('performanceWarning', (warning) => {
      this.emit('performanceWarning', warning);
    });
  }

  /**
   * Execute operation with full performance optimization
   * @param {string} operation - Operation name
   * @param {Function} fn - Function to execute
   * @param {object} context - Execution context
   * @returns {Promise} Optimized result
   */
  async executeOptimized(operation, fn, context = {}) {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(operation, context);
    
    // Check cache first
    const cached = this.cacheManager.get(cacheKey);
    if (cached) {
      const responseTime = Date.now() - startTime;
      this.monitor.recordRequest({
        operation,
        responseTime,
        success: true,
        cacheHit: true,
        qualityScore: cached.metadata?.qualityScore || 0,
        provider: cached.metadata?.provider
      });
      return cached;
    }

    try {
      // Execute with request optimization
      const result = await this.requestOptimizer.executeOptimizedRequest(fn, context);
      const responseTime = Date.now() - startTime;

      // Calculate quality score
      const qualityScore = this.calculateQualityScore(result, context);

      // Cache result if quality is good
      if (qualityScore >= 0.6) {
        this.cacheManager.set(cacheKey, result, {
          qualityScore,
          provider: result.metadata?.provider
        });
      }

      // Record metrics
      this.monitor.recordRequest({
        operation,
        responseTime,
        success: true,
        cacheHit: false,
        qualityScore,
        provider: result.metadata?.provider
      });

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      this.monitor.recordRequest({
        operation,
        responseTime,
        success: false,
        cacheHit: false,
        qualityScore: 0,
        provider: context.provider
      });

      throw error;
    }
  }

  /**
   * Generate cache key for operation
   * @param {string} operation - Operation name
   * @param {object} context - Context data
   * @returns {string} Cache key
   */
  generateCacheKey(operation, context) {
    const keyData = {
      operation,
      query: context.query,
      provider: context.provider,
      options: context.options
    };
    return `${operation}:${JSON.stringify(keyData)}`;
  }

  /**
   * Calculate quality score for result
   * @param {object} result - Operation result
   * @param {object} context - Context data
   * @returns {number} Quality score (0-1)
   */
  calculateQualityScore(result, context) {
    let score = 0.5; // Base score

    // Check result completeness
    if (result.results && result.results.length > 0) score += 0.2;
    if (result.synthesis && result.synthesis.summary) score += 0.1;
    if (result.metadata && result.metadata.sourcesCount > 0) score += 0.1;

    // Check relevance indicators
    if (result.synthesis && result.synthesis.confidence > 0.7) score += 0.1;
    if (result.results && result.results.some(r => r.score > 0.8)) score += 0.1;

    return Math.min(score, 1.0);
  }

  /**
   * Get comprehensive performance statistics
   */
  getStats() {
    return {
      monitor: this.monitor.getMetrics(),
      cache: this.cacheManager.getStats(),
      requestOptimizer: this.requestOptimizer.getStats(),
      timestamp: new Date().toISOString()
    };
  }
}

export default PerformanceOptimizer;
