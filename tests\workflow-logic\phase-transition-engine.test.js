import { test, expect, describe, beforeEach, afterEach, afterAll, beforeAll, spyOn } from 'bun:test';
import { PhaseTransitionEngine } from '../../src/workflow-logic/phase-transition-engine.js';
import { PHASE_DEFINITIONS, PHASE_IDS } from '../../src/workflow-logic/phase-definitions.js';
import fs from 'fs/promises';
import path from 'path';

describe('PhaseTransitionEngine - WLR-001 Tests', () => {
  let engine;
  let projectRoot;

  beforeAll(async () => {
    projectRoot = await fs.mkdtemp(path.join(process.cwd(), 'test-wlr-001-'));
    
    // Create the necessary directory structure
    await fs.mkdir(path.join(projectRoot, '.guidant', 'workflow'), { recursive: true });
    
    // Create phase directories and mock deliverables
    for (const phase of Object.values(PHASE_IDS)) {
      await fs.mkdir(path.join(projectRoot, '.guidant', phase), { recursive: true });
      
      // Add mock deliverables for all phases except 'architecture' to test missing deliverables
      if (phase !== PHASE_IDS.ARCHITECTURE) {
        await fs.writeFile(path.join(projectRoot, '.guidant', phase, 'test.txt'), 'mock data');
      }
    }
  });

  afterAll(async () => {
    await fs.rm(projectRoot, { recursive: true, force: true });
  });

  beforeEach(() => {
    engine = new PhaseTransitionEngine(projectRoot, { enableCaching: false });
    
    // Mock the transformation itself to focus on validation and history
    spyOn(engine, 'performTransformation').mockResolvedValue({ 
      success: true, 
      transformation: {}, 
      enhancedContext: {} 
    });
  });

  describe('Transition Validation and History Recording', () => {
    test('should PASS for a valid FORWARD transition and record history', async () => {
      const result = await engine.executeTransition(PHASE_IDS.CONCEPT, PHASE_IDS.REQUIREMENTS, 'FORWARD');
      
      expect(result.success).toBe(true);
      expect(result.validation.isValid).toBe(true);
      
      // Verify history was written to file
      const historyPath = path.join(projectRoot, '.guidant', 'workflow', 'transition-history.json');
      const historyExists = await fileExists(historyPath);
      expect(historyExists).toBe(true);
      
      const historyContent = await fs.readFile(historyPath, 'utf8');
      const history = JSON.parse(historyContent);
      
      expect(history.length).toBeGreaterThan(0);
      expect(history[0].fromPhase).toBe(PHASE_IDS.CONCEPT);
      expect(history[0].toPhase).toBe(PHASE_IDS.REQUIREMENTS);
      expect(history[0].transitionType).toBe('FORWARD');
    });

    test('should FAIL for a BACKWARD transition without justification', async () => {
      const result = await engine.executeTransition(PHASE_IDS.DESIGN, PHASE_IDS.REQUIREMENTS, 'BACKWARD', null);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('justification is required');
    });

    test('should PASS for a BACKWARD transition with justification and record history', async () => {
      const justification = "Revisiting requirements based on design feedback.";
      const result = await engine.executeTransition(PHASE_IDS.DESIGN, PHASE_IDS.REQUIREMENTS, 'BACKWARD', justification);

      expect(result.success).toBe(true);
      expect(result.validation.isValid).toBe(true);
      
      // Verify history was written to file with the correct justification
      const historyPath = path.join(projectRoot, '.guidant', 'workflow', 'transition-history.json');
      const historyContent = await fs.readFile(historyPath, 'utf8');
      const history = JSON.parse(historyContent);
      
      // Find the entry for this specific transition
      const entry = history.find(h => 
        h.fromPhase === PHASE_IDS.DESIGN && 
        h.toPhase === PHASE_IDS.REQUIREMENTS && 
        h.transitionType === 'BACKWARD'
      );
      
      expect(entry).toBeDefined();
      expect(entry.justification).toBe(justification);
    });
    
    test('should PASS for a valid PARALLEL transition and record history', async () => {
      const justification = "Beginning design and architecture work concurrently.";
      const result = await engine.executeTransition(PHASE_IDS.REQUIREMENTS, PHASE_IDS.DESIGN, 'PARALLEL', justification);

      expect(result.success).toBe(true);
      expect(result.validation.isValid).toBe(true);
      
      // Verify history was written to file
      const historyPath = path.join(projectRoot, '.guidant', 'workflow', 'transition-history.json');
      const historyContent = await fs.readFile(historyPath, 'utf8');
      const history = JSON.parse(historyContent);
      
      // Find the entry for this specific transition
      const entry = history.find(h => 
        h.fromPhase === PHASE_IDS.REQUIREMENTS && 
        h.toPhase === PHASE_IDS.DESIGN && 
        h.transitionType === 'PARALLEL'
      );
      
      expect(entry).toBeDefined();
      expect(entry.justification).toBe(justification);
    });

    test('should FAIL for an invalid PARALLEL transition', async () => {
      const result = await engine.executeTransition(PHASE_IDS.CONCEPT, PHASE_IDS.DESIGN, 'PARALLEL', "Invalid parallel move");
      expect(result.success).toBe(false);
      expect(result.error).toContain('not allowed for type PARALLEL');
    });

    test('should PASS for a valid SKIP transition and record history', async () => {
      const justification = "Fast-tracking to implementation for a prototype.";
      const result = await engine.executeTransition(PHASE_IDS.CONCEPT, PHASE_IDS.IMPLEMENTATION, 'SKIP', justification);
      
      expect(result.success).toBe(true);
      expect(result.validation.isValid).toBe(true);
      
      // Verify history was written to file
      const historyPath = path.join(projectRoot, '.guidant', 'workflow', 'transition-history.json');
      const historyContent = await fs.readFile(historyPath, 'utf8');
      const history = JSON.parse(historyContent);
      
      // Find the entry for this specific transition
      const entry = history.find(h => 
        h.fromPhase === PHASE_IDS.CONCEPT && 
        h.toPhase === PHASE_IDS.IMPLEMENTATION && 
        h.transitionType === 'SKIP'
      );
      
      expect(entry).toBeDefined();
      expect(entry.justification).toBe(justification);
    });

    test('should PASS for an EMERGENCY transition and record history', async () => {
      const justification = "Critical hotfix required in production.";
      const result = await engine.executeTransition(PHASE_IDS.DESIGN, PHASE_IDS.IMPLEMENTATION, 'EMERGENCY', justification);
      
      expect(result.success).toBe(true);
      expect(result.validation.isValid).toBe(true);
      
      // Verify history was written to file
      const historyPath = path.join(projectRoot, '.guidant', 'workflow', 'transition-history.json');
      const historyContent = await fs.readFile(historyPath, 'utf8');
      const history = JSON.parse(historyContent);
      
      // Find the entry for this specific transition
      const entry = history.find(h => 
        h.fromPhase === PHASE_IDS.DESIGN && 
        h.toPhase === PHASE_IDS.IMPLEMENTATION && 
        h.transitionType === 'EMERGENCY'
      );
      
      expect(entry).toBeDefined();
      expect(entry.justification).toBe(justification);
    });

    test('should FAIL if deliverables are missing for the fromPhase', async () => {
      // This test transitions from 'architecture' which has no mock deliverable created.
      const result = await engine.executeTransition(PHASE_IDS.ARCHITECTURE, PHASE_IDS.IMPLEMENTATION, 'FORWARD');
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Missing required deliverables');
    });
  });
});

// Helper function to check if a file exists
async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
} 