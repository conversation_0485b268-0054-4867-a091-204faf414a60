/**
 * File paths and directory constants for Guidant
 */

export const GUIDANT_DIR = '.guidant';

// Project structure paths
export const PROJECT_CONFIG = '.guidant/project/config.json';
export const PROJECT_PHASES = '.guidant/project/phases.json';
export const PROJECT_METADATA = '.guidant/project/metadata.json';

// Workflow paths
export const CURRENT_PHASE = '.guidant/workflow/current-phase.json';
export const QUALITY_GATES = '.guidant/workflow/quality-gates.json';
export const DEPENDENCIES = '.guidant/workflow/dependencies.json';

// Context paths
export const DECISIONS = '.guidant/context/decisions.json';
export const RESEARCH = '.guidant/context/research.json';
export const SESSIONS = '.guidant/context/sessions.json';
export const USER_FEEDBACK = '.guidant/context/user-feedback.json';
export const USER_PREFERENCES = '.guidant/context/user-preferences.json';
export const SESSION_RECOVERY = '.guidant/context/session-recovery.json';

// Deliverables paths organized by phase
export const RESEARCH_DIR = '.guidant/deliverables/research';
export const REQUIREMENTS_DIR = '.guidant/deliverables/requirements';
export const WIREFRAMES_DIR = '.guidant/deliverables/wireframes';
export const ARCHITECTURE_DIR = '.guidant/deliverables/architecture';
export const IMPLEMENTATION_DIR = '.guidant/deliverables/implementation';
export const DEPLOYMENT_DIR = '.guidant/deliverables/deployment';

// Specific files
export const PRD_FILE = '.guidant/deliverables/requirements/prd.md';

// Reports paths
export const PROGRESS_REPORTS_DIR = '.guidant/reports/progress-reports';
export const QUALITY_REPORTS_DIR = '.guidant/reports/quality-reports';
export const BUSINESS_REPORTS_DIR = '.guidant/reports/business-reports';

// AI coordination paths
export const AI_CAPABILITIES = '.guidant/ai/capabilities.json';
export const CURRENT_ROLE = '.guidant/ai/current-role.json';
export const TASK_TICKETS = '.guidant/ai/task-tickets';
export const TASKS_STORAGE = '.guidant/tasks/tasks.json';
