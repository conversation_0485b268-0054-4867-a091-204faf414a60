# WLR-000: Conversational Onboarding Spike

This directory contains a lightweight prototype to validate the user experience of the conversational onboarding process for Guidant.

## 🎯 Purpose

The goal of this spike is to quickly test and refine the feel of the interactive, chat-based project initialization flow before committing to a full-scale implementation (`WLR-004`).

We aim to answer:
- Is the proposed MCP tool design (`guidant_init_project`, `guidant_answer_question`) intuitive for an AI agent to use?
- Does the back-and-forth conversation feel natural from a user's perspective?
- Can we capture the necessary project details effectively through this method?

## 🚀 How to Run the Prototype

1.  **Start the Mock Server:**
    Open a terminal and run the mock MCP server. This simulates the Guidant back-end.
    ```bash
    node prototypes/onboarding-spike/mock-mcp-server.cjs
    ```
    The server will start on `http://localhost:3000`.

2.  **Run the Test Client:**
    In a **second terminal**, run the test client. This simulates the AI agent driving the conversation.
    ```bash
    node prototypes/onboarding-spike/test-client.cjs
    ```

3.  **Interact with the Simulation:**
    The test client will prompt you for input as if you were the end-user. Answer the questions to proceed through the simulated onboarding flow.

## 📝 Findings & Observations

- **Finding 1:** The core back-and-forth conversational flow feels natural and is not confusing. The basic MCP tool design (`init`, `answer`) is validated as effective.
- **Finding 2:** The strictly linear, one-answer-per-question model is too restrictive for creative or reference-based inputs. A user may want to provide multiple competitor links, UI design examples, or a list of features all at once.
- **Finding 3:** The current system doesn't prompt or allow for this "one-to-many" type of data collection, which could lead to missed context early in the project.

## 💡 Refinements for WLR-004

Based on the findings, the following refinements will be incorporated into the full implementation plan for `WLR-004`:

- **Refinement 1:** Introduce a new question `type` or logic to handle list-based answers (e.g., `list_of_urls`, `list_of_text`).
- **Refinement 2:** The AI Agent's conversational script (what I would say) must be updated to handle these list-based questions. For example, after receiving one link, I should ask, "That's a great example. Do you have any others?" and continue looping until the user indicates they are finished.
- **Refinement 3:** The `guidant_answer_question` tool will need to be designed to accept an array of items for these specific question types, or the client-side logic will need to handle collecting the list before submitting it.
