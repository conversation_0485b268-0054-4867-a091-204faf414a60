/**
 * Decision Prompts <PERSON><PERSON>
 * Handles MCP Prompt requests for decision-making templates
 */

/**
 * Decision Prompt Handler
 * Provides structured decision-making prompts for various scenarios
 */
export class DecisionPromptHandler {
  constructor(existingInfrastructure = {}) {
    this.decisionManager = existingInfrastructure.decisionManager;
    
    this.templates = {
      'decision-framework': {
        name: 'decision-framework',
        description: 'Structured decision-making framework for complex choices',
        arguments: [
          { name: 'decision_type', description: 'Type of decision to make', required: true },
          { name: 'stakeholders', description: 'Key stakeholders involved in the decision', required: false },
          { name: 'timeline', description: 'Decision timeline and urgency', required: false },
          { name: 'constraints', description: 'Decision constraints and limitations', required: false }
        ]
      },
      'technical-decision': {
        name: 'technical-decision',
        description: 'Framework for technical architecture and implementation decisions',
        arguments: [
          { name: 'technical_domain', description: 'Technical area for the decision', required: true },
          { name: 'complexity', description: 'Technical complexity level', required: false },
          { name: 'impact_scope', description: 'Scope of technical impact', required: false }
        ]
      },
      'business-decision': {
        name: 'business-decision',
        description: 'Framework for business strategy and operational decisions',
        arguments: [
          { name: 'business_area', description: 'Business area affected by the decision', required: true },
          { name: 'financial_impact', description: 'Expected financial impact level', required: false },
          { name: 'strategic_alignment', description: 'Alignment with business strategy', required: false }
        ]
      },
      'risk-assessment': {
        name: 'risk-assessment',
        description: 'Comprehensive risk assessment for decision making',
        arguments: [
          { name: 'risk_category', description: 'Primary risk category to assess', required: true },
          { name: 'risk_tolerance', description: 'Organization risk tolerance level', required: false },
          { name: 'mitigation_budget', description: 'Available budget for risk mitigation', required: false }
        ]
      }
    };
  }

  /**
   * Get prompt templates for this handler
   * @returns {Array} Array of prompt templates
   */
  getPromptTemplates() {
    return Object.values(this.templates);
  }

  /**
   * Handle prompt get request
   * @param {string} name - Prompt name
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async handlePromptGet(name, args = {}) {
    switch (name) {
      case 'decision-framework':
        return await this.generateDecisionFrameworkPrompt(args);
      case 'technical-decision':
        return await this.generateTechnicalDecisionPrompt(args);
      case 'business-decision':
        return await this.generateBusinessDecisionPrompt(args);
      case 'risk-assessment':
        return await this.generateRiskAssessmentPrompt(args);
      default:
        throw new Error(`Unknown decision prompt: ${name}`);
    }
  }

  /**
   * Generate decision framework prompt
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async generateDecisionFrameworkPrompt(args) {
    const { 
      decision_type, 
      stakeholders = 'project team', 
      timeline = 'standard', 
      constraints = 'budget and resources' 
    } = args;

    const promptContent = `# Decision Framework: ${decision_type}

## Decision Context
**Decision Type**: ${decision_type}
**Key Stakeholders**: ${stakeholders}
**Timeline**: ${timeline}
**Constraints**: ${constraints}

## Structured Decision-Making Process

### 1. Problem Definition
**Objective**: Clearly define the decision that needs to be made

#### Key Questions:
- **What exactly needs to be decided?** Define the specific choice or problem
- **Why is this decision necessary now?** Understand the driving factors
- **What happens if no decision is made?** Assess the cost of inaction
- **Who is affected by this decision?** Identify all impacted parties
- **What are the success criteria?** Define how to measure a good decision

#### Decision Statement:
Create a clear, specific statement of the decision to be made:
"We need to decide [specific choice] by [timeline] in order to [objective] while considering [key constraints]."

### 2. Stakeholder Analysis
**Objective**: Understand all parties involved and their interests

#### Stakeholder Mapping:
- **Primary Stakeholders**: ${stakeholders}
- **Secondary Stakeholders**: [Identify additional affected parties]
- **Decision Makers**: [Who has final authority]
- **Influencers**: [Who can impact the decision]
- **Implementers**: [Who will execute the decision]

#### Stakeholder Interests:
For each stakeholder group, identify:
- Their primary concerns and interests
- How they're affected by potential outcomes
- Their decision criteria and priorities
- Their level of influence and involvement
- Potential resistance or support factors

### 3. Option Generation
**Objective**: Develop comprehensive set of viable alternatives

#### Brainstorming Guidelines:
- Generate multiple creative alternatives
- Include "do nothing" as an option
- Consider hybrid or combination approaches
- Think beyond obvious choices
- Involve diverse perspectives in generation

#### Option Documentation:
For each option, document:
- **Description**: Clear explanation of the option
- **Requirements**: What's needed to implement
- **Timeline**: Implementation timeframe
- **Resources**: Required resources and costs
- **Risks**: Potential downsides and challenges
- **Benefits**: Expected advantages and outcomes

### 4. Evaluation Criteria
**Objective**: Establish clear, weighted criteria for evaluation

#### Criteria Categories:
- **Strategic Alignment**: How well does it support overall goals?
- **Financial Impact**: What are the costs and financial benefits?
- **Risk Level**: What risks does it introduce or mitigate?
- **Implementation Feasibility**: How realistic is successful implementation?
- **Timeline Compatibility**: Does it fit within required timeframes?
- **Resource Requirements**: Are necessary resources available?
- **Stakeholder Acceptance**: Will stakeholders support this option?

#### Weighting System:
Assign relative importance weights to each criterion:
- Critical (30-40%): Must-have requirements
- Important (20-30%): Significant impact factors
- Moderate (10-20%): Nice-to-have considerations
- Minor (5-10%): Minimal impact factors

### 5. Option Evaluation
**Objective**: Systematically assess each option against criteria

#### Evaluation Matrix:
Create a scoring matrix with:
- Options as rows
- Criteria as columns
- Weighted scores for each option-criterion combination
- Total weighted scores for comparison

#### Scoring Guidelines:
- Use consistent scale (e.g., 1-10 or 1-5)
- Base scores on evidence and analysis
- Document reasoning for each score
- Consider both quantitative and qualitative factors
- Validate scores with stakeholder input

### 6. Risk Assessment
**Objective**: Understand and plan for potential risks

#### Risk Analysis for Top Options:
For each leading option, assess:
- **Probability**: How likely are negative outcomes?
- **Impact**: How severe would negative outcomes be?
- **Mitigation**: What can be done to reduce risks?
- **Contingency**: What backup plans are available?
- **Monitoring**: How will risks be tracked?

#### Risk Matrix:
Plot risks on probability vs. impact matrix:
- High probability, high impact: Requires immediate attention
- High probability, low impact: Monitor and manage
- Low probability, high impact: Develop contingency plans
- Low probability, low impact: Accept or minimal mitigation

### 7. Decision Making
**Objective**: Make the final decision based on analysis

#### Decision Process:
1. **Review Analysis**: Summarize evaluation results
2. **Consider Intangibles**: Factor in qualitative considerations
3. **Stakeholder Input**: Gather final stakeholder perspectives
4. **Make Decision**: Choose the best option based on analysis
5. **Document Rationale**: Record reasoning for future reference

#### Decision Documentation:
- **Chosen Option**: Clear description of selected alternative
- **Rationale**: Why this option was selected
- **Trade-offs**: What was sacrificed for this choice
- **Success Metrics**: How success will be measured
- **Review Schedule**: When to reassess the decision

### 8. Implementation Planning
**Objective**: Plan successful execution of the decision

#### Implementation Framework:
- **Action Plan**: Specific steps and timeline
- **Resource Allocation**: Required resources and assignments
- **Communication Plan**: How to announce and explain the decision
- **Change Management**: How to manage transition and adoption
- **Monitoring Plan**: How to track implementation progress
- **Success Metrics**: Key performance indicators
- **Review Points**: Scheduled decision review and adjustment

## Decision Quality Checklist

### Process Quality:
- [ ] All relevant stakeholders were involved
- [ ] Multiple viable options were considered
- [ ] Clear evaluation criteria were established
- [ ] Systematic evaluation was conducted
- [ ] Risks were thoroughly assessed
- [ ] Decision rationale was documented

### Content Quality:
- [ ] Decision addresses the core problem
- [ ] Solution is feasible and realistic
- [ ] Resources and timeline are adequate
- [ ] Risks are acceptable and manageable
- [ ] Success can be measured and tracked
- [ ] Implementation plan is comprehensive

## Success Factors
A high-quality decision process should result in:
- **Clarity**: Everyone understands what was decided and why
- **Commitment**: Stakeholders support the decision and implementation
- **Confidence**: Decision makers are confident in the choice
- **Completeness**: All important factors were considered
- **Consistency**: Decision aligns with values and strategy
- **Contingency**: Plans exist for potential challenges

Remember: The goal is not just to make a decision, but to make the best possible decision given available information and constraints, with strong stakeholder buy-in and a clear implementation path.`;

    return {
      description: `Structured decision framework for ${decision_type}`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  /**
   * Generate technical decision prompt
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async generateTechnicalDecisionPrompt(args) {
    const { 
      technical_domain, 
      complexity = 'medium', 
      impact_scope = 'project' 
    } = args;

    const promptContent = `# Technical Decision Framework: ${technical_domain}

## Technical Decision Context
**Domain**: ${technical_domain}
**Complexity**: ${complexity}
**Impact Scope**: ${impact_scope}

## Technical Decision Process

### 1. Technical Requirements Analysis
- **Functional Requirements**: What must the solution accomplish?
- **Non-Functional Requirements**: Performance, scalability, security needs
- **Integration Requirements**: How must it work with existing systems?
- **Compliance Requirements**: Standards and regulations to meet
- **Future Requirements**: Anticipated future needs and growth

### 2. Technical Options Evaluation
- **Architecture Patterns**: Evaluate different architectural approaches
- **Technology Stack**: Compare technology options and trade-offs
- **Implementation Approaches**: Different ways to build the solution
- **Vendor vs. Build**: Make vs. buy analysis
- **Open Source vs. Commercial**: Licensing and support considerations

### 3. Technical Risk Assessment
- **Technical Risks**: Implementation challenges and unknowns
- **Performance Risks**: Scalability and performance concerns
- **Security Risks**: Vulnerability and threat assessment
- **Maintenance Risks**: Long-term support and evolution challenges
- **Integration Risks**: Compatibility and integration issues

### 4. Implementation Planning
- **Development Approach**: Methodology and process selection
- **Resource Requirements**: Skills, tools, and infrastructure needs
- **Timeline Estimation**: Realistic development and deployment schedule
- **Quality Assurance**: Testing and validation strategy
- **Deployment Strategy**: Rollout and go-live planning

This is a comprehensive technical decision framework for ${technical_domain} decisions.`;

    return {
      description: `Technical decision framework for ${technical_domain}`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  // Additional prompt generation methods would be implemented here...
  async generateBusinessDecisionPrompt(args) {
    const { business_area, financial_impact = 'medium', strategic_alignment = 'high' } = args;
    
    const promptContent = `# Business Decision Framework: ${business_area}

## Business Decision Context
**Business Area**: ${business_area}
**Financial Impact**: ${financial_impact}
**Strategic Alignment**: ${strategic_alignment}

## Business Decision Process
[Detailed business decision framework would be generated here]

This is a placeholder for the full business decision prompt implementation.`;

    return {
      description: `Business decision framework for ${business_area}`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  async generateRiskAssessmentPrompt(args) {
    const { risk_category, risk_tolerance = 'moderate', mitigation_budget = 'standard' } = args;
    
    const promptContent = `# Risk Assessment Framework: ${risk_category}

## Risk Assessment Context
**Risk Category**: ${risk_category}
**Risk Tolerance**: ${risk_tolerance}
**Mitigation Budget**: ${mitigation_budget}

## Risk Assessment Process
[Detailed risk assessment framework would be generated here]

This is a placeholder for the full risk assessment prompt implementation.`;

    return {
      description: `Risk assessment framework for ${risk_category}`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  /**
   * Validate prompt arguments
   * @param {string} name - Prompt name
   * @param {object} args - Prompt arguments
   * @returns {object} Validation result
   */
  validatePrompt(name, args) {
    const template = this.templates[name];
    if (!template) {
      return { valid: false, error: `Unknown prompt: ${name}` };
    }

    const requiredArgs = template.arguments.filter(arg => arg.required);
    const missingArgs = requiredArgs.filter(arg => !args.hasOwnProperty(arg.name));

    if (missingArgs.length > 0) {
      return {
        valid: false,
        error: `Missing required arguments: ${missingArgs.map(arg => arg.name).join(', ')}`
      };
    }

    return { valid: true };
  }
}

export default DecisionPromptHandler;
