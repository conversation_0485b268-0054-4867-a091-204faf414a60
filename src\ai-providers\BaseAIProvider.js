/**
 * BaseAIProvider Class
 *
 * Defines the common interface and foundational logic for all AI providers.
 * Each specific provider (e.g., GoogleVertexProvider, AnthropicProvider) should extend this class.
 * This pattern is adapted from TaskMaster's provider abstraction for consistency and reliability.
 */
export class BaseAIProvider {
  constructor(config) {
    if (new.target === BaseAIProvider) {
      throw new TypeError("Cannot construct BaseAIProvider instances directly");
    }

    this.config = config;
    this.providerName = config.provider;
    this.modelId = config.modelId;
  }

  /**
   * Generates text content based on a given prompt.
   * This method must be implemented by each subclass.
   *
   * @param {string} prompt The input text to the model.
   * @param {object} options Additional options for generation (e.g., temperature, maxTokens).
   * @returns {Promise<object>} A promise that resolves to an object containing the generated text, usage data, and other relevant metadata.
   * @throws {Error} If the method is not implemented by the subclass.
   */
  async generate(prompt, options = {}) {
    throw new Error("generate() method must be implemented by the provider subclass");
  }

  /**
   * Returns the name of the provider.
   * @returns {string} The name of the provider (e.g., 'google-vertex', 'anthropic').
   */
  getName() {
    return this.providerName;
  }

  /**
   * Returns the model ID being used by this provider instance.
   * @returns {string} The model ID (e.g., 'gemini-2.5-flash-preview-05-20').
   */
  getModelId() {
    return this.modelId;
  }

  /**
   * A placeholder for a method to get token usage and cost information.
   * Subclasses should implement this to return detailed telemetry.
   *
   * @param {object} response The raw response object from the provider's generate call.
   * @returns {object} An object containing token counts and cost, if available.
   */
  getUsage(response) {
    // Default implementation returns an empty object.
    // Subclasses should parse the provider-specific response to extract usage data.
    return {
      inputTokens: response.usage?.inputTokens || 0,
      outputTokens: response.usage?.outputTokens || 0,
      totalTokens: response.usage?.totalTokens || 0,
      cost: response.usage?.cost || 0,
    };
  }
} 