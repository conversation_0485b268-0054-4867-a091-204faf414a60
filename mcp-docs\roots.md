# Model Context Protocol - Roots

Roots are a concept in MCP that define the boundaries where servers can operate. They provide a way for clients to inform servers about relevant resources and their locations.

## What are Roots?

A root is a URI that a client suggests a server should focus on. When a client connects to a server, it declares which roots the server should work with. While primarily used for filesystem paths, roots can be any valid URI including HTTP URLs.

For example, roots could be:
```
file:///home/<USER>/projects/myapp
https://api.example.com/v1
```

## Why Use Roots?

Roots serve several important purposes:

1. **Scope Definition**: They establish the boundaries where a server should operate
2. **Resource Location**: They help servers locate and access resources
3. **Security Boundaries**: They provide implicit permission boundaries
4. **Context Awareness**: They give servers context about the user's environment
5. **Organization**: They help separate different resource domains

## How Roots Work

When a client supports roots, it:

1. Declares the roots capability during connection
2. Provides a list of suggested roots to the server
3. Notifies the server when roots change (if supported)

While roots are informational and not strictly enforcing, servers should:

1. Respect the provided roots
2. Use root URIs to locate and access resources
3. Limit operations to resources within the defined roots
4. Handle multiple roots appropriately

## Common Use Cases

Roots are commonly used to define:

1. **Project Directories**: Local filesystem paths for development projects
2. **API Endpoints**: Base URLs for web services
3. **Database Connections**: Connection strings for databases
4. **Content Repositories**: Paths to document or media collections
5. **Configuration Locations**: Paths to configuration files or directories

## Best Practices

When working with roots:

1. Use clear, descriptive root names
2. Provide specific, focused roots rather than overly broad ones
3. Update roots when the user's context changes
4. Handle relative paths appropriately within roots
5. Respect root boundaries for security and organization
6. Implement proper error handling for resources outside roots
7. Document expected root formats for users

## Example

Here's how a typical MCP client might expose roots:

```json
{
  "roots": [
    {
      "uri": "file:///home/<USER>/projects/frontend",
      "name": "Frontend Repository"
    },
    {
      "uri": "https://api.example.com/v1",
      "name": "API Endpoint"
    }
  ]
}
```

This configuration suggests the server focus on both a local repository and an API endpoint while keeping them logically separated.

Source: https://modelcontextprotocol.io/docs/concepts/roots 