/**
 * @file decision-translator.js
 * @description Translates technical choices into business language with clear trade-offs
 */

import { readJSONFile, writeJSONFile } from '../utils/file-utils.js';
import { DECISION_TEMPLATES } from './templates/decision-templates.js';
import { AITranslationService } from './ai-translation-service.js';
import path from 'path';

/**
 * Translate technical terms to business language
 * @async
 * @param {string} technicalDescription - The technical description to translate
 * @param {string} expertiseLevel - User expertise level (novice, intermediate, expert)
 * @returns {Promise<string>} The business-friendly description
 */
export async function translateTechnicalToBusinessTerms(technicalDescription, expertiseLevel = 'novice') {
  // For intermediate/expert users, keep more technical terms
  if (expertiseLevel !== 'novice') {
    return technicalDescription;
  }
  
  // Get the technical term mappings
  const translator = new DecisionTranslator();
  const technicalToBusinessMap = translator._buildTermMappings();
  
  // Replace known technical terms with business terms
  let businessDescription = technicalDescription;
  
  for (const [technical, business] of Object.entries(technicalToBusinessMap)) {
    // Use regex with word boundaries to replace whole words only
    const regex = new RegExp(`\\b${technical}\\b`, 'gi');
    businessDescription = businessDescription.replace(regex, business);
  }
  
  // If the description still contains technical terms, use AI translation
  if (translator._isDescriptionTechnical(businessDescription) && translator.useAI) {
    try {
      // If the translator has mockMode enabled, it will return a mock response
      businessDescription = await translator.aiTranslator.translateTerm(
        technicalDescription,
        '',
        expertiseLevel
      );
    } catch (error) {
      console.error('Error using AI translation:', error);
      // Fall back to the partially translated description
    }
  }
  
  return businessDescription;
}

/**
 * Decision Translator class responsible for converting technical choices into
 * business-friendly language and options
 */
export class DecisionTranslator {
  constructor(projectRoot = '.') {
    this.projectRoot = projectRoot;
    this.decisionsPath = path.join(projectRoot, '.guidant', 'context', 'decisions.json');
    this.userPreferencesPath = path.join(projectRoot, '.guidant', 'context', 'user-preferences.json');
    this.customTemplatesPath = path.join(projectRoot, '.guidant', 'context', 'custom-templates.json');
    this.technicalToBusinessMap = this._buildTermMappings();
    this.customTemplates = [];
    this.aiTranslator = new AITranslationService(projectRoot);
    this.useAI = true; // Flag to control AI usage
    
    // Load any existing custom templates
    this._loadCustomTemplates();
  }

  /**
   * Load custom templates from the file system
   * @private
   * @async
   */
  async _loadCustomTemplates() {
    try {
      this.customTemplates = await readJSONFile(this.customTemplatesPath);
    } catch (error) {
      // If file doesn't exist or is empty, start with empty array
      this.customTemplates = [];
    }
  }

  /**
   * Save custom templates to the file system
   * @private
   * @async
   */
  async _saveCustomTemplates() {
    try {
      await writeJSONFile(this.customTemplatesPath, this.customTemplates);
    } catch (error) {
      console.error('Error saving custom templates:', error);
    }
  }

  /**
   * Build technical to business term mappings from templates
   * @private
   * @returns {Object} Mapping of technical terms to business terms
   */
  _buildTermMappings() {
    const mapping = {};
    
    // Build from predefined mappings in templates
    if (DECISION_TEMPLATES.translation_patterns && 
        DECISION_TEMPLATES.translation_patterns.technical_term_mapping) {
      DECISION_TEMPLATES.translation_patterns.technical_term_mapping.forEach(item => {
        const [technical, business] = Object.entries(item)[0];
        mapping[technical] = business;
      });
    }
    
    return mapping;
  }

  /**
   * Translate a technical term to business language
   * @async
   * @param {string} technicalTerm - The technical term to translate
   * @param {string} expertiseLevel - User expertise level (novice, intermediate, expert)
   * @param {string} context - Optional context about the term's usage
   * @returns {Promise<string>} The business-friendly term or original term if no translation exists
   */
  async translateTerm(technicalTerm, expertiseLevel = 'novice', context = '') {
    // For intermediate/expert users, we might keep technical terms but add explanations
    if (expertiseLevel !== 'novice' && technicalTerm in this.technicalToBusinessMap) {
      return `${technicalTerm} (${this.technicalToBusinessMap[technicalTerm]})`;
    }
    
    // Check if we have a predefined mapping
    if (technicalTerm in this.technicalToBusinessMap) {
      return this.technicalToBusinessMap[technicalTerm];
    }
    
    // If we don't have a mapping and AI is enabled, use AI translation
    if (this.useAI) {
      try {
        const aiTranslation = await this.aiTranslator.translateTerm(technicalTerm, context, expertiseLevel);
        
        // Cache the AI translation for future use
        this.technicalToBusinessMap[technicalTerm] = aiTranslation;
        
        return aiTranslation;
      } catch (error) {
        console.error('Error using AI translation:', error);
        // Fall back to the original term if AI fails
        return technicalTerm;
      }
    }
    
    // If AI is disabled or fails, return the original term
    return technicalTerm;
  }

  /**
   * Get user expertise level from preferences
   * @async
   * @returns {string} User expertise level (defaults to 'novice')
   */
  async getUserExpertiseLevel() {
    try {
      const userPreferences = await readJSONFile(this.userPreferencesPath);
      return userPreferences.expertise_level || 'novice';
    } catch (error) {
      console.error('Error reading user preferences:', error);
      return 'novice'; // Default to novice if preferences can't be read
    }
  }

  /**
   * Translate a decision context into business-friendly options
   * @async
   * @param {string} decisionContext - The context identifier (e.g., 'framework_selection')
   * @returns {Object} Translated decision options with business implications
   */
  async translateDecisionOptions(decisionContext) {
    const expertiseLevel = await this.getUserExpertiseLevel();
    const template = await this._getDecisionTemplate(decisionContext);
    
    if (!template) {
      throw new Error(`No decision template found for context: ${decisionContext}`);
    }
    
    const translatedOptions = await Promise.all(template.options.map(async option => {
      // Translate the title and description
      const translatedTitle = await this._translateOptionTitle(option.title, expertiseLevel, decisionContext);
      const translatedDescription = await this._translateDescription(option.description, expertiseLevel, decisionContext);
      
      // If AI is enabled and this is a custom option, enhance the business impact descriptions
      let businessImpact = option.businessImpact;
      let timeImplication = option.timeImplication;
      let costImplication = option.costImplication;
      let riskAssessment = option.riskAssessment;
      
      if (this.useAI && option.isCustom && option.technicalDetails) {
        try {
          // Generate AI-powered business impact description
          const aiBusinessImpact = await this.aiTranslator.generateBusinessImpact(
            option.title,
            option.technicalDetails,
            option.businessImpact?.level || 'medium'
          );
          
          // Update the business impact with AI-generated description
          businessImpact = {
            level: option.businessImpact?.level || 'medium',
            description: aiBusinessImpact
          };
        } catch (error) {
          console.error('Error generating AI business impact:', error);
          // Keep the original business impact if AI fails
        }
      }
      
      return {
        id: option.id,
        title: translatedTitle,
        description: translatedDescription,
        businessImpact: businessImpact,
        timeImplication: timeImplication,
        costImplication: costImplication,
        riskAssessment: riskAssessment,
        technicalDetails: expertiseLevel !== 'novice' ? option.technicalDetails : undefined,
        isRecommended: option.isRecommended || false,
        isCustom: option.isCustom || false
      };
    }));
    
    return {
      decisionId: template.id,
      context: decisionContext,
      title: template.title,
      description: template.description,
      options: translatedOptions,
      isCustomTemplate: template.isCustom || false
    };
  }

  /**
   * Get a decision template by context
   * @private
   * @async
   * @param {string} decisionContext - The decision context to look up
   * @returns {Promise<Object|null>} The decision template or null if not found
   */
  async _getDecisionTemplate(decisionContext) {
    // First check in built-in templates
    const builtInTemplate = DECISION_TEMPLATES.decisions.find(d => d.context === decisionContext);
    if (builtInTemplate) {
      return builtInTemplate;
    }
    
    // Ensure custom templates are loaded
    if (this.customTemplates.length === 0) {
      await this._loadCustomTemplates();
    }
    
    // Then check in custom templates
    const customTemplate = this.customTemplates.find(d => d.context === decisionContext);
    return customTemplate || null;
  }

  /**
   * Translate an option title based on user expertise
   * @private
   * @async
   * @param {string} title - The technical option title
   * @param {string} expertiseLevel - User expertise level
   * @param {string} context - Decision context for additional context
   * @returns {Promise<string>} Translated title
   */
  async _translateOptionTitle(title, expertiseLevel, context) {
    // For novice users, we might want to simplify titles completely
    if (expertiseLevel === 'novice') {
      // Check if this is a known technical framework/tool with a simpler name
      for (const [technical, business] of Object.entries(this.technicalToBusinessMap)) {
        if (title.includes(technical)) {
          return title.replace(technical, business);
        }
      }
      
      // If we don't have a mapping and AI is enabled, use AI translation
      if (this.useAI) {
        try {
          return await this.aiTranslator.translateTerm(title, context, expertiseLevel);
        } catch (error) {
          console.error('Error translating title with AI:', error);
          // Fall back to the original title if AI fails
        }
      }
    }
    
    return title;
  }

  /**
   * Translate a technical description to business language
   * @private
   * @async
   * @param {string} description - Technical description
   * @param {string} expertiseLevel - User expertise level
   * @param {string} context - Decision context for additional context
   * @returns {Promise<string>} Business-friendly description
   */
  async _translateDescription(description, expertiseLevel, context) {
    if (expertiseLevel === 'novice') {
      let translatedDesc = description;
      
      // Replace all technical terms with business terms
      for (const [technical, business] of Object.entries(this.technicalToBusinessMap)) {
        const regex = new RegExp(`\\b${technical}\\b`, 'gi');
        translatedDesc = translatedDesc.replace(regex, business);
      }
      
      // If AI is enabled and the description is still quite technical, use AI translation
      if (this.useAI && this._isDescriptionTechnical(translatedDesc)) {
        try {
          return await this.aiTranslator.translateTerm(translatedDesc, context, expertiseLevel);
        } catch (error) {
          console.error('Error translating description with AI:', error);
          // Fall back to the basic translated description if AI fails
        }
      }
      
      return translatedDesc;
    }
    
    return description;
  }

  /**
   * Check if a description contains technical terms
   * @private
   * @param {string} description - The description to check
   * @returns {boolean} True if the description contains technical terms
   */
  _isDescriptionTechnical(description) {
    // List of common technical terms that might indicate a technical description
    const technicalTerms = [
      'api', 'framework', 'library', 'component', 'function', 'method',
      'class', 'object', 'interface', 'implementation', 'architecture',
      'database', 'schema', 'query', 'server', 'client', 'protocol',
      'algorithm', 'optimization', 'performance', 'scalability', 'deployment'
    ];
    
    // Check if any technical terms are present in the description
    return technicalTerms.some(term => {
      const regex = new RegExp(`\\b${term}\\b`, 'i');
      return regex.test(description);
    });
  }

  /**
   * Record a decision made by the user
   * @async
   * @param {string} decisionId - The ID of the decision
   * @param {string} choiceId - The ID of the chosen option
   * @param {string} rationale - User's rationale for the decision
   * @param {Object} customDetails - Optional custom details for custom choices
   * @returns {Promise<Object>} The recorded decision
   */
  async recordDecision(decisionId, choiceId, rationale, customDetails = null) {
    try {
      // Read existing decisions
      let decisions = [];
      try {
        decisions = await readJSONFile(this.decisionsPath);
      } catch (error) {
        // If file doesn't exist or is empty, start with empty array
        decisions = [];
      }
      
      // Get the decision template - check both built-in and custom templates
      let template = DECISION_TEMPLATES.decisions.find(d => d.id === decisionId);
      if (!template) {
        template = this.customTemplates.find(d => d.id === decisionId);
      }
      
      if (!template) {
        throw new Error(`Decision template not found for ID: ${decisionId}`);
      }
      
      // Get the chosen option
      const option = template.options.find(o => o.id === choiceId);
      if (!option) {
        throw new Error(`Option not found for ID: ${choiceId} in decision: ${decisionId}`);
      }
      
      // Create decision record
      const decision = {
        id: `${decisionId}_${Date.now()}`,
        decisionId,
        decisionTitle: template.title,
        context: template.context,
        choiceId,
        choiceTitle: option.title,
        rationale,
        timestamp: new Date().toISOString(),
        businessImpact: option.businessImpact,
        wasRecommended: option.isRecommended || false,
        confidenceScore: this._calculateConfidenceScore(option, rationale)
      };
      
      // If this is a custom choice and custom details are provided, add them
      if (option.isCustom && customDetails) {
        decision.customDetails = customDetails;
      }
      
      // Add to decisions array
      decisions.push(decision);
      
      // Save updated decisions
      await writeJSONFile(this.decisionsPath, decisions);
      
      return decision;
    } catch (error) {
      console.error('Error recording decision:', error);
      throw error;
    }
  }

  /**
   * Create a custom decision template for technologies not in our predefined list
   * @async
   * @param {string} context - The decision context (e.g., 'custom_framework')
   * @param {string} title - The title for the decision
   * @param {string} description - The description of the decision
   * @param {Array} options - Array of technology options
   * @returns {Promise<Object>} The created decision template
   */
  async createCustomDecisionTemplate(context, title, description, options) {
    // If AI is enabled, enhance the template with AI-generated content
    if (this.useAI && options.length > 0) {
      try {
        // For each option, enhance with AI-generated business content if needed
        const enhancedOptions = await Promise.all(options.map(async option => {
          // If the option has technical details but lacks business impact descriptions,
          // use AI to generate them
          if (option.technicalDetails && 
              (!option.businessImpact || !option.businessImpact.description)) {
            
            const template = await this.aiTranslator.generateDecisionTemplate(
              option.title,
              option.description || '',
              option.technicalDetails
            );
            
            // Merge the AI-generated content with the original option
            return {
              ...option,
              description: option.description || template.description,
              businessImpact: option.businessImpact || template.businessImpact,
              timeImplication: option.timeImplication || template.timeImplication,
              costImplication: option.costImplication || template.costImplication,
              riskAssessment: option.riskAssessment || template.riskAssessment
            };
          }
          
          return option;
        }));
        
        // Create the template with enhanced options
        const template = DECISION_TEMPLATES.createCustomDecisionTemplate(
          context,
          title,
          description,
          enhancedOptions
        );
        
        // Add the template to our custom templates array
        this.customTemplates.push(template);
        
        // Save the updated custom templates
        await this._saveCustomTemplates();
        
        return template;
      } catch (error) {
        console.error('Error enhancing template with AI:', error);
        // Fall back to the basic template creation if AI fails
      }
    }
    
    // Create the template without AI enhancement
    const template = DECISION_TEMPLATES.createCustomDecisionTemplate(
      context,
      title,
      description,
      options
    );
    
    // Add the template to our custom templates array
    this.customTemplates.push(template);
    
    // Save the updated custom templates
    await this._saveCustomTemplates();
    
    return template;
  }

  /**
   * Calculate a confidence score for a decision
   * @private
   * @param {Object} option - The chosen option
   * @param {string} rationale - User's rationale
   * @returns {number} Confidence score (0-100)
   */
  _calculateConfidenceScore(option, rationale) {
    let score = 50; // Base score
    
    // If this was the recommended option, increase score
    if (option.isRecommended) {
      score += 20;
    }
    
    // If user provided detailed rationale, increase score
    if (rationale && rationale.length > 30) {
      score += 15;
    }
    
    // If option has low risk, increase score
    if (option.riskAssessment && option.riskAssessment.level === 'low') {
      score += 15;
    }
    
    // Cap at 100
    return Math.min(score, 100);
  }

  /**
   * Add a custom technology term to the translation mapping
   * @async
   * @param {string} technicalTerm - The technical term to add
   * @param {string} businessTerm - The business-friendly translation
   * @returns {Promise<Object>} The updated mapping
   */
  async addCustomTermMapping(technicalTerm, businessTerm) {
    this.technicalToBusinessMap[technicalTerm] = businessTerm;
    
    // Save the updated mapping to a custom terms file
    try {
      const customTermsPath = path.join(this.projectRoot, '.guidant', 'context', 'custom-terms.json');
      let customTerms = {};
      
      try {
        customTerms = await readJSONFile(customTermsPath);
      } catch (error) {
        // If file doesn't exist or is empty, start with empty object
        customTerms = {};
      }
      
      customTerms[technicalTerm] = businessTerm;
      await writeJSONFile(customTermsPath, customTerms);
    } catch (error) {
      console.error('Error saving custom term mapping:', error);
    }
    
    return this.technicalToBusinessMap;
  }

  /**
   * Enable or disable AI-powered translations
   * @param {boolean} enabled - Whether AI should be enabled
   */
  setAIEnabled(enabled) {
    this.useAI = enabled;
  }
  
  /**
   * Get all available decision contexts
   * @async
   * @returns {Promise<Array>} Array of available decision contexts
   */
  async getAvailableDecisionContexts() {
    // Get built-in contexts
    const builtInContexts = DECISION_TEMPLATES.decisions.map(d => ({
      context: d.context,
      title: d.title,
      isCustom: false
    }));
    
    // Ensure custom templates are loaded
    if (this.customTemplates.length === 0) {
      await this._loadCustomTemplates();
    }
    
    // Get custom contexts
    const customContexts = this.customTemplates.map(d => ({
      context: d.context,
      title: d.title,
      isCustom: true
    }));
    
    // Combine and return
    return [...builtInContexts, ...customContexts];
  }
}

export default DecisionTranslator; 