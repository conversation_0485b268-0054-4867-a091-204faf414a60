/**
 * Interactive Element Generator
 * Adds click events, tooltips, and navigation links to Mermaid diagrams
 * for enhanced usability and user engagement
 */

/**
 * Interactive Element Generator
 */
export class InteractiveElementGenerator {
  constructor(config = {}) {
    this.config = {
      enableClickEvents: config.enableClickEvents !== false,
      enableTooltips: config.enableTooltips !== false,
      enableNavigation: config.enableNavigation !== false,
      enableHighlighting: config.enableHighlighting !== false,
      enableAnimations: config.enableAnimations || false,
      tooltipStyle: config.tooltipStyle || 'modern',
      navigationStyle: config.navigationStyle || 'inline',
      ...config
    };

    this.interactiveElements = new Map();
    this.navigationMap = new Map();
    this.tooltipData = new Map();
    this.clickHandlers = new Map();
  }

  /**
   * Add interactive elements to Mermaid diagram
   */
  addInteractiveElements(mermaidCode, userFlow, context = {}) {
    if (!this.config.enableClickEvents && !this.config.enableTooltips && !this.config.enableNavigation) {
      return mermaidCode; // Return original if no interactive features enabled
    }

    let enhancedCode = mermaidCode;

    // Add click events
    if (this.config.enableClickEvents) {
      enhancedCode = this.addClickEvents(enhancedCode, userFlow, context);
    }

    // Add tooltips
    if (this.config.enableTooltips) {
      enhancedCode = this.addTooltips(enhancedCode, userFlow, context);
    }

    // Add navigation links
    if (this.config.enableNavigation) {
      enhancedCode = this.addNavigationLinks(enhancedCode, userFlow, context);
    }

    // Add highlighting
    if (this.config.enableHighlighting) {
      enhancedCode = this.addHighlighting(enhancedCode, userFlow, context);
    }

    // Add animations
    if (this.config.enableAnimations) {
      enhancedCode = this.addAnimations(enhancedCode, userFlow, context);
    }

    return enhancedCode;
  }

  /**
   * Add click events to diagram nodes
   */
  addClickEvents(mermaidCode, userFlow, context) {
    const lines = mermaidCode.split('\n');
    const clickEvents = [];

    // Extract node definitions and add click events
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Match node definitions (various formats)
      const nodeMatches = [
        line.match(/^\s*(\w+)\[(.+?)\]/),           // Rectangle nodes
        line.match(/^\s*(\w+)\{(.+?)\}/),           // Diamond nodes
        line.match(/^\s*(\w+)\(\((.+?)\)\)/),       // Circle nodes
        line.match(/^\s*(\w+)\>(.+?)\]/),           // Asymmetric nodes
        line.match(/^\s*(\w+)\|(.+?)\|/)            // Subroutine nodes
      ];

      for (const match of nodeMatches) {
        if (match) {
          const nodeId = match[1];
          const nodeLabel = match[2];
          
          // Generate click event based on node type and content
          const clickEvent = this.generateClickEvent(nodeId, nodeLabel, userFlow, context);
          if (clickEvent) {
            clickEvents.push(clickEvent);
            this.clickHandlers.set(nodeId, clickEvent);
          }
          break;
        }
      }
    }

    // Add click events to the end of the diagram
    if (clickEvents.length > 0) {
      const enhancedLines = [...lines];
      enhancedLines.push('');
      enhancedLines.push('    %% Interactive Click Events');
      enhancedLines.push(...clickEvents.map(event => `    ${event}`));
      return enhancedLines.join('\n');
    }

    return mermaidCode;
  }

  /**
   * Add tooltips to diagram nodes
   */
  addTooltips(mermaidCode, userFlow, context) {
    const lines = mermaidCode.split('\n');
    const tooltips = [];

    // Generate tooltips for nodes
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Match node definitions
      const nodeMatches = [
        line.match(/^\s*(\w+)\[(.+?)\]/),
        line.match(/^\s*(\w+)\{(.+?)\}/),
        line.match(/^\s*(\w+)\(\((.+?)\)\)/)
      ];

      for (const match of nodeMatches) {
        if (match) {
          const nodeId = match[1];
          const nodeLabel = match[2];
          
          // Generate tooltip content
          const tooltipContent = this.generateTooltipContent(nodeId, nodeLabel, userFlow, context);
          if (tooltipContent) {
            const tooltip = `click ${nodeId} "javascript:showTooltip('${nodeId}', '${this.escapeString(tooltipContent)}')"`;
            tooltips.push(tooltip);
            this.tooltipData.set(nodeId, tooltipContent);
          }
          break;
        }
      }
    }

    // Add tooltips to the diagram
    if (tooltips.length > 0) {
      const enhancedLines = [...lines];
      enhancedLines.push('');
      enhancedLines.push('    %% Interactive Tooltips');
      enhancedLines.push(...tooltips.map(tooltip => `    ${tooltip}`));
      return enhancedLines.join('\n');
    }

    return mermaidCode;
  }

  /**
   * Add navigation links to diagram nodes
   */
  addNavigationLinks(mermaidCode, userFlow, context) {
    const lines = mermaidCode.split('\n');
    const navigationLinks = [];

    // Generate navigation links
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // Match node definitions
      const nodeMatch = line.match(/^\s*(\w+)[\[\{(].+?[\]\})]/) || 
                       line.match(/^\s*(\w+)\s*:/);

      if (nodeMatch) {
        const nodeId = nodeMatch[1];
        
        // Generate navigation link
        const navLink = this.generateNavigationLink(nodeId, userFlow, context);
        if (navLink) {
          navigationLinks.push(navLink);
          this.navigationMap.set(nodeId, navLink);
        }
      }
    }

    // Add navigation links to the diagram
    if (navigationLinks.length > 0) {
      const enhancedLines = [...lines];
      enhancedLines.push('');
      enhancedLines.push('    %% Navigation Links');
      enhancedLines.push(...navigationLinks.map(link => `    ${link}`));
      return enhancedLines.join('\n');
    }

    return mermaidCode;
  }

  /**
   * Add highlighting to important nodes
   */
  addHighlighting(mermaidCode, userFlow, context) {
    const lines = mermaidCode.split('\n');
    const highlights = [];

    // Identify important nodes for highlighting
    const importantNodes = this.identifyImportantNodes(mermaidCode, userFlow);

    for (const nodeId of importantNodes) {
      const highlightClass = this.getHighlightClass(nodeId, userFlow);
      highlights.push(`class ${nodeId} ${highlightClass}`);
    }

    // Add highlighting styles
    if (highlights.length > 0) {
      const enhancedLines = [...lines];
      enhancedLines.push('');
      enhancedLines.push('    %% Node Highlighting');
      enhancedLines.push(...this.generateHighlightStyles());
      enhancedLines.push(...highlights.map(highlight => `    ${highlight}`));
      return enhancedLines.join('\n');
    }

    return mermaidCode;
  }

  /**
   * Add animations to diagram
   */
  addAnimations(mermaidCode, userFlow, context) {
    const lines = mermaidCode.split('\n');
    const animations = [];

    // Add animation classes
    animations.push('classDef animated fill:#f9f9f9,stroke:#333,stroke-width:2px,animation:pulse 2s infinite');
    animations.push('classDef highlight fill:#ffeb3b,stroke:#f57f17,stroke-width:3px,animation:glow 1.5s ease-in-out infinite alternate');

    // Apply animations to specific node types
    const animatedNodes = this.identifyAnimatedNodes(mermaidCode, userFlow);
    for (const nodeId of animatedNodes) {
      animations.push(`class ${nodeId} animated`);
    }

    if (animations.length > 0) {
      const enhancedLines = [...lines];
      enhancedLines.push('');
      enhancedLines.push('    %% Animations');
      enhancedLines.push(...animations.map(animation => `    ${animation}`));
      return enhancedLines.join('\n');
    }

    return mermaidCode;
  }

  /**
   * Generate click event for a node
   */
  generateClickEvent(nodeId, nodeLabel, userFlow, context) {
    const nodeType = this.identifyNodeType(nodeLabel);
    
    switch (nodeType) {
      case 'start':
        return `click ${nodeId} "javascript:showFlowDetails('${userFlow.id}', 'start')"`;
      case 'end':
        return `click ${nodeId} "javascript:showFlowDetails('${userFlow.id}', 'end')"`;
      case 'decision':
        return `click ${nodeId} "javascript:showDecisionDetails('${nodeId}', '${this.escapeString(nodeLabel)}')"`;
      case 'process':
        return `click ${nodeId} "javascript:showProcessDetails('${nodeId}', '${this.escapeString(nodeLabel)}')"`;
      case 'error':
        return `click ${nodeId} "javascript:showErrorDetails('${nodeId}', '${this.escapeString(nodeLabel)}')"`;
      default:
        return `click ${nodeId} "javascript:showNodeDetails('${nodeId}', '${this.escapeString(nodeLabel)}')"`;
    }
  }

  /**
   * Generate tooltip content for a node
   */
  generateTooltipContent(nodeId, nodeLabel, userFlow, context) {
    const nodeType = this.identifyNodeType(nodeLabel);
    
    let tooltip = `<strong>${nodeLabel}</strong><br/>`;
    
    switch (nodeType) {
      case 'start':
        tooltip += `Flow: ${userFlow.title}<br/>Persona: ${userFlow.persona || 'User'}<br/>Trigger: ${userFlow.trigger || 'User action'}`;
        break;
      case 'end':
        tooltip += `Exit Point<br/>Flow: ${userFlow.title}<br/>Success Criteria: ${userFlow.success_criteria?.[0] || 'Task completed'}`;
        break;
      case 'decision':
        const relatedDecision = this.findRelatedDecisionPoint(nodeLabel, userFlow);
        tooltip += `Decision Point<br/>Condition: ${relatedDecision || nodeLabel}<br/>Branches: Yes/No paths`;
        break;
      case 'process':
        tooltip += `Process Step<br/>Action: ${nodeLabel}<br/>Type: User/System interaction`;
        break;
      case 'error':
        const relatedError = this.findRelatedErrorHandling(nodeLabel, userFlow);
        tooltip += `Error Handling<br/>Error: ${relatedError || nodeLabel}<br/>Recovery: Available`;
        break;
      default:
        tooltip += `Step: ${nodeLabel}<br/>Flow: ${userFlow.title}<br/>Click for details`;
    }
    
    return tooltip;
  }

  /**
   * Generate navigation link for a node
   */
  generateNavigationLink(nodeId, userFlow, context) {
    // Generate links to related flows or external resources
    if (userFlow.relationships && userFlow.relationships.length > 0) {
      const relatedFlow = userFlow.relationships[0];
      return `click ${nodeId} "javascript:navigateToFlow('${relatedFlow.flowId}')"`;
    }
    
    // Link to flow documentation
    return `click ${nodeId} "javascript:showFlowDocumentation('${userFlow.id}')"`;
  }

  /**
   * Identify node type from label
   */
  identifyNodeType(nodeLabel) {
    const label = nodeLabel.toLowerCase();
    
    if (label.includes('start') || label.includes('begin')) return 'start';
    if (label.includes('end') || label.includes('finish') || label.includes('complete')) return 'end';
    if (label.includes('?') || label.includes('decision') || label.includes('check')) return 'decision';
    if (label.includes('error') || label.includes('fail') || label.includes('exception')) return 'error';
    
    return 'process';
  }

  /**
   * Identify important nodes for highlighting
   */
  identifyImportantNodes(mermaidCode, userFlow) {
    const importantNodes = [];
    const lines = mermaidCode.split('\n');
    
    for (const line of lines) {
      const nodeMatch = line.match(/^\s*(\w+)[\[\{(]/);
      if (nodeMatch) {
        const nodeId = nodeMatch[1];
        const nodeLabel = this.extractNodeLabel(line);
        
        // Highlight start/end nodes and decision points
        const nodeType = this.identifyNodeType(nodeLabel);
        if (['start', 'end', 'decision', 'error'].includes(nodeType)) {
          importantNodes.push(nodeId);
        }
      }
    }
    
    return importantNodes;
  }

  /**
   * Identify nodes for animation
   */
  identifyAnimatedNodes(mermaidCode, userFlow) {
    const animatedNodes = [];
    const lines = mermaidCode.split('\n');
    
    for (const line of lines) {
      const nodeMatch = line.match(/^\s*(\w+)[\[\{(]/);
      if (nodeMatch) {
        const nodeId = nodeMatch[1];
        const nodeLabel = this.extractNodeLabel(line);
        
        // Animate decision points and error nodes
        const nodeType = this.identifyNodeType(nodeLabel);
        if (['decision', 'error'].includes(nodeType)) {
          animatedNodes.push(nodeId);
        }
      }
    }
    
    return animatedNodes;
  }

  /**
   * Generate highlight styles
   */
  generateHighlightStyles() {
    return [
      'classDef startNode fill:#e8f5e8,stroke:#4caf50,stroke-width:3px',
      'classDef endNode fill:#fff3e0,stroke:#ff9800,stroke-width:3px',
      'classDef decisionNode fill:#e3f2fd,stroke:#2196f3,stroke-width:3px',
      'classDef errorNode fill:#ffebee,stroke:#f44336,stroke-width:3px',
      'classDef processNode fill:#f3e5f5,stroke:#9c27b0,stroke-width:2px'
    ];
  }

  /**
   * Get highlight class for node
   */
  getHighlightClass(nodeId, userFlow) {
    // This would be determined by the node type
    const nodeType = this.identifyNodeTypeById(nodeId, userFlow);
    
    switch (nodeType) {
      case 'start': return 'startNode';
      case 'end': return 'endNode';
      case 'decision': return 'decisionNode';
      case 'error': return 'errorNode';
      default: return 'processNode';
    }
  }

  /**
   * Extract node label from line
   */
  extractNodeLabel(line) {
    const matches = [
      line.match(/\[(.+?)\]/),
      line.match(/\{(.+?)\}/),
      line.match(/\(\((.+?)\)\)/),
      line.match(/\>(.+?)\]/),
      line.match(/\|(.+?)\|/)
    ];
    
    for (const match of matches) {
      if (match) return match[1];
    }
    
    return '';
  }

  /**
   * Find related decision point
   */
  findRelatedDecisionPoint(nodeLabel, userFlow) {
    if (!userFlow.decision_points) return null;
    
    const labelLower = nodeLabel.toLowerCase();
    return userFlow.decision_points.find(decision => 
      labelLower.includes(decision.toLowerCase().split(' ')[0])
    );
  }

  /**
   * Find related error handling
   */
  findRelatedErrorHandling(nodeLabel, userFlow) {
    if (!userFlow.error_handling) return null;
    
    const labelLower = nodeLabel.toLowerCase();
    return userFlow.error_handling.find(error => 
      labelLower.includes(error.toLowerCase().split(' ')[0])
    );
  }

  /**
   * Identify node type by ID
   */
  identifyNodeTypeById(nodeId, userFlow) {
    // This is a simplified implementation
    // In practice, you'd maintain a mapping of node IDs to types
    if (nodeId.includes('start') || nodeId === 'node1') return 'start';
    if (nodeId.includes('end') || nodeId.includes('exit')) return 'end';
    if (nodeId.includes('decision') || nodeId.includes('check')) return 'decision';
    if (nodeId.includes('error') || nodeId.includes('fail')) return 'error';
    
    return 'process';
  }

  /**
   * Escape string for JavaScript
   */
  escapeString(str) {
    return str.replace(/'/g, "\\'").replace(/"/g, '\\"').replace(/\n/g, '\\n');
  }

  /**
   * Generate JavaScript helper functions for interactivity
   */
  generateJavaScriptHelpers() {
    return `
// Interactive Mermaid Diagram Helper Functions
function showFlowDetails(flowId, nodeType) {
  console.log('Flow Details:', flowId, nodeType);
  // Implementation would show flow details in a modal or sidebar
}

function showDecisionDetails(nodeId, nodeLabel) {
  console.log('Decision Details:', nodeId, nodeLabel);
  // Implementation would show decision logic and branches
}

function showProcessDetails(nodeId, nodeLabel) {
  console.log('Process Details:', nodeId, nodeLabel);
  // Implementation would show process step details
}

function showErrorDetails(nodeId, nodeLabel) {
  console.log('Error Details:', nodeId, nodeLabel);
  // Implementation would show error handling information
}

function showNodeDetails(nodeId, nodeLabel) {
  console.log('Node Details:', nodeId, nodeLabel);
  // Implementation would show general node information
}

function showTooltip(nodeId, content) {
  console.log('Tooltip:', nodeId, content);
  // Implementation would show tooltip with rich content
}

function navigateToFlow(flowId) {
  console.log('Navigate to Flow:', flowId);
  // Implementation would navigate to related flow
}

function showFlowDocumentation(flowId) {
  console.log('Show Documentation:', flowId);
  // Implementation would show flow documentation
}
`;
  }

  /**
   * Get interactive elements summary
   */
  getInteractiveElementsSummary() {
    return {
      clickHandlers: this.clickHandlers.size,
      tooltips: this.tooltipData.size,
      navigationLinks: this.navigationMap.size,
      totalInteractiveElements: this.clickHandlers.size + this.tooltipData.size + this.navigationMap.size,
      enabledFeatures: {
        clickEvents: this.config.enableClickEvents,
        tooltips: this.config.enableTooltips,
        navigation: this.config.enableNavigation,
        highlighting: this.config.enableHighlighting,
        animations: this.config.enableAnimations
      }
    };
  }
}
