/**
 * Component Mapping System
 * Translates functional requirements into ASCII component representations
 */

import { UIBestPracticesIntelligence } from '../research/ui-best-practices-intelligence.js';

/**
 * Component Mapper
 */
export class ComponentMapper {
  constructor(config = {}) {
    this.componentTemplates = this.initializeComponentTemplates();
    this.interactionIndicators = this.initializeInteractionIndicators();
    this.componentPatterns = this.initializeComponentPatterns();

    // Initialize research intelligence
    this.intelligence = new UIBestPracticesIntelligence(config.intelligence || {});
    this.researchEnabled = config.enableResearch !== false;
  }

  /**
   * Map functional requirements to UI components with research intelligence
   */
  async mapRequirementsToComponents(functionalRequirements, userStories = [], context = {}) {
    const components = [];
    const componentMap = new Map();

    // Process functional requirements
    for (const req of functionalRequirements) {
      const reqComponents = this.extractComponentsFromRequirement(req);
      for (const component of reqComponents) {
        const key = `${component.type}_${component.name}`;
        if (!componentMap.has(key)) {
          componentMap.set(key, component);
          components.push(component);
        } else {
          // Merge with existing component
          this.mergeComponents(componentMap.get(key), component);
        }
      }
    }

    // Process user stories for additional context
    for (const story of userStories) {
      const storyComponents = this.extractComponentsFromUserStory(story);
      for (const component of storyComponents) {
        const key = `${component.type}_${component.name}`;
        if (!componentMap.has(key)) {
          componentMap.set(key, component);
          components.push(component);
        } else {
          // Add user story context to existing component
          this.addUserStoryContext(componentMap.get(key), story);
        }
      }
    }

    // Add default structural components if not present
    this.ensureStructuralComponents(components);

    // Apply research intelligence to enhance components
    if (this.researchEnabled) {
      await this.enhanceComponentsWithIntelligence(components, context);
    }

    // Sort components by priority and layout order
    return this.sortComponentsByPriority(components);
  }

  /**
   * Extract components from functional requirement
   */
  extractComponentsFromRequirement(requirement) {
    const reqText = typeof requirement === 'object' 
      ? (requirement.description || requirement.title || requirement.name || '')
      : requirement;
    
    const components = [];
    const reqLower = reqText.toLowerCase();
    
    // Authentication components
    if (this.matchesPattern(reqLower, this.componentPatterns.authentication)) {
      components.push(this.createComponent('form', 'Login Form', {
        fields: ['username', 'password'],
        actions: ['login', 'forgot_password'],
        validation: true,
        security: 'high',
        sourceRequirement: requirement
      }));
    }
    
    // Form components
    if (this.matchesPattern(reqLower, this.componentPatterns.forms)) {
      const formType = this.detectFormType(reqLower);
      const fields = this.extractFormFields(reqLower);
      components.push(this.createComponent('form', `${formType} Form`, {
        fields,
        actions: this.extractFormActions(reqLower),
        validation: this.requiresValidation(reqLower),
        sourceRequirement: requirement
      }));
    }
    
    // Data display components
    if (this.matchesPattern(reqLower, this.componentPatterns.dataDisplay)) {
      const displayType = this.detectDisplayType(reqLower);
      components.push(this.createComponent(displayType, `Data ${displayType}`, {
        dataSource: this.extractDataSource(reqLower),
        columns: this.extractColumns(reqLower),
        actions: this.extractDataActions(reqLower),
        pagination: this.requiresPagination(reqLower),
        sourceRequirement: requirement
      }));
    }
    
    // Navigation components
    if (this.matchesPattern(reqLower, this.componentPatterns.navigation)) {
      components.push(this.createComponent('navigation', 'Main Navigation', {
        items: this.extractNavigationItems(reqLower),
        style: this.detectNavigationStyle(reqLower),
        responsive: true,
        sourceRequirement: requirement
      }));
    }
    
    // Search components
    if (this.matchesPattern(reqLower, this.componentPatterns.search)) {
      components.push(this.createComponent('search', 'Search Interface', {
        searchType: this.detectSearchType(reqLower),
        filters: this.extractSearchFilters(reqLower),
        suggestions: this.requiresSuggestions(reqLower),
        sourceRequirement: requirement
      }));
    }
    
    // File upload components
    if (this.matchesPattern(reqLower, this.componentPatterns.fileUpload)) {
      components.push(this.createComponent('upload', 'File Upload', {
        fileTypes: this.extractFileTypes(reqLower),
        multiple: this.allowsMultipleFiles(reqLower),
        dragDrop: true,
        sourceRequirement: requirement
      }));
    }
    
    // Dashboard components
    if (this.matchesPattern(reqLower, this.componentPatterns.dashboard)) {
      components.push(this.createComponent('dashboard', 'Analytics Dashboard', {
        widgets: this.extractDashboardWidgets(reqLower),
        layout: 'grid',
        customizable: this.isCustomizable(reqLower),
        sourceRequirement: requirement
      }));
    }

    // Content display components
    if (this.matchesPattern(reqLower, ['content', 'article', 'blog', 'news'])) {
      components.push(this.createComponent('content', 'Main Content', {
        contentType: this.detectContentType(reqLower),
        sourceRequirement: requirement
      }));
    }

    // Sidebar components
    if (this.matchesPattern(reqLower, ['sidebar', 'menu', 'aside'])) {
      components.push(this.createComponent('sidebar', 'Sidebar Menu', {
        items: this.extractNavigationItems(reqLower),
        sourceRequirement: requirement
      }));
    }
    
    return components;
  }

  /**
   * Extract components from user story
   */
  extractComponentsFromUserStory(story) {
    const storyText = typeof story === 'object' 
      ? (story.story || story.description || story.title || '')
      : story;
    
    const components = [];
    const storyLower = storyText.toLowerCase();
    
    // Extract persona-specific components
    const persona = this.extractPersona(story);
    
    // Extract action-based components
    const actions = this.extractUserActions(storyLower);
    for (const action of actions) {
      const component = this.mapActionToComponent(action, persona, story);
      if (component) {
        components.push(component);
      }
    }
    
    return components;
  }

  /**
   * Create component with ASCII representation
   */
  createComponent(type, name, properties = {}) {
    const template = this.componentTemplates[type] || this.componentTemplates.default;
    
    const component = {
      id: this.generateComponentId(type, name),
      type,
      name,
      asciiTemplate: template.ascii,
      dimensions: template.dimensions,
      interactions: properties.interactions || template.interactions || [],
      properties: {
        ...template.defaultProperties,
        ...properties
      },
      metadata: {
        createdAt: new Date().toISOString(),
        source: 'requirement_mapping',
        confidence: this.calculateMappingConfidence(type, properties)
      }
    };
    
    // Generate dynamic ASCII representation
    component.asciiRepresentation = this.generateASCIIRepresentation(component);
    
    return component;
  }

  /**
   * Generate ASCII representation for component
   */
  generateASCIIRepresentation(component) {
    const template = component.asciiTemplate || this.componentTemplates.default?.ascii || '┌─ {name} ─┐\n│ Content │\n└─────────┘';
    const dimensions = component.dimensions || { width: 30, height: 5 };
    const { width, height } = dimensions;

    let ascii = template;

    // Replace placeholders with actual content
    ascii = ascii.replace('{name}', component.name || 'Component');
    ascii = ascii.replace('{width}', width);
    ascii = ascii.replace('{height}', height);
    
    // Add interaction indicators
    if (component.interactions && component.interactions.length > 0) {
      ascii = this.addInteractionIndicators(ascii, component.interactions);
    }
    
    // Add dynamic content based on component type
    switch (component.type) {
      case 'form':
        ascii = this.generateFormASCII(component);
        break;
      case 'table':
        ascii = this.generateTableASCII(component);
        break;
      case 'navigation':
        ascii = this.generateNavigationASCII(component);
        break;
      case 'button':
        ascii = this.generateButtonASCII(component);
        break;
      default:
        ascii = this.generateGenericASCII(component);
    }
    
    return ascii;
  }

  /**
   * Generate form ASCII representation
   */
  generateFormASCII(component) {
    const fields = component.properties.fields || [];
    const actions = component.properties.actions || [];
    const width = component.dimensions.width;
    
    let ascii = `┌${'─'.repeat(width - 2)}┐\n`;
    ascii += `│ ${component.name.padEnd(width - 3)}│\n`;
    ascii += `├${'─'.repeat(width - 2)}┤\n`;
    
    // Add form fields
    for (const field of fields) {
      const fieldName = typeof field === 'string' ? field : field.name;
      const fieldType = typeof field === 'object' ? field.type : 'text';
      const indicator = this.getFieldTypeIndicator(fieldType);
      
      ascii += `│ ${fieldName}: ${indicator.padEnd(width - fieldName.length - 5)}│\n`;
    }
    
    if (fields.length > 0) {
      ascii += `├${'─'.repeat(width - 2)}┤\n`;
    }
    
    // Add action buttons
    for (const action of actions) {
      const actionName = typeof action === 'string' ? action : action.name;
      ascii += `│ [${actionName}]${' '.repeat(width - actionName.length - 5)}│\n`;
    }
    
    ascii += `└${'─'.repeat(width - 2)}┘`;
    
    return ascii;
  }

  /**
   * Generate table ASCII representation
   */
  generateTableASCII(component) {
    const columns = component.properties.columns || ['Column 1', 'Column 2', 'Column 3'];
    const width = component.dimensions.width;
    const colWidth = Math.floor((width - columns.length - 1) / columns.length);
    
    let ascii = `┌${'─'.repeat(width - 2)}┐\n`;
    ascii += `│ ${component.name.padEnd(width - 3)}│\n`;
    ascii += `├${'─'.repeat(width - 2)}┤\n`;
    
    // Header row
    let headerRow = '│';
    for (let i = 0; i < columns.length; i++) {
      const col = columns[i].substring(0, colWidth - 1);
      headerRow += ` ${col.padEnd(colWidth - 1)}`;
      if (i < columns.length - 1) headerRow += '│';
    }
    headerRow += '│\n';
    ascii += headerRow;
    
    ascii += `├${'─'.repeat(width - 2)}┤\n`;
    
    // Data rows (sample)
    for (let row = 0; row < 3; row++) {
      let dataRow = '│';
      for (let i = 0; i < columns.length; i++) {
        const cellData = `Data ${row + 1}.${i + 1}`;
        dataRow += ` ${cellData.padEnd(colWidth - 1)}`;
        if (i < columns.length - 1) dataRow += '│';
      }
      dataRow += '│\n';
      ascii += dataRow;
    }
    
    ascii += `└${'─'.repeat(width - 2)}┘`;
    
    return ascii;
  }

  /**
   * Generate navigation ASCII representation
   */
  generateNavigationASCII(component) {
    const items = component.properties.items || ['Home', 'About', 'Services', 'Contact'];
    const width = component.dimensions.width;
    
    let ascii = `┌${'─'.repeat(width - 2)}┐\n`;
    
    let navRow = '│ ';
    for (let i = 0; i < items.length; i++) {
      navRow += `[${items[i]}]`;
      if (i < items.length - 1) navRow += ' ';
    }
    navRow = navRow.padEnd(width - 1) + '│\n';
    ascii += navRow;
    
    ascii += `└${'─'.repeat(width - 2)}┘`;
    
    return ascii;
  }

  /**
   * Generate button ASCII representation
   */
  generateButtonASCII(component) {
    const buttonText = component.name;
    const width = Math.max(buttonText.length + 4, 12);
    
    let ascii = `┌${'─'.repeat(width - 2)}┐\n`;
    ascii += `│ ${buttonText.padEnd(width - 3)}│\n`;
    ascii += `└${'─'.repeat(width - 2)}┘`;
    
    return ascii;
  }

  /**
   * Generate generic component ASCII representation
   */
  generateGenericASCII(component) {
    const width = component.dimensions.width;
    const height = component.dimensions.height;
    
    let ascii = `┌${'─'.repeat(width - 2)}┐\n`;
    
    // Add component name in center
    const nameRow = Math.floor(height / 2);
    for (let i = 1; i < height - 1; i++) {
      if (i === nameRow) {
        const padding = Math.floor((width - component.name.length - 2) / 2);
        ascii += `│${' '.repeat(padding)}${component.name}${' '.repeat(width - component.name.length - padding - 2)}│\n`;
      } else {
        ascii += `│${' '.repeat(width - 2)}│\n`;
      }
    }
    
    ascii += `└${'─'.repeat(width - 2)}┘`;
    
    return ascii;
  }

  // Helper methods and initialization
  initializeComponentTemplates() {
    return {
      form: {
        ascii: '┌─ {name} ─┐\n│ [Fields] │\n│ [Submit] │\n└──────────┘',
        dimensions: { width: 30, height: 8 },
        interactions: ['input', 'submit', 'validate'],
        defaultProperties: { validation: true, responsive: true }
      },
      table: {
        ascii: '┌─ {name} ─┐\n│ Col1│Col2│\n│ ────┼────│\n│ Data│Data│\n└──────────┘',
        dimensions: { width: 40, height: 10 },
        interactions: ['sort', 'filter', 'select'],
        defaultProperties: { sortable: true, pagination: true }
      },
      navigation: {
        ascii: '┌─ Navigation ─┐\n│ [Home][About]│\n└──────────────┘',
        dimensions: { width: 50, height: 3 },
        interactions: ['click', 'navigate'],
        defaultProperties: { responsive: true, collapsible: true }
      },
      button: {
        ascii: '┌─────────┐\n│ {name}  │\n└─────────┘',
        dimensions: { width: 12, height: 3 },
        interactions: ['click'],
        defaultProperties: { clickable: true }
      },
      search: {
        ascii: '┌─ Search ─────┐\n│ [___________] │\n│ [🔍] [Filter] │\n└───────────────┘',
        dimensions: { width: 35, height: 4 },
        interactions: ['input', 'search', 'filter'],
        defaultProperties: { autocomplete: true }
      },
      upload: {
        ascii: '┌─ File Upload ─┐\n│ Drag files here│\n│ or [Browse...] │\n└────────────────┘',
        dimensions: { width: 25, height: 4 },
        interactions: ['drag', 'drop', 'browse'],
        defaultProperties: { multiple: false }
      },
      dashboard: {
        ascii: '┌─ Dashboard ───┐\n│ [Widget1][W2] │\n│ [Widget3][W4] │\n└───────────────┘',
        dimensions: { width: 60, height: 15 },
        interactions: ['customize', 'resize'],
        defaultProperties: { customizable: true }
      },
      default: {
        ascii: '┌─ {name} ─┐\n│ Content │\n└─────────┘',
        dimensions: { width: 20, height: 3 },
        interactions: [],
        defaultProperties: {}
      }
    };
  }

  initializeInteractionIndicators() {
    return {
      click: '👆',
      input: '✏️',
      drag: '👋',
      hover: '🖱️',
      submit: '✅',
      search: '🔍',
      filter: '🔽',
      sort: '↕️',
      navigate: '→',
      validate: '✓'
    };
  }

  initializeComponentPatterns() {
    return {
      authentication: ['login', 'signin', 'authenticate', 'credentials', 'password', 'username'],
      forms: ['form', 'input', 'field', 'submit', 'create', 'add', 'register', 'signup'],
      dataDisplay: ['table', 'list', 'grid', 'display', 'show', 'view', 'data'],
      navigation: ['menu', 'nav', 'navigate', 'link', 'route', 'page'],
      search: ['search', 'find', 'filter', 'query', 'lookup'],
      fileUpload: ['upload', 'file', 'attach', 'import', 'document'],
      dashboard: ['dashboard', 'overview', 'summary', 'metrics', 'analytics']
    };
  }

  // Pattern matching and extraction methods
  matchesPattern(text, patterns) {
    return patterns.some(pattern => text.includes(pattern));
  }

  detectFormType(text) {
    if (text.includes('login') || text.includes('signin')) return 'Login';
    if (text.includes('register') || text.includes('signup')) return 'Registration';
    if (text.includes('contact')) return 'Contact';
    if (text.includes('feedback')) return 'Feedback';
    return 'Data Entry';
  }

  extractFormFields(text) {
    const commonFields = {
      'email': 'email',
      'password': 'password',
      'name': 'text',
      'phone': 'tel',
      'address': 'text',
      'message': 'textarea',
      'date': 'date',
      'number': 'number'
    };
    
    const fields = [];
    for (const [field, type] of Object.entries(commonFields)) {
      if (text.includes(field)) {
        fields.push({ name: field, type });
      }
    }
    
    return fields.length > 0 ? fields : [{ name: 'input', type: 'text' }];
  }

  extractFormActions(text) {
    const actions = [];
    if (text.includes('submit') || text.includes('save')) actions.push('submit');
    if (text.includes('cancel')) actions.push('cancel');
    if (text.includes('reset')) actions.push('reset');
    return actions.length > 0 ? actions : ['submit'];
  }

  detectDisplayType(text) {
    if (text.includes('table')) return 'table';
    if (text.includes('list')) return 'list';
    if (text.includes('grid')) return 'grid';
    return 'table';
  }

  extractDataSource(text) {
    // Simple extraction - could be enhanced with NLP
    const sources = ['users', 'products', 'orders', 'customers', 'items', 'records'];
    for (const source of sources) {
      if (text.includes(source)) return source;
    }
    return 'data';
  }

  extractColumns(text) {
    // Extract potential column names from text
    const commonColumns = ['name', 'email', 'date', 'status', 'id', 'title', 'description'];
    const found = commonColumns.filter(col => text.includes(col));
    return found.length > 0 ? found : ['Column 1', 'Column 2', 'Column 3'];
  }

  extractNavigationItems(text) {
    const commonItems = ['home', 'about', 'services', 'contact', 'products', 'blog'];
    const found = commonItems.filter(item => text.includes(item));
    return found.length > 0 ? found : ['Home', 'About', 'Services', 'Contact'];
  }

  generateComponentId(type, name) {
    const sanitized = name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
    return `${type}_${sanitized}_${Date.now()}`;
  }

  calculateMappingConfidence(type, properties) {
    // Simple confidence calculation based on available information
    let confidence = 0.5;
    
    if (properties.sourceRequirement) confidence += 0.2;
    if (properties.fields && properties.fields.length > 0) confidence += 0.2;
    if (properties.actions && properties.actions.length > 0) confidence += 0.1;
    
    return Math.min(confidence, 1.0);
  }

  getFieldTypeIndicator(fieldType) {
    const indicators = {
      text: '[_________]',
      email: '[email@___]',
      password: '[*********]',
      number: '[123______]',
      date: '[DD/MM/YY_]',
      textarea: '[_________]\n│         [_________]',
      select: '[▼ Select_]',
      checkbox: '[☐] Option',
      radio: '(○) Option'
    };
    return indicators[fieldType] || '[_________]';
  }

  // Component management methods
  mergeComponents(existing, newComponent) {
    // Merge properties and interactions
    existing.properties = { ...existing.properties, ...newComponent.properties };
    existing.interactions = [...new Set([...existing.interactions, ...newComponent.interactions])];
    existing.metadata.confidence = Math.max(existing.metadata.confidence, newComponent.metadata.confidence);
  }

  addUserStoryContext(component, story) {
    if (!component.userStories) component.userStories = [];
    component.userStories.push(story);
    component.metadata.confidence += 0.1;
  }

  ensureStructuralComponents(components) {
    const hasHeader = components.some(c => c.type === 'header');
    const hasNavigation = components.some(c => c.type === 'navigation');
    const hasFooter = components.some(c => c.type === 'footer');
    
    if (!hasHeader) {
      components.unshift(this.createComponent('header', 'Header', { priority: 1 }));
    }
    
    if (!hasNavigation && components.length > 3) {
      components.splice(1, 0, this.createComponent('navigation', 'Main Navigation', { priority: 2 }));
    }
    
    if (!hasFooter) {
      components.push(this.createComponent('footer', 'Footer', { priority: 10 }));
    }
  }

  sortComponentsByPriority(components) {
    return components.sort((a, b) => {
      const priorityA = a.properties.priority || 5;
      const priorityB = b.properties.priority || 5;
      return priorityA - priorityB;
    });
  }

  addInteractionIndicators(ascii, interactions) {
    // Add interaction indicators to ASCII representation
    let enhanced = ascii;
    
    for (const interaction of interactions) {
      const indicator = this.interactionIndicators[interaction];
      if (indicator) {
        enhanced = enhanced.replace('│', `│${indicator}`);
        break; // Add only one indicator to avoid clutter
      }
    }
    
    return enhanced;
  }

  // User story processing methods
  extractPersona(story) {
    if (typeof story === 'object' && story.persona) {
      return story.persona;
    }
    
    const storyText = typeof story === 'object' ? story.story : story;
    const personaPatterns = ['user', 'admin', 'customer', 'manager', 'visitor'];
    
    for (const pattern of personaPatterns) {
      if (storyText.toLowerCase().includes(pattern)) {
        return pattern;
      }
    }
    
    return 'user';
  }

  extractUserActions(text) {
    const actionPatterns = ['create', 'view', 'edit', 'delete', 'search', 'filter', 'sort', 'login', 'logout'];
    const found = [];
    
    for (const action of actionPatterns) {
      if (text.includes(action)) {
        found.push(action);
      }
    }
    
    return found;
  }

  mapActionToComponent(action, persona, story) {
    const actionMappings = {
      'create': { type: 'form', name: 'Create Form' },
      'edit': { type: 'form', name: 'Edit Form' },
      'view': { type: 'display', name: 'View Component' },
      'search': { type: 'search', name: 'Search Interface' },
      'login': { type: 'form', name: 'Login Form' },
      'delete': { type: 'button', name: 'Delete Button' }
    };
    
    const mapping = actionMappings[action];
    if (!mapping) return null;
    
    return this.createComponent(mapping.type, mapping.name, {
      persona,
      userStory: story,
      action
    });
  }

  requiresValidation(text) {
    return text.includes('valid') || text.includes('required') || text.includes('check');
  }

  requiresPagination(text) {
    return text.includes('page') || text.includes('many') || text.includes('large');
  }

  requiresSuggestions(text) {
    return text.includes('suggest') || text.includes('auto') || text.includes('complete');
  }

  allowsMultipleFiles(text) {
    return text.includes('multiple') || text.includes('many') || text.includes('files');
  }

  isCustomizable(text) {
    return text.includes('custom') || text.includes('configure') || text.includes('personalize');
  }

  extractFileTypes(text) {
    const types = ['pdf', 'doc', 'image', 'csv', 'excel'];
    return types.filter(type => text.includes(type));
  }

  extractDashboardWidgets(text) {
    const widgets = ['chart', 'graph', 'metric', 'summary', 'report'];
    return widgets.filter(widget => text.includes(widget));
  }

  detectContentType(text) {
    if (text.includes('article') || text.includes('blog')) return 'article';
    if (text.includes('news')) return 'news';
    if (text.includes('product')) return 'product';
    return 'general';
  }

  detectNavigationStyle(text) {
    if (text.includes('sidebar')) return 'sidebar';
    if (text.includes('tab')) return 'tabs';
    return 'horizontal';
  }

  detectSearchType(text) {
    if (text.includes('advanced')) return 'advanced';
    if (text.includes('filter')) return 'filtered';
    return 'simple';
  }

  extractSearchFilters(text) {
    const filters = ['date', 'category', 'status', 'type', 'location'];
    return filters.filter(filter => text.includes(filter));
  }

  extractDataActions(text) {
    const actions = ['edit', 'delete', 'view', 'export', 'print'];
    return actions.filter(action => text.includes(action));
  }

  /**
   * Enhance components with research intelligence
   */
  async enhanceComponentsWithIntelligence(components, context) {
    const enhancementPromises = components.map(async (component) => {
      try {
        const enhanced = await this.intelligence.enhanceComponentWithBestPractices(
          component,
          context
        );

        // Update the component with enhanced properties
        Object.assign(component, enhanced);

        // Regenerate ASCII with intelligence
        component.asciiRepresentation = this.generateIntelligentASCII(component);

        return component;
      } catch (error) {
        console.warn(`Failed to enhance component ${component.name}:`, error.message);
        return component;
      }
    });

    await Promise.allSettled(enhancementPromises);
  }

  /**
   * Generate intelligent ASCII representation
   */
  generateIntelligentASCII(component) {
    // Start with base ASCII
    let ascii = this.generateASCIIRepresentation(component);

    // Add intelligence indicators if available
    if (component.bestPractices && component.bestPractices.length > 0) {
      ascii = this.addIntelligenceIndicators(ascii, component);
    }

    // Add accessibility indicators
    if (component.accessibility) {
      ascii = this.addAccessibilityIndicators(ascii, component);
    }

    return ascii;
  }

  /**
   * Add intelligence indicators to ASCII
   */
  addIntelligenceIndicators(ascii, component) {
    const lines = ascii.split('\n');

    // Add intelligence badge to first line
    if (lines.length > 0) {
      lines[0] = lines[0].replace('│', '│✨');
    }

    // Add best practices summary at the end
    if (component.bestPractices && component.bestPractices.length > 0) {
      lines.push('');
      lines.push('💡 Best Practices Applied:');
      for (const practice of component.bestPractices.slice(0, 2)) {
        lines.push(`   • ${practice.practice}`);
      }
    }

    return lines.join('\n');
  }

  /**
   * Add accessibility indicators to ASCII
   */
  addAccessibilityIndicators(ascii, component) {
    const lines = ascii.split('\n');

    // Add accessibility badge
    if (lines.length > 0 && component.accessibility) {
      lines[0] = lines[0].replace('│', '│♿');
    }

    // Add accessibility features summary
    if (component.accessibility && component.accessibility.ariaAttributes) {
      lines.push('');
      lines.push('♿ Accessibility Features:');
      lines.push(`   • ARIA: ${component.accessibility.ariaAttributes.slice(0, 2).join(', ')}`);
      lines.push(`   • Keyboard: ${component.accessibility.keyboardNavigation.slice(0, 1).join('')}`);
    }

    return lines.join('\n');
  }

  /**
   * Map individual component for wireframe renderer integration
   */
  mapComponent(component, context = {}) {
    const componentText = typeof component === 'string' ? component :
      (component.name || component.title || component.description || '');

    const componentLower = componentText.toLowerCase();

    // Determine component type and properties
    const mappedComponent = {
      name: componentText,
      type: this.inferComponentTypeFromText(componentLower),
      properties: this.extractComponentProperties(componentLower),
      interactions: this.extractComponentInteractions(componentLower),
      priority: context.index ? context.index + 2 : 5 // Start after header
    };

    // Add context-specific enhancements
    if (context.wireframeContext) {
      mappedComponent.wireframeId = context.wireframeContext.id;
      mappedComponent.wireframeTitle = context.wireframeContext.title;
    }

    return mappedComponent;
  }

  /**
   * Infer component type from text description
   */
  inferComponentTypeFromText(text) {
    // Authentication components
    if (this.matchesPattern(text, ['login', 'signin', 'auth', 'credentials'])) {
      return 'form';
    }

    // Form components
    if (this.matchesPattern(text, ['form', 'input', 'field', 'submit', 'create', 'add'])) {
      return 'form';
    }

    // Data display components
    if (this.matchesPattern(text, ['table', 'list', 'grid', 'data', 'records'])) {
      return 'table';
    }

    // Navigation components
    if (this.matchesPattern(text, ['nav', 'menu', 'navigation', 'link', 'route'])) {
      return 'navigation';
    }

    // Search components
    if (this.matchesPattern(text, ['search', 'find', 'filter', 'query'])) {
      return 'search';
    }

    // Action components
    if (this.matchesPattern(text, ['button', 'action', 'submit', 'save', 'delete'])) {
      return 'button';
    }

    // File upload components
    if (this.matchesPattern(text, ['upload', 'file', 'attach', 'import'])) {
      return 'upload';
    }

    // Dashboard components
    if (this.matchesPattern(text, ['dashboard', 'chart', 'graph', 'metrics'])) {
      return 'dashboard';
    }

    // Layout components
    if (this.matchesPattern(text, ['sidebar', 'aside', 'panel'])) {
      return 'sidebar';
    }

    if (this.matchesPattern(text, ['header', 'top', 'banner'])) {
      return 'header';
    }

    if (this.matchesPattern(text, ['footer', 'bottom'])) {
      return 'footer';
    }

    // Default to content
    return 'content';
  }

  /**
   * Extract component properties from text
   */
  extractComponentProperties(text) {
    const properties = {};

    // Extract validation requirements
    if (text.includes('required') || text.includes('validation')) {
      properties.validation = true;
    }

    // Extract accessibility requirements
    if (text.includes('accessible') || text.includes('aria') || text.includes('screen reader')) {
      properties.accessibility = true;
    }

    // Extract responsive requirements
    if (text.includes('responsive') || text.includes('mobile') || text.includes('tablet')) {
      properties.responsive = true;
    }

    // Extract interaction requirements
    if (text.includes('interactive') || text.includes('click') || text.includes('hover')) {
      properties.interactive = true;
    }

    return properties;
  }

  /**
   * Extract component interactions from text
   */
  extractComponentInteractions(text) {
    const interactions = [];

    if (text.includes('click') || text.includes('button')) interactions.push('click');
    if (text.includes('input') || text.includes('type') || text.includes('enter')) interactions.push('input');
    if (text.includes('drag') || text.includes('drop')) interactions.push('drag');
    if (text.includes('hover') || text.includes('mouse')) interactions.push('hover');
    if (text.includes('submit') || text.includes('save')) interactions.push('submit');
    if (text.includes('search') || text.includes('find')) interactions.push('search');
    if (text.includes('filter') || text.includes('sort')) interactions.push('filter');
    if (text.includes('navigate') || text.includes('link')) interactions.push('navigate');

    return interactions;
  }
}
