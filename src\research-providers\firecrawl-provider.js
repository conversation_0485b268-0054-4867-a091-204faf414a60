/**
 * Firecrawl Research Provider
 * Implements comprehensive web content extraction, website structure analysis,
 * bulk content processing, and content format conversion using Firecrawl API
 */

import { BaseResearchProvider } from './base-research-provider.js';

/**
 * Firecrawl-specific content processing and analysis
 */
export class FirecrawlContentProcessor {
  constructor(config = {}) {
    this.maxContentLength = config.maxContentLength || 50000;
    this.preferredFormats = config.preferredFormats || ['markdown', 'structured'];
    this.contentFilters = config.contentFilters || [];
  }

  /**
   * Process and analyze extracted content
   * @param {Array} pages - Extracted pages from Firecrawl
   * @param {object} context - Processing context
   * @returns {Array} Processed and analyzed content
   */
  processContent(pages, context = {}) {
    if (!Array.isArray(pages)) return [];

    return pages
      .map(page => this.processPage(page, context))
      .filter(page => this.isValidContent(page))
      .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));
  }

  /**
   * Process individual page content
   * @param {object} page - Individual page data
   * @param {object} context - Processing context
   * @returns {object} Processed page with analysis
   */
  processPage(page, context) {
    const processed = {
      ...page,
      contentLength: this.getContentLength(page),
      contentType: this.detectContentType(page),
      relevanceScore: this.calculateRelevance(page, context),
      extractedData: this.extractStructuredData(page),
      summary: this.generateSummary(page)
    };

    // Apply content filters
    if (this.contentFilters.length > 0) {
      processed.filtered = this.applyFilters(processed);
    }

    return processed;
  }

  /**
   * Calculate content relevance score
   * @param {object} page - Page data
   * @param {object} context - Search context
   * @returns {number} Relevance score (0-1)
   */
  calculateRelevance(page, context) {
    let score = 0.5; // Base score

    // URL relevance
    if (context.targetDomain && page.url) {
      const pageDomain = this.extractDomain(page.url);
      if (pageDomain === context.targetDomain) {
        score += 0.2;
      }
    }

    // Content quality indicators
    const contentLength = this.getContentLength(page);
    if (contentLength > 1000) score += 0.1;
    if (contentLength > 5000) score += 0.1;

    // Title relevance
    if (context.keywords && page.title) {
      const titleLower = page.title.toLowerCase();
      const matchingKeywords = context.keywords.filter(keyword => 
        titleLower.includes(keyword.toLowerCase())
      );
      score += (matchingKeywords.length / context.keywords.length) * 0.2;
    }

    // Structured data bonus
    if (page.structured || this.hasStructuredData(page)) {
      score += 0.15;
    }

    return Math.min(score, 1.0);
  }

  getContentLength(page) {
    if (page.markdown) return page.markdown.length;
    if (page.content) return page.content.length;
    if (page.text) return page.text.length;
    return 0;
  }

  detectContentType(page) {
    if (page.structured) return 'structured';
    if (page.markdown) return 'markdown';
    if (page.html) return 'html';
    return 'text';
  }

  extractStructuredData(page) {
    // Extract structured data from page content
    const data = {};
    
    if (page.structured) {
      data.structured = page.structured;
    }
    
    if (page.metadata) {
      data.metadata = page.metadata;
    }

    return data;
  }

  generateSummary(page) {
    const content = page.markdown || page.content || page.text || '';
    if (content.length <= 200) return content;
    
    // Simple summary generation (first 200 characters)
    return content.substring(0, 200) + '...';
  }

  isValidContent(page) {
    const contentLength = this.getContentLength(page);
    return contentLength > 100 && contentLength <= this.maxContentLength;
  }

  hasStructuredData(page) {
    return !!(page.structured || page.metadata || page.schema);
  }

  applyFilters(page) {
    // Apply content filters (simplified implementation)
    return this.contentFilters.reduce((filtered, filter) => {
      return filter(filtered);
    }, page);
  }

  extractDomain(url) {
    try {
      return new URL(url).hostname;
    } catch {
      return '';
    }
  }
}

/**
 * Firecrawl Research Provider
 * Provides comprehensive web content extraction and analysis capabilities
 */
export class FirecrawlProvider extends BaseResearchProvider {
  constructor(config = {}) {
    super({
      name: 'Firecrawl',
      baseUrl: config.baseUrl || 'https://api.firecrawl.dev',
      ...config
    });

    this.processor = new FirecrawlContentProcessor(config.processing || {});
    
    // Firecrawl-specific configuration
    this.defaultFormats = config.defaultFormats || ['markdown'];
    this.defaultExtractDepth = config.defaultExtractDepth || 'basic';
    this.maxConcurrentBrowsers = config.maxConcurrentBrowsers || 5;
    this.defaultTimeout = config.defaultTimeout || 30000;
    this.onlyMainContent = config.onlyMainContent !== false;
  }

  /**
   * Validate Firecrawl configuration
   * @returns {Promise<boolean>} Configuration validity
   */
  async validateConfig() {
    if (!this.apiKey) {
      throw new Error('Firecrawl API key is required');
    }

    if (!this.baseUrl) {
      throw new Error('Firecrawl base URL is required');
    }

    // Test API key validity with a simple scrape request
    try {
      await this.scrapeUrl('https://example.com', { formats: ['markdown'] });
      return true;
    } catch (error) {
      throw new Error(`Firecrawl API validation failed: ${error.message}`);
    }
  }

  /**
   * Scrape single URL for content
   * @param {string} url - URL to scrape
   * @param {object} options - Scraping options
   * @returns {Promise<object>} Scraped content
   */
  async scrapeUrl(url, options = {}) {
    const scrapeParams = {
      url,
      formats: options.formats || this.defaultFormats,
      onlyMainContent: options.onlyMainContent ?? this.onlyMainContent,
      timeout: options.timeout || this.defaultTimeout
    };

    // Add extraction schema if provided
    if (options.schema) {
      scrapeParams.extract = {
        schema: options.schema,
        prompt: options.extractPrompt || 'Extract structured data based on the schema'
      };
    }

    // Add actions if provided
    if (options.actions) {
      scrapeParams.actions = options.actions;
    }

    try {
      const response = await this.makeRequest('/v1/scrape', scrapeParams);
      
      return {
        url,
        content: response.data,
        metadata: {
          provider: 'Firecrawl',
          timestamp: new Date().toISOString(),
          formats: scrapeParams.formats,
          extractDepth: 'single_page'
        }
      };
    } catch (error) {
      this.emit('scrapeError', { url, error });
      throw new Error(`Firecrawl scrape failed: ${error.message}`);
    }
  }

  /**
   * Crawl website for comprehensive content extraction
   * @param {string} baseUrl - Base URL to crawl
   * @param {object} options - Crawling options
   * @returns {Promise<object>} Crawl results
   */
  async crawlWebsite(baseUrl, options = {}) {
    const crawlParams = {
      url: baseUrl,
      limit: options.limit || 50,
      maxDepth: options.maxDepth || 2,
      allowExternalLinks: options.allowExternalLinks || false,
      formats: options.formats || this.defaultFormats,
      onlyMainContent: options.onlyMainContent ?? this.onlyMainContent
    };

    // Add path filters
    if (options.includePaths) {
      crawlParams.includePaths = options.includePaths;
    }
    
    if (options.excludePaths) {
      crawlParams.excludePaths = options.excludePaths;
    }

    try {
      const response = await this.makeRequest('/v1/crawl', crawlParams);
      
      if (response.id) {
        // Async crawl - monitor status
        return await this.monitorCrawlStatus(response.id, options.pollInterval || 5000);
      } else {
        // Sync crawl - return results immediately
        return this.processCrawlResults(response, baseUrl, options);
      }
    } catch (error) {
      this.emit('crawlError', { baseUrl, error });
      throw new Error(`Firecrawl crawl failed: ${error.message}`);
    }
  }

  /**
   * Monitor async crawl status until completion
   * @param {string} crawlId - Crawl job ID
   * @param {number} pollInterval - Polling interval in milliseconds
   * @returns {Promise<object>} Final crawl results
   */
  async monitorCrawlStatus(crawlId, pollInterval = 5000) {
    let attempts = 0;
    const maxAttempts = 120; // 10 minutes max

    while (attempts < maxAttempts) {
      try {
        const status = await this.makeRequest(`/v1/crawl/${crawlId}/status`);
        
        if (status.status === 'completed') {
          return this.processCrawlResults(status, null, {});
        } else if (status.status === 'failed') {
          throw new Error(`Crawl failed: ${status.error || 'Unknown error'}`);
        }
        
        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, pollInterval));
        attempts++;
        
        this.emit('crawlProgress', { crawlId, status: status.status, attempts });
        
      } catch (error) {
        if (attempts >= maxAttempts - 1) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, pollInterval));
        attempts++;
      }
    }
    
    throw new Error('Crawl monitoring timeout');
  }

  /**
   * Process crawl results with content analysis
   * @param {object} response - Raw crawl response
   * @param {string} baseUrl - Base URL that was crawled
   * @param {object} options - Original crawl options
   * @returns {object} Processed crawl results
   */
  processCrawlResults(response, baseUrl, options) {
    const pages = response.data || [];
    const processedPages = this.processor.processContent(pages, {
      targetDomain: baseUrl ? this.processor.extractDomain(baseUrl) : null,
      keywords: options.keywords || []
    });

    return {
      baseUrl,
      totalPages: pages.length,
      processedPages: processedPages.length,
      pages: processedPages,
      websiteStructure: this.analyzeWebsiteStructure(processedPages),
      contentSummary: this.generateContentSummary(processedPages),
      metadata: {
        provider: 'Firecrawl',
        timestamp: new Date().toISOString(),
        crawlDepth: options.maxDepth || 2,
        extractDepth: this.defaultExtractDepth
      }
    };
  }

  /**
   * Extract structured data from multiple URLs
   * @param {Array} urls - URLs to extract from
   * @param {object} schema - Extraction schema
   * @param {object} options - Extraction options
   * @returns {Promise<object>} Extracted structured data
   */
  async extractStructuredData(urls, schema, options = {}) {
    const extractParams = {
      urls: Array.isArray(urls) ? urls : [urls],
      schema,
      prompt: options.prompt || 'Extract structured data based on the provided schema'
    };

    try {
      const response = await this.makeRequest('/v1/extract', extractParams);
      
      return {
        urls: extractParams.urls,
        schema,
        extractedData: response.data || [],
        metadata: {
          provider: 'Firecrawl',
          timestamp: new Date().toISOString(),
          extractionType: 'structured'
        }
      };
    } catch (error) {
      this.emit('extractError', { urls, schema, error });
      throw new Error(`Firecrawl extraction failed: ${error.message}`);
    }
  }

  /**
   * Perform bulk content processing
   * @param {Array} urls - URLs to process
   * @param {object} options - Processing options
   * @returns {Promise<object>} Bulk processing results
   */
  async bulkContentProcessing(urls, options = {}) {
    const batchSize = options.batchSize || 10;
    const batches = this.createBatches(urls, batchSize);
    const results = [];

    for (const batch of batches) {
      try {
        const batchResults = await Promise.allSettled(
          batch.map(url => this.scrapeUrl(url, options))
        );

        const successfulResults = batchResults
          .filter(result => result.status === 'fulfilled')
          .map(result => result.value);

        results.push(...successfulResults);
        
        // Rate limiting delay between batches
        if (batches.indexOf(batch) < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        this.emit('bulkProcessingError', { batch, error });
      }
    }

    return {
      totalUrls: urls.length,
      processedUrls: results.length,
      results: this.processor.processContent(
        results.map(r => r.content).flat(),
        { keywords: options.keywords || [] }
      ),
      metadata: {
        provider: 'Firecrawl',
        timestamp: new Date().toISOString(),
        processingType: 'bulk',
        batchSize
      }
    };
  }

  /**
   * Analyze website structure from crawled pages
   * @param {Array} pages - Crawled pages
   * @returns {object} Website structure analysis
   */
  analyzeWebsiteStructure(pages) {
    const domains = new Set();
    const paths = new Set();
    const contentTypes = new Map();

    pages.forEach(page => {
      if (page.url) {
        try {
          const url = new URL(page.url);
          domains.add(url.hostname);
          paths.add(url.pathname);
        } catch (e) {
          // Invalid URL, skip
        }
      }

      const contentType = page.contentType || 'unknown';
      contentTypes.set(contentType, (contentTypes.get(contentType) || 0) + 1);
    });

    return {
      totalPages: pages.length,
      uniqueDomains: domains.size,
      uniquePaths: paths.size,
      contentTypeDistribution: Object.fromEntries(contentTypes),
      averageContentLength: pages.reduce((sum, page) => 
        sum + (page.contentLength || 0), 0) / pages.length
    };
  }

  /**
   * Generate content summary from processed pages
   * @param {Array} pages - Processed pages
   * @returns {object} Content summary
   */
  generateContentSummary(pages) {
    const totalContent = pages.reduce((sum, page) => 
      sum + (page.contentLength || 0), 0);
    
    const topPages = pages
      .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
      .slice(0, 5);

    return {
      totalContentLength: totalContent,
      averageContentLength: totalContent / pages.length,
      topRelevantPages: topPages.map(page => ({
        url: page.url,
        title: page.title,
        relevanceScore: page.relevanceScore,
        summary: page.summary
      })),
      contentTypes: this.getContentTypeDistribution(pages)
    };
  }

  /**
   * Get content type distribution
   * @param {Array} pages - Pages to analyze
   * @returns {object} Content type distribution
   */
  getContentTypeDistribution(pages) {
    const distribution = {};
    pages.forEach(page => {
      const type = page.contentType || 'unknown';
      distribution[type] = (distribution[type] || 0) + 1;
    });
    return distribution;
  }

  /**
   * Create batches from array of URLs
   * @param {Array} urls - URLs to batch
   * @param {number} batchSize - Size of each batch
   * @returns {Array} Array of batches
   */
  createBatches(urls, batchSize) {
    const batches = [];
    for (let i = 0; i < urls.length; i += batchSize) {
      batches.push(urls.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * Bulk scrape multiple URLs with concurrency control
   * @param {Array} urls - URLs to scrape
   * @param {object} options - Scraping options
   * @returns {Promise<Array>} Scraping results
   */
  async bulkScrape(urls, options = {}) {
    const concurrency = options.concurrency || 3;
    const results = [];

    // Process URLs in batches to respect concurrency limits
    for (let i = 0; i < urls.length; i += concurrency) {
      const batch = urls.slice(i, i + concurrency);
      const batchPromises = batch.map(url =>
        this.scrapeUrl(url, options).catch(error => ({
          url,
          error: error.message,
          success: false
        }))
      );

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * Search method implementation for base class compatibility
   * @param {string} query - Search query (treated as URL)
   * @param {object} options - Search options
   * @returns {Promise<object>} Search results
   */
  async search(query, options = {}) {
    // Treat query as URL for Firecrawl
    if (this.isValidUrl(query)) {
      const result = await this.scrapeUrl(query, options);
      return {
        query,
        results: [result],
        metadata: {
          type: 'url_scrape',
          provider: 'Firecrawl',
          timestamp: new Date().toISOString()
        }
      };
    } else {
      throw new Error('Firecrawl requires valid URLs for search');
    }
  }

  /**
   * Check if string is a valid URL
   * @param {string} string - String to check
   * @returns {boolean} Whether string is valid URL
   */
  isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch {
      return false;
    }
  }
}

export default FirecrawlProvider;
