/**
 * Placeholder Logger Utilities
 * Provides a basic log function.
 */

/**
 * Basic log function.
 * @param {string} level - Log level (e.g., 'info', 'warn', 'error').
 * @param {string} message - The message to log.
 */
export function log(level, message) {
  const timestamp = new Date().toISOString();
  switch (level.toLowerCase()) {
    case 'info':
      console.info(`[${timestamp}] [INFO] ${message}`);
      break;
    case 'warn':
      console.warn(`[${timestamp}] [WARN] ${message}`);
      break;
    case 'error':
      console.error(`[${timestamp}] [ERROR] ${message}`);
      break;
    default:
      console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
  }
}

export default {
  log,
};