[{"id": "framework_selection_1749946130455", "decisionId": "framework_selection", "decisionTitle": "Choose Your Website Building Foundation", "context": "framework_selection", "choiceId": "react", "choiceTitle": "React", "rationale": "This option best fits our project needs and timeline.", "timestamp": "2025-06-15T00:08:50.456Z", "businessImpact": {"level": "high", "description": "Excellent for complex, interactive applications"}, "wasRecommended": true, "confidenceScore": 100}, {"id": "custom_custom_state_management_1749946816996_1749946817019", "decisionId": "custom_custom_state_management_1749946816996", "decisionTitle": "Choose Your State Management Solution", "context": "custom_state_management", "choiceId": "option_0", "choiceTitle": "Redux", "rationale": "We chose Redux because our application has complex state requirements and the team has experience with it.", "timestamp": "2025-06-15T00:20:17.019Z", "businessImpact": {"level": "high", "description": "Excellent for complex applications with many state changes"}, "wasRecommended": true, "confidenceScore": 100}]