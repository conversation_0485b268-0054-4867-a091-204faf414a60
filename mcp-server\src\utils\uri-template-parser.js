/**
 * URI Template <PERSON>rser (RFC 6570)
 * Handles MCP Resource URI template parsing and expansion
 */

/**
 * URI Template Parser implementing RFC 6570 for MCP Resources
 */
export class URITemplateParser {
  /**
   * Parse and expand a URI template with given parameters
   * @param {string} template - URI template (e.g., "guidant://project/{projectId}/state")
   * @param {object} params - Parameters to substitute
   * @returns {string} Expanded URI
   */
  static parse(template, params = {}) {
    if (!template || typeof template !== 'string') {
      throw new Error('URI template must be a non-empty string');
    }

    if (!this.validate(template)) {
      throw new Error(`Invalid URI template format: ${template}`);
    }

    // Handle simple variable substitution {var}
    return template.replace(/\{([^}]+)\}/g, (match, expression) => {
      const { varName, modifier } = this.parseExpression(expression);
      
      if (params.hasOwnProperty(varName)) {
        const value = params[varName];
        return this.formatValue(value, modifier);
      }
      
      // Optional parameters (marked with ?) can be omitted
      if (modifier === '?') {
        return '';
      }
      
      // Required parameters must be provided
      throw new Error(`Missing required parameter: ${varName}`);
    });
  }

  /**
   * Parse a template expression to extract variable name and modifier
   * @param {string} expression - Expression inside {}
   * @returns {object} Parsed expression
   */
  static parseExpression(expression) {
    const modifierMatch = expression.match(/^([^?*+]+)([?*+]?)$/);
    
    if (!modifierMatch) {
      throw new Error(`Invalid expression: ${expression}`);
    }

    return {
      varName: modifierMatch[1],
      modifier: modifierMatch[2] || null
    };
  }

  /**
   * Format a value based on its modifier
   * @param {any} value - Value to format
   * @param {string|null} modifier - Modifier (?, *, +, or null)
   * @returns {string} Formatted value
   */
  static formatValue(value, modifier) {
    if (value === null || value === undefined) {
      return '';
    }

    const stringValue = String(value);

    switch (modifier) {
      case '?':
        // Optional parameter - include if present
        return stringValue;
      case '*':
        // Explode modifier for arrays/objects (simplified)
        if (Array.isArray(value)) {
          return value.join(',');
        }
        return stringValue;
      case '+':
        // Reserved character encoding
        return encodeURIComponent(stringValue);
      default:
        // Standard encoding
        return encodeURIComponent(stringValue);
    }
  }

  /**
   * Validate URI template syntax
   * @param {string} template - Template to validate
   * @returns {boolean} True if valid
   */
  static validate(template) {
    // Basic RFC 6570 validation
    const rfc6570Pattern = /^[^{}]*(\{[^{}]+\}[^{}]*)*$/;
    
    if (!rfc6570Pattern.test(template)) {
      return false;
    }

    // Validate each expression
    const expressions = template.match(/\{([^}]+)\}/g) || [];
    
    for (const expr of expressions) {
      const content = expr.slice(1, -1); // Remove { }
      if (!this.validateExpression(content)) {
        return false;
      }
    }

    return true;
  }

  /**
   * Validate a single expression
   * @param {string} expression - Expression to validate
   * @returns {boolean} True if valid
   */
  static validateExpression(expression) {
    // Valid variable name pattern: alphanumeric, underscore, hyphen
    const varPattern = /^[a-zA-Z0-9_-]+[?*+]?$/;
    return varPattern.test(expression);
  }

  /**
   * Extract parameter names from a URI template
   * @param {string} template - URI template
   * @returns {Array<string>} Array of parameter names
   */
  static extractParameters(template) {
    const expressions = template.match(/\{([^}]+)\}/g) || [];
    
    return expressions.map(expr => {
      const content = expr.slice(1, -1);
      const { varName } = this.parseExpression(content);
      return varName;
    });
  }

  /**
   * Check if a URI matches a template pattern
   * @param {string} uri - URI to check
   * @param {string} template - Template pattern
   * @returns {object|null} Extracted parameters or null if no match
   */
  static match(uri, template) {
    try {
      // Convert template to regex pattern
      const regexPattern = template.replace(/\{([^}]+)\}/g, (match, expression) => {
        const { modifier } = this.parseExpression(expression);
        
        // Create capture group based on modifier
        switch (modifier) {
          case '?':
            return '([^/]*?)'; // Optional, non-greedy
          default:
            return '([^/]+)'; // Required
        }
      });

      const regex = new RegExp(`^${regexPattern}$`);
      const match = uri.match(regex);

      if (!match) {
        return null;
      }

      // Extract parameter names and values
      const paramNames = this.extractParameters(template);
      const params = {};

      for (let i = 0; i < paramNames.length; i++) {
        const value = match[i + 1];
        if (value !== undefined && value !== '') {
          params[paramNames[i]] = decodeURIComponent(value);
        }
      }

      return params;
    } catch (error) {
      return null;
    }
  }

  /**
   * Create a URI template matcher for efficient matching
   * @param {string} template - Template to create matcher for
   * @returns {object} Matcher object with match method
   */
  static createMatcher(template) {
    const paramNames = this.extractParameters(template);
    const regexPattern = template.replace(/\{([^}]+)\}/g, (match, expression) => {
      const { modifier } = this.parseExpression(expression);
      
      switch (modifier) {
        case '?':
          return '([^/]*?)';
        default:
          return '([^/]+)';
      }
    });

    const regex = new RegExp(`^${regexPattern}$`);

    return {
      template,
      paramNames,
      regex,
      match(uri) {
        const match = uri.match(regex);
        if (!match) return null;

        const params = {};
        for (let i = 0; i < paramNames.length; i++) {
          const value = match[i + 1];
          if (value !== undefined && value !== '') {
            params[paramNames[i]] = decodeURIComponent(value);
          }
        }
        return params;
      }
    };
  }
}

/**
 * MCP Resource URI utilities
 */
export class MCPResourceURI {
  /**
   * Create a Guidant resource URI
   * @param {string} type - Resource type (project, deliverable, analytics, workflow)
   * @param {string} id - Resource ID
   * @param {string} [subpath] - Optional subpath
   * @returns {string} Guidant resource URI
   */
  static create(type, id, subpath = '') {
    const base = `guidant://${type}/${encodeURIComponent(id)}`;
    return subpath ? `${base}/${subpath}` : base;
  }

  /**
   * Parse a Guidant resource URI
   * @param {string} uri - URI to parse
   * @returns {object|null} Parsed URI components or null if invalid
   */
  static parse(uri) {
    const guidantPattern = /^guidant:\/\/([^/]+)\/([^/]+)(?:\/(.*))?$/;
    const match = uri.match(guidantPattern);

    if (!match) {
      return null;
    }

    return {
      scheme: 'guidant',
      type: match[1],
      id: decodeURIComponent(match[2]),
      subpath: match[3] || '',
      full: uri
    };
  }

  /**
   * Validate a Guidant resource URI
   * @param {string} uri - URI to validate
   * @returns {boolean} True if valid
   */
  static validate(uri) {
    return this.parse(uri) !== null;
  }
}

export default URITemplateParser;
