/**
 * UI Best Practices Intelligence
 * Research-driven UI best practices intelligence for enhanced visual generation
 */

import { VisualResearchInterface } from './visual-research-interface.js';
import { VisualIntelligenceCache } from './visual-intelligence-cache.js';

/**
 * UI Best Practices Intelligence Engine
 * Enhanced with advanced pattern recognition, confidence scoring, and actionable insights
 */
export class UIBestPracticesIntelligence {
  constructor(config = {}) {
    this.researchInterface = new VisualResearchInterface(config.research || {});
    this.config = {
      enableResearch: config.enableResearch !== false,
      cacheResults: config.cacheResults !== false,
      intelligenceLevel: config.intelligenceLevel || 'enhanced', // basic, enhanced, comprehensive
      minConfidenceThreshold: config.minConfidenceThreshold || 0.6,
      maxRecommendations: config.maxRecommendations || 8,
      enablePatternRecognition: config.enablePatternRecognition !== false,
      enableConsensusBuilding: config.enableConsensusBuilding !== false,
      ...config
    };

    // Initialize enhanced visual intelligence cache
    this.visualCache = new VisualIntelligenceCache({
      maxCacheSize: config.maxCacheSize || 500,
      enableQualityScoring: config.enableQualityScoring !== false,
      enableIntelligentInvalidation: config.enableIntelligentInvalidation !== false,
      enableAPIOptimization: config.enableAPIOptimization !== false,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring !== false
    });

    // Legacy cache maps for backward compatibility (deprecated)
    this.practicesCache = new Map();
    this.patternCache = new Map();
    this.consensusCache = new Map();
    this.componentIntelligence = this.initializeComponentIntelligence();
    this.researchMetrics = {
      totalQueries: 0,
      cacheHits: 0,
      patternMatches: 0,
      consensusBuilt: 0
    };
  }

  /**
   * Enhance component with UI best practices using advanced intelligence
   */
  async enhanceComponentWithBestPractices(component, context = {}) {
    if (!this.config.enableResearch) {
      return this.applyBasicBestPractices(component);
    }

    try {
      this.researchMetrics.totalQueries++;

      // Use enhanced visual intelligence cache
      const cacheKey = this.visualCache.generateCacheKey(
        'ui_best_practices',
        component.type,
        context.framework || 'react',
        context
      );

      // Try to get from enhanced cache first
      const cached = await this.visualCache.get(cacheKey, context);
      if (cached) {
        this.researchMetrics.cacheHits++;
        return cached;
      }

      // Parallel research for better performance
      const [practices, accessibility, patterns] = await Promise.all([
        this.researchInterface.researchUIBestPractices(component.type, context),
        this.researchInterface.researchAccessibilityGuidelines(component.type, context),
        this.config.enablePatternRecognition ? this.researchDesignPatterns(component.type, context) : null
      ]);

      // Build consensus from multiple research sources
      const consensus = this.config.enableConsensusBuilding ?
        await this.buildResearchConsensus(practices, accessibility, patterns, component.type) : null;

      // Apply advanced intelligence to component
      const enhancedComponent = await this.applyAdvancedIntelligence(
        component,
        practices,
        accessibility,
        patterns,
        consensus,
        context
      );

      // Cache the enhanced result using visual intelligence cache
      await this.visualCache.set(cacheKey, enhancedComponent, {
        requestType: component.type,
        provider: 'ui_best_practices',
        timestamp: Date.now()
      });

      return enhancedComponent;
    } catch (error) {
      console.warn('Research-enhanced practices failed, using fallback:', error.message);
      return this.applyBasicBestPractices(component);
    }
  }

  /**
   * Apply advanced intelligence enhancements to component
   */
  async applyAdvancedIntelligence(component, practices, accessibility, patterns, consensus, context) {
    const enhanced = { ...component };

    // Extract and score applicable practices
    enhanced.bestPractices = await this.extractScoredPractices(practices, component.type, consensus);

    // Apply accessibility enhancements with confidence scoring
    enhanced.accessibility = this.applyEnhancedAccessibility(accessibility, component, consensus);

    // Apply pattern-based intelligence
    enhanced.patterns = patterns ? this.applyPatternIntelligence(patterns, component.type) : null;

    // Apply component-specific intelligence with consensus
    enhanced.intelligence = await this.applyAdvancedComponentIntelligence(
      component, practices, accessibility, patterns, consensus, context
    );

    // Generate actionable insights
    enhanced.actionableInsights = this.generateActionableInsights(
      enhanced.bestPractices, enhanced.accessibility, enhanced.patterns, component.type
    );

    // Update ASCII representation with advanced enhancements
    enhanced.asciiRepresentation = this.enhanceASCIIWithAdvancedIntelligence(
      component.asciiRepresentation || '',
      enhanced
    );

    // Calculate overall intelligence confidence
    const intelligenceConfidence = this.calculateIntelligenceConfidence(
      practices, accessibility, patterns, consensus
    );

    // Add comprehensive metadata
    enhanced.metadata = {
      ...enhanced.metadata,
      intelligenceApplied: true,
      intelligenceLevel: this.config.intelligenceLevel,
      practicesCount: enhanced.bestPractices.length,
      accessibilityLevel: enhanced.accessibility?.guidelines?.length || 0,
      patternsFound: enhanced.patterns?.length || 0,
      actionableInsightsCount: enhanced.actionableInsights.length,
      intelligenceConfidence,
      researchConfidence: practices.confidence || 0,
      consensusStrength: consensus?.strength || 0,
      enhancedAt: new Date().toISOString(),
      researchSources: this.getResearchSourcesSummary(practices, accessibility, patterns)
    };

    return enhanced;
  }

  /**
   * Extract and score applicable practices with advanced analysis
   */
  async extractScoredPractices(practices, componentType, consensus) {
    if (!practices.bestPractices) return [];

    const applicable = [];
    const componentRules = this.componentIntelligence[componentType] || {};

    for (const practice of practices.bestPractices) {
      if (this.isPracticeApplicable(practice, componentType, componentRules)) {
        const score = await this.calculateAdvancedPracticeScore(
          practice, componentType, consensus, practices.confidence
        );

        if (score.total >= this.config.minConfidenceThreshold) {
          applicable.push({
            practice,
            priority: score.priority,
            confidence: score.confidence,
            applicability: score.applicability,
            totalScore: score.total,
            implementation: this.suggestDetailedImplementation(practice, componentType, score),
            evidence: this.extractPracticeEvidence(practice, practices),
            consensusSupport: consensus ? this.getConsensusSupport(practice, consensus) : null
          });
        }
      }
    }

    return applicable
      .sort((a, b) => b.totalScore - a.totalScore)
      .slice(0, this.config.maxRecommendations);
  }

  /**
   * Apply enhanced accessibility enhancements with confidence scoring
   */
  applyEnhancedAccessibility(accessibility, component, consensus) {
    const enhancements = {
      ariaAttributes: [],
      keyboardNavigation: [],
      screenReaderSupport: [],
      visualIndicators: [],
      guidelines: [],
      confidence: 0,
      priority: 'medium'
    };

    // Add ARIA attributes based on component type and research
    enhancements.ariaAttributes = this.generateAriaAttributes(component.type, accessibility);

    // Add keyboard navigation support
    enhancements.keyboardNavigation = this.generateKeyboardNavigation(component.type);

    // Add screen reader support
    enhancements.screenReaderSupport = this.generateScreenReaderSupport(component.type);

    // Add visual accessibility indicators
    enhancements.visualIndicators = this.generateVisualIndicators(component.type);

    // Add research-based guidelines
    if (accessibility.guidelines) {
      enhancements.guidelines = accessibility.guidelines.slice(0, 5);
    }

    // Calculate accessibility confidence
    enhancements.confidence = this.calculateAccessibilityConfidence(accessibility, consensus);

    // Determine priority based on component type and guidelines
    enhancements.priority = this.determineAccessibilityPriority(component.type, accessibility);

    return enhancements;
  }

  /**
   * Apply advanced component-specific intelligence
   */
  async applyAdvancedComponentIntelligence(component, practices, accessibility, patterns, consensus, context) {
    const intelligence = {
      recommendations: [],
      optimizations: [],
      warnings: [],
      enhancements: [],
      confidence: 0,
      researchBased: true
    };

    // Get base component intelligence
    switch (component.type) {
      case 'form':
        intelligence.recommendations.push(...this.getFormIntelligence(component, practices));
        break;
      case 'table':
        intelligence.recommendations.push(...this.getTableIntelligence(component, practices));
        break;
      case 'navigation':
        intelligence.recommendations.push(...this.getNavigationIntelligence(component, practices));
        break;
      case 'search':
        intelligence.recommendations.push(...this.getSearchIntelligence(component, practices));
        break;
      case 'dashboard':
        intelligence.recommendations.push(...this.getDashboardIntelligence(component, practices));
        break;
      default:
        intelligence.recommendations.push(...this.getGenericIntelligence(component, practices));
    }

    // Add research-enhanced optimizations
    if (practices && practices.bestPractices) {
      intelligence.optimizations.push(...this.extractOptimizations(practices, component.type));
    }

    // Add pattern-based enhancements
    if (patterns && patterns.patterns) {
      intelligence.enhancements.push(...this.extractPatternEnhancements(patterns, component.type));
    }

    // Add context-specific intelligence
    if (context.industry) {
      intelligence.recommendations.push(...this.getIndustrySpecificIntelligence(
        component.type,
        context.industry
      ));
    }

    // Add warnings based on research
    intelligence.warnings.push(...this.generateIntelligenceWarnings(component, practices, accessibility));

    // Calculate intelligence confidence
    intelligence.confidence = this.calculateComponentIntelligenceConfidence(
      practices, accessibility, patterns, consensus
    );

    return intelligence;
  }

  /**
   * Enhance ASCII representation with intelligence
   */
  enhanceASCIIWithIntelligence(originalASCII, enhancedComponent) {
    let enhanced = originalASCII;

    // Add accessibility indicators
    if (enhancedComponent.accessibility?.ariaAttributes?.length > 0) {
      enhanced = this.addAccessibilityIndicators(enhanced, enhancedComponent.accessibility);
    }

    // Add best practice indicators
    if (enhancedComponent.bestPractices?.length > 0) {
      enhanced = this.addBestPracticeIndicators(enhanced, enhancedComponent.bestPractices);
    }

    // Add intelligence annotations
    enhanced = this.addIntelligenceAnnotations(enhanced, enhancedComponent.intelligence);

    return enhanced;
  }

  // Component-specific intelligence methods
  getFormIntelligence(component, practices) {
    return [
      '✓ Use clear, descriptive labels above form fields',
      '✓ Implement real-time validation with helpful error messages',
      '✓ Group related fields with fieldsets and legends',
      '✓ Provide clear indication of required vs optional fields',
      '✓ Use appropriate input types (email, tel, date, etc.)',
      '✓ Include progress indicators for multi-step forms',
      '✓ Implement proper tab order for keyboard navigation',
      '✓ Add confirmation for destructive actions'
    ];
  }

  getTableIntelligence(component, practices) {
    return [
      '✓ Use zebra striping for better row readability',
      '✓ Implement sortable column headers with clear indicators',
      '✓ Add filtering and search capabilities',
      '✓ Include pagination for large datasets (>50 rows)',
      '✓ Provide row selection with clear visual feedback',
      '✓ Use consistent alignment (numbers right, text left)',
      '✓ Include loading states and empty state messaging',
      '✓ Implement responsive table behavior for mobile'
    ];
  }

  getNavigationIntelligence(component, practices) {
    return [
      '✓ Maintain consistent navigation across all pages',
      '✓ Highlight current page/section clearly',
      '✓ Use breadcrumbs for deep navigation hierarchies',
      '✓ Implement responsive navigation (hamburger menu)',
      '✓ Provide skip links for accessibility',
      '✓ Use descriptive link text (avoid "click here")',
      '✓ Group related navigation items logically',
      '✓ Include search functionality in main navigation'
    ];
  }

  getSearchIntelligence(component, practices) {
    return [
      '✓ Provide autocomplete/suggestions as user types',
      '✓ Include advanced filtering options',
      '✓ Show search results count and sorting options',
      '✓ Implement search history and saved searches',
      '✓ Use clear placeholder text with examples',
      '✓ Provide "no results" state with helpful suggestions',
      '✓ Include search within results functionality',
      '✓ Support keyboard shortcuts (Ctrl+K, /)'
    ];
  }

  getDashboardIntelligence(component, practices) {
    return [
      '✓ Prioritize most important metrics prominently',
      '✓ Use consistent chart types and color schemes',
      '✓ Provide customizable widget arrangements',
      '✓ Include time period selectors and filters',
      '✓ Implement real-time data updates where appropriate',
      '✓ Add export functionality for reports',
      '✓ Use progressive disclosure for detailed views',
      '✓ Include contextual help and tooltips'
    ];
  }

  getGenericIntelligence(component, practices) {
    return [
      '✓ Follow consistent design patterns',
      '✓ Provide clear visual hierarchy',
      '✓ Use appropriate spacing and typography',
      '✓ Implement proper loading and error states',
      '✓ Ensure responsive design across devices',
      '✓ Add helpful micro-interactions and feedback'
    ];
  }

  getIndustrySpecificIntelligence(componentType, industry) {
    const industryRules = {
      healthcare: [
        '⚕️ Ensure HIPAA compliance for data handling',
        '⚕️ Use clear medical terminology with explanations',
        '⚕️ Implement strong authentication and audit trails'
      ],
      finance: [
        '💰 Follow financial regulations (PCI DSS, SOX)',
        '💰 Use secure data transmission and storage',
        '💰 Provide clear transaction confirmations'
      ],
      ecommerce: [
        '🛒 Optimize for conversion and user trust',
        '🛒 Implement clear product information display',
        '🛒 Provide secure checkout process'
      ],
      education: [
        '📚 Design for accessibility and diverse learners',
        '📚 Use progressive disclosure for complex content',
        '📚 Implement clear learning progress indicators'
      ]
    };

    return industryRules[industry] || [];
  }

  // Accessibility enhancement methods
  generateAriaAttributes(componentType, accessibility) {
    const ariaMap = {
      form: ['aria-label', 'aria-describedby', 'aria-required', 'aria-invalid'],
      table: ['aria-label', 'aria-sort', 'aria-rowcount', 'aria-colcount'],
      navigation: ['aria-label', 'aria-current', 'aria-expanded'],
      search: ['aria-label', 'aria-autocomplete', 'aria-expanded'],
      button: ['aria-label', 'aria-pressed', 'aria-disabled'],
      dashboard: ['aria-label', 'aria-live', 'aria-atomic']
    };

    return ariaMap[componentType] || ['aria-label'];
  }

  generateKeyboardNavigation(componentType) {
    const keyboardMap = {
      form: ['Tab/Shift+Tab navigation', 'Enter to submit', 'Escape to cancel'],
      table: ['Arrow keys for cell navigation', 'Space to select', 'Enter to activate'],
      navigation: ['Tab navigation', 'Enter/Space to activate', 'Arrow keys for menus'],
      search: ['Tab to focus', 'Enter to search', 'Arrow keys for suggestions'],
      button: ['Tab to focus', 'Enter/Space to activate'],
      dashboard: ['Tab navigation', 'Arrow keys for widgets', 'Enter to expand']
    };

    return keyboardMap[componentType] || ['Tab navigation', 'Enter to activate'];
  }

  generateScreenReaderSupport(componentType) {
    const screenReaderMap = {
      form: ['Descriptive labels', 'Error announcements', 'Progress updates'],
      table: ['Column headers', 'Row/column counts', 'Sort status'],
      navigation: ['Current page indication', 'Menu structure', 'Link purposes'],
      search: ['Results count', 'Suggestion announcements', 'Filter status'],
      button: ['Action descriptions', 'State changes', 'Confirmation messages'],
      dashboard: ['Widget descriptions', 'Data updates', 'Status changes']
    };

    return screenReaderMap[componentType] || ['Descriptive content', 'Status updates'];
  }

  generateVisualIndicators(componentType) {
    return [
      'High contrast focus indicators',
      'Clear visual hierarchy',
      'Consistent color usage',
      'Adequate spacing and sizing',
      'Error and success state indicators'
    ];
  }

  // Enhancement methods
  addAccessibilityIndicators(ascii, accessibility) {
    // Add accessibility annotations to ASCII
    const lines = ascii.split('\n');
    if (lines.length > 0) {
      lines[0] += ' ♿'; // Add accessibility indicator
    }
    return lines.join('\n');
  }

  addBestPracticeIndicators(ascii, bestPractices) {
    // Add best practice annotations
    const lines = ascii.split('\n');
    if (lines.length > 0 && bestPractices.length > 0) {
      lines[0] += ' ✨'; // Add best practices indicator
    }
    return lines.join('\n');
  }

  addIntelligenceAnnotations(ascii, intelligence) {
    // Add intelligence annotations
    if (intelligence.recommendations.length > 0) {
      ascii += '\n\n' + '💡 Intelligence Recommendations:';
      for (const rec of intelligence.recommendations.slice(0, 3)) {
        ascii += '\n   ' + rec;
      }
    }
    return ascii;
  }

  // Utility methods
  isPracticeApplicable(practice, componentType, rules) {
    const practiceText = practice.toLowerCase();
    const componentKeywords = rules.keywords || [componentType];
    
    return componentKeywords.some(keyword => 
      practiceText.includes(keyword.toLowerCase())
    );
  }

  calculatePracticePriority(practice, componentType) {
    const practiceText = practice.toLowerCase();
    let priority = 1;

    // Higher priority for accessibility and usability
    if (practiceText.includes('accessibility') || practiceText.includes('aria')) priority += 3;
    if (practiceText.includes('usability') || practiceText.includes('user')) priority += 2;
    if (practiceText.includes('performance') || practiceText.includes('fast')) priority += 2;
    if (practiceText.includes('security') || practiceText.includes('safe')) priority += 3;

    return priority;
  }

  suggestImplementation(practice, componentType) {
    // Provide implementation suggestions based on practice and component type
    return `Apply "${practice}" to ${componentType} component`;
  }

  applyBasicBestPractices(component) {
    // Fallback when research is disabled
    const basic = { ...component };
    basic.bestPractices = this.getBasicBestPractices(component.type);
    basic.accessibility = this.getBasicAccessibility(component.type);
    basic.metadata = {
      ...basic.metadata,
      intelligenceApplied: false,
      fallbackUsed: true
    };
    return basic;
  }

  getBasicBestPractices(componentType) {
    const basicMap = {
      form: [{ practice: 'Use clear labels', priority: 3 }],
      table: [{ practice: 'Include column headers', priority: 3 }],
      navigation: [{ practice: 'Maintain consistency', priority: 3 }],
      search: [{ practice: 'Provide clear placeholder', priority: 2 }],
      button: [{ practice: 'Use descriptive text', priority: 2 }]
    };

    return basicMap[componentType] || [{ practice: 'Follow design standards', priority: 1 }];
  }

  getBasicAccessibility(componentType) {
    return {
      ariaAttributes: this.generateAriaAttributes(componentType, {}),
      keyboardNavigation: this.generateKeyboardNavigation(componentType),
      screenReaderSupport: this.generateScreenReaderSupport(componentType)
    };
  }

  initializeComponentIntelligence() {
    return {
      form: {
        keywords: ['form', 'input', 'field', 'validation', 'submit'],
        priority: ['accessibility', 'validation', 'usability']
      },
      table: {
        keywords: ['table', 'data', 'grid', 'sort', 'filter'],
        priority: ['performance', 'usability', 'accessibility']
      },
      navigation: {
        keywords: ['navigation', 'menu', 'nav', 'link', 'breadcrumb'],
        priority: ['consistency', 'accessibility', 'usability']
      },
      search: {
        keywords: ['search', 'filter', 'find', 'query'],
        priority: ['performance', 'usability', 'relevance']
      },
      button: {
        keywords: ['button', 'action', 'click', 'submit'],
        priority: ['accessibility', 'clarity', 'feedback']
      }
    };
  }

  // ===== ADVANCED INTELLIGENCE METHODS =====

  /**
   * Research design patterns for component type
   */
  async researchDesignPatterns(componentType, context) {
    const cacheKey = `patterns_${componentType}_${JSON.stringify(context)}`;
    if (this.patternCache.has(cacheKey)) {
      const cached = this.patternCache.get(cacheKey);
      if (this.isCacheValid(cached)) {
        this.researchMetrics.patternMatches++;
        return cached.data;
      }
    }

    try {
      const patterns = await this.researchInterface.researchComponentPatterns(
        componentType,
        context.framework || 'react',
        context
      );

      if (this.config.cacheResults) {
        this.patternCache.set(cacheKey, {
          data: patterns,
          timestamp: Date.now()
        });
      }

      return patterns;
    } catch (error) {
      console.warn('Pattern research failed:', error.message);
      return null;
    }
  }

  /**
   * Build consensus from multiple research sources
   */
  async buildResearchConsensus(practices, accessibility, patterns, componentType) {
    const consensusKey = `consensus_${componentType}_${Date.now()}`;

    try {
      this.researchMetrics.consensusBuilt++;

      const consensus = {
        practices: this.buildPracticesConsensus(practices),
        accessibility: this.buildAccessibilityConsensus(accessibility),
        patterns: patterns ? this.buildPatternsConsensus(patterns) : null,
        strength: 0,
        confidence: 0,
        sources: []
      };

      // Calculate consensus strength
      consensus.strength = this.calculateConsensusStrength(consensus);
      consensus.confidence = this.calculateConsensusConfidence(consensus);
      consensus.sources = this.extractConsensusSources(practices, accessibility, patterns);

      if (this.config.cacheResults) {
        this.consensusCache.set(consensusKey, {
          data: consensus,
          timestamp: Date.now()
        });
      }

      return consensus;
    } catch (error) {
      console.warn('Consensus building failed:', error.message);
      return null;
    }
  }

  /**
   * Calculate advanced practice score with multiple factors
   */
  async calculateAdvancedPracticeScore(practice, componentType, consensus, baseConfidence) {
    const practiceText = practice.toLowerCase();

    // Base priority score
    let priority = this.calculatePracticePriority(practice, componentType);

    // Confidence score based on research quality
    let confidence = baseConfidence || 0.5;

    // Applicability score based on component type
    let applicability = this.calculateApplicabilityScore(practice, componentType);

    // Consensus boost if available
    if (consensus) {
      const consensusBoost = this.getConsensusBoost(practice, consensus);
      confidence += consensusBoost * 0.2;
      priority += consensusBoost;
    }

    // Pattern matching boost
    if (this.config.enablePatternRecognition) {
      const patternBoost = this.getPatternMatchingBoost(practice, componentType);
      applicability += patternBoost * 0.15;
    }

    // Calculate total score
    const total = (priority * 0.4) + (confidence * 0.35) + (applicability * 0.25);

    return {
      priority: Math.min(priority, 10),
      confidence: Math.min(confidence, 1),
      applicability: Math.min(applicability, 1),
      total: Math.min(total, 10)
    };
  }

  /**
   * Generate actionable insights from research data
   */
  generateActionableInsights(bestPractices, accessibility, patterns, componentType) {
    const insights = [];

    // High-priority practice insights
    const highPriorityPractices = bestPractices
      .filter(p => p.totalScore >= 7)
      .slice(0, 3);

    for (const practice of highPriorityPractices) {
      insights.push({
        type: 'best_practice',
        priority: 'high',
        title: `Implement ${practice.practice}`,
        description: practice.implementation,
        confidence: practice.confidence,
        evidence: practice.evidence,
        actionSteps: this.generateActionSteps(practice, componentType)
      });
    }

    // Critical accessibility insights
    if (accessibility.guidelines) {
      const criticalA11y = accessibility.guidelines.slice(0, 2);
      for (const guideline of criticalA11y) {
        insights.push({
          type: 'accessibility',
          priority: 'critical',
          title: `Accessibility: ${guideline}`,
          description: `Ensure ${componentType} component follows accessibility guidelines`,
          confidence: 0.9,
          actionSteps: this.generateA11yActionSteps(guideline, componentType)
        });
      }
    }

    // Pattern-based insights
    if (patterns && patterns.patterns) {
      const topPattern = patterns.patterns[0];
      if (topPattern) {
        insights.push({
          type: 'pattern',
          priority: 'medium',
          title: `Follow ${topPattern.library} patterns`,
          description: `Use established patterns from ${topPattern.library}`,
          confidence: 0.8,
          actionSteps: this.generatePatternActionSteps(topPattern, componentType)
        });
      }
    }

    return insights.sort((a, b) => {
      const priorityOrder = { critical: 3, high: 2, medium: 1, low: 0 };
      return priorityOrder[b.priority] - priorityOrder[a.priority];
    });
  }

  /**
   * Enhanced ASCII representation with advanced intelligence
   */
  enhanceASCIIWithAdvancedIntelligence(originalASCII, enhancedComponent) {
    let enhanced = originalASCII;

    // Add intelligence confidence indicator
    const confidence = enhancedComponent.metadata?.intelligenceConfidence || 0;
    const confidenceIndicator = confidence >= 0.8 ? '🎯' : confidence >= 0.6 ? '📊' : '📈';

    // Add accessibility indicators
    if (enhancedComponent.accessibility?.ariaAttributes?.length > 0) {
      enhanced = this.addAccessibilityIndicators(enhanced, enhancedComponent.accessibility);
    }

    // Add best practice indicators with confidence
    if (enhancedComponent.bestPractices?.length > 0) {
      enhanced = this.addAdvancedBestPracticeIndicators(enhanced, enhancedComponent.bestPractices);
    }

    // Add pattern indicators
    if (enhancedComponent.patterns?.length > 0) {
      enhanced = this.addPatternIndicators(enhanced, enhancedComponent.patterns);
    }

    // Add actionable insights summary
    if (enhancedComponent.actionableInsights?.length > 0) {
      enhanced = this.addInsightsSummary(enhanced, enhancedComponent.actionableInsights);
    }

    // Add confidence header
    const lines = enhanced.split('\n');
    if (lines.length > 0) {
      lines[0] = `${confidenceIndicator} ${lines[0]} (Intelligence: ${Math.round(confidence * 100)}%)`;
    }

    return lines.join('\n');
  }

  // ===== HELPER METHODS =====

  /**
   * Generate cache key for enhancement results
   */
  generateEnhancementCacheKey(component, context) {
    const contextHash = JSON.stringify({
      type: component.type,
      industry: context.industry,
      framework: context.framework,
      platform: context.platform
    });
    return `enhancement_${component.type}_${Buffer.from(contextHash).toString('base64').slice(0, 16)}`;
  }

  /**
   * Check if cached result is still valid
   */
  isCacheValid(cached) {
    const maxAge = this.config.cacheResults ? 3600000 : 0; // 1 hour
    return Date.now() - cached.timestamp < maxAge;
  }

  /**
   * Calculate intelligence confidence from multiple sources
   */
  calculateIntelligenceConfidence(practices, accessibility, patterns, consensus) {
    let confidence = 0;
    let sources = 0;

    if (practices && practices.confidence) {
      confidence += practices.confidence * 0.4;
      sources++;
    }

    if (accessibility && accessibility.guidelines) {
      confidence += 0.8 * 0.3; // Accessibility is generally high confidence
      sources++;
    }

    if (patterns && patterns.patterns) {
      confidence += 0.7 * 0.2; // Pattern matching is medium confidence
      sources++;
    }

    if (consensus && consensus.confidence) {
      confidence += consensus.confidence * 0.1; // Consensus adds small boost
    }

    return sources > 0 ? Math.min(confidence, 1) : 0;
  }

  /**
   * Get research sources summary
   */
  getResearchSourcesSummary(practices, accessibility, patterns) {
    const sources = [];

    if (practices && practices.metadata) {
      sources.push(`UI Practices: ${practices.metadata.provider}`);
    }

    if (accessibility && accessibility.metadata) {
      sources.push(`Accessibility: ${accessibility.metadata.provider}`);
    }

    if (patterns && patterns.metadata) {
      sources.push(`Patterns: ${patterns.metadata.provider}`);
    }

    return sources;
  }

  /**
   * Build practices consensus from research
   */
  buildPracticesConsensus(practices) {
    if (!practices || !practices.bestPractices) return [];

    const consensus = [];
    const practiceFrequency = new Map();

    // Count frequency of similar practices
    for (const practice of practices.bestPractices) {
      const normalized = this.normalizePractice(practice);
      practiceFrequency.set(normalized, (practiceFrequency.get(normalized) || 0) + 1);
    }

    // Build consensus from frequent practices
    for (const [practice, frequency] of practiceFrequency) {
      if (frequency >= 2) { // Appears in multiple sources
        consensus.push({
          practice,
          frequency,
          confidence: Math.min(frequency / practices.bestPractices.length, 1)
        });
      }
    }

    return consensus;
  }

  /**
   * Calculate applicability score for practice
   */
  calculateApplicabilityScore(practice, componentType) {
    const practiceText = practice.toLowerCase();
    const componentRules = this.componentIntelligence[componentType] || {};
    const keywords = componentRules.keywords || [componentType];

    let score = 0.5; // Base score

    // Check keyword matches
    for (const keyword of keywords) {
      if (practiceText.includes(keyword.toLowerCase())) {
        score += 0.2;
      }
    }

    // Check priority areas
    const priorities = componentRules.priority || [];
    for (const priority of priorities) {
      if (practiceText.includes(priority.toLowerCase())) {
        score += 0.15;
      }
    }

    return Math.min(score, 1);
  }

  /**
   * Generate detailed implementation suggestions
   */
  suggestDetailedImplementation(practice, componentType, score) {
    const base = `Apply "${practice}" to ${componentType} component`;

    if (score.total >= 8) {
      return `${base} - High priority implementation with immediate impact`;
    } else if (score.total >= 6) {
      return `${base} - Recommended implementation for improved UX`;
    } else {
      return `${base} - Consider implementation based on project requirements`;
    }
  }

  /**
   * Extract evidence for practice from research
   */
  extractPracticeEvidence(practice, practices) {
    // Extract supporting evidence from research results
    return {
      sources: practices.metadata?.provider || 'Research',
      confidence: practices.confidence || 0.5,
      researchDate: practices.metadata?.timestamp || new Date().toISOString()
    };
  }

  /**
   * Generate action steps for implementing practice
   */
  generateActionSteps(practice, componentType) {
    const steps = [];
    const practiceText = practice.practice.toLowerCase();

    if (practiceText.includes('accessibility') || practiceText.includes('aria')) {
      steps.push('Add appropriate ARIA attributes');
      steps.push('Test with screen readers');
      steps.push('Verify keyboard navigation');
    } else if (practiceText.includes('validation')) {
      steps.push('Implement real-time validation');
      steps.push('Add clear error messages');
      steps.push('Test edge cases');
    } else if (practiceText.includes('performance')) {
      steps.push('Optimize rendering performance');
      steps.push('Implement lazy loading if applicable');
      steps.push('Monitor performance metrics');
    } else {
      steps.push(`Research specific implementation for ${componentType}`);
      steps.push('Create prototype or mockup');
      steps.push('Test with users');
    }

    return steps;
  }

  /**
   * Normalize practice text for comparison
   */
  normalizePractice(practice) {
    return practice.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Add advanced best practice indicators to ASCII
   */
  addAdvancedBestPracticeIndicators(ascii, bestPractices) {
    const lines = ascii.split('\n');
    const highConfidencePractices = bestPractices.filter(p => p.confidence >= 0.8);

    if (lines.length > 0 && highConfidencePractices.length > 0) {
      lines[0] += ` ✨×${highConfidencePractices.length}`;
    }

    return lines.join('\n');
  }

  /**
   * Add pattern indicators to ASCII
   */
  addPatternIndicators(ascii, patterns) {
    const lines = ascii.split('\n');
    if (lines.length > 0 && patterns.length > 0) {
      lines[0] += ' 🔄';
    }
    return lines.join('\n');
  }

  /**
   * Add insights summary to ASCII
   */
  addInsightsSummary(ascii, insights) {
    const criticalInsights = insights.filter(i => i.priority === 'critical').length;
    const highInsights = insights.filter(i => i.priority === 'high').length;

    if (criticalInsights > 0 || highInsights > 0) {
      ascii += `\n\n💡 Insights: ${criticalInsights} critical, ${highInsights} high priority`;
    }

    return ascii;
  }

  // ===== ADDITIONAL HELPER METHODS =====

  /**
   * Apply pattern intelligence to component
   */
  applyPatternIntelligence(patterns, componentType) {
    if (!patterns || !patterns.patterns) return [];

    const applicablePatterns = [];

    for (const pattern of patterns.patterns.slice(0, 3)) {
      applicablePatterns.push({
        library: pattern.library,
        pattern: pattern.patterns?.[0] || 'Standard pattern',
        confidence: 0.7,
        applicability: this.calculatePatternApplicability(pattern, componentType),
        implementation: `Use ${pattern.library} ${componentType} pattern`,
        examples: pattern.examples || []
      });
    }

    return applicablePatterns.sort((a, b) => b.applicability - a.applicability);
  }

  /**
   * Calculate pattern applicability score
   */
  calculatePatternApplicability(pattern, componentType) {
    let score = 0.5; // Base score

    if (pattern.library && pattern.library.toLowerCase().includes(componentType)) {
      score += 0.3;
    }

    if (pattern.patterns && pattern.patterns.length > 0) {
      score += 0.2;
    }

    return Math.min(score, 1);
  }

  /**
   * Calculate accessibility confidence
   */
  calculateAccessibilityConfidence(accessibility, consensus) {
    let confidence = 0.7; // Base confidence for accessibility research

    if (accessibility.guidelines && accessibility.guidelines.length > 0) {
      confidence += 0.1;
    }

    if (consensus && consensus.accessibility) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1);
  }

  /**
   * Determine accessibility priority
   */
  determineAccessibilityPriority(componentType, accessibility) {
    const criticalComponents = ['form', 'navigation', 'search'];

    if (criticalComponents.includes(componentType)) {
      return 'critical';
    }

    if (accessibility.guidelines && accessibility.guidelines.length > 3) {
      return 'high';
    }

    return 'medium';
  }

  /**
   * Extract optimizations from research
   */
  extractOptimizations(practices, componentType) {
    const optimizations = [];

    if (practices.bestPractices) {
      for (const practice of practices.bestPractices.slice(0, 3)) {
        if (practice.toLowerCase().includes('performance') ||
            practice.toLowerCase().includes('optimize')) {
          optimizations.push(`Optimize: ${practice}`);
        }
      }
    }

    return optimizations;
  }

  /**
   * Extract pattern-based enhancements
   */
  extractPatternEnhancements(patterns, componentType) {
    const enhancements = [];

    if (patterns.patterns) {
      for (const pattern of patterns.patterns.slice(0, 2)) {
        enhancements.push(`Apply ${pattern.library} pattern for ${componentType}`);
      }
    }

    return enhancements;
  }

  /**
   * Generate intelligence warnings
   */
  generateIntelligenceWarnings(component, practices, accessibility) {
    const warnings = [];

    // Check for missing accessibility
    if (!accessibility.guidelines || accessibility.guidelines.length === 0) {
      warnings.push(`⚠️ Limited accessibility research for ${component.type}`);
    }

    // Check for low confidence practices
    if (practices.confidence && practices.confidence < 0.5) {
      warnings.push(`⚠️ Low confidence in research results for ${component.type}`);
    }

    return warnings;
  }

  /**
   * Calculate component intelligence confidence
   */
  calculateComponentIntelligenceConfidence(practices, accessibility, patterns, consensus) {
    let confidence = 0;
    let factors = 0;

    if (practices && practices.confidence) {
      confidence += practices.confidence * 0.4;
      factors++;
    }

    if (accessibility && accessibility.guidelines) {
      confidence += 0.8 * 0.3;
      factors++;
    }

    if (patterns && patterns.patterns) {
      confidence += 0.7 * 0.2;
      factors++;
    }

    if (consensus && consensus.confidence) {
      confidence += consensus.confidence * 0.1;
    }

    return factors > 0 ? Math.min(confidence, 1) : 0.5;
  }

  // Placeholder methods for consensus building (simplified implementations)
  buildAccessibilityConsensus(accessibility) { return accessibility.guidelines || []; }
  buildPatternsConsensus(patterns) { return patterns.patterns || []; }
  calculateConsensusStrength(consensus) { return 0.7; }
  calculateConsensusConfidence(consensus) { return 0.8; }
  extractConsensusSources(practices, accessibility, patterns) { return ['research']; }
  getConsensusSupport(practice, consensus) { return 0.5; }
  getConsensusBoost(practice, consensus) { return 0.1; }
  getPatternMatchingBoost(practice, componentType) { return 0.1; }
  generateA11yActionSteps(guideline, componentType) { return ['Implement accessibility guideline']; }
  generatePatternActionSteps(pattern, componentType) { return ['Follow established pattern']; }
}
