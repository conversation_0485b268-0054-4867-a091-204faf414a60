/**
 * Conversation Flow Test
 * 
 * Tests the interactive onboarding conversation flow implementation.
 */

import { describe, test, expect, beforeEach, afterEach, mock } from 'bun:test';
import path from 'path';
import fs from 'fs/promises';
import os from 'os';

// Import modules to test
import { ConversationStateManager } from '../../src/interactive-onboarding/conversation-state-manager.js';
import { QuestionFlowEngine } from '../../src/interactive-onboarding/question-flow-engine.js';

// Test constants
const TEST_PROJECT_ROOT = path.join(os.tmpdir(), `guidant-test-${Date.now()}`);
const TEST_SESSION_ID = 'test-session-123';

// Mock data
const mockProjectIdea = 'I want to build a personal blog';
const mockSession = {
  sessionId: TEST_SESSION_ID,
  conversationData: {
    projectIdea: mockProjectIdea,
    projectType: 'general',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    state: 'in_progress',
    currentQuestion: null,
    answers: {},
    questionHistory: [],
    skippedQuestions: []
  }
};

describe('Interactive Onboarding Conversation Flow', () => {
  // Setup and teardown
  beforeEach(async () => {
    // Create test directory
    await fs.mkdir(TEST_PROJECT_ROOT, { recursive: true });
    await fs.mkdir(path.join(TEST_PROJECT_ROOT, '.guidant'), { recursive: true });
    await fs.mkdir(path.join(TEST_PROJECT_ROOT, '.guidant', 'sessions'), { recursive: true });
    await fs.mkdir(path.join(TEST_PROJECT_ROOT, '.guidant', 'context'), { recursive: true });
    await fs.mkdir(path.join(TEST_PROJECT_ROOT, '.guidant', 'context', 'conversations'), { recursive: true });
  });

  afterEach(async () => {
    // Clean up test directory
    try {
      await fs.rm(TEST_PROJECT_ROOT, { recursive: true, force: true });
    } catch (error) {
      console.error('Error cleaning up test directory:', error);
    }
  });

  // Test ConversationStateManager
  describe('ConversationStateManager', () => {
    test('should initialize a new session', async () => {
      const manager = new ConversationStateManager(TEST_PROJECT_ROOT);
      const session = await manager.initSession({
        projectIdea: mockProjectIdea,
        agentName: 'TestAgent',
        agentId: 'test-agent-123'
      });

      expect(session).toBeDefined();
      expect(session.sessionId).toBeDefined();
      expect(session.conversationData.projectIdea).toBe(mockProjectIdea);
      expect(session.conversationData.state).toBe('initialized');
    });

    test('should save and retrieve a session', async () => {
      const manager = new ConversationStateManager(TEST_PROJECT_ROOT);

      // Create a proper session first
      const session = await manager.initSession({
        projectIdea: mockProjectIdea,
        agentName: 'TestAgent',
        agentId: 'test-agent-123'
      });

      // Retrieve the session
      const retrievedSession = await manager.getSession(session.sessionId);

      expect(retrievedSession).toBeDefined();
      expect(retrievedSession.sessionId).toBe(session.sessionId);
      expect(retrievedSession.conversationData.projectIdea).toBe(mockProjectIdea);
    });

    test('should record an answer', async () => {
      const manager = new ConversationStateManager(TEST_PROJECT_ROOT);

      // Create a proper session first
      const session = await manager.initSession({
        projectIdea: mockProjectIdea,
        agentName: 'TestAgent',
        agentId: 'test-agent-123'
      });

      // Record an answer
      const questionId = 'project_name';
      const answer = 'My Awesome Blog';
      await manager.recordAnswer(session.sessionId, questionId, answer);

      // Retrieve the session
      const updatedSession = await manager.getSession(session.sessionId);

      expect(updatedSession.conversationData.answers[questionId]).toBeDefined();
      expect(updatedSession.conversationData.answers[questionId].value).toBe(answer);
      expect(updatedSession.conversationData.questionHistory).toContain(questionId);
    });

    test('should skip a question', async () => {
      const manager = new ConversationStateManager(TEST_PROJECT_ROOT);

      // Create a proper session first
      const session = await manager.initSession({
        projectIdea: mockProjectIdea,
        agentName: 'TestAgent',
        agentId: 'test-agent-123'
      });

      // Skip a question
      const questionId = 'project_description';
      await manager.skipQuestion(session.sessionId, questionId);

      // Retrieve the session
      const updatedSession = await manager.getSession(session.sessionId);

      expect(updatedSession.conversationData.skippedQuestions).toContain(questionId);
      expect(updatedSession.conversationData.navigationHistory.length).toBeGreaterThan(0);
    });

    test('should go back to a previous question', async () => {
      const manager = new ConversationStateManager(TEST_PROJECT_ROOT);

      // Create a proper session first
      const session = await manager.initSession({
        projectIdea: mockProjectIdea,
        agentName: 'TestAgent',
        agentId: 'test-agent-123'
      });

      // Go back to a previous question
      const prevQuestionId = 'project_description';
      await manager.goBack(session.sessionId, prevQuestionId);

      // Retrieve the session
      const updatedSession = await manager.getSession(session.sessionId);

      expect(updatedSession.conversationData.navigationHistory.length).toBeGreaterThan(0);
      expect(updatedSession.conversationData.navigationHistory[0].action).toBe('back');
    });
  });

  // Test QuestionFlowEngine
  describe('QuestionFlowEngine', () => {
    test('should load question flows', async () => {
      const engine = new QuestionFlowEngine(TEST_PROJECT_ROOT);
      
      // Get general flow
      const generalFlow = engine.getQuestionFlow('general');
      
      expect(generalFlow).toBeDefined();
      expect(Array.isArray(generalFlow)).toBe(true);
      expect(generalFlow.length).toBeGreaterThan(0);
      
      // Check first question
      const firstQuestion = generalFlow[0];
      expect(firstQuestion.id).toBeDefined();
      expect(firstQuestion.type).toBeDefined();
      expect(firstQuestion.prompt).toBeDefined();
    });

    test('should get next question', () => {
      const engine = new QuestionFlowEngine(TEST_PROJECT_ROOT);
      
      // Create conversation data with no answers
      const conversationData = {
        projectType: 'general',
        answers: {},
        questionHistory: []
      };
      
      // Get next question
      const nextQuestion = engine.getNextQuestion(conversationData);
      
      expect(nextQuestion).toBeDefined();
      expect(nextQuestion.id).toBeDefined();
      expect(nextQuestion.type).toBeDefined();
      expect(nextQuestion.prompt).toBeDefined();
    });

    test('should validate and process answers', () => {
      const engine = new QuestionFlowEngine(TEST_PROJECT_ROOT);
      
      // Create a test question
      const textQuestion = {
        id: 'test_question',
        type: 'text',
        prompt: 'Test question',
        minLength: 2,
        maxLength: 50
      };
      
      // Valid answer
      const validResult = engine.processAnswer(textQuestion, 'Valid answer', {});
      expect(validResult.valid).toBe(true);
      expect(validResult.processed).toBe('Valid answer');
      
      // Invalid answer (too short)
      const invalidResult = engine.processAnswer(textQuestion, 'A', {});
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.error).toBeDefined();
    });

    test('should handle list questions', () => {
      const engine = new QuestionFlowEngine(TEST_PROJECT_ROOT);
      
      // Create a list question
      const listQuestion = {
        id: 'test_list',
        type: 'list',
        prompt: 'Add items to the list',
        continuationPrompt: 'Add another or say done',
        listItemName: 'items',
        itemType: 'text',
        minLength: 2
      };
      
      // Process first item
      const result1 = engine.processAnswer(listQuestion, 'Item 1', {});
      expect(result1.valid).toBe(true);
      expect(result1.listStarted).toBe(true);
      expect(result1.currentListQuestion).toBeDefined();
      expect(result1.currentListQuestion.collectedItems).toEqual(['Item 1']);
      
      // Get continuation question
      const conversationData = {
        currentListQuestion: result1.currentListQuestion
      };
      const continuationQuestion = engine.continueListQuestion(conversationData);
      expect(continuationQuestion).toBeDefined();
      expect(continuationQuestion.prompt).toBe('Add another or say done');
      expect(continuationQuestion.isContinuation).toBe(true);
    });

    test('should calculate progress info', () => {
      const engine = new QuestionFlowEngine(TEST_PROJECT_ROOT);
      
      // Create conversation data with some answers
      const conversationData = {
        projectType: 'general',
        answers: {
          'project_name': { value: 'Test Project' },
          'project_description': { value: 'A test project' }
        },
        questionHistory: ['project_name', 'project_description']
      };
      
      // Get progress info
      const progress = engine.getProgressInfo(conversationData);
      
      expect(progress).toBeDefined();
      expect(progress.currentQuestion).toBeDefined();
      expect(progress.totalQuestions).toBeGreaterThan(0);
      expect(progress.answeredQuestions).toBe(2);
      expect(progress.percentComplete).toBeGreaterThan(0);
    });
  });
}); 