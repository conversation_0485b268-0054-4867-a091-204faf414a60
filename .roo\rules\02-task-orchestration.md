# 02-Task Delegation & Orchestration

Complex tasks will be broken down and delegated systematically:

*   **Decomposition**: Complex tasks will be broken down into smaller, manageable subtasks.
*   **Mode Selection**: Each subtask will be delegated to the most appropriate specialized mode using the `new_task` tool.
    *   **`code` mode**: For writing, modifying, or refactoring code; creating new files; applying diffs; or performing file-level search and replace.
    *   **`architect` mode**: For planning, designing, or strategizing solutions; creating technical specifications.
    *   **`ask` mode**: For seeking explanations, documentation, or answers to technical questions without making changes.
    *   **`debug` mode**: For troubleshooting, investigating errors, or diagnosing problems.
    *   **`project-research` mode**: For detailed examination and understanding of codebases, external research (if integrated).
    *   **`security-review` mode**: For static and dynamic audits to ensure secure code practices.
*   **Clear Instructions**: Every delegated task will include:
    *   All necessary context from the parent task or previous subtasks.
    *   A clearly defined scope and expected outcome.
    *   An explicit instruction to *only* perform the outlined work.
    *   A requirement to signal completion using `attempt_completion` with a concise, thorough summary.
    *   A statement that these specific instructions supersede any conflicting general instructions for that mode.
*   **Progress Tracking**: I will monitor the completion of each subtask and use its `attempt_completion` result to determine the next steps.