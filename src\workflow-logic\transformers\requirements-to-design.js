/**
 * Requirements to Design Transformer
 * Transforms requirements and user stories into design specifications
 */

import { BaseTransformer } from './base-transformer.js';
import { ASCIIWireframeRenderer } from '../../visual-generation/generators/ascii-wireframe-renderer.js';
import { MermaidDiagramRenderer } from '../../visual-generation/generators/mermaid-diagram-renderer.js';

export class RequirementsToDesignTransformer extends BaseTransformer {
  constructor(analyzer, options = {}) {
    super(analyzer, options);

    // Initialize ASCII wireframe renderer for visual generation
    this.asciiRenderer = new ASCIIWireframeRenderer({
      enableVisualGeneration: options.enableVisualGeneration !== false,
      enableCaching: options.enableCaching !== false,
      enhanceWithIntelligence: options.enhanceWithIntelligence !== false,
      generateMultipleBreakpoints: options.generateMultipleBreakpoints || false,
      ...options.visualGeneration
    });

    // Initialize Mermaid diagram renderer for user flow visualization with validation
    this.mermaidRenderer = new MermaidDiagramRenderer({
      enableDiagramGeneration: options.enableDiagramGeneration !== false,
      enableCaching: options.enableCaching !== false,
      generateMultipleDiagramTypes: options.generateMultipleDiagramTypes || false,
      enhanceWithIntelligence: options.enhanceWithIntelligence !== false,
      saveToDeliverables: options.saveToDeliverables !== false,
      enableValidation: options.enableValidation !== false,
      enableOptimization: options.enableOptimization !== false,
      enableAssetManagement: options.enableAssetManagement !== false,
      enableInteractiveElements: options.enableInteractiveElements !== false,
      ...options.diagramGeneration
    });
  }
  /**
   * Transform requirements analysis into design specifications
   * @param {object} analysis - Deliverable analysis from requirements phase
   * @param {string} targetPhase - Target phase for transformation (design)
   * @returns {object} Design transformation result
   */
  async transform(analysis, targetPhase) {
    try {
      const insights = this.extractCommonInsights(analysis);
      
      // Extract requirements insights with fallback handling
      const functionalReqs = analysis.insights.prd_complete?.functional_requirements || 
                           analysis.insights.functional_requirements || [];
      const userStories = analysis.insights.user_stories?.stories || 
                         analysis.insights.user_stories || [];
      const featureSpecs = analysis.insights.feature_specifications || [];
      
      // Transform into design specifications
      const wireframes = await this.generateWireframes(functionalReqs, userStories, {
        enableVisualGeneration: true,
        context: insights
      });
      const userFlows = await this.generateUserFlows(userStories, featureSpecs, {
        enableDiagramGeneration: true,
        context: insights,
        functionalRequirements: functionalReqs
      });
      const componentSpecs = this.generateComponentSpecs(functionalReqs, featureSpecs);
      const designSystem = this.generateDesignSystem(componentSpecs);
      
      const transformation = {
        type: 'requirements_to_design',
        insights,
        wireframes,
        userFlows,
        componentSpecs,
        designSystem,
        techStack: this.generateTechStack({
          frontend: this.determineFrontendNeeds(functionalReqs),
          ui_framework: this.determineUIFrameworkNeeds(componentSpecs)
        }),
        decisions: this.generateDecisions(analysis, { wireframes, userFlows, componentSpecs }),
        recommendations: this.generateRecommendations(analysis, targetPhase),
        transformedAt: new Date().toISOString()
      };

      return this.validateOutput(transformation);

    } catch (error) {
      console.error(`Requirements to Design transformation failed: ${error.message}`);
      throw new Error(`Transformation failed: ${error.message}`);
    }
  }

  /**
   * Generate wireframes based on functional requirements and user stories
   * Enhanced with ASCII visual generation while maintaining backward compatibility
   * @param {array} functionalReqs - Functional requirements
   * @param {array} userStories - User stories
   * @param {object} options - Generation options including visual generation settings
   * @returns {array} Generated wireframes with optional visual representations
   */
  async generateWireframes(functionalReqs, userStories, options = {}) {
    const wireframes = [];
    
    // Generate wireframes from functional requirements
    functionalReqs.slice(0, 5).forEach((req, index) => {
      const reqTitle = typeof req === 'object' ? (req.title || req.name || `Requirement ${index + 1}`) : req;
      const reqDescription = typeof req === 'object' ? req.description : req;
      
      wireframes.push({
        id: `WF-${index + 1}`,
        title: `Wireframe for ${reqTitle}`,
        description: `Visual layout for ${reqDescription} functionality`,
        components: this.determineWireframeComponents(req),
        interactions: this.determineWireframeInteractions(req),
        priority: typeof req === 'object' ? req.priority || 'medium' : 'medium',
        responsiveBreakpoints: ['mobile', 'tablet', 'desktop'],
        accessibility: ['keyboard-navigation', 'screen-reader-friendly', 'high-contrast']
      });
    });

    // Add wireframes from user stories if not covered
    userStories.slice(0, 3).forEach((story, index) => {
      const storyTitle = typeof story === 'object' ? story.story || story.title : story;
      
      if (!wireframes.some(wf => wf.title.toLowerCase().includes(storyTitle.toLowerCase().substring(0, 20)))) {
        wireframes.push({
          id: `WF-US-${index + 1}`,
          title: `User Story Wireframe: ${storyTitle.substring(0, 50)}...`,
          description: `Interface design for user story: ${storyTitle}`,
          components: ['User Interface', 'Action Controls', 'Feedback Area'],
          interactions: ['User Input', 'System Response', 'Navigation'],
          priority: typeof story === 'object' ? story.priority || 'medium' : 'medium',
          responsiveBreakpoints: ['mobile', 'tablet', 'desktop'],
          accessibility: ['keyboard-navigation', 'screen-reader-friendly']
        });
      }
    });

    // Enhance wireframes with ASCII visual generation if enabled
    if (options.enableVisualGeneration && this.asciiRenderer) {
      try {
        const enhancedWireframes = await this.asciiRenderer.enhanceWireframes(wireframes, {
          context: options.context,
          breakpoint: options.breakpoint || 'desktop',
          showLabels: options.showLabels !== false,
          generateMultipleBreakpoints: options.generateMultipleBreakpoints || false
        });

        // Add visual generation metadata to transformation
        const renderingStats = this.asciiRenderer.getRenderingStats();
        console.log(`✅ Enhanced ${enhancedWireframes.length} wireframes with ASCII visual generation`);
        console.log(`   Cache hit rate: ${(renderingStats.cacheHitRate * 100).toFixed(1)}%`);
        console.log(`   Enhancement rate: ${(renderingStats.enhancementRate * 100).toFixed(1)}%`);

        return enhancedWireframes;
      } catch (error) {
        console.warn('ASCII wireframe enhancement failed, using standard wireframes:', error.message);
        return wireframes; // Graceful fallback maintains backward compatibility
      }
    }

    return wireframes;
  }

  /**
   * Generate wireframe comparison view for multiple wireframes
   * @param {array} wireframes - Array of wireframes to compare
   * @param {object} options - Comparison options
   * @returns {string} ASCII comparison view
   */
  async generateWireframeComparison(wireframes, options = {}) {
    if (!this.asciiRenderer) {
      return 'Visual comparison not available - ASCII renderer not initialized';
    }

    try {
      return await this.asciiRenderer.generateWireframeComparison(wireframes, options);
    } catch (error) {
      console.warn('Wireframe comparison generation failed:', error.message);
      return 'Wireframe comparison generation failed';
    }
  }

  /**
   * Save visual wireframes to deliverables directory
   * @param {array} wireframes - Enhanced wireframes with visual representations
   * @param {string} projectPath - Project root path
   * @returns {object} Save operation results
   */
  async saveVisualWireframes(wireframes, projectPath = process.cwd()) {
    const fs = await import('fs/promises');
    const path = await import('path');

    const deliverablesPath = path.join(projectPath, '.guidant', 'deliverables', 'wireframes');

    try {
      // Ensure deliverables directory exists
      await fs.mkdir(deliverablesPath, { recursive: true });

      const savedFiles = [];
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

      // Save individual wireframes
      for (let i = 0; i < wireframes.length; i++) {
        const wireframe = wireframes[i];

        if (wireframe.visualRepresentation?.ascii) {
          const filename = `wireframe-${wireframe.id || i + 1}-${timestamp}.txt`;
          const filepath = path.join(deliverablesPath, filename);

          let content = `# ${wireframe.title}\n`;
          content += `Generated: ${new Date().toISOString()}\n`;
          content += `Description: ${wireframe.description || 'No description'}\n\n`;

          const ascii = typeof wireframe.visualRepresentation.ascii === 'string' ?
            wireframe.visualRepresentation.ascii :
            wireframe.visualRepresentation.ascii.primary;

          content += ascii;

          // Add breakpoint variations if available
          if (wireframe.visualRepresentation.ascii.breakpoints) {
            content += '\n\n' + '='.repeat(80) + '\n';
            content += 'RESPONSIVE BREAKPOINTS\n';
            content += '='.repeat(80) + '\n\n';

            for (const [breakpoint, breakpointAscii] of Object.entries(wireframe.visualRepresentation.ascii.breakpoints)) {
              content += `## ${breakpoint.toUpperCase()}\n\n`;
              content += breakpointAscii + '\n\n';
            }
          }

          await fs.writeFile(filepath, content, 'utf8');
          savedFiles.push({ wireframe: wireframe.id, file: filename, path: filepath });
        }
      }

      // Save comparison view
      if (wireframes.length > 1) {
        const comparisonContent = await this.generateWireframeComparison(wireframes);
        const comparisonFilename = `wireframes-comparison-${timestamp}.txt`;
        const comparisonPath = path.join(deliverablesPath, comparisonFilename);

        await fs.writeFile(comparisonPath, comparisonContent, 'utf8');
        savedFiles.push({ type: 'comparison', file: comparisonFilename, path: comparisonPath });
      }

      console.log(`✅ Saved ${savedFiles.length} visual wireframe files to ${deliverablesPath}`);

      return {
        success: true,
        savedFiles,
        deliverablesPath,
        totalFiles: savedFiles.length
      };

    } catch (error) {
      console.error('Failed to save visual wireframes:', error.message);
      return {
        success: false,
        error: error.message,
        savedFiles: []
      };
    }
  }

  /**
   * Generate user flows from user stories and feature specifications
   * Enhanced with Mermaid diagram generation while maintaining backward compatibility
   * @param {array} userStories - User stories
   * @param {array} featureSpecs - Feature specifications
   * @param {object} options - Generation options including diagram generation settings
   * @returns {array} Generated user flows with optional Mermaid diagrams
   */
  async generateUserFlows(userStories, featureSpecs, options = {}) {
    const userFlows = [];
    
    // Generate user flows from user stories
    userStories.slice(0, 4).forEach((story, index) => {
      const storyContent = typeof story === 'object' ? story.story || story.title : story;
      const persona = typeof story === 'object' ? story.persona || 'User' : 'User';
      
      userFlows.push({
        id: `UF-${index + 1}`,
        title: `User Flow for ${persona}: ${storyContent.substring(0, 40)}...`,
        persona: persona,
        trigger: this.extractTrigger(storyContent),
        steps: this.generateFlowSteps(storyContent, story),
        decision_points: this.generateDecisionPoints(storyContent),
        exit_points: this.generateExitPoints(storyContent),
        error_handling: this.generateErrorHandling(storyContent),
        success_criteria: typeof story === 'object' ? story.acceptanceCriteria || ['Task completed successfully'] : ['Task completed successfully']
      });
    });

    // Generate user flows from feature specifications
    featureSpecs.slice(0, 3).forEach((spec, index) => {
      const specContent = typeof spec === 'object' ? (spec.description || spec.name || spec.title) : spec;
      if (!specContent) return;

      const specTitle = typeof spec === 'object' ? (spec.name || spec.title || `Feature Spec ${index + 1}`) : `Feature Spec ${index + 1}`;
      const persona = typeof spec === 'object' ? spec.persona || 'System' : 'System';

      userFlows.push({
        id: `UF-FS-${index + 1}`,
        title: `User Flow for ${specTitle}`,
        persona: persona,
        trigger: this.extractTrigger(specContent),
        steps: this.generateFlowSteps(specContent, spec),
        decision_points: this.generateDecisionPoints(specContent),
        exit_points: this.generateExitPoints(specContent),
        error_handling: this.generateErrorHandling(specContent),
        success_criteria: typeof spec === 'object' ? spec.acceptanceCriteria || ['Feature implemented successfully'] : ['Feature implemented successfully']
      });
    });

    // Enhance user flows with Mermaid diagram generation if enabled
    if (options.enableDiagramGeneration && this.mermaidRenderer) {
      try {
        let enhancedUserFlows;

        // Use validation and enhancement if available
        if (options.enableValidation || options.enableOptimization) {
          enhancedUserFlows = await this.mermaidRenderer.validateAndEnhanceUserFlows(
            userFlows,
            userStories,
            options.functionalRequirements || [],
            {
              context: options.context,
              diagramType: options.diagramType || 'flowchart',
              generateMultipleDiagramTypes: options.generateMultipleDiagramTypes || false,
              enableValidation: options.enableValidation,
              enableOptimization: options.enableOptimization
            }
          );
        } else {
          enhancedUserFlows = await this.mermaidRenderer.enhanceUserFlows(
            userFlows,
            userStories,
            options.functionalRequirements || [],
            {
              context: options.context,
              diagramType: options.diagramType || 'flowchart',
              generateMultipleDiagramTypes: options.generateMultipleDiagramTypes || false
            }
          );
        }

        // Add diagram generation metadata to transformation
        const renderingStats = this.mermaidRenderer.getRenderingStats();
        console.log(`✅ Enhanced ${enhancedUserFlows.length} user flows with Mermaid diagrams`);
        console.log(`   Cache hit rate: ${(renderingStats.cacheHitRate * 100).toFixed(1)}%`);
        console.log(`   Diagrams generated: ${renderingStats.diagramsGenerated}`);
        console.log(`   Flows mapped: ${renderingStats.flowsMapped}`);

        return enhancedUserFlows;
      } catch (error) {
        console.warn('Mermaid diagram enhancement failed, using standard user flows:', error.message);
        return userFlows; // Graceful fallback maintains backward compatibility
      }
    }

    return userFlows;
  }

  /**
   * Generate user flow comparison with Mermaid diagrams
   * @param {array} userFlows - Array of user flows to compare
   * @param {object} options - Comparison options
   * @returns {string} Markdown comparison with Mermaid diagrams
   */
  async generateUserFlowComparison(userFlows, options = {}) {
    if (!this.mermaidRenderer) {
      return 'User flow comparison not available - Mermaid renderer not initialized';
    }

    try {
      return await this.mermaidRenderer.generateFlowComparison(userFlows, options);
    } catch (error) {
      console.warn('User flow comparison generation failed:', error.message);
      return 'User flow comparison generation failed';
    }
  }

  /**
   * Save Mermaid user flow diagrams to deliverables directory
   * @param {array} userFlows - Enhanced user flows with Mermaid diagrams
   * @param {string} projectPath - Project root path
   * @returns {object} Save operation results
   */
  async saveMermaidUserFlows(userFlows, projectPath = process.cwd()) {
    if (!this.mermaidRenderer) {
      return {
        success: false,
        error: 'Mermaid renderer not initialized',
        savedFiles: []
      };
    }

    try {
      return await this.mermaidRenderer.saveFlowDiagrams(userFlows, projectPath);
    } catch (error) {
      console.error('Failed to save Mermaid user flows:', error.message);
      return {
        success: false,
        error: error.message,
        savedFiles: []
      };
    }
  }

  /**
   * Generate component specifications from requirements and features
   * @param {array} functionalReqs - Functional requirements
   * @param {array} featureSpecs - Feature specifications
   * @returns {array} Generated component specifications
   */
  generateComponentSpecs(functionalReqs, featureSpecs) {
    const componentSpecs = [];
    const componentMap = new Map();

    // Generate components from functional requirements
    functionalReqs.slice(0, 6).forEach((req, index) => {
      const reqTitle = typeof req === 'object' ? (req.title || req.name || `Requirement ${index + 1}`) : req;
      const componentName = this.generateComponentName(reqTitle);
      
      if (!componentMap.has(componentName)) {
        componentMap.set(componentName, {
          id: `CS-${componentSpecs.length + 1}`,
          name: componentName,
          description: `Component for ${reqTitle} functionality`,
          type: this.determineComponentType(req),
          props: this.generateComponentProps(req),
          state: this.generateComponentState(req),
          methods: this.generateComponentMethods(req),
          styling: this.generateComponentStyling(req),
          accessibility: this.generateAccessibilityFeatures(req),
          priority: typeof req === 'object' ? req.priority || 'medium' : 'medium',
          dependencies: [],
          testingRequirements: this.generateTestingRequirements(req)
        });
        componentSpecs.push(componentMap.get(componentName));
      }
    });

    return componentSpecs;
  }

  /**
   * Generate design system specifications
   * @param {array} componentSpecs - Component specifications
   * @returns {object} Design system specification
   */
  generateDesignSystem(componentSpecs) {
    return {
      colorPalette: {
        primary: ['#007bff', '#0056b3', '#004085'],
        secondary: ['#6c757d', '#545b62', '#3d4449'],
        success: ['#28a745', '#1e7e34', '#155724'],
        warning: ['#ffc107', '#d39e00', '#b08800'],
        error: ['#dc3545', '#bd2130', '#a71e2a'],
        neutral: ['#f8f9fa', '#e9ecef', '#dee2e6', '#ced4da']
      },
      typography: {
        fontFamily: {
          primary: 'Inter, system-ui, sans-serif',
          monospace: 'Monaco, Consolas, monospace'
        },
        fontSize: {
          xs: '0.75rem',
          sm: '0.875rem',
          base: '1rem',
          lg: '1.125rem',
          xl: '1.25rem',
          '2xl': '1.5rem',
          '3xl': '1.875rem'
        },
        fontWeight: {
          normal: '400',
          medium: '500',
          semibold: '600',
          bold: '700'
        }
      },
      spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
        '2xl': '3rem'
      },
      borderRadius: {
        sm: '0.25rem',
        md: '0.375rem',
        lg: '0.5rem',
        xl: '0.75rem'
      },
      components: componentSpecs.map(spec => ({
        name: spec.name,
        variants: this.generateComponentVariants(spec),
        states: spec.state || []
      }))
    };
  }

  // Helper methods for component generation
  determineWireframeComponents(req) {
    const reqText = typeof req === 'object' ? (req.description || req.title || '') : req;
    const components = ['Header', 'Navigation'];
    
    if (reqText.toLowerCase().includes('form') || reqText.toLowerCase().includes('input')) {
      components.push('Form', 'Input Fields', 'Submit Button');
    }
    if (reqText.toLowerCase().includes('list') || reqText.toLowerCase().includes('table')) {
      components.push('Data Table', 'Pagination');
    }
    if (reqText.toLowerCase().includes('auth') || reqText.toLowerCase().includes('login')) {
      components.push('Login Form', 'Authentication');
    }
    
    components.push('Content Area', 'Footer');
    return components;
  }

  determineWireframeInteractions(req) {
    const reqText = typeof req === 'object' ? (req.description || req.title || '') : req;
    const interactions = ['Click', 'Navigation'];
    
    if (reqText.toLowerCase().includes('form')) {
      interactions.push('Form Submit', 'Input Validation');
    }
    if (reqText.toLowerCase().includes('search')) {
      interactions.push('Search', 'Filter');
    }
    if (reqText.toLowerCase().includes('drag') || reqText.toLowerCase().includes('drop')) {
      interactions.push('Drag and Drop');
    }
    
    return interactions;
  }

  extractTrigger(storyContent) {
    if (storyContent.toLowerCase().includes('login') || storyContent.toLowerCase().includes('sign in')) {
      return 'User needs to authenticate';
    }
    if (storyContent.toLowerCase().includes('create') || storyContent.toLowerCase().includes('add')) {
      return 'User wants to create new content';
    }
    if (storyContent.toLowerCase().includes('view') || storyContent.toLowerCase().includes('see')) {
      return 'User wants to view information';
    }
    return 'User initiates action';
  }

  generateFlowSteps(storyContent, story) {
    const steps = ['User enters system'];
    
    if (storyContent.toLowerCase().includes('login')) {
      steps.push('User provides credentials', 'System validates credentials');
    }
    if (storyContent.toLowerCase().includes('form')) {
      steps.push('User fills out form', 'User submits form');
    }
    if (storyContent.toLowerCase().includes('search')) {
      steps.push('User enters search criteria', 'System returns results');
    }
    
    steps.push('User completes action', 'System provides feedback');
    return steps;
  }

  generateDecisionPoints(storyContent) {
    const decisions = [];
    
    if (storyContent.toLowerCase().includes('auth') || storyContent.toLowerCase().includes('login')) {
      decisions.push('Valid credentials?', 'User authorized?');
    }
    if (storyContent.toLowerCase().includes('form')) {
      decisions.push('Valid input?', 'Required fields completed?');
    }
    
    decisions.push('Action successful?');
    return decisions;
  }

  generateExitPoints(storyContent) {
    return ['Success', 'Error', 'Cancel', 'Timeout'];
  }

  generateErrorHandling(storyContent) {
    return [
      'Display error message',
      'Provide recovery options',
      'Log error for debugging',
      'Graceful degradation'
    ];
  }

  generateComponentName(reqTitle) {
    return reqTitle
      .replace(/[^a-zA-Z0-9\s]/g, '')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('') + 'Component';
  }

  determineComponentType(req) {
    const reqText = typeof req === 'object' ? (req.description || req.title || '') : req;

    if (reqText.toLowerCase().includes('form')) return 'form';
    if (reqText.toLowerCase().includes('button')) return 'interactive';
    if (reqText.toLowerCase().includes('display') || reqText.toLowerCase().includes('show')) return 'display';
    if (reqText.toLowerCase().includes('input')) return 'input';
    if (reqText.toLowerCase().includes('navigation')) return 'navigation';

    return 'functional';
  }

  generateComponentProps(req) {
    const baseProps = ['className', 'id'];
    const reqText = typeof req === 'object' ? (req.description || req.title || '') : req;

    if (reqText.toLowerCase().includes('data')) {
      baseProps.push('data', 'loading', 'error');
    }
    if (reqText.toLowerCase().includes('action') || reqText.toLowerCase().includes('click')) {
      baseProps.push('onClick', 'onAction');
    }
    if (reqText.toLowerCase().includes('form')) {
      baseProps.push('onSubmit', 'validation', 'initialValues');
    }

    return baseProps;
  }

  generateComponentState(req) {
    const baseState = ['isLoading'];
    const reqText = typeof req === 'object' ? (req.description || req.title || '') : req;

    if (reqText.toLowerCase().includes('form')) {
      baseState.push('formData', 'errors', 'isValid');
    }
    if (reqText.toLowerCase().includes('active') || reqText.toLowerCase().includes('select')) {
      baseState.push('isActive', 'isSelected');
    }
    if (reqText.toLowerCase().includes('error')) {
      baseState.push('hasError', 'errorMessage');
    }

    return baseState;
  }

  generateComponentMethods(req) {
    const baseMethods = ['render'];
    const reqText = typeof req === 'object' ? (req.description || req.title || '') : req;

    if (reqText.toLowerCase().includes('form')) {
      baseMethods.push('handleSubmit', 'validateInput', 'resetForm');
    }
    if (reqText.toLowerCase().includes('action')) {
      baseMethods.push('handleAction', 'executeAction');
    }
    if (reqText.toLowerCase().includes('data')) {
      baseMethods.push('fetchData', 'updateData');
    }

    return baseMethods;
  }

  generateComponentStyling(req) {
    return {
      layout: 'flexbox',
      responsive: true,
      theme: 'system',
      customizable: true
    };
  }

  generateAccessibilityFeatures(req) {
    return [
      'ARIA labels',
      'Keyboard navigation',
      'Screen reader support',
      'High contrast mode',
      'Focus management'
    ];
  }

  generateTestingRequirements(req) {
    return [
      'Unit tests for all methods',
      'Integration tests for user interactions',
      'Accessibility testing',
      'Visual regression testing'
    ];
  }

  generateComponentVariants(spec) {
    const variants = ['default'];

    if (spec.type === 'interactive') {
      variants.push('primary', 'secondary', 'outline');
    }
    if (spec.type === 'display') {
      variants.push('compact', 'detailed');
    }
    if (spec.type === 'form') {
      variants.push('inline', 'stacked');
    }

    return variants;
  }

  determineFrontendNeeds(functionalReqs) {
    // Analyze requirements to suggest frontend framework
    const hasComplexUI = functionalReqs.some(req => {
      const reqText = typeof req === 'object' ? (req.description || req.title || '') : req;
      return reqText.toLowerCase().includes('interactive') ||
             reqText.toLowerCase().includes('dynamic') ||
             reqText.toLowerCase().includes('real-time');
    });

    return hasComplexUI ? 'react' : 'vanilla';
  }

  determineUIFrameworkNeeds(componentSpecs) {
    // Determine if a UI framework is needed based on component complexity
    const complexComponents = componentSpecs.filter(spec =>
      spec.type === 'interactive' || spec.dependencies.length > 0
    );

    return complexComponents.length > 3 ? 'material-ui' : 'custom';
  }
}
