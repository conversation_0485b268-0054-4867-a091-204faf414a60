/**
 * Mermaid Syntax Generator
 * Converts user stories and user flows into valid Mermaid diagram syntax
 * Supports flowcharts, sequence diagrams, and state diagrams
 */

/**
 * Mermaid Syntax Generator
 */
export class MermaidSyntaxGenerator {
  constructor(config = {}) {
    this.config = {
      defaultDiagramType: config.defaultDiagramType || 'flowchart',
      includeErrorPaths: config.includeErrorPaths !== false,
      includeDecisionPoints: config.includeDecisionPoints !== false,
      nodeIdPrefix: config.nodeIdPrefix || 'node',
      enableStyling: config.enableStyling !== false,
      compactMode: config.compactMode || false,
      ...config
    };

    this.nodeCounter = 0;
    this.generatedNodes = new Map();
    this.connections = [];
    this.styles = [];
  }

  /**
   * Generate Mermaid diagram from user flow
   */
  generateFromUserFlow(userFlow, diagramType = null) {
    this.resetGenerator();
    
    const type = diagramType || this.config.defaultDiagramType;
    
    switch (type) {
      case 'flowchart':
        return this.generateFlowchart(userFlow);
      case 'sequence':
        return this.generateSequenceDiagram(userFlow);
      case 'state':
        return this.generateStateDiagram(userFlow);
      default:
        return this.generateFlowchart(userFlow);
    }
  }

  /**
   * Generate flowchart diagram
   */
  generateFlowchart(userFlow) {
    let mermaid = 'flowchart TD\n';
    
    // Add title as comment
    mermaid += `    %% ${userFlow.title || 'User Flow'}\n\n`;
    
    // Generate start node
    const startNode = this.createNode('start', 'Start', 'circle');
    mermaid += `    ${startNode.id}((${startNode.label}))\n`;
    
    let previousNode = startNode;
    
    // Process flow steps
    if (userFlow.steps && userFlow.steps.length > 0) {
      for (let i = 0; i < userFlow.steps.length; i++) {
        const step = userFlow.steps[i];
        const stepNode = this.createNode('step', step, 'rect');
        
        mermaid += `    ${stepNode.id}[${stepNode.label}]\n`;
        mermaid += `    ${previousNode.id} --> ${stepNode.id}\n`;
        
        // Add decision points if configured
        if (this.config.includeDecisionPoints && userFlow.decision_points) {
          const relatedDecision = this.findRelatedDecision(step, userFlow.decision_points);
          if (relatedDecision) {
            const decisionNode = this.createNode('decision', relatedDecision, 'diamond');
            mermaid += `    ${decisionNode.id}{${decisionNode.label}}\n`;
            mermaid += `    ${stepNode.id} --> ${decisionNode.id}\n`;
            
            // Add success path
            const successNode = this.createNode('success', 'Continue', 'rect');
            mermaid += `    ${successNode.id}[${successNode.label}]\n`;
            mermaid += `    ${decisionNode.id} -->|Yes| ${successNode.id}\n`;
            
            // Add error path if enabled
            if (this.config.includeErrorPaths) {
              const errorNode = this.createNode('error', 'Handle Error', 'rect');
              mermaid += `    ${errorNode.id}[${errorNode.label}]\n`;
              mermaid += `    ${decisionNode.id} -->|No| ${errorNode.id}\n`;
              
              // Error handling flow
              if (userFlow.error_handling && userFlow.error_handling.length > 0) {
                const errorHandling = userFlow.error_handling[0];
                const errorHandlingNode = this.createNode('error_handling', errorHandling, 'rect');
                mermaid += `    ${errorHandlingNode.id}[${errorHandlingNode.label}]\n`;
                mermaid += `    ${errorNode.id} --> ${errorHandlingNode.id}\n`;
                
                // Loop back to previous step or exit
                mermaid += `    ${errorHandlingNode.id} --> ${stepNode.id}\n`;
              }
            }
            
            previousNode = successNode;
          } else {
            previousNode = stepNode;
          }
        } else {
          previousNode = stepNode;
        }
      }
    }
    
    // Add end nodes based on exit points
    if (userFlow.exit_points && userFlow.exit_points.length > 0) {
      for (const exitPoint of userFlow.exit_points) {
        const exitNode = this.createNode('exit', exitPoint, 'circle');
        mermaid += `    ${exitNode.id}((${exitNode.label}))\n`;
        mermaid += `    ${previousNode.id} --> ${exitNode.id}\n`;
      }
    } else {
      // Default end node
      const endNode = this.createNode('end', 'End', 'circle');
      mermaid += `    ${endNode.id}((${endNode.label}))\n`;
      mermaid += `    ${previousNode.id} --> ${endNode.id}\n`;
    }
    
    // Add styling if enabled
    if (this.config.enableStyling) {
      mermaid += this.generateStyling();
    }
    
    return mermaid;
  }

  /**
   * Generate sequence diagram
   */
  generateSequenceDiagram(userFlow) {
    let mermaid = 'sequenceDiagram\n';
    
    // Add title
    mermaid += `    title ${userFlow.title || 'User Flow Sequence'}\n\n`;
    
    // Define participants
    const participants = this.extractParticipants(userFlow);
    for (const participant of participants) {
      mermaid += `    participant ${participant.id} as ${participant.name}\n`;
    }
    mermaid += '\n';
    
    // Generate sequence steps
    if (userFlow.steps && userFlow.steps.length > 0) {
      for (let i = 0; i < userFlow.steps.length; i++) {
        const step = userFlow.steps[i];
        const { from, to, message } = this.parseSequenceStep(step, participants);
        
        mermaid += `    ${from} ->> ${to}: ${message}\n`;
        
        // Add activation if it's a system process
        if (to === 'System' || to === 'S') {
          mermaid += `    activate ${to}\n`;
          mermaid += `    ${to} -->> ${from}: Response\n`;
          mermaid += `    deactivate ${to}\n`;
        }
      }
    }
    
    // Add error handling sequences
    if (this.config.includeErrorPaths && userFlow.error_handling) {
      mermaid += '\n    %% Error Handling\n';
      for (const errorStep of userFlow.error_handling) {
        mermaid += `    System ->> User: ${errorStep}\n`;
      }
    }
    
    return mermaid;
  }

  /**
   * Generate state diagram
   */
  generateStateDiagram(userFlow) {
    let mermaid = 'stateDiagram-v2\n';
    
    // Add title as comment
    mermaid += `    %% ${userFlow.title || 'User Flow States'}\n\n`;
    
    // Start state
    mermaid += '    [*] --> Initial\n';
    
    let currentState = 'Initial';
    
    // Process steps as states
    if (userFlow.steps && userFlow.steps.length > 0) {
      for (let i = 0; i < userFlow.steps.length; i++) {
        const step = userFlow.steps[i];
        const stateName = this.sanitizeStateName(step);
        
        mermaid += `    ${currentState} --> ${stateName}\n`;
        
        // Add state description
        mermaid += `    ${stateName} : ${step}\n`;
        
        // Add decision states
        if (this.config.includeDecisionPoints && userFlow.decision_points) {
          const relatedDecision = this.findRelatedDecision(step, userFlow.decision_points);
          if (relatedDecision) {
            const decisionState = this.sanitizeStateName(relatedDecision);
            mermaid += `    ${stateName} --> ${decisionState}\n`;
            mermaid += `    ${decisionState} : ${relatedDecision}\n`;
            
            // Success path
            const nextState = i < userFlow.steps.length - 1 ? 
              this.sanitizeStateName(userFlow.steps[i + 1]) : 'Success';
            mermaid += `    ${decisionState} --> ${nextState} : Yes\n`;
            
            // Error path
            if (this.config.includeErrorPaths) {
              mermaid += `    ${decisionState} --> ErrorState : No\n`;
              mermaid += `    ErrorState --> ${stateName} : Retry\n`;
              mermaid += `    ErrorState --> [*] : Exit\n`;
            }
            
            currentState = nextState;
          } else {
            currentState = stateName;
          }
        } else {
          currentState = stateName;
        }
      }
    }
    
    // End states
    if (userFlow.exit_points && userFlow.exit_points.length > 0) {
      for (const exitPoint of userFlow.exit_points) {
        const exitState = this.sanitizeStateName(exitPoint);
        mermaid += `    ${currentState} --> ${exitState}\n`;
        mermaid += `    ${exitState} --> [*]\n`;
      }
    } else {
      mermaid += `    ${currentState} --> [*]\n`;
    }
    
    return mermaid;
  }

  /**
   * Generate multiple diagram types for comparison
   */
  generateMultipleDiagrams(userFlow) {
    return {
      flowchart: this.generateFromUserFlow(userFlow, 'flowchart'),
      sequence: this.generateFromUserFlow(userFlow, 'sequence'),
      state: this.generateFromUserFlow(userFlow, 'state'),
      metadata: {
        userFlowId: userFlow.id,
        title: userFlow.title,
        generatedAt: new Date().toISOString(),
        diagramTypes: ['flowchart', 'sequence', 'state']
      }
    };
  }

  // Helper methods
  resetGenerator() {
    this.nodeCounter = 0;
    this.generatedNodes.clear();
    this.connections = [];
    this.styles = [];
  }

  createNode(type, label, shape = 'rect') {
    const id = `${this.config.nodeIdPrefix}${++this.nodeCounter}`;
    const sanitizedLabel = this.sanitizeLabel(label);
    
    const node = {
      id,
      label: sanitizedLabel,
      type,
      shape
    };
    
    this.generatedNodes.set(id, node);
    return node;
  }

  sanitizeLabel(label) {
    // Remove special characters and limit length
    return label.replace(/[^\w\s-]/g, '').substring(0, 30);
  }

  sanitizeStateName(name) {
    // Convert to valid state name
    return name.replace(/[^\w]/g, '_').replace(/_{2,}/g, '_');
  }

  findRelatedDecision(step, decisions) {
    // Simple matching - could be enhanced with NLP
    const stepLower = step.toLowerCase();
    return decisions.find(decision => {
      const decisionLower = decision.toLowerCase();
      return stepLower.includes(decisionLower.split(' ')[0]) || 
             decisionLower.includes(stepLower.split(' ')[0]);
    });
  }

  extractParticipants(userFlow) {
    const participants = [
      { id: 'U', name: userFlow.persona || 'User' },
      { id: 'S', name: 'System' }
    ];
    
    // Add additional participants based on steps
    if (userFlow.steps) {
      for (const step of userFlow.steps) {
        if (step.toLowerCase().includes('admin')) {
          participants.push({ id: 'A', name: 'Admin' });
          break;
        }
      }
    }
    
    return participants;
  }

  parseSequenceStep(step, participants) {
    const stepLower = step.toLowerCase();
    
    // Determine direction based on step content
    if (stepLower.includes('user') && stepLower.includes('enter')) {
      return { from: 'U', to: 'S', message: step };
    } else if (stepLower.includes('system') && stepLower.includes('provide')) {
      return { from: 'S', to: 'U', message: step };
    } else if (stepLower.includes('user')) {
      return { from: 'U', to: 'S', message: step };
    } else {
      return { from: 'S', to: 'U', message: step };
    }
  }

  generateStyling() {
    let styling = '\n    %% Styling\n';
    styling += '    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px\n';
    styling += '    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px\n';
    styling += '    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px\n';
    styling += '    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px\n';
    
    // Apply classes to nodes
    for (const [id, node] of this.generatedNodes) {
      if (node.type === 'start' || node.type === 'end' || node.type === 'exit') {
        styling += `    class ${id} startEnd\n`;
      } else if (node.type === 'decision') {
        styling += `    class ${id} decision\n`;
      } else if (node.type === 'error' || node.type === 'error_handling') {
        styling += `    class ${id} error\n`;
      } else {
        styling += `    class ${id} process\n`;
      }
    }
    
    return styling;
  }

  /**
   * Validate generated Mermaid syntax
   */
  validateSyntax(mermaidCode) {
    const validation = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // Basic syntax validation
    if (!mermaidCode.trim()) {
      validation.isValid = false;
      validation.errors.push('Empty diagram code');
      return validation;
    }

    // Check for diagram type declaration
    const diagramTypes = ['flowchart', 'sequenceDiagram', 'stateDiagram-v2'];
    const hasValidType = diagramTypes.some(type => mermaidCode.includes(type));
    
    if (!hasValidType) {
      validation.isValid = false;
      validation.errors.push('Missing or invalid diagram type declaration');
    }

    // Check for basic structure
    const lines = mermaidCode.split('\n').filter(line => line.trim());
    if (lines.length < 3) {
      validation.warnings.push('Diagram appears to be very simple');
    }

    return validation;
  }
}
