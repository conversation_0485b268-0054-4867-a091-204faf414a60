/**
 * Guidant AI Orchestration Workflow Prompts <PERSON><PERSON>
 * Handles MCP Prompt requests for Guidant-specific AI agent orchestration and non-technical guidance
 */

/**
 * Guidant Workflow Prompt Handler
 * Provides structured prompts for AI agent orchestration, non-technical user guidance, and intelligent project workflows
 */
export class WorkflowPromptHandler {
  constructor(existingInfrastructure = {}) {
    this.workflowManager = existingInfrastructure.workflowManager;

    this.templates = {
      'guidant-project-orchestration': {
        name: 'guidant-project-orchestration',
        description: 'Orchestrate AI agents for non-technical users through project development phases',
        arguments: [
          { name: 'project_type', description: 'Type of project (restaurant-app, ecommerce, saas, etc.)', required: true },
          { name: 'user_expertise', description: 'User technical expertise level (beginner/intermediate/advanced)', required: false },
          { name: 'project_phase', description: 'Current project phase (onboarding/requirements/design/implementation)', required: false },
          { name: 'business_context', description: 'Business context and constraints', required: false }
        ]
      },
      'guidant-conversational-guidance': {
        name: 'guidant-conversational-guidance',
        description: 'Guide non-technical users through complex decisions with AI agent assistance',
        arguments: [
          { name: 'decision_context', description: 'Context of the decision to be made', required: true },
          { name: 'user_background', description: 'User background and experience level', required: false },
          { name: 'available_options', description: 'Available options or choices', required: false },
          { name: 'business_impact', description: 'Business impact and importance level', required: false }
        ]
      },
      'guidant-agent-coordination': {
        name: 'guidant-agent-coordination',
        description: 'Coordinate multiple AI agents for complex project workflows',
        arguments: [
          { name: 'workflow_complexity', description: 'Complexity of the workflow (simple/moderate/complex)', required: true },
          { name: 'agent_roles', description: 'Required AI agent roles and specializations', required: false },
          { name: 'coordination_style', description: 'Coordination approach (sequential/parallel/adaptive)', required: false },
          { name: 'quality_requirements', description: 'Quality and validation requirements', required: false }
        ]
      },
      'guidant-user-onboarding': {
        name: 'guidant-user-onboarding',
        description: 'Onboard non-technical users to AI-assisted project development',
        arguments: [
          { name: 'user_goals', description: 'User project goals and objectives', required: true },
          { name: 'technical_comfort', description: 'User comfort level with technology', required: false },
          { name: 'learning_style', description: 'Preferred learning and interaction style', required: false },
          { name: 'project_timeline', description: 'Desired project timeline and urgency', required: false }
        ]
      }
    };
  }

  /**
   * Get prompt templates for this handler
   * @returns {Array} Array of prompt templates
   */
  getPromptTemplates() {
    return Object.values(this.templates);
  }

  /**
   * Handle prompt get request
   * @param {string} name - Prompt name
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async handlePromptGet(name, args = {}) {
    switch (name) {
      case 'guidant-project-orchestration':
        return await this.generateProjectOrchestrationPrompt(args);
      case 'guidant-conversational-guidance':
        return await this.generateConversationalGuidancePrompt(args);
      case 'guidant-agent-coordination':
        return await this.generateAgentCoordinationPrompt(args);
      case 'guidant-user-onboarding':
        return await this.generateUserOnboardingPrompt(args);
      default:
        throw new Error(`Unknown Guidant workflow prompt: ${name}`);
    }
  }

  /**
   * Generate Guidant project orchestration prompt
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async generateProjectOrchestrationPrompt(args) {
    const {
      project_type,
      user_expertise = 'beginner',
      project_phase = 'onboarding',
      business_context = 'standard business requirements'
    } = args;

    const promptContent = `# Guidant AI Agent Orchestration: ${project_type} Project

## Orchestration Objective
Guide a **${user_expertise}** user through **${project_type}** development using intelligent AI agent coordination during the **${project_phase}** phase, considering **${business_context}**.

## AI Agent Orchestration Framework

### 1. User-Centric Approach
- **Non-Technical Language**: Use business-friendly terminology, avoid technical jargon
- **Progressive Disclosure**: Reveal complexity gradually based on user comfort level
- **Context Awareness**: Maintain awareness of user goals, constraints, and preferences
- **Empathetic Guidance**: Acknowledge user concerns and provide reassuring support
- **Decision Support**: Present options clearly with business impact explanations

### 2. Intelligent Agent Coordination
- **Research Agent**: Automatically gather market intelligence and competitive analysis
- **Analysis Agent**: Process requirements and provide strategic recommendations
- **Design Agent**: Create visual wireframes and system architecture diagrams
- **Implementation Agent**: Generate detailed technical specifications and task breakdowns
- **Quality Agent**: Validate decisions and ensure business alignment

### 3. Phase-Specific Orchestration (${project_phase} phase)
${project_phase === 'onboarding' ? `
- **Discovery**: Understand user vision, goals, and constraints through conversational interface
- **Education**: Explain development process in business terms with clear milestones
- **Expectation Setting**: Establish realistic timelines and resource requirements
- **Foundation Building**: Gather essential project information for subsequent phases` : ''}
${project_phase === 'requirements' ? `
- **Business Analysis**: Extract functional requirements from user conversations
- **Market Research**: Automatically research industry standards and best practices
- **Feature Prioritization**: Guide user through feature importance and complexity trade-offs
- **Scope Definition**: Establish clear project boundaries and success criteria` : ''}
${project_phase === 'design' ? `
- **Visual Creation**: Generate wireframes and mockups for user feedback
- **Architecture Planning**: Design system architecture with scalability considerations
- **Technology Selection**: Recommend appropriate technologies based on requirements
- **User Experience Design**: Optimize workflows for end-user satisfaction` : ''}
${project_phase === 'implementation' ? `
- **Task Generation**: Break down design into manageable development tasks
- **Resource Planning**: Estimate time, cost, and skill requirements
- **Risk Assessment**: Identify potential challenges and mitigation strategies
- **Progress Tracking**: Provide clear visibility into development progress` : ''}

### 4. Conversational Intelligence
- **Context Tracking**: Maintain conversation history and user preferences
- **Adaptive Responses**: Adjust communication style based on user feedback
- **Proactive Guidance**: Anticipate user needs and offer relevant suggestions
- **Decision Synthesis**: Combine user input with AI research for optimal recommendations
- **Progress Celebration**: Acknowledge milestones and maintain user motivation

### 5. Business-Focused Deliverables
- **Executive Summaries**: High-level project overviews for stakeholders
- **Visual Representations**: Wireframes, diagrams, and mockups for clarity
- **Implementation Roadmaps**: Clear timelines with business milestones
- **Risk Assessments**: Business impact analysis with mitigation strategies
- **Success Metrics**: Measurable outcomes aligned with business objectives

## Guidant Orchestration Questions
Address these key aspects for effective AI agent coordination:

1. **User Understanding**: How well do we understand the user's business vision and constraints?
2. **Agent Coordination**: Which AI agents should be involved and in what sequence?
3. **Communication Style**: What level of technical detail is appropriate for this user?
4. **Decision Points**: Where does the user need guidance vs autonomous AI action?
5. **Quality Assurance**: How do we ensure recommendations align with business goals?
6. **Progress Tracking**: How do we maintain user engagement and confidence?
7. **Adaptation**: How do we adjust the approach based on user feedback?
8. **Value Delivery**: What immediate value can we provide to build user trust?

## Guidant Orchestration Deliverables

### 1. User Journey Map
- Conversational flow design for ${project_type} projects
- Decision points where user input is required
- Automated research and analysis touchpoints
- Progress milestones and celebration moments
- Escalation paths for complex decisions

### 2. AI Agent Coordination Plan
- Agent roles and responsibilities matrix
- Information flow between agents
- Handoff protocols and quality gates
- Conflict resolution mechanisms
- Performance monitoring and optimization

### 3. Business Communication Framework
- Non-technical language guidelines
- Visual communication standards (wireframes, diagrams)
- Progress reporting templates
- Stakeholder update formats
- Success story documentation

### 4. Adaptive Intelligence System
- User preference learning mechanisms
- Context-aware recommendation engines
- Conversation intelligence integration
- Feedback loop optimization
- Continuous improvement protocols

### 5. Quality Assurance Framework
- Business alignment validation
- User satisfaction monitoring
- Technical feasibility verification
- Risk assessment and mitigation
- Success metrics and KPIs

## Guidant Best Practices for ${project_type} Projects
- **User-Centric Design**: Always prioritize user understanding and comfort
- **Progressive Complexity**: Introduce technical concepts gradually
- **Visual Communication**: Use wireframes and diagrams to clarify concepts
- **Autonomous Intelligence**: Let AI agents handle research and analysis
- **Business Focus**: Frame all decisions in business impact terms
- **Continuous Adaptation**: Adjust approach based on user feedback and progress

## Success Criteria for AI Orchestration
The orchestration should achieve:
- High user confidence and satisfaction with the guidance process
- Efficient progression through project phases with minimal confusion
- Quality deliverables that align with business objectives
- Seamless coordination between multiple AI agents
- Adaptive responses to changing user needs and project requirements
- Clear business value delivery at each milestone

Please orchestrate AI agents to provide exceptional guidance for this ${project_type} project, ensuring the ${user_expertise} user feels supported and confident throughout the ${project_phase} phase.`;

    return {
      description: `Guidant AI orchestration prompt for ${project_type} project (${user_expertise} user, ${project_phase} phase)`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  /**
   * Generate Guidant conversational guidance prompt
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async generateConversationalGuidancePrompt(args) {
    const {
      decision_context,
      user_background = 'non-technical business user',
      available_options = 'multiple options available',
      business_impact = 'medium impact decision'
    } = args;

    const promptContent = `# Guidant Conversational Decision Guidance

## Guidance Objective
Provide empathetic, clear guidance to a **${user_background}** through **${decision_context}** with **${business_impact}**, considering **${available_options}**.

## Conversational Guidance Framework

### 1. User Understanding and Empathy
- **Active Listening**: Acknowledge user concerns and validate their perspective
- **Context Gathering**: Understand the full business and personal context
- **Comfort Assessment**: Gauge user confidence level and technical comfort
- **Goal Alignment**: Ensure clear understanding of desired outcomes
- **Constraint Recognition**: Identify budget, timeline, and resource limitations

### 2. Information Simplification
- **Business Language**: Translate technical concepts into business terms
- **Visual Aids**: Use analogies, examples, and visual representations
- **Progressive Disclosure**: Introduce complexity gradually as user becomes comfortable
- **Relevance Filtering**: Focus only on information that impacts the decision
- **Impact Explanation**: Clearly explain consequences of each option

### 3. Decision Support Structure
- **Option Presentation**: Present choices clearly with pros/cons in business terms
- **Risk Assessment**: Explain risks in terms of business impact, not technical details
- **Recommendation Logic**: Provide clear reasoning for recommendations
- **Confidence Building**: Help user understand why the recommendation makes sense
- **Fallback Planning**: Discuss what happens if the decision needs to change

### 4. Conversational Flow Management
- **Pacing Control**: Allow user to process information at their own speed
- **Question Encouragement**: Create safe space for user to ask questions
- **Clarification Loops**: Ensure understanding before moving to next topic
- **Decision Validation**: Confirm user comfort with the chosen direction
- **Next Steps Clarity**: Provide clear, actionable next steps

## Guidant Conversation Questions
Guide the conversation with these key questions:

1. **Understanding Check**: "Help me understand what's most important to you in this decision?"
2. **Concern Exploration**: "What aspects of this decision are you most concerned about?"
3. **Success Definition**: "What would a successful outcome look like for your business?"
4. **Constraint Identification**: "What limitations or requirements do we need to work within?"
5. **Impact Assessment**: "How will this decision affect your day-to-day operations?"
6. **Timeline Clarification**: "What's your ideal timeline for implementing this decision?"
7. **Support Needs**: "What kind of ongoing support would make you feel most confident?"
8. **Alternative Exploration**: "Are there other approaches we should consider?"

## Conversational Guidance Structure

### Phase 1: Context and Comfort Building
- Establish rapport and trust with the user
- Understand business context and personal concerns
- Assess technical comfort level and communication preferences
- Clarify decision scope and importance

### Phase 2: Information Gathering and Education
- Explore available options in business-friendly terms
- Explain implications and trade-offs clearly
- Use examples and analogies relevant to user's industry
- Address concerns and misconceptions proactively

### Phase 3: Decision Support and Validation
- Present recommendations with clear business reasoning
- Validate user understanding and comfort level
- Discuss implementation approach and timeline
- Plan for ongoing support and adjustment

## Expected Conversational Outcomes
The guidance should achieve:
- **User Confidence**: User feels informed and confident about the decision
- **Clear Understanding**: Complex concepts explained in accessible terms
- **Aligned Expectations**: Realistic timeline and outcome expectations set
- **Reduced Anxiety**: User concerns addressed and fears alleviated
- **Actionable Plan**: Clear next steps with appropriate support
- **Relationship Building**: Trust established for ongoing collaboration

## Communication Best Practices
- **Empathetic Tone**: Acknowledge that technical decisions can feel overwhelming
- **Patient Pacing**: Allow time for questions and processing
- **Visual Support**: Use diagrams, examples, and analogies when helpful
- **Validation Loops**: Regularly check understanding and comfort level
- **Positive Reinforcement**: Celebrate good questions and decision progress
- **Safety Net**: Reassure that decisions can be adjusted as needed

Provide guidance that makes the user feel supported, informed, and confident in their decision-making process for: ${decision_context}.`;

    return {
      description: `Guidant conversational guidance for ${decision_context} (${user_background}, ${business_impact})`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  /**
   * Generate Guidant agent coordination prompt
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async generateAgentCoordinationPrompt(args) {
    const {
      workflow_complexity,
      agent_roles = 'research, analysis, design, implementation',
      coordination_style = 'adaptive',
      quality_requirements = 'high quality with business alignment'
    } = args;

    const promptContent = `# Guidant AI Agent Coordination

## Coordination Objective
Orchestrate multiple AI agents for **${workflow_complexity}** workflow with **${coordination_style}** coordination, involving **${agent_roles}** agents to achieve **${quality_requirements}**.

## AI Agent Coordination Framework

### 1. Agent Role Definition and Specialization
- **Research Agent**: Market intelligence, competitive analysis, technology research
- **Analysis Agent**: Requirements analysis, business logic design, strategic recommendations
- **Design Agent**: Visual wireframes, system architecture, user experience design
- **Implementation Agent**: Technical specifications, task breakdown, development planning
- **Quality Agent**: Business alignment validation, risk assessment, success metrics

### 2. Coordination Patterns (${coordination_style} style)
${coordination_style === 'sequential' ? `
- **Linear Workflow**: Agents work in predefined sequence
- **Handoff Protocols**: Clear deliverable transfer between agents
- **Quality Gates**: Validation checkpoints between agent phases
- **Progress Tracking**: Sequential milestone completion monitoring` : ''}
${coordination_style === 'parallel' ? `
- **Concurrent Execution**: Multiple agents work simultaneously
- **Information Sharing**: Real-time data exchange between agents
- **Conflict Resolution**: Mechanisms to resolve conflicting recommendations
- **Synchronization Points**: Regular coordination and alignment meetings` : ''}
${coordination_style === 'adaptive' ? `
- **Dynamic Orchestration**: Agent involvement based on current needs
- **Context-Aware Routing**: Intelligent agent selection based on task requirements
- **Feedback Loops**: Continuous adjustment based on progress and quality
- **Emergent Coordination**: Agents self-organize based on workflow demands` : ''}

### 3. Information Flow and Communication
- **Shared Context**: Centralized project context accessible to all agents
- **Communication Protocols**: Standardized formats for inter-agent communication
- **Decision Tracking**: Record of all agent decisions and recommendations
- **Conflict Resolution**: Structured approach to resolving agent disagreements
- **User Integration**: Clear channels for user input and feedback integration

### 4. Quality Assurance and Validation
- **Cross-Agent Validation**: Agents review and validate each other's work
- **Business Alignment Checks**: Ensure all recommendations align with user goals
- **Consistency Verification**: Maintain consistency across agent outputs
- **Risk Assessment**: Collaborative risk identification and mitigation
- **Success Metrics**: Shared understanding of quality and success criteria

## Agent Coordination Questions
Address these key coordination aspects:

1. **Agent Selection**: Which agents are best suited for the current workflow requirements?
2. **Coordination Style**: Should agents work sequentially, in parallel, or adaptively?
3. **Information Sharing**: How will agents share context and findings effectively?
4. **Quality Control**: How will we ensure consistency and quality across agent outputs?
5. **Conflict Resolution**: What happens when agents provide conflicting recommendations?
6. **User Integration**: How will user feedback be incorporated into agent coordination?
7. **Progress Monitoring**: How will we track and measure coordination effectiveness?
8. **Adaptation**: How will coordination adjust based on workflow complexity changes?

## Coordination Implementation Plan

### Phase 1: Agent Initialization and Setup
- Define specific roles and responsibilities for each agent
- Establish shared context and communication protocols
- Set up quality validation and feedback mechanisms
- Initialize coordination monitoring and tracking systems

### Phase 2: Workflow Execution and Coordination
- Execute agent coordination according to chosen style
- Monitor inter-agent communication and collaboration
- Validate outputs for consistency and business alignment
- Integrate user feedback and adjust coordination as needed

### Phase 3: Quality Assurance and Optimization
- Conduct cross-agent validation and quality checks
- Optimize coordination patterns based on performance data
- Document successful coordination patterns for future use
- Implement continuous improvement mechanisms

## Expected Coordination Outcomes
The agent coordination should achieve:
- **Seamless Collaboration**: Smooth information flow and task handoffs between agents
- **Quality Consistency**: Consistent quality and alignment across all agent outputs
- **Efficient Resource Use**: Optimal utilization of each agent's specialized capabilities
- **User Satisfaction**: Coordinated outputs that meet user needs and expectations
- **Adaptive Performance**: Ability to adjust coordination based on changing requirements
- **Knowledge Synthesis**: Combined agent intelligence exceeding individual capabilities

## Coordination Best Practices
- **Clear Role Definition**: Each agent has well-defined responsibilities and boundaries
- **Effective Communication**: Standardized protocols for inter-agent information sharing
- **Quality Focus**: Continuous validation of outputs against business requirements
- **User-Centric Approach**: All coordination serves the ultimate goal of user success
- **Continuous Learning**: Coordination patterns improve based on experience and feedback
- **Flexibility**: Ability to adapt coordination style based on workflow complexity

Coordinate AI agents effectively to deliver exceptional results for this ${workflow_complexity} workflow with ${quality_requirements}.`;

    return {
      description: `Guidant AI agent coordination for ${workflow_complexity} workflow (${coordination_style} style)`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  /**
   * Generate Guidant user onboarding prompt
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async generateUserOnboardingPrompt(args) {
    const {
      user_goals,
      technical_comfort = 'beginner',
      learning_style = 'conversational',
      project_timeline = 'flexible'
    } = args;

    const promptContent = `# Guidant User Onboarding: AI-Assisted Project Development

## Onboarding Objective
Welcome and guide a **${technical_comfort}** user with **${learning_style}** learning style through AI-assisted project development to achieve **${user_goals}** within a **${project_timeline}** timeline.

## Guidant Onboarding Framework

### 1. Welcome and Comfort Building
- **Warm Introduction**: Create a welcoming, non-intimidating environment
- **Expectation Setting**: Explain how Guidant will support their journey
- **Comfort Assessment**: Gauge technical comfort and adjust communication accordingly
- **Success Stories**: Share relevant examples of similar successful projects
- **Support Assurance**: Emphasize ongoing support and guidance availability

### 2. Goal Understanding and Validation
- **Vision Exploration**: Understand the user's business vision and objectives
- **Success Definition**: Help user articulate what success looks like
- **Constraint Identification**: Discover budget, timeline, and resource limitations
- **Priority Setting**: Guide user through feature and outcome prioritization
- **Reality Check**: Ensure goals are achievable within stated constraints

### 3. Learning Style Adaptation (${learning_style} approach)
${learning_style === 'conversational' ? `
- **Interactive Dialogue**: Learn through natural conversation and questions
- **Progressive Discovery**: Reveal information through guided conversation
- **Real-time Clarification**: Immediate answers to questions as they arise
- **Contextual Examples**: Use relevant examples during conversation` : ''}
${learning_style === 'visual' ? `
- **Diagram-Driven Learning**: Use wireframes, flowcharts, and visual aids
- **Progressive Visualization**: Build understanding through visual progression
- **Interactive Mockups**: Show concepts through clickable prototypes
- **Visual Roadmaps**: Present timelines and processes visually` : ''}
${learning_style === 'hands-on' ? `
- **Learning by Doing**: Immediate practical application of concepts
- **Incremental Building**: Start with simple tasks and build complexity
- **Trial and Feedback**: Encourage experimentation with guidance
- **Practical Examples**: Use real project elements for learning` : ''}

### 4. Technical Comfort Accommodation (${technical_comfort} level)
${technical_comfort === 'beginner' ? `
- **Business Language**: Avoid technical jargon, use business terminology
- **Concept Simplification**: Break complex ideas into digestible pieces
- **Analogy Usage**: Explain technical concepts through familiar analogies
- **Patient Pacing**: Allow extra time for understanding and questions
- **Confidence Building**: Celebrate understanding and progress regularly` : ''}
${technical_comfort === 'intermediate' ? `
- **Balanced Communication**: Mix business and technical language appropriately
- **Concept Bridging**: Connect business needs to technical solutions
- **Selective Detail**: Provide technical details when relevant and helpful
- **Skill Building**: Gradually introduce more technical concepts
- **Empowerment Focus**: Build confidence in technical decision-making` : ''}
${technical_comfort === 'advanced' ? `
- **Technical Depth**: Provide detailed technical explanations when requested
- **Architecture Discussion**: Engage in system design and technical trade-offs
- **Best Practices**: Share industry standards and technical best practices
- **Efficiency Focus**: Move quickly through basic concepts to advanced topics
- **Collaboration Style**: Treat as technical partner in decision-making` : ''}

## Guidant Onboarding Questions
Guide the onboarding conversation with these key questions:

1. **Vision Understanding**: "Tell me about your business idea and what you're hoping to achieve?"
2. **Success Metrics**: "How will you know when your project is successful?"
3. **Timeline Expectations**: "What's your ideal timeline for getting this project launched?"
4. **Resource Assessment**: "What resources (time, budget, team) do you have available?"
5. **Experience Level**: "Have you worked on similar projects before? What was that like?"
6. **Comfort Zone**: "What aspects of this project feel most challenging or intimidating?"
7. **Learning Preferences**: "How do you prefer to learn new things - through conversation, visuals, or hands-on?"
8. **Support Needs**: "What kind of support would make you feel most confident moving forward?"

## Onboarding Journey Structure (${project_timeline} timeline)

### Phase 1: Discovery and Foundation (Days 1-3)
- Understand user's business vision and goals
- Assess technical comfort level and learning preferences
- Establish realistic timeline and milestone expectations
- Create initial project foundation and context
- Build confidence and trust in the Guidant process

### Phase 2: Education and Planning (Days 4-7)
- Explain development process in user-friendly terms
- Introduce key concepts relevant to their project type
- Create visual roadmap of the journey ahead
- Establish communication preferences and check-in schedule
- Begin gathering detailed requirements through conversation

### Phase 3: Engagement and Momentum (Week 2)
- Start collaborative requirement gathering and analysis
- Introduce AI agent capabilities and how they'll help
- Create first visual deliverables (wireframes, diagrams)
- Establish feedback loops and iteration processes
- Build excitement and momentum for the project

### Phase 4: Confidence and Independence (Week 3+)
- Guide user through first major decisions with AI support
- Demonstrate value through tangible progress and deliverables
- Establish user confidence in the process and their own judgment
- Transition to ongoing collaborative development mode
- Set up long-term success patterns and support systems

## Onboarding Success Criteria
The onboarding should achieve:
- **User Confidence**: User feels supported and confident about the journey ahead
- **Clear Understanding**: User understands the development process and their role
- **Realistic Expectations**: Appropriate timeline and outcome expectations are set
- **Strong Foundation**: Solid project foundation with clear goals and constraints
- **Engagement**: User is excited and motivated to continue the project
- **Trust Building**: Strong relationship established between user and Guidant system

## Onboarding Best Practices for ${technical_comfort} Users
- **Patience and Empathy**: Acknowledge that starting a new project can feel overwhelming
- **Celebration of Progress**: Recognize and celebrate each step forward
- **Safety and Support**: Create a safe environment for questions and concerns
- **Practical Value**: Provide immediate, tangible value to build confidence
- **Adaptive Communication**: Adjust style and pace based on user feedback
- **Future Vision**: Help user visualize the successful outcome of their project

## Expected Onboarding Outcomes
- User feels welcomed and supported in their project journey
- Clear understanding of how Guidant will help achieve their goals
- Realistic timeline and milestone expectations established
- Strong foundation for ongoing collaborative development
- User confidence in both the process and their own capabilities
- Excitement and motivation to continue with the project

Create an onboarding experience that makes the user feel confident, supported, and excited about achieving their goals: ${user_goals}.`;

    return {
      description: `Guidant user onboarding for ${user_goals} (${technical_comfort} user, ${learning_style} style)`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  /**
   * Validate prompt arguments
   * @param {string} name - Prompt name
   * @param {object} args - Prompt arguments
   * @returns {object} Validation result
   */
  validatePrompt(name, args) {
    const template = this.templates[name];
    if (!template) {
      return { valid: false, error: `Unknown prompt: ${name}` };
    }

    const requiredArgs = template.arguments.filter(arg => arg.required);
    const missingArgs = requiredArgs.filter(arg => !args.hasOwnProperty(arg.name));

    if (missingArgs.length > 0) {
      return {
        valid: false,
        error: `Missing required arguments: ${missingArgs.map(arg => arg.name).join(', ')}`
      };
    }

    return { valid: true };
  }
}

export default WorkflowPromptHandler;
