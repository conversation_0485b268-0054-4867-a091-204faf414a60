/**
 * Version Control Manager for Visual Assets
 * Implements diff tracking, rollback capabilities, and change history
 */

import fs from 'fs/promises';
import path from 'path';
import { writeJSONFile, readJSONFile } from '../../file-management/reliable-file-manager.js';
import { VisualAsset } from './visual-asset-models.js';

/**
 * Visual Asset Version Control Manager
 */
export class VersionControlManager {
  constructor(projectRoot = process.cwd()) {
    this.projectRoot = projectRoot;
    this.versionDir = path.join(projectRoot, '.guidant/deliverables/wireframes/.versions');
  }

  /**
   * Initialize version control system
   */
  async initialize() {
    await fs.mkdir(this.versionDir, { recursive: true });
    
    // Create version control registry
    const registryPath = path.join(this.versionDir, 'version-registry.json');
    try {
      await fs.access(registryPath);
    } catch {
      await writeJSONFile(registryPath, {
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        assets: {},
        totalVersions: 0,
        lastActivity: new Date().toISOString()
      });
    }
    
    return {
      initialized: true,
      versionDirectory: this.versionDir
    };
  }

  /**
   * Create version snapshot of asset
   */
  async createVersion(asset, changeDescription = '', metadata = {}) {
    if (!(asset instanceof VisualAsset)) {
      throw new Error('Asset must be an instance of VisualAsset');
    }

    await this.initialize();
    
    const versionId = this.generateVersionId(asset.id);
    const versionPath = path.join(this.versionDir, asset.id);
    
    // Create asset-specific version directory
    await fs.mkdir(versionPath, { recursive: true });
    
    // Save version snapshot
    const snapshotPath = path.join(versionPath, `${versionId}.json`);
    const versionSnapshot = {
      versionId,
      assetId: asset.id,
      version: asset.version.toString(),
      timestamp: new Date().toISOString(),
      changeDescription,
      metadata: {
        ...metadata,
        fileSize: JSON.stringify(asset.toJSON()).length,
        changeType: metadata.changeType || 'update',
        triggeredBy: metadata.triggeredBy || 'system',
        userAgent: metadata.userAgent || 'Guidant Visual Generator'
      },
      snapshot: asset.toJSON(),
      checksum: this.calculateChecksum(asset.toJSON())
    };
    
    await writeJSONFile(snapshotPath, versionSnapshot);
    
    // Update version registry
    await this.updateVersionRegistry(asset.id, versionSnapshot);
    
    // Calculate diff from previous version
    const diff = await this.calculateDiff(asset.id, versionId);
    if (diff) {
      const diffPath = path.join(versionPath, `${versionId}.diff.json`);
      await writeJSONFile(diffPath, diff);
    }
    
    return {
      versionId,
      snapshotPath,
      diffPath: diff ? path.join(versionPath, `${versionId}.diff.json`) : null,
      timestamp: versionSnapshot.timestamp
    };
  }

  /**
   * Get version history for an asset
   */
  async getVersionHistory(assetId, options = {}) {
    const registry = await this.getVersionRegistry();
    const assetVersions = registry.assets[assetId];
    
    if (!assetVersions) {
      return {
        assetId,
        versions: [],
        totalVersions: 0
      };
    }
    
    let versions = [...assetVersions.versions];
    
    // Apply filters
    if (options.limit) {
      versions = versions.slice(-options.limit);
    }
    
    if (options.dateFrom || options.dateTo) {
      versions = versions.filter(version => {
        const versionDate = new Date(version.timestamp);
        if (options.dateFrom && versionDate < new Date(options.dateFrom)) return false;
        if (options.dateTo && versionDate > new Date(options.dateTo)) return false;
        return true;
      });
    }
    
    if (options.changeType) {
      versions = versions.filter(version => 
        version.metadata.changeType === options.changeType
      );
    }
    
    // Sort by timestamp
    versions.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    return {
      assetId,
      versions,
      totalVersions: assetVersions.totalVersions,
      firstVersion: assetVersions.firstVersion,
      lastVersion: assetVersions.lastVersion
    };
  }

  /**
   * Get specific version of an asset
   */
  async getVersion(assetId, versionId) {
    const versionPath = path.join(this.versionDir, assetId, `${versionId}.json`);
    
    try {
      const versionData = await readJSONFile(versionPath);
      return versionData;
    } catch (error) {
      throw new Error(`Version ${versionId} not found for asset ${assetId}`);
    }
  }

  /**
   * Rollback asset to specific version
   */
  async rollbackToVersion(assetId, versionId, rollbackMetadata = {}) {
    const versionData = await this.getVersion(assetId, versionId);
    
    // Create rollback record
    const rollbackRecord = {
      rollbackId: this.generateVersionId(assetId),
      assetId,
      targetVersionId: versionId,
      timestamp: new Date().toISOString(),
      rollbackReason: rollbackMetadata.reason || 'Manual rollback',
      rollbackBy: rollbackMetadata.rollbackBy || 'system',
      previousState: rollbackMetadata.previousState || null
    };
    
    // Save rollback record
    const rollbackPath = path.join(this.versionDir, assetId, 'rollbacks.json');
    let rollbacks = { records: [] };
    try {
      rollbacks = await readJSONFile(rollbackPath);
    } catch {
      // File doesn't exist, use empty structure
    }
    
    rollbacks.records.push(rollbackRecord);
    await writeJSONFile(rollbackPath, rollbacks);
    
    return {
      rollbackId: rollbackRecord.rollbackId,
      restoredSnapshot: versionData.snapshot,
      rollbackTimestamp: rollbackRecord.timestamp,
      targetVersion: versionId
    };
  }

  /**
   * Compare two versions and get diff
   */
  async compareVersions(assetId, versionId1, versionId2) {
    const [version1, version2] = await Promise.all([
      this.getVersion(assetId, versionId1),
      this.getVersion(assetId, versionId2)
    ]);
    
    return this.generateDetailedDiff(version1.snapshot, version2.snapshot, {
      version1: versionId1,
      version2: versionId2,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Get diff for a specific version
   */
  async getVersionDiff(assetId, versionId) {
    const diffPath = path.join(this.versionDir, assetId, `${versionId}.diff.json`);
    
    try {
      return await readJSONFile(diffPath);
    } catch {
      return null; // No diff available (probably first version)
    }
  }

  /**
   * Get rollback history for an asset
   */
  async getRollbackHistory(assetId) {
    const rollbackPath = path.join(this.versionDir, assetId, 'rollbacks.json');
    
    try {
      const rollbacks = await readJSONFile(rollbackPath);
      return rollbacks.records.sort((a, b) => 
        new Date(b.timestamp) - new Date(a.timestamp)
      );
    } catch {
      return [];
    }
  }

  /**
   * Clean up old versions based on retention policy
   */
  async cleanupVersions(assetId, retentionPolicy = {}) {
    const policy = {
      maxVersions: retentionPolicy.maxVersions || 10,
      maxAge: retentionPolicy.maxAge || 30, // days
      keepMajorVersions: retentionPolicy.keepMajorVersions !== false,
      ...retentionPolicy
    };
    
    const history = await this.getVersionHistory(assetId);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - policy.maxAge);
    
    const versionsToDelete = [];
    const versionsToKeep = [];
    
    // Sort versions by timestamp (newest first)
    const sortedVersions = history.versions.sort((a, b) => 
      new Date(b.timestamp) - new Date(a.timestamp)
    );
    
    sortedVersions.forEach((version, index) => {
      const versionDate = new Date(version.timestamp);
      const isMajorVersion = policy.keepMajorVersions && 
        version.metadata.changeType === 'major';
      
      if (index < policy.maxVersions || 
          versionDate > cutoffDate || 
          isMajorVersion) {
        versionsToKeep.push(version);
      } else {
        versionsToDelete.push(version);
      }
    });
    
    // Delete old versions
    const deletedVersions = [];
    for (const version of versionsToDelete) {
      try {
        const versionPath = path.join(this.versionDir, assetId, `${version.versionId}.json`);
        const diffPath = path.join(this.versionDir, assetId, `${version.versionId}.diff.json`);
        
        await fs.unlink(versionPath);
        try {
          await fs.unlink(diffPath);
        } catch {
          // Diff file might not exist
        }
        
        deletedVersions.push(version.versionId);
      } catch (error) {
        console.warn(`Failed to delete version ${version.versionId}: ${error.message}`);
      }
    }
    
    // Update registry
    if (deletedVersions.length > 0) {
      await this.updateVersionRegistryAfterCleanup(assetId, deletedVersions);
    }
    
    return {
      deletedVersions,
      keptVersions: versionsToKeep.length,
      cleanupTimestamp: new Date().toISOString()
    };
  }

  /**
   * Get version control statistics
   */
  async getVersionControlStats() {
    const registry = await this.getVersionRegistry();
    
    const stats = {
      totalAssets: Object.keys(registry.assets).length,
      totalVersions: registry.totalVersions,
      averageVersionsPerAsset: 0,
      storageUsed: 0,
      oldestVersion: null,
      newestVersion: null,
      assetStats: {}
    };
    
    if (stats.totalAssets > 0) {
      stats.averageVersionsPerAsset = stats.totalVersions / stats.totalAssets;
    }
    
    // Calculate detailed statistics
    for (const [assetId, assetData] of Object.entries(registry.assets)) {
      stats.assetStats[assetId] = {
        totalVersions: assetData.totalVersions,
        firstVersion: assetData.firstVersion,
        lastVersion: assetData.lastVersion,
        storageUsed: await this.calculateAssetStorageUsage(assetId)
      };
      
      stats.storageUsed += stats.assetStats[assetId].storageUsed;
      
      if (!stats.oldestVersion || assetData.firstVersion < stats.oldestVersion) {
        stats.oldestVersion = assetData.firstVersion;
      }
      
      if (!stats.newestVersion || assetData.lastVersion > stats.newestVersion) {
        stats.newestVersion = assetData.lastVersion;
      }
    }
    
    return stats;
  }

  // Helper methods
  generateVersionId(assetId) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `v_${timestamp}_${random}`;
  }

  calculateChecksum(data) {
    // Simple checksum calculation - could be enhanced with crypto
    const str = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }

  async updateVersionRegistry(assetId, versionSnapshot) {
    const registryPath = path.join(this.versionDir, 'version-registry.json');
    const registry = await readJSONFile(registryPath);
    
    if (!registry.assets[assetId]) {
      registry.assets[assetId] = {
        totalVersions: 0,
        versions: [],
        firstVersion: versionSnapshot.timestamp,
        lastVersion: versionSnapshot.timestamp
      };
    }
    
    const assetData = registry.assets[assetId];
    assetData.versions.push({
      versionId: versionSnapshot.versionId,
      version: versionSnapshot.version,
      timestamp: versionSnapshot.timestamp,
      changeDescription: versionSnapshot.changeDescription,
      metadata: versionSnapshot.metadata,
      checksum: versionSnapshot.checksum
    });
    
    assetData.totalVersions++;
    assetData.lastVersion = versionSnapshot.timestamp;
    
    registry.totalVersions++;
    registry.lastActivity = new Date().toISOString();
    
    await writeJSONFile(registryPath, registry);
  }

  async calculateDiff(assetId, currentVersionId) {
    const registry = await this.getVersionRegistry();
    const assetVersions = registry.assets[assetId];
    
    if (!assetVersions || assetVersions.versions.length < 2) {
      return null; // No previous version to compare
    }
    
    // Get previous version
    const versions = assetVersions.versions.sort((a, b) => 
      new Date(a.timestamp) - new Date(b.timestamp)
    );
    const currentIndex = versions.findIndex(v => v.versionId === currentVersionId);
    
    if (currentIndex <= 0) {
      return null; // No previous version
    }
    
    const previousVersionId = versions[currentIndex - 1].versionId;
    const [currentVersion, previousVersion] = await Promise.all([
      this.getVersion(assetId, currentVersionId),
      this.getVersion(assetId, previousVersionId)
    ]);
    
    return this.generateDetailedDiff(
      previousVersion.snapshot, 
      currentVersion.snapshot,
      {
        previousVersionId,
        currentVersionId,
        timestamp: new Date().toISOString()
      }
    );
  }

  generateDetailedDiff(oldData, newData, metadata = {}) {
    const diff = {
      ...metadata,
      changes: {
        added: {},
        modified: {},
        removed: {},
        unchanged: {}
      },
      summary: {
        totalChanges: 0,
        addedCount: 0,
        modifiedCount: 0,
        removedCount: 0
      }
    };
    
    // Simple diff implementation - could be enhanced with more sophisticated algorithms
    const oldKeys = new Set(Object.keys(oldData));
    const newKeys = new Set(Object.keys(newData));
    
    // Find added keys
    for (const key of newKeys) {
      if (!oldKeys.has(key)) {
        diff.changes.added[key] = newData[key];
        diff.summary.addedCount++;
      }
    }
    
    // Find removed keys
    for (const key of oldKeys) {
      if (!newKeys.has(key)) {
        diff.changes.removed[key] = oldData[key];
        diff.summary.removedCount++;
      }
    }
    
    // Find modified keys
    for (const key of newKeys) {
      if (oldKeys.has(key)) {
        if (JSON.stringify(oldData[key]) !== JSON.stringify(newData[key])) {
          diff.changes.modified[key] = {
            old: oldData[key],
            new: newData[key]
          };
          diff.summary.modifiedCount++;
        } else {
          diff.changes.unchanged[key] = newData[key];
        }
      }
    }
    
    diff.summary.totalChanges = diff.summary.addedCount + 
                               diff.summary.modifiedCount + 
                               diff.summary.removedCount;
    
    return diff;
  }

  async getVersionRegistry() {
    const registryPath = path.join(this.versionDir, 'version-registry.json');
    try {
      return await readJSONFile(registryPath);
    } catch {
      await this.initialize();
      return await readJSONFile(registryPath);
    }
  }

  async updateVersionRegistryAfterCleanup(assetId, deletedVersionIds) {
    const registryPath = path.join(this.versionDir, 'version-registry.json');
    const registry = await readJSONFile(registryPath);
    
    if (registry.assets[assetId]) {
      const assetData = registry.assets[assetId];
      assetData.versions = assetData.versions.filter(v => 
        !deletedVersionIds.includes(v.versionId)
      );
      assetData.totalVersions = assetData.versions.length;
      
      registry.totalVersions -= deletedVersionIds.length;
      registry.lastActivity = new Date().toISOString();
      
      await writeJSONFile(registryPath, registry);
    }
  }

  async calculateAssetStorageUsage(assetId) {
    const assetVersionPath = path.join(this.versionDir, assetId);
    
    try {
      const files = await fs.readdir(assetVersionPath);
      let totalSize = 0;
      
      for (const file of files) {
        const filePath = path.join(assetVersionPath, file);
        const stats = await fs.stat(filePath);
        totalSize += stats.size;
      }
      
      return totalSize;
    } catch {
      return 0;
    }
  }
}
