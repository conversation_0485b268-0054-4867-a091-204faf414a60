/**
 * Conversation State Manager
 * 
 * Manages the state of interactive onboarding conversations, including:
 * - Session creation and retrieval
 * - State transitions
 * - Answer storage and retrieval
 * - Navigation (back, skip, etc.)
 * - Progress tracking
 * 
 * This is the core state machine that powers the conversational onboarding experience.
 * It integrates with the existing SessionRecoveryManager to leverage the established
 * session management infrastructure.
 */

import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { getSessionRecoveryManager } from '../workflow-logic/session-recovery-manager.js';

// State constants
const CONVERSATION_STATES = {
  INITIALIZED: 'initialized',
  IN_PROGRESS: 'in_progress',
  SKIPPED_QUESTION: 'skipped_question',
  COMPLETED: 'completed',
  ABANDONED: 'abandoned',
  AWAITING_FEEDBACK: 'awaiting_feedback',
  PROCESSING_FEEDBACK: 'processing_feedback'
};

// Navigation actions
const NAVIGATION_ACTIONS = {
  NEXT: 'next',
  BACK: 'back',
  SKIP: 'skip',
  CHANGE: 'change',
  COMPLETE: 'complete',
  PROVIDE_FEEDBACK: 'provide_feedback',
  REQUEST_CHANGE: 'request_change'
};

export class ConversationStateManager {
  constructor(projectRoot) {
    this.projectRoot = projectRoot || process.cwd();
    this.guidantDir = path.join(this.projectRoot, '.guidant');
    this.conversationDir = path.join(this.guidantDir, 'context', 'conversations');
    this.sessionRecoveryManager = getSessionRecoveryManager(projectRoot);
    this.ensureDirectoriesExist();
  }

  /**
   * Ensure required directories exist
   */
  ensureDirectoriesExist() {
    if (!fs.existsSync(this.guidantDir)) {
      fs.mkdirSync(this.guidantDir, { recursive: true });
    }

    if (!fs.existsSync(this.conversationDir)) {
      fs.mkdirSync(this.conversationDir, { recursive: true });
    }

    // Ensure SessionRecoveryManager directories exist
    const contextDir = path.join(this.guidantDir, 'context');
    if (!fs.existsSync(contextDir)) {
      fs.mkdirSync(contextDir, { recursive: true });
    }
  }

  /**
   * Initialize a new conversation session
   * 
   * @param {Object} options - Session options
   * @param {string} options.projectIdea - Initial project idea
   * @param {string} options.projectType - Project type if known (optional)
   * @returns {Promise<Object>} Session object with ID and initial state
   */
  async initSession(options = {}) {
    const sessionId = uuidv4();
    const timestamp = new Date().toISOString();
    
    // Create conversation data specific to onboarding
    const conversationData = {
      id: sessionId,
      createdAt: timestamp,
      updatedAt: timestamp,
      state: CONVERSATION_STATES.INITIALIZED,
      currentQuestionIndex: 0,
      questionHistory: [],
      answers: {},
      projectIdea: options.projectIdea || '',
      projectType: options.projectType || null,
      skippedQuestions: [],
      navigationHistory: [],
      feedbackHistory: []
    };
    
    // Save conversation data to conversations directory
    await this.saveConversationData(conversationData);
    
    // Create a session in the main session recovery system
    const session = await this.sessionRecoveryManager.createSession({
      sessionId,
      projectName: options.projectIdea || 'New Project',
      currentPhase: 'onboarding',
      currentOrientation: `You're starting a new project: ${options.projectIdea || 'Untitled Project'}`,
      agentName: options.agentName || 'Guidant',
      agentId: options.agentId || 'onboarding-agent'
    });
    
    // Update the recovery prompt to indicate onboarding
    await this.sessionRecoveryManager.updateRecoveryPrompt(
      sessionId,
      `Welcome to your project! We're in the onboarding phase for: ${options.projectIdea || 'your new project'}`
    );
    
    // Return combined data
    return {
      ...session,
      conversationData
    };
  }

  /**
   * Get an existing session by ID
   * 
   * @param {string} sessionId - The session ID
   * @returns {Promise<Object|null>} Session object or null if not found
   */
  async getSession(sessionId) {
    try {
      // Get the main session data
      const session = await this.sessionRecoveryManager.getSession(sessionId);
      
      if (!session) {
        return null;
      }
      
      // Get the conversation-specific data
      const conversationData = await this.getConversationData(sessionId);
      
      if (!conversationData) {
        return session; // Return just the session if no conversation data
      }
      
      // Combine and return
      return {
        ...session,
        conversationData
      };
    } catch (error) {
      console.error(`Error reading session ${sessionId}:`, error);
      return null;
    }
  }

  /**
   * Get conversation-specific data
   * 
   * @param {string} sessionId - The session ID
   * @returns {Object|null} Conversation data or null if not found
   */
  getConversationData(sessionId) {
    const conversationPath = path.join(this.conversationDir, `${sessionId}.json`);
    
    if (!fs.existsSync(conversationPath)) {
      return null;
    }
    
    try {
      const conversationData = fs.readFileSync(conversationPath, 'utf8');
      return JSON.parse(conversationData);
    } catch (error) {
      console.error(`Error reading conversation data ${sessionId}:`, error);
      return null;
    }
  }

  /**
   * Save conversation data to disk
   * 
   * @param {Object} conversationData - The conversation data to save
   */
  saveConversationData(conversationData) {
    const conversationPath = path.join(this.conversationDir, `${conversationData.id}.json`);
    conversationData.updatedAt = new Date().toISOString();
    
    try {
      fs.writeFileSync(conversationPath, JSON.stringify(conversationData, null, 2), 'utf8');
    } catch (error) {
      console.error(`Error saving conversation data ${conversationData.id}:`, error);
      throw new Error(`Failed to save conversation data: ${error.message}`);
    }
  }
  
  /**
   * Save session with updated conversation data
   * 
   * @param {Object} session - The session object containing conversationData
   */
  async saveSession(session) {
    if (!session || !session.conversationData) {
      throw new Error('Invalid session or missing conversation data');
    }
    
    // Save the conversation-specific data
    this.saveConversationData(session.conversationData);
    
    // Update the main session's lastActive timestamp
    await this.sessionRecoveryManager.updateSession(
      session.sessionId,
      (currentSession) => ({
        ...currentSession,
        lastActive: new Date().toISOString()
      })
    );
  }

  /**
   * Record an answer to the current question
   * 
   * @param {string} sessionId - The session ID
   * @param {string} questionId - The question ID
   * @param {*} answer - The answer value
   * @returns {Promise<Object>} Updated session
   */
  async recordAnswer(sessionId, questionId, answer) {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Record the answer in conversation data
    session.conversationData.answers[questionId] = {
      value: answer,
      answeredAt: new Date().toISOString()
    };
    
    // Update session state
    session.conversationData.state = CONVERSATION_STATES.IN_PROGRESS;
    session.conversationData.questionHistory.push(questionId);
    
    // If this was a skipped question, remove it from skipped list
    session.conversationData.skippedQuestions = 
      session.conversationData.skippedQuestions.filter(q => q !== questionId);
    
    // Add to navigation history
    session.conversationData.navigationHistory.push({
      action: NAVIGATION_ACTIONS.NEXT,
      questionId,
      timestamp: new Date().toISOString()
    });
    
    // Update the main session's recovery data
    await this.sessionRecoveryManager.updateLastMessage(
      sessionId,
      `Answered question: ${questionId}`,
      `Current progress: ${Object.keys(session.conversationData.answers).length} questions answered`
    );
    
    // Update orientation info
    await this.sessionRecoveryManager.updateOrientation(
      sessionId,
      `You're building ${session.conversationData.projectIdea || 'a new project'}. ` +
      `You've answered ${Object.keys(session.conversationData.answers).length} questions so far.`
    );
    
    await this.saveSession(session);
    return session;
  }

  /**
   * Skip the current question
   * 
   * @param {string} sessionId - The session ID
   * @param {string} questionId - The question ID to skip
   * @returns {Promise<Object>} Updated session
   */
  async skipQuestion(sessionId, questionId) {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Add to skipped questions if not already there
    if (!session.conversationData.skippedQuestions.includes(questionId)) {
      session.conversationData.skippedQuestions.push(questionId);
    }
    
    // Update session state
    session.conversationData.state = CONVERSATION_STATES.SKIPPED_QUESTION;
    
    // Add to navigation history
    session.conversationData.navigationHistory.push({
      action: NAVIGATION_ACTIONS.SKIP,
      questionId,
      timestamp: new Date().toISOString()
    });
    
    // Update the main session's recovery data
    await this.sessionRecoveryManager.updateLastMessage(
      sessionId,
      `Skipped question: ${questionId}`,
      `You can come back to this question later`
    );
    
    await this.saveSession(session);
    return session;
  }

  /**
   * Go back to a previous question
   * 
   * @param {string} sessionId - The session ID
   * @param {string} questionId - The target question ID
   * @returns {Promise<Object>} Updated session
   */
  async goBack(sessionId, questionId) {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Add to navigation history
    session.conversationData.navigationHistory.push({
      action: NAVIGATION_ACTIONS.BACK,
      questionId,
      timestamp: new Date().toISOString()
    });
    
    // Update the main session's recovery data
    await this.sessionRecoveryManager.updateLastMessage(
      sessionId,
      `Going back to question: ${questionId}`,
      `Revisiting previous question`
    );
    
    await this.saveSession(session);
    return session;
  }

  /**
   * Change a previously answered question
   * 
   * @param {string} sessionId - The session ID
   * @param {string} questionId - The question ID to change
   * @param {*} newAnswer - The new answer
   * @returns {Promise<Object>} Updated session
   */
  async changeAnswer(sessionId, questionId, newAnswer) {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Update the answer
    session.conversationData.answers[questionId] = {
      value: newAnswer,
      answeredAt: new Date().toISOString(),
      changedAt: new Date().toISOString(),
      previousValue: session.conversationData.answers[questionId]?.value
    };
    
    // Add to navigation history
    session.conversationData.navigationHistory.push({
      action: NAVIGATION_ACTIONS.CHANGE,
      questionId,
      timestamp: new Date().toISOString()
    });
    
    // Update the main session's recovery data
    await this.sessionRecoveryManager.updateLastMessage(
      sessionId,
      `Changed answer to question: ${questionId}`,
      `Updated previous response`
    );
    
    await this.saveSession(session);
    return session;
  }

  /**
   * Complete the conversation
   * 
   * @param {string} sessionId - The session ID
   * @returns {Promise<Object>} Completed session
   */
  async completeConversation(sessionId) {
    const session = await this.getSession(sessionId);
    
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Update session state
    session.conversationData.state = CONVERSATION_STATES.COMPLETED;
    session.conversationData.completedAt = new Date().toISOString();
    
    // Add to navigation history
    session.conversationData.navigationHistory.push({
      action: NAVIGATION_ACTIONS.COMPLETE,
      timestamp: new Date().toISOString()
    });
    
    // Update the main session's recovery data and orientation
    await this.sessionRecoveryManager.updateLastMessage(
      sessionId,
      `Completed onboarding conversation`,
      `Ready to begin development`
    );
    
    await this.sessionRecoveryManager.updateOrientation(
      sessionId,
      `You've completed the onboarding process for ${session.conversationData.projectIdea || 'your new project'}. ` +
      `You're now ready to begin development.`
    );
    
    await this.sessionRecoveryManager.updateRecoveryPrompt(
      sessionId,
      `Welcome back to ${session.conversationData.projectIdea || 'your project'}! ` +
      `You've completed onboarding and are ready to start development.`
    );
    
    await this.saveSession(session);
    return session;
  }

  /**
   * Record feedback or a change request from the user
   *
   * @param {string} sessionId - The session ID
   * @param {string} feedbackType - Type of feedback (e.g., 'general_feedback', 'change_request')
   * @param {Object} feedbackData - The feedback content and related context
   * @param {string} feedbackData.text - The user's textual feedback
   * @param {string} [feedbackData.targetElementId] - Optional ID of the UI element the feedback refers to
   * @param {Object} [feedbackData.context] - Optional additional context for the feedback
   * @returns {Promise<Object>} Updated session
   */
  async recordFeedback(sessionId, feedbackType, feedbackData) {
    const session = await this.getSession(sessionId);

    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    session.conversationData.feedbackHistory.push({
      type: feedbackType,
      ...feedbackData,
      recordedAt: new Date().toISOString()
    });

    // Potentially update state based on feedback type
    if (feedbackType === 'request_change') {
      session.conversationData.state = CONVERSATION_STATES.PROCESSING_FEEDBACK; // Or a more specific state
    } else {
      session.conversationData.state = CONVERSATION_STATES.AWAITING_FEEDBACK; // Or back to IN_PROGRESS
    }
    
    session.conversationData.navigationHistory.push({
      action: feedbackType === 'request_change' ? NAVIGATION_ACTIONS.REQUEST_CHANGE : NAVIGATION_ACTIONS.PROVIDE_FEEDBACK,
      details: feedbackData.text,
      timestamp: new Date().toISOString()
    });

    await this.sessionRecoveryManager.updateLastMessage(
      sessionId,
      `Feedback received: ${feedbackData.text.substring(0, 50)}...`,
      `Feedback type: ${feedbackType}`
    );

    await this.saveSession(session);
    return session;
  }

  /**
   * Get all conversation sessions
   * 
   * @returns {Promise<Array>} Array of session objects
   */
  async getAllSessions() {
    try {
      // Get all conversation files
      const conversationFiles = fs.readdirSync(this.conversationDir)
        .filter(file => file.endsWith('.json'));
      
      // Get all session IDs from conversation files
      const sessionIds = conversationFiles.map(file => file.replace('.json', ''));
      
      // Get full session data for each ID
      const sessions = [];
      for (const sessionId of sessionIds) {
        const session = await this.getSession(sessionId);
        if (session) {
          sessions.push(session);
        }
      }
      
      return sessions;
    } catch (error) {
      console.error('Error reading sessions:', error);
      return [];
    }
  }

  /**
   * Get the most recent session
   * 
   * @returns {Promise<Object|null>} Most recent session or null if none found
   */
  async getMostRecentSession() {
    try {
      // First try to get the most recent session from the session recovery manager
      const recoveryInfo = await this.sessionRecoveryManager.getRecoveryInfo();
      
      if (recoveryInfo && recoveryInfo.sessionId) {
        const session = await this.getSession(recoveryInfo.sessionId);
        if (session) {
          return session;
        }
      }
      
      // Fallback to checking conversation files
      const conversationFiles = fs.readdirSync(this.conversationDir)
        .filter(file => file.endsWith('.json'));
      
      if (conversationFiles.length === 0) {
        return null;
      }
      
      // Get file stats to find the most recently modified file
      const fileStats = conversationFiles.map(file => ({
        file,
        stats: fs.statSync(path.join(this.conversationDir, file))
      }));
      
      // Sort by modification time (most recent first)
      fileStats.sort((a, b) => b.stats.mtime - a.stats.mtime);
      
      // Get the session for the most recent file
      const mostRecentSessionId = fileStats[0].file.replace('.json', '');
      return await this.getSession(mostRecentSessionId);
    } catch (error) {
      console.error('Error getting most recent session:', error);
      return null;
    }
  }
}

export default ConversationStateManager;
export { CONVERSATION_STATES, NAVIGATION_ACTIONS }; 