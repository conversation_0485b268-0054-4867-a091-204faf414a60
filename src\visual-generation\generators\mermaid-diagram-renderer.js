/**
 * Mermaid Diagram Renderer Integration
 * Integrates Mermaid diagram generation with requirements-to-design transformer
 * while maintaining backward compatibility
 */

import { UserFlowMappingEngine } from './user-flow-mapping-engine.js';
import { MermaidSyntaxGenerator } from './mermaid-syntax-generator.js';
import { InteractiveElementGenerator } from './interactive-element-generator.js';
import { VisualIntelligenceCache } from '../research/visual-intelligence-cache.js';
import { VisualAssetManager } from '../management/visual-asset-manager.js';
import { DiagramValidator } from '../validation/diagram-validator.js';

/**
 * Mermaid Diagram Renderer
 * Bridges the gap between user flow generation and Mermaid diagram visualization
 */
export class MermaidDiagramRenderer {
  constructor(config = {}) {
    this.config = {
      enableDiagramGeneration: config.enableDiagramGeneration !== false,
      enableCaching: config.enableCaching !== false,
      generateMultipleDiagramTypes: config.generateMultipleDiagramTypes || false,
      defaultDiagramType: config.defaultDiagramType || 'flowchart',
      enhanceWithIntelligence: config.enhanceWithIntelligence !== false,
      saveToDeliverables: config.saveToDeliverables !== false,
      enableInteractiveElements: config.enableInteractiveElements !== false,
      enableAssetManagement: config.enableAssetManagement !== false,
      enableValidation: config.enableValidation !== false,
      enableOptimization: config.enableOptimization !== false,
      ...config
    };

    // Initialize components
    this.flowMappingEngine = new UserFlowMappingEngine({
      enableIntelligentMapping: this.config.enhanceWithIntelligence,
      includeErrorPaths: true,
      includeDecisionPoints: true,
      generateMultipleDiagramTypes: this.config.generateMultipleDiagramTypes
    });

    this.mermaidGenerator = new MermaidSyntaxGenerator({
      includeErrorPaths: true,
      includeDecisionPoints: true,
      enableStyling: true
    });

    // Initialize interactive element generator if enabled
    if (this.config.enableInteractiveElements) {
      this.interactiveGenerator = new InteractiveElementGenerator({
        enableClickEvents: config.enableClickEvents !== false,
        enableTooltips: config.enableTooltips !== false,
        enableNavigation: config.enableNavigation !== false,
        enableHighlighting: config.enableHighlighting !== false,
        enableAnimations: config.enableAnimations || false
      });
    }

    // Initialize visual intelligence cache if enabled
    if (this.config.enableCaching) {
      this.visualCache = new VisualIntelligenceCache({
        maxCacheSize: config.maxCacheSize || 200,
        enableQualityScoring: true,
        enablePerformanceMonitoring: true
      });
    }

    // Initialize visual asset manager if enabled
    if (this.config.enableAssetManagement) {
      this.assetManager = new VisualAssetManager({
        baseDirectory: config.baseDirectory || '.guidant/deliverables',
        enableVersioning: config.enableVersioning !== false,
        enableMetadata: config.enableMetadata !== false,
        enableSearch: config.enableSearch !== false
      });
    }

    // Initialize diagram validator if enabled
    if (this.config.enableValidation) {
      this.diagramValidator = new DiagramValidator({
        enableSyntaxValidation: config.enableSyntaxValidation !== false,
        enableLayoutOptimization: config.enableLayoutOptimization !== false,
        enableReadabilityAnalysis: config.enableReadabilityAnalysis !== false,
        enableQualityScoring: config.enableQualityScoring !== false,
        maxNodeCount: config.maxNodeCount || 50,
        maxConnectionCount: config.maxConnectionCount || 100,
        minReadabilityScore: config.minReadabilityScore || 0.7
      });
    }

    this.renderingStats = {
      totalRenders: 0,
      cacheHits: 0,
      diagramsGenerated: 0,
      flowsMapped: 0,
      averageRenderTime: 0
    };
  }

  /**
   * Enhanced user flow generation that integrates with existing generateUserFlows method
   */
  async enhanceUserFlows(userFlows, userStories = [], functionalRequirements = [], context = {}) {
    if (!this.config.enableDiagramGeneration) {
      return userFlows; // Return original flows if diagram generation disabled
    }

    const startTime = Date.now();
    this.renderingStats.totalRenders++;

    try {
      const enhancedFlows = [];

      // Process existing user flows
      for (const userFlow of userFlows) {
        const enhanced = await this.enhanceIndividualUserFlow(userFlow, context);
        enhancedFlows.push(enhanced);
      }

      // Generate additional flows from user stories and requirements if provided
      if (userStories.length > 0 || functionalRequirements.length > 0) {
        const additionalFlowsResult = await this.flowMappingEngine.mapUserFlows(
          userStories, 
          functionalRequirements, 
          context
        );

        // Add mapped flows to enhanced flows
        for (const mappedFlow of additionalFlowsResult.flows) {
          const enhanced = await this.enhanceIndividualUserFlow(mappedFlow, context);
          enhancedFlows.push(enhanced);
          this.renderingStats.flowsMapped++;
        }
      }

      // Update performance stats
      const renderTime = Date.now() - startTime;
      this.renderingStats.averageRenderTime = 
        (this.renderingStats.averageRenderTime * (this.renderingStats.totalRenders - 1) + renderTime) / 
        this.renderingStats.totalRenders;

      this.renderingStats.diagramsGenerated += enhancedFlows.length;

      return enhancedFlows;

    } catch (error) {
      console.warn('Mermaid diagram enhancement failed, returning original flows:', error.message);
      return userFlows; // Graceful fallback
    }
  }

  /**
   * Enhance individual user flow with Mermaid diagram
   */
  async enhanceIndividualUserFlow(userFlow, context) {
    // Generate cache key for this user flow
    const cacheKey = this.generateFlowCacheKey(userFlow, context);

    // Try cache first if enabled
    if (this.config.enableCaching && this.visualCache) {
      const cached = await this.visualCache.get(cacheKey, context);
      if (cached) {
        this.renderingStats.cacheHits++;
        return cached;
      }
    }

    // Generate Mermaid diagrams
    const mermaidDiagrams = await this.generateMermaidDiagrams(userFlow, context);

    // Create enhanced user flow
    const enhancedUserFlow = {
      ...userFlow,
      // Maintain backward compatibility - all original fields preserved
      mermaidDiagrams: mermaidDiagrams,
      visualRepresentation: {
        type: 'mermaid',
        diagrams: mermaidDiagrams,
        metadata: {
          generatedAt: new Date().toISOString(),
          renderingEngine: 'MermaidDiagramRenderer',
          diagramTypes: Object.keys(mermaidDiagrams),
          enhanced: true
        }
      },
      // Add visual metadata without breaking existing structure
      metadata: {
        ...userFlow.metadata,
        hasMermaidDiagrams: true,
        diagramGenerationEnabled: true,
        renderingStats: {
          diagramCount: Object.keys(mermaidDiagrams).length,
          renderTime: Date.now()
        }
      }
    };

    // Apply intelligence enhancements if enabled
    if (this.config.enhanceWithIntelligence) {
      await this.applyIntelligenceEnhancements(enhancedUserFlow, context);
    }

    // Cache the enhanced user flow
    if (this.config.enableCaching && this.visualCache) {
      await this.visualCache.set(cacheKey, enhancedUserFlow, {
        requestType: 'user_flow_enhancement',
        provider: 'mermaid_diagram_renderer',
        timestamp: Date.now()
      });
    }

    return enhancedUserFlow;
  }

  /**
   * Generate Mermaid diagrams for user flow
   */
  async generateMermaidDiagrams(userFlow, context) {
    const diagrams = {};

    if (this.config.generateMultipleDiagramTypes) {
      // Generate multiple diagram types
      const multipleDiagrams = this.mermaidGenerator.generateMultipleDiagrams(userFlow);
      
      diagrams.flowchart = multipleDiagrams.flowchart;
      diagrams.sequence = multipleDiagrams.sequence;
      diagrams.state = multipleDiagrams.state;
      diagrams.metadata = multipleDiagrams.metadata;
    } else {
      // Generate single diagram type
      const diagramType = context.diagramType || this.config.defaultDiagramType;
      const diagram = this.mermaidGenerator.generateFromUserFlow(userFlow, diagramType);
      
      diagrams[diagramType] = diagram;
      diagrams.metadata = {
        userFlowId: userFlow.id,
        title: userFlow.title,
        generatedAt: new Date().toISOString(),
        diagramTypes: [diagramType]
      };
    }

    // Add interactive elements if enabled
    if (this.config.enableInteractiveElements && this.interactiveGenerator) {
      for (const [type, diagram] of Object.entries(diagrams)) {
        if (type !== 'metadata') {
          diagrams[type] = this.interactiveGenerator.addInteractiveElements(
            diagram,
            userFlow,
            { diagramType: type, ...context }
          );
        }
      }
    }

    // Validate and optimize generated diagrams
    if (this.config.enableValidation && this.diagramValidator) {
      for (const [type, diagram] of Object.entries(diagrams)) {
        if (type !== 'metadata') {
          const validation = await this.diagramValidator.validateDiagram(diagram, type, {
            flowId: userFlow.id,
            persona: userFlow.persona,
            complexity: userFlow.complexity
          });

          // Store validation results in metadata
          if (!diagrams.metadata.validation) {
            diagrams.metadata.validation = {};
          }
          diagrams.metadata.validation[type] = validation;

          // Apply optimizations if enabled and needed
          if (this.config.enableOptimization &&
              (validation.qualityScore < 0.8 || validation.readabilityScore < 0.8)) {
            const optimization = this.diagramValidator.optimizeDiagram(diagram, type, validation);
            if (optimization.optimizations.length > 0) {
              diagrams[type] = optimization.optimizedCode;
              diagrams.metadata.validation[type].optimizations = optimization.optimizations;
              diagrams.metadata.validation[type].improvementScore = optimization.improvementScore;
              console.log(`✅ Optimized ${type} diagram for flow ${userFlow.id}: ${optimization.optimizations.join(', ')}`);
            }
          }

          // Log validation results
          if (!validation.isValid) {
            console.warn(`⚠️ Validation issues in ${type} diagram for flow ${userFlow.id}:`, validation.errors);
          } else if (validation.warnings.length > 0) {
            console.log(`ℹ️ Validation warnings for ${type} diagram for flow ${userFlow.id}:`, validation.warnings);
          }
        }
      }
    } else {
      // Fallback to basic syntax validation
      for (const [type, diagram] of Object.entries(diagrams)) {
        if (type !== 'metadata') {
          const validation = this.mermaidGenerator.validateSyntax(diagram);
          if (!validation.isValid) {
            console.warn(`Invalid ${type} diagram generated for flow ${userFlow.id}:`, validation.errors);
          }
        }
      }
    }

    return diagrams;
  }

  /**
   * Apply intelligence enhancements to user flow
   */
  async applyIntelligenceEnhancements(userFlow, context) {
    try {
      // Add flow complexity analysis
      if (userFlow.complexity) {
        userFlow.visualRepresentation.complexity = {
          level: userFlow.complexity,
          analysis: this.analyzeFlowComplexity(userFlow)
        };
      }

      // Add persona-specific styling
      if (userFlow.persona) {
        userFlow.visualRepresentation.personaSpecific = {
          persona: userFlow.persona,
          styling: this.getPersonaSpecificStyling(userFlow.persona)
        };
      }

      // Add interaction indicators
      if (userFlow.decision_points && userFlow.decision_points.length > 0) {
        userFlow.visualRepresentation.interactions = {
          decisionPoints: userFlow.decision_points.length,
          complexity: userFlow.decision_points.length > 3 ? 'high' : 'medium'
        };
      }

      // Add error handling indicators
      if (userFlow.error_handling && userFlow.error_handling.length > 0) {
        userFlow.visualRepresentation.errorHandling = {
          pathCount: userFlow.error_handling.length,
          robustness: userFlow.error_handling.length > 2 ? 'high' : 'medium'
        };
      }

      userFlow.visualRepresentation.metadata.enhanced = true;

    } catch (error) {
      console.warn('Intelligence enhancement failed:', error.message);
    }
  }

  /**
   * Analyze flow complexity
   */
  analyzeFlowComplexity(userFlow) {
    const stepCount = userFlow.steps ? userFlow.steps.length : 0;
    const decisionCount = userFlow.decision_points ? userFlow.decision_points.length : 0;
    const errorCount = userFlow.error_handling ? userFlow.error_handling.length : 0;

    const analysis = {
      stepComplexity: stepCount <= 5 ? 'low' : stepCount <= 10 ? 'medium' : 'high',
      decisionComplexity: decisionCount <= 2 ? 'low' : decisionCount <= 5 ? 'medium' : 'high',
      errorComplexity: errorCount <= 2 ? 'low' : errorCount <= 4 ? 'medium' : 'high',
      overallScore: stepCount + (decisionCount * 2) + (errorCount * 1.5)
    };

    analysis.recommendation = analysis.overallScore <= 10 ? 
      'Simple flow, easy to understand' :
      analysis.overallScore <= 20 ? 
      'Moderate complexity, consider simplification' :
      'High complexity, recommend breaking into smaller flows';

    return analysis;
  }

  /**
   * Get persona-specific styling
   */
  getPersonaSpecificStyling(persona) {
    const personaStyles = {
      'Administrator': { color: '#ff6b6b', icon: '👨‍💼' },
      'Manager': { color: '#4ecdc4', icon: '👩‍💼' },
      'Customer': { color: '#45b7d1', icon: '👤' },
      'Developer': { color: '#96ceb4', icon: '👨‍💻' },
      'User': { color: '#feca57', icon: '👥' }
    };

    return personaStyles[persona] || personaStyles['User'];
  }

  /**
   * Generate cache key for user flow
   */
  generateFlowCacheKey(userFlow, context) {
    const keyData = {
      flowId: userFlow.id,
      title: userFlow.title,
      steps: userFlow.steps?.length || 0,
      decisions: userFlow.decision_points?.length || 0,
      diagramType: context.diagramType || this.config.defaultDiagramType,
      multiple: this.config.generateMultipleDiagramTypes
    };

    return `mermaid_flow_${JSON.stringify(keyData)}`.replace(/[^a-zA-Z0-9_]/g, '_');
  }

  /**
   * Get rendering statistics
   */
  getRenderingStats() {
    const baseStats = {
      ...this.renderingStats,
      cacheHitRate: this.renderingStats.totalRenders > 0 ?
        this.renderingStats.cacheHits / this.renderingStats.totalRenders : 0,
      diagramsPerFlow: this.renderingStats.flowsMapped > 0 ?
        this.renderingStats.diagramsGenerated / this.renderingStats.flowsMapped : 0
    };

    // Add interactive element statistics if available
    if (this.interactiveGenerator) {
      baseStats.interactiveElements = this.interactiveGenerator.getInteractiveElementsSummary();
    }

    // Add validation statistics if available
    if (this.diagramValidator) {
      baseStats.validation = this.diagramValidator.getValidationStats();
    }

    // Add asset management statistics if available
    if (this.assetManager) {
      baseStats.assetManagement = this.assetManager.getManagementStats();
    }

    return baseStats;
  }

  /**
   * Generate flow comparison with Mermaid diagrams
   */
  async generateFlowComparison(userFlows, context = {}) {
    const enhancedFlows = await this.enhanceUserFlows(userFlows, [], [], context);
    
    let comparison = '# User Flow Diagram Comparison\n\n';
    
    for (let i = 0; i < enhancedFlows.length; i++) {
      const flow = enhancedFlows[i];
      comparison += `## ${i + 1}. ${flow.title}\n\n`;
      
      // Add flow metadata
      comparison += `**Persona**: ${flow.persona || 'User'}  \n`;
      comparison += `**Complexity**: ${flow.complexity || 'medium'}  \n`;
      comparison += `**Steps**: ${flow.steps?.length || 0}  \n`;
      comparison += `**Decision Points**: ${flow.decision_points?.length || 0}  \n`;
      comparison += `**Error Paths**: ${flow.error_handling?.length || 0}  \n\n`;
      
      // Add Mermaid diagrams
      if (flow.mermaidDiagrams) {
        for (const [type, diagram] of Object.entries(flow.mermaidDiagrams)) {
          if (type !== 'metadata') {
            comparison += `### ${type.charAt(0).toUpperCase() + type.slice(1)} Diagram\n\n`;
            comparison += '```mermaid\n';
            comparison += diagram;
            comparison += '\n```\n\n';
          }
        }
      }
      
      comparison += '---\n\n';
    }
    
    return comparison;
  }

  /**
   * Save Mermaid diagrams using asset manager or traditional file system
   */
  async saveFlowDiagrams(userFlows, projectPath = process.cwd()) {
    if (!this.config.saveToDeliverables) {
      return { success: false, message: 'Save to deliverables disabled' };
    }

    // Use asset manager if enabled
    if (this.config.enableAssetManagement && this.assetManager) {
      return await this.saveFlowDiagramsWithAssetManager(userFlows);
    }

    // Fallback to traditional file system approach
    return await this.saveFlowDiagramsTraditional(userFlows, projectPath);
  }

  /**
   * Save diagrams using Visual Asset Manager
   */
  async saveFlowDiagramsWithAssetManager(userFlows) {
    try {
      const savedAssets = [];
      const timestamp = new Date().toISOString();

      // Save individual flow diagrams
      for (let i = 0; i < userFlows.length; i++) {
        const flow = userFlows[i];

        if (flow.mermaidDiagrams) {
          for (const [type, diagram] of Object.entries(flow.mermaidDiagrams)) {
            if (type !== 'metadata') {
              const assetMetadata = {
                title: `${flow.title} - ${type.charAt(0).toUpperCase() + type.slice(1)}`,
                description: `${type} diagram for ${flow.title}`,
                flowId: flow.id,
                diagramType: type,
                persona: flow.persona,
                complexity: flow.complexity,
                createdAt: timestamp,
                source: 'mermaid_diagram_renderer'
              };

              const result = await this.assetManager.storeAsset(diagram, 'mermaid', assetMetadata);
              if (result.success) {
                savedAssets.push({
                  flow: flow.id,
                  type,
                  assetId: result.assetId,
                  version: result.version,
                  filePath: result.filePath
                });
              }
            }
          }
        }
      }

      // Save interactive JavaScript helpers if available
      if (this.interactiveGenerator) {
        const jsHelpers = this.generateInteractiveHelpers();
        const helpersMetadata = {
          title: 'Interactive Mermaid Helpers',
          description: 'JavaScript helper functions for interactive Mermaid diagrams',
          createdAt: timestamp,
          source: 'interactive_element_generator'
        };

        const helpersResult = await this.assetManager.storeAsset(jsHelpers, 'javascript', helpersMetadata);
        if (helpersResult.success) {
          savedAssets.push({
            type: 'javascript_helpers',
            assetId: helpersResult.assetId,
            version: helpersResult.version,
            filePath: helpersResult.filePath
          });
        }
      }

      // Save comparison view if multiple flows
      if (userFlows.length > 1) {
        const comparisonContent = await this.generateFlowComparison(userFlows);
        const comparisonMetadata = {
          title: 'User Flow Comparison',
          description: `Comparison of ${userFlows.length} user flows`,
          flowCount: userFlows.length,
          createdAt: timestamp,
          source: 'mermaid_diagram_renderer'
        };

        const comparisonResult = await this.assetManager.storeAsset(comparisonContent, 'comparison', comparisonMetadata);
        if (comparisonResult.success) {
          savedAssets.push({
            type: 'comparison',
            assetId: comparisonResult.assetId,
            version: comparisonResult.version,
            filePath: comparisonResult.filePath
          });
        }
      }

      console.log(`✅ Saved ${savedAssets.length} visual assets using Asset Manager`);

      return {
        success: true,
        savedAssets,
        totalAssets: savedAssets.length,
        managementStats: this.assetManager.getManagementStats()
      };

    } catch (error) {
      console.error('Failed to save diagrams with asset manager:', error.message);
      return {
        success: false,
        error: error.message,
        savedAssets: []
      };
    }
  }

  /**
   * Traditional file system save (fallback)
   */
  async saveFlowDiagramsTraditional(userFlows, projectPath) {

    const fs = await import('fs/promises');
    const path = await import('path');

    const deliverablesPath = path.join(projectPath, '.guidant', 'deliverables', 'user-flows');
    
    try {
      // Ensure deliverables directory exists
      await fs.mkdir(deliverablesPath, { recursive: true });

      const savedFiles = [];
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

      // Save individual flow diagrams
      for (let i = 0; i < userFlows.length; i++) {
        const flow = userFlows[i];
        
        if (flow.mermaidDiagrams) {
          for (const [type, diagram] of Object.entries(flow.mermaidDiagrams)) {
            if (type !== 'metadata') {
              const filename = `flow-${flow.id || i + 1}-${type}-${timestamp}.mmd`;
              const filepath = path.join(deliverablesPath, filename);
              
              let content = `%% ${flow.title} - ${type.charAt(0).toUpperCase() + type.slice(1)} Diagram\n`;
              content += `%% Generated: ${new Date().toISOString()}\n`;
              content += `%% Persona: ${flow.persona || 'User'}\n`;
              content += `%% Complexity: ${flow.complexity || 'medium'}\n\n`;
              content += diagram;
              
              await fs.writeFile(filepath, content, 'utf8');
              savedFiles.push({ 
                flow: flow.id, 
                type, 
                file: filename, 
                path: filepath 
              });
            }
          }
        }
      }

      // Save comparison view
      if (userFlows.length > 1) {
        const comparisonContent = await this.generateFlowComparison(userFlows);
        const comparisonFilename = `user-flows-comparison-${timestamp}.md`;
        const comparisonPath = path.join(deliverablesPath, comparisonFilename);
        
        await fs.writeFile(comparisonPath, comparisonContent, 'utf8');
        savedFiles.push({ 
          type: 'comparison', 
          file: comparisonFilename, 
          path: comparisonPath 
        });
      }

      console.log(`✅ Saved ${savedFiles.length} Mermaid diagram files to ${deliverablesPath}`);
      
      return {
        success: true,
        savedFiles,
        deliverablesPath,
        totalFiles: savedFiles.length
      };

    } catch (error) {
      console.error('Failed to save Mermaid diagrams:', error.message);
      return {
        success: false,
        error: error.message,
        savedFiles: []
      };
    }
  }

  /**
   * Generate JavaScript helpers for interactive elements
   */
  generateInteractiveHelpers() {
    if (!this.interactiveGenerator) {
      return '// Interactive elements not enabled';
    }

    return this.interactiveGenerator.generateJavaScriptHelpers();
  }

  /**
   * Generate enhanced flow comparison with interactive elements
   */
  async generateInteractiveFlowComparison(userFlows, context = {}) {
    // Enable interactive elements for comparison
    const originalConfig = this.config.enableInteractiveElements;
    this.config.enableInteractiveElements = true;

    try {
      const comparison = await this.generateFlowComparison(userFlows, context);

      // Add JavaScript helpers to the comparison
      const jsHelpers = this.generateInteractiveHelpers();

      let enhancedComparison = comparison;
      enhancedComparison += '\n\n## Interactive Features\n\n';
      enhancedComparison += 'The diagrams above include interactive elements:\n';
      enhancedComparison += '- **Click Events**: Click on nodes to see detailed information\n';
      enhancedComparison += '- **Tooltips**: Hover over nodes for quick details\n';
      enhancedComparison += '- **Navigation**: Navigate between related flows\n';
      enhancedComparison += '- **Highlighting**: Important nodes are visually emphasized\n\n';

      enhancedComparison += '### JavaScript Helpers\n\n';
      enhancedComparison += '```javascript\n';
      enhancedComparison += jsHelpers;
      enhancedComparison += '\n```\n';

      return enhancedComparison;
    } finally {
      // Restore original configuration
      this.config.enableInteractiveElements = originalConfig;
    }
  }

  /**
   * Search visual assets using asset manager
   */
  async searchVisualAssets(criteria = {}) {
    if (!this.assetManager) {
      throw new Error('Asset management not enabled');
    }

    return await this.assetManager.searchAssets(criteria);
  }

  /**
   * List all visual assets
   */
  async listVisualAssets(options = {}) {
    if (!this.assetManager) {
      throw new Error('Asset management not enabled');
    }

    return await this.assetManager.listAssets(options);
  }

  /**
   * Retrieve visual asset by ID
   */
  async retrieveVisualAsset(assetId, version = null) {
    if (!this.assetManager) {
      throw new Error('Asset management not enabled');
    }

    return await this.assetManager.retrieveAsset(assetId, version);
  }

  /**
   * Delete visual asset
   */
  async deleteVisualAsset(assetId, deleteVersions = true) {
    if (!this.assetManager) {
      throw new Error('Asset management not enabled');
    }

    return await this.assetManager.deleteAsset(assetId, deleteVersions);
  }

  /**
   * Export visual assets
   */
  async exportVisualAssets(exportPath, criteria = {}) {
    if (!this.assetManager) {
      throw new Error('Asset management not enabled');
    }

    return await this.assetManager.exportAssets(exportPath, criteria);
  }

  /**
   * Get visual asset management statistics
   */
  getAssetManagementStats() {
    if (!this.assetManager) {
      return { enabled: false, message: 'Asset management not enabled' };
    }

    return {
      enabled: true,
      ...this.assetManager.getManagementStats()
    };
  }

  /**
   * Validate specific diagram
   */
  async validateDiagram(mermaidCode, diagramType, context = {}) {
    if (!this.diagramValidator) {
      throw new Error('Diagram validation not enabled');
    }

    return await this.diagramValidator.validateDiagram(mermaidCode, diagramType, context);
  }

  /**
   * Optimize specific diagram
   */
  async optimizeDiagram(mermaidCode, diagramType, validationResult = null) {
    if (!this.diagramValidator) {
      throw new Error('Diagram validation not enabled');
    }

    // Validate first if no validation result provided
    if (!validationResult) {
      validationResult = await this.diagramValidator.validateDiagram(mermaidCode, diagramType);
    }

    return this.diagramValidator.optimizeDiagram(mermaidCode, diagramType, validationResult);
  }

  /**
   * Get validation statistics
   */
  getValidationStats() {
    if (!this.diagramValidator) {
      return { enabled: false, message: 'Diagram validation not enabled' };
    }

    return {
      enabled: true,
      ...this.diagramValidator.getValidationStats()
    };
  }

  /**
   * Validate and enhance user flows with comprehensive validation
   */
  async validateAndEnhanceUserFlows(userFlows, userStories = [], functionalRequirements = [], options = {}) {
    // First enhance with standard process
    const enhancedFlows = await this.enhanceUserFlows(userFlows, userStories, functionalRequirements, options);

    // Then apply additional validation and optimization if enabled
    if (this.config.enableValidation && this.diagramValidator) {
      for (const flow of enhancedFlows) {
        if (flow.mermaidDiagrams) {
          const validationResults = {};
          const optimizationResults = {};

          for (const [type, diagram] of Object.entries(flow.mermaidDiagrams)) {
            if (type !== 'metadata') {
              // Validate diagram
              const validation = await this.validateDiagram(diagram, type, {
                flowId: flow.id,
                persona: flow.persona,
                complexity: flow.complexity
              });

              validationResults[type] = validation;

              // Optimize if needed and enabled
              if (this.config.enableOptimization &&
                  (validation.qualityScore < 0.8 || validation.readabilityScore < 0.8)) {
                const optimization = await this.optimizeDiagram(diagram, type, validation);
                if (optimization.optimizations.length > 0) {
                  flow.mermaidDiagrams[type] = optimization.optimizedCode;
                  optimizationResults[type] = optimization;
                }
              }
            }
          }

          // Add validation and optimization results to flow metadata
          if (!flow.visualRepresentation) {
            flow.visualRepresentation = {};
          }
          flow.visualRepresentation.validation = validationResults;
          if (Object.keys(optimizationResults).length > 0) {
            flow.visualRepresentation.optimization = optimizationResults;
          }
        }
      }
    }

    return enhancedFlows;
  }
}
