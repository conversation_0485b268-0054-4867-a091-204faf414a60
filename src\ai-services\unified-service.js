/**
 * @file unified-service.js
 * @description Mock AI service for testing
 */

/**
 * Generate text using AI
 * @param {Object} params - Parameters for text generation
 * @returns {Object} Result with generated text
 */
export async function generateTextService(params) {
  return {
    mainResult: {
      text: 'AI-translated business description'
    },
    telemetryData: {
      timestamp: new Date().toISOString(),
      modelUsed: 'mock-model',
      inputTokens: 10,
      outputTokens: 5,
      totalTokens: 15,
      totalCost: 0
    }
  };
}

/**
 * Generate object using AI
 * @param {Object} params - Parameters for object generation
 * @returns {Object} Result with generated object
 */
export async function generateObjectService(params) {
  return {
    mainResult: {
      object: {
        title: 'Mock Title',
        description: 'Mock Description',
        businessImpact: {
          level: 'medium',
          description: 'Moderate impact on business operations'
        },
        timeImplication: {
          level: 'medium',
          description: 'Standard implementation timeline'
        },
        costImplication: {
          level: 'medium',
          description: 'Average cost implications'
        },
        riskAssessment: {
          level: 'medium',
          description: 'Balanced risk profile'
        }
      }
    },
    telemetryData: {
      timestamp: new Date().toISOString(),
      modelUsed: 'mock-model',
      inputTokens: 20,
      outputTokens: 10,
      totalTokens: 30,
      totalCost: 0
    }
  };
}

export default {
  generateTextService,
  generateObjectService
}; 