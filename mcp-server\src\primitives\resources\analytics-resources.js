/**
 * Analytics Resources Handler
 * Handles MCP Resource requests for analytics and insights data
 */

import { URITemplate<PERSON><PERSON>er, MCPResourceURI } from '../../utils/uri-template-parser.js';
import { RESOURCE_TEMPLATES } from '../../schemas/resource-schemas.js';

/**
 * Analytics Resource Handler
 * Converts existing analytics tools to MCP Resources
 */
export class AnalyticsResourceHandler {
  constructor(existingAnalyticsManager) {
    this.analyticsManager = existingAnalyticsManager;
    this.templates = {
      'project_insights': RESOURCE_TEMPLATES.project_insights,
      'performance_metrics': RESOURCE_TEMPLATES.performance_metrics,
      'quality_assessment': RESOURCE_TEMPLATES.quality_assessment
    };
  }

  /**
   * Get resource templates for this handler
   * @returns {Array} Array of resource templates
   */
  getResourceTemplates() {
    return Object.values(this.templates);
  }

  /**
   * Handle resource read request
   * @param {string} uri - Resource URI
   * @returns {object} Resource content
   */
  async handleResourceRead(uri) {
    const parsed = MCPResourceURI.parse(uri);
    if (!parsed || parsed.type !== 'analytics') {
      throw new Error(`Invalid analytics resource URI: ${uri}`);
    }

    const { id: projectId, subpath } = parsed;

    try {
      // Parse subpath for different analytics resources
      if (subpath.startsWith('insights')) {
        const timeframe = this.extractParameter(subpath, 'insights');
        return await this.getProjectInsights(projectId, timeframe, uri);
      } else if (subpath.startsWith('metrics')) {
        const metric = this.extractParameter(subpath, 'metrics');
        return await this.getPerformanceMetrics(projectId, metric, uri);
      } else if (subpath.startsWith('quality')) {
        const component = this.extractParameter(subpath, 'quality');
        return await this.getQualityAssessment(projectId, component, uri);
      } else {
        throw new Error(`Unknown analytics resource path: ${subpath}`);
      }
    } catch (error) {
      throw new Error(`Failed to read analytics resource: ${error.message}`);
    }
  }

  /**
   * Extract parameter from subpath
   * @param {string} subpath - Resource subpath
   * @param {string} prefix - Expected prefix
   * @returns {string|null} Extracted parameter or null
   */
  extractParameter(subpath, prefix) {
    const parts = subpath.split('/');
    if (parts[0] === prefix && parts.length > 1) {
      return decodeURIComponent(parts[1]);
    }
    return null;
  }

  /**
   * Get project insights data
   * @param {string} projectId - Project ID
   * @param {string|null} timeframe - Optional timeframe filter
   * @param {string} uri - Resource URI
   * @returns {object} Project insights resource
   */
  async getProjectInsights(projectId, timeframe, uri) {
    try {
      const insights = await this.analyticsManager.extractInsights({ 
        projectId,
        timeframe: timeframe || 'all'
      });
      
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          projectId,
          timeframe: timeframe || 'all',
          generated: new Date().toISOString(),
          insights: {
            trends: insights.trends || [],
            patterns: insights.patterns || [],
            anomalies: insights.anomalies || [],
            predictions: insights.predictions || [],
            recommendations: insights.recommendations || []
          },
          metrics: {
            productivity: insights.productivity || 0,
            quality: insights.quality || 0,
            velocity: insights.velocity || 0,
            efficiency: insights.efficiency || 0
          },
          summary: {
            totalInsights: (insights.trends?.length || 0) + (insights.patterns?.length || 0),
            criticalIssues: insights.criticalIssues || 0,
            improvementAreas: insights.improvementAreas || [],
            successFactors: insights.successFactors || []
          },
          metadata: {
            resourceType: 'project_insights',
            version: '1.0',
            dataSource: insights.dataSource || 'system',
            confidence: insights.confidence || 0.8,
            lastAccessed: new Date().toISOString(),
            ...insights.metadata
          }
        }, null, 2)
      };
    } catch (error) {
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          projectId,
          timeframe: timeframe || 'all',
          generated: new Date().toISOString(),
          insights: {
            trends: [],
            patterns: [],
            anomalies: [],
            predictions: [],
            recommendations: ['Unable to generate insights due to error']
          },
          metrics: {},
          summary: {
            totalInsights: 0,
            criticalIssues: 0,
            improvementAreas: [],
            successFactors: []
          },
          metadata: {
            resourceType: 'project_insights',
            version: '1.0',
            error: error.message,
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    }
  }

  /**
   * Get performance metrics data
   * @param {string} projectId - Project ID
   * @param {string|null} metric - Optional metric filter
   * @param {string} uri - Resource URI
   * @returns {object} Performance metrics resource
   */
  async getPerformanceMetrics(projectId, metric, uri) {
    try {
      const metrics = await this.analyticsManager.getPerformanceMetrics({ 
        projectId,
        metric: metric || 'all'
      });
      
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          projectId,
          metric: metric || 'all',
          timestamp: new Date().toISOString(),
          performance: {
            overall: metrics.overall || 0,
            throughput: metrics.throughput || 0,
            latency: metrics.latency || 0,
            errorRate: metrics.errorRate || 0,
            availability: metrics.availability || 0
          },
          trends: {
            daily: metrics.dailyTrends || [],
            weekly: metrics.weeklyTrends || [],
            monthly: metrics.monthlyTrends || []
          },
          benchmarks: {
            industry: metrics.industryBenchmarks || {},
            historical: metrics.historicalBenchmarks || {},
            targets: metrics.targets || {}
          },
          alerts: metrics.alerts || [],
          metadata: {
            resourceType: 'performance_metrics',
            version: '1.0',
            collectionPeriod: metrics.collectionPeriod || '24h',
            lastAccessed: new Date().toISOString(),
            ...metrics.metadata
          }
        }, null, 2)
      };
    } catch (error) {
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          projectId,
          metric: metric || 'all',
          timestamp: new Date().toISOString(),
          performance: {},
          trends: {},
          benchmarks: {},
          alerts: [],
          metadata: {
            resourceType: 'performance_metrics',
            version: '1.0',
            error: error.message,
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    }
  }

  /**
   * Get quality assessment data
   * @param {string} projectId - Project ID
   * @param {string|null} component - Optional component filter
   * @param {string} uri - Resource URI
   * @returns {object} Quality assessment resource
   */
  async getQualityAssessment(projectId, component, uri) {
    try {
      const assessment = await this.analyticsManager.getQualityAssessment({ 
        projectId,
        component: component || 'all'
      });
      
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          projectId,
          component: component || 'all',
          assessed: new Date().toISOString(),
          scores: {
            overall: assessment.overall || 0,
            code: assessment.code || 0,
            documentation: assessment.documentation || 0,
            testing: assessment.testing || 0,
            architecture: assessment.architecture || 0
          },
          details: {
            strengths: assessment.strengths || [],
            weaknesses: assessment.weaknesses || [],
            risks: assessment.risks || [],
            opportunities: assessment.opportunities || []
          },
          recommendations: assessment.recommendations || [],
          history: assessment.history || [],
          metadata: {
            resourceType: 'quality_assessment',
            version: '1.0',
            assessor: assessment.assessor || 'system',
            methodology: assessment.methodology || 'automated',
            lastAccessed: new Date().toISOString(),
            ...assessment.metadata
          }
        }, null, 2)
      };
    } catch (error) {
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          projectId,
          component: component || 'all',
          assessed: new Date().toISOString(),
          scores: {},
          details: {
            strengths: [],
            weaknesses: [],
            risks: [],
            opportunities: []
          },
          recommendations: ['Unable to assess quality due to error'],
          history: [],
          metadata: {
            resourceType: 'quality_assessment',
            version: '1.0',
            error: error.message,
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    }
  }

  /**
   * Check if this handler can handle a given URI
   * @param {string} uri - URI to check
   * @returns {boolean} True if this handler can process the URI
   */
  canHandle(uri) {
    const parsed = MCPResourceURI.parse(uri);
    return parsed && parsed.type === 'analytics';
  }
}

export default AnalyticsResourceHandler;
