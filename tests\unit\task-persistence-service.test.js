import { describe, it, expect, beforeEach, mock } from 'bun:test';
import { TaskPersistenceService } from '../../src/task-management/task-persistence-service.js';
import { RichTaskSchema, TASK_STATUSES, TASK_PRIORITIES } from '../../src/task-management/task-schema.js';
import { TASKS_STORAGE } from '../../src/constants/paths.js';
import { v4 as uuidv4 } from 'uuid';

// Mock the file-management module using <PERSON><PERSON>'s mocking
mock.module('../../src/file-management/project-structure.js', () => ({
  readProjectFile: mock(async () => {}), // Default mock, will be overridden in beforeEach
  writeProjectFileWithLock: mock(async () => {}),
  updateProjectFile: mock(async () => {}),
}));

// Import the mocked functions after mock.module has been called
// For Bun, direct import works after mocking the module.
import { readProjectFile, writeProjectFileWithLock, updateProjectFile } from '../../src/file-management/project-structure.js';


describe('TaskPersistenceService', () => {
  let taskService;
  let mockTasksDataStore; // In-memory store for our mock

  beforeEach(() => {
    taskService = new TaskPersistenceService('mock_project_root');
    mockTasksDataStore = []; // This will simulate the content of tasks.json

    // Reset mocks and their implementations before each test
    readProjectFile.mockClear();
    writeProjectFileWithLock.mockClear();
    updateProjectFile.mockClear();

    // Default mock implementations
    readProjectFile.mockImplementation(async (filePath, projectRoot) => {
      if (filePath === TASKS_STORAGE) {
        // console.log('Mock readProjectFile called, returning:', JSON.parse(JSON.stringify(mockTasksDataStore)));
        return JSON.parse(JSON.stringify(mockTasksDataStore)); // Deep copy
      }
      // console.log(`Mock readProjectFile: File not found for ${filePath}`);
      const error = new Error(`File not found: ${filePath}`);
      error.message = `Failed to read ${filePath}: File not found`; // Mimic error structure
      throw error;
    });

    writeProjectFileWithLock.mockImplementation(async (filePath, data, projectRoot) => {
      if (filePath === TASKS_STORAGE) {
        // console.log('Mock writeProjectFileWithLock called with data:', data);
        mockTasksDataStore = JSON.parse(JSON.stringify(data)); // Deep copy
        return { success: true, path: filePath, data: [...mockTasksDataStore] };
      }
      throw new Error(`Cannot write to ${filePath}`);
    });

    updateProjectFile.mockImplementation(async (filePath, updateFn, projectRoot) => {
      if (filePath === TASKS_STORAGE) {
        const currentTasks = JSON.parse(JSON.stringify(mockTasksDataStore));
        try {
          const updatedTasks = await updateFn(currentTasks); // updateFn might be async
          mockTasksDataStore = updatedTasks;
          return { success: true, path: filePath, data: [...mockTasksDataStore] };
        } catch (e) {
          // If updateFn throws, the mock should simulate a failed file update by re-throwing.
          // This allows the service's catch block to handle it.
          throw e;
        }
      }
      throw new Error(`Cannot update ${filePath}`);
    });
  });

  describe('ensureTasksFileExists', () => {
    it('should create an empty tasks file if it does not exist', async () => {
      // Override readProjectFile for this specific test case to simulate file not found
      readProjectFile.mockImplementationOnce(async (filePath) => {
        if (filePath === TASKS_STORAGE) {
            const error = new Error('File not found');
            error.message = 'Failed to read .guidant/tasks/tasks.json: File not found';
            throw error;
        }
        throw new Error(`Unexpected path for readProjectFile in test: ${filePath}`);
      });
      await taskService.ensureTasksFileExists();
      expect(writeProjectFileWithLock).toHaveBeenCalledTimes(1);
      expect(writeProjectFileWithLock).toHaveBeenCalledWith(TASKS_STORAGE, [], 'mock_project_root');
      expect(mockTasksDataStore).toEqual([]);
    });

    it('should do nothing if the tasks file already exists', async () => {
      mockTasksDataStore = [{ id: 'task1', title: 'Existing Task' }];
      // readProjectFile is already set up to return mockTasksDataStore
      await taskService.ensureTasksFileExists();
      expect(writeProjectFileWithLock).not.toHaveBeenCalled();
    });
  });

  describe('createTask', () => {
    const baseTaskData = { title: 'New Task', description: 'A test task' };

    it('should create a task with valid data and assign id, timestamps, default status', async () => {
      const taskData = { ...baseTaskData };
      const createdTask = await taskService.createTask(taskData);
      
      expect(createdTask.id).toBeString();
      expect(createdTask.title).toBe(taskData.title);
      expect(createdTask.status).toBe(TASK_STATUSES.PENDING);
      expect(new Date(createdTask.createdAt).toISOString()).toBe(createdTask.createdAt);
      expect(new Date(createdTask.updatedAt).toISOString()).toBe(createdTask.updatedAt);
      expect(mockTasksDataStore.length).toBe(1);
      expect(mockTasksDataStore[0].id).toBe(createdTask.id);
    });

    it('should use provided id if available', async () => {
        const specificId = uuidv4();
        const taskData = { ...baseTaskData, id: specificId };
        const createdTask = await taskService.createTask(taskData);
        expect(createdTask.id).toBe(specificId);
    });

    it('should throw an error if required fields are missing', async () => {
      const invalidData = { description: 'Missing title' }; // Missing 'title'
      expect(async () => await taskService.createTask(invalidData)).toThrow(new Error('Task validation failed: Missing required field: title'));
    });

    it('should throw an error for invalid field types', async () => {
      const invalidData = { ...baseTaskData, title: 123, status: TASK_STATUSES.PENDING, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString(), id: uuidv4() };
      expect(async () => await taskService.createTask(invalidData)).toThrow(new Error('Task validation failed: Invalid type for field title. Expected string, got number'));
    });
    
    it('should throw an error if a task with the same ID already exists', async () => {
      const id = uuidv4();
      await taskService.createTask({ ...baseTaskData, id });
      expect(async () => await taskService.createTask({ ...baseTaskData, id })).toThrow(new Error(`Failed to create task: Task with ID ${id} already exists.`));
    });

    it('should validate and create a task with dependencies if they exist', async () => {
      const dep1 = await taskService.createTask({ title: 'Dependency Task 1' });
      const dep2 = await taskService.createTask({ title: 'Dependency Task 2' });
      
      const taskDataWithDeps = { ...baseTaskData, dependencies: [dep1.id, dep2.id] };
      const createdTask = await taskService.createTask(taskDataWithDeps);

      expect(createdTask.dependencies).toEqual([dep1.id, dep2.id]);
      expect(mockTasksDataStore.find(t => t.id === createdTask.id).dependencies).toEqual([dep1.id, dep2.id]);
    });

    it('should throw an error if a dependency ID does not exist', async () => {
      const existingDep = await taskService.createTask({ title: 'Existing Dependency' });
      const taskDataWithInvalidDep = { ...baseTaskData, dependencies: [existingDep.id, 'non_existent_id'] };
      
      expect(async () => await taskService.createTask(taskDataWithInvalidDep))
        .toThrow(new Error('Failed to create task: Dependency validation failed: Task with ID non_existent_id listed in dependencies does not exist.'));
    });
  });

  describe('getTaskById', () => {
    it('should retrieve an existing task by ID', async () => {
      const taskData = { title: 'Test Get', description: 'Task to get' };
      const createdTask = await taskService.createTask(taskData);
      
      const foundTask = await taskService.getTaskById(createdTask.id);
      expect(foundTask).toEqual(createdTask);
    });

    it('should return null for a non-existent task ID', async () => {
      const foundTask = await taskService.getTaskById('non_existent_id');
      expect(foundTask).toBeNull();
    });
  });

  describe('getAllTasks', () => {
    it('should return an empty array if no tasks exist', async () => {
      const tasks = await taskService.getAllTasks();
      expect(tasks).toEqual([]);
    });

    it('should return all tasks', async () => {
      const task1 = await taskService.createTask({ title: 'Task 1' });
      const task2 = await taskService.createTask({ title: 'Task 2' });
      
      const tasks = await taskService.getAllTasks();
      expect(tasks.length).toBe(2);
      expect(tasks).toContainEqual(task1);
      expect(tasks).toContainEqual(task2);
    });
  });

  describe('updateTask', () => {
    let existingTask;
    beforeEach(async () => {
      existingTask = await taskService.createTask({ title: 'Original Title', priority: TASK_PRIORITIES.LOW });
    });

    it('should update fields of an existing task and its updatedAt timestamp', async () => {
      const updates = { title: 'Updated Title', status: TASK_STATUSES.IN_PROGRESS, priority: TASK_PRIORITIES.HIGH };
      const originalUpdatedAt = existingTask.updatedAt;
      
      // Ensure a slight delay for timestamp comparison
      await new Promise(resolve => setTimeout(resolve, 10)); 
      
      const updatedTask = await taskService.updateTask(existingTask.id, updates);

      expect(updatedTask.title).toBe('Updated Title');
      expect(updatedTask.status).toBe(TASK_STATUSES.IN_PROGRESS);
      expect(updatedTask.priority).toBe(TASK_PRIORITIES.HIGH);
      expect(updatedTask.updatedAt).not.toBe(originalUpdatedAt);
      expect(new Date(updatedTask.updatedAt) > new Date(originalUpdatedAt)).toBe(true);
    });

    it('should throw an error when trying to update a non-existent task', async () => {
      expect(async () => await taskService.updateTask('non_existent_id', { title: 'New Title' }))
        .toThrow(new Error('Failed to update task non_existent_id: Task with ID non_existent_id not found.'));
    });

    it('should throw an error for invalid update data', async () => {
      expect(async () => await taskService.updateTask(existingTask.id, { priority: 'invalid_priority' }))
        .toThrow(new Error(`Failed to update task ${existingTask.id}: Updated task validation failed: Invalid value for field priority. Allowed values: high, medium, low`));
    });

    it('should throw an error if trying to change task ID', async () => {
      expect(async () => await taskService.updateTask(existingTask.id, { id: 'new_id' }))
        .toThrow(new Error('Cannot change task ID during update.'));
    });

    it('should validate and update dependencies if they exist', async () => {
      const dep1 = await taskService.createTask({ title: 'Dep 1 for Update' });
      const dep2 = await taskService.createTask({ title: 'Dep 2 for Update' });
      const updates = { dependencies: [dep1.id, dep2.id] };
      
      const updatedTask = await taskService.updateTask(existingTask.id, updates);
      expect(updatedTask.dependencies).toEqual([dep1.id, dep2.id]);
    });

    it('should throw an error if an updated dependency ID does not exist', async () => {
      const updates = { dependencies: ['non_existent_dep_id'] };
      expect(async () => await taskService.updateTask(existingTask.id, updates))
        .toThrow(new Error(`Failed to update task ${existingTask.id}: Dependency validation failed during update: Task with ID non_existent_dep_id listed in dependencies does not exist.`));
    });
  });

  describe('deleteTask', () => {
    it('should delete an existing task and return true', async () => {
      const taskToDelete = await taskService.createTask({ title: 'To Be Deleted' });
      expect(mockTasksDataStore.length).toBe(1);
      
      const result = await taskService.deleteTask(taskToDelete.id);
      expect(result).toBe(true);
      expect(mockTasksDataStore.length).toBe(0);
      
      const foundTask = await taskService.getTaskById(taskToDelete.id);
      expect(foundTask).toBeNull();
    });

    it('should return false if trying to delete a non-existent task', async () => {
      const result = await taskService.deleteTask('non_existent_id');
      // The current implementation of updateProjectFile for delete doesn't distinguish
      // between "not found" and "successfully deleted an empty list".
      // It will return true if the filter results in no change to an empty list.
      // For a more robust test, we'd check if an error was thrown or if the list remained unchanged.
      // Given the mock, if the task wasn't there, the list length doesn't change, so `deleted` remains false.
      expect(result).toBe(false); 
    });
  });

  describe('queryTasks', () => {
    beforeEach(async () => {
      // Clear and populate tasks for querying
      mockTasksDataStore = [];
      await taskService.createTask({ id: 'task1', title: 'Alpha Task', status: TASK_STATUSES.PENDING, priority: TASK_PRIORITIES.HIGH, tags: ['urgent', 'frontend'] });
      await taskService.createTask({ id: 'task2', title: 'Bravo Task', status: TASK_STATUSES.IN_PROGRESS, priority: TASK_PRIORITIES.MEDIUM, tags: ['backend'] });
      await taskService.createTask({ id: 'task3', title: 'Charlie Task', status: TASK_STATUSES.PENDING, priority: TASK_PRIORITIES.HIGH, tags: ['urgent', 'design'] });
      await taskService.createTask({ id: 'task4', title: 'Delta Task', status: TASK_STATUSES.DONE, priority: TASK_PRIORITIES.LOW, tags: ['frontend'] });
    });

    it('should return all tasks if filter is empty', async () => {
      const tasks = await taskService.queryTasks({});
      expect(tasks.length).toBe(4);
    });

    it('should filter by status', async () => {
      const tasks = await taskService.queryTasks({ status: TASK_STATUSES.PENDING });
      expect(tasks.length).toBe(2);
      expect(tasks.every(t => t.status === TASK_STATUSES.PENDING)).toBe(true);
    });

    it('should filter by priority', async () => {
      const tasks = await taskService.queryTasks({ priority: TASK_PRIORITIES.HIGH });
      expect(tasks.length).toBe(2);
      expect(tasks.every(t => t.priority === TASK_PRIORITIES.HIGH)).toBe(true);
    });

    it('should filter by multiple criteria (status and priority)', async () => {
      const tasks = await taskService.queryTasks({ status: TASK_STATUSES.PENDING, priority: TASK_PRIORITIES.HIGH });
      expect(tasks.length).toBe(2); // task1 and task3
      expect(tasks.find(t=>t.id === 'task1')).toBeDefined();
      expect(tasks.find(t=>t.id === 'task3')).toBeDefined();
    });
    
    it('should filter by a tag if the field is an array', async () => {
        const tasks = await taskService.queryTasks({ tags: 'urgent' });
        expect(tasks.length).toBe(2); // task1, task3
        expect(tasks.find(t=>t.id === 'task1')).toBeDefined();
        expect(tasks.find(t=>t.id === 'task3')).toBeDefined();
    });

    it('should filter by multiple tags if the field is an array and query value is an array', async () => {
        const tasks = await taskService.queryTasks({ tags: ['urgent', 'frontend'] });
        expect(tasks.length).toBe(1); // task1
        expect(tasks[0].id).toBe('task1');
    });

    it('should return an empty array if no tasks match the filter', async () => {
      const tasks = await taskService.queryTasks({ status: 'non_existent_status' });
      expect(tasks.length).toBe(0);
    });
  });
});