/**
 * Context7 Research Provider Unit Tests
 * 
 * Tests for the Context7Provider class including library resolution,
 * documentation retrieval, and technical research capabilities.
 */

import { describe, it, expect, beforeEach, afterEach, mock, spyOn } from 'bun:test';
import { Context7Provider } from '../../src/research-providers/context7-provider.js';

describe('Context7Provider', () => {
  let provider;
  let mockConfig;

  beforeEach(() => {
    mockConfig = {
      baseUrl: 'https://mcp.context7.com',
      defaultTokens: 10000,
      minTokens: 1000,
      preferredLanguages: ['javascript', 'typescript', 'python'],
      rateLimit: {
        requestsPerMinute: 60
      }
    };
    provider = new Context7Provider(mockConfig);
  });

  afterEach(() => {
    mock.restore();
  });

  describe('Constructor', () => {
    it('should initialize with correct configuration', () => {
      expect(provider.name).toBe('Context7');
      expect(provider.baseUrl).toBe('https://mcp.context7.com');
      expect(provider.defaultTokens).toBe(10000);
      expect(provider.preferredLanguages).toEqual(['javascript', 'typescript', 'python']);
    });

    it('should use default configuration when not provided', () => {
      const defaultProvider = new Context7Provider();
      expect(defaultProvider.name).toBe('Context7');
      expect(defaultProvider.baseUrl).toBe('https://mcp.context7.com');
      expect(defaultProvider.defaultTokens).toBe(10000);
    });
  });

  describe('Configuration Validation', () => {
    it('should validate base URL requirement', async () => {
      const invalidProvider = new Context7Provider({ baseUrl: null });
      await expect(invalidProvider.validateConfig()).rejects.toThrow('Context7 base URL is required');
    });

    it('should pass validation with successful library resolution', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          libraries: [{ context7CompatibleLibraryID: '/facebook/react', trustScore: 0.9 }]
        })
      });

      await expect(provider.validateConfig()).resolves.toBe(true);
      mockFetch.mockRestore();
    });
  });

  describe('Library Resolution', () => {
    it('should resolve library name to Context7 ID', async () => {
      const mockResponse = {
        libraries: [
          { context7CompatibleLibraryID: '/facebook/react', trustScore: 0.9 },
          { context7CompatibleLibraryID: '/reactjs/react', trustScore: 0.7 }
        ]
      };

      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const libraryId = await provider.resolveLibraryId('react');

      expect(libraryId).toBe('/facebook/react'); // Should return highest trust score
      mockFetch.mockRestore();
    });

    it('should handle library not found', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ libraries: [] })
      });

      await expect(provider.resolveLibraryId('nonexistent-library')).rejects.toThrow('No library found for: nonexistent-library');
      mockFetch.mockRestore();
    });

    it('should handle API errors during resolution', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      });

      await expect(provider.resolveLibraryId('react')).rejects.toThrow('Library resolution failed');
      mockFetch.mockRestore();
    });
  });

  describe('Documentation Retrieval', () => {
    it('should retrieve library documentation', async () => {
      const mockResponse = {
        content: '# React Documentation\n\nReact is a JavaScript library...',
        metadata: { tokens: 5000 }
      };

      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      });

      const docs = await provider.getLibraryDocs('/facebook/react', 'getting started', 5000);

      expect(docs.libraryId).toBe('/facebook/react');
      expect(docs.topic).toBe('getting started');
      expect(docs.documentation).toBe('# React Documentation\n\nReact is a JavaScript library...');
      expect(docs.metadata.tokens).toBe(5000);

      mockFetch.mockRestore();
    });

    it('should respect token limits', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ content: 'Documentation content' })
      });

      await provider.getLibraryDocs('/facebook/react', '', 500);

      const fetchCall = mockFetch.mock.calls[0];
      const requestBody = JSON.parse(fetchCall[1].body);

      // Should use minTokens (1000) instead of requested 500
      expect(requestBody.tokens).toBe(1000);

      mockFetch.mockRestore();
    });

    it('should handle documentation retrieval errors', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      });

      await expect(provider.getLibraryDocs('/facebook/react')).rejects.toThrow('Documentation retrieval failed');
      mockFetch.mockRestore();
    });
  });

  describe('Search Functionality', () => {
    it('should search for library documentation', async () => {
      // Mock library resolution
      const mockResolveResponse = {
        libraries: [{ context7CompatibleLibraryID: '/facebook/react', trustScore: 0.9 }]
      };

      // Mock documentation retrieval
      const mockDocsResponse = {
        content: '# React Documentation\n\nReact is a JavaScript library for building user interfaces.',
        metadata: { tokens: 5000 }
      };

      const mockFetch = spyOn(global, 'fetch')
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResolveResponse)
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockDocsResponse)
        });

      const result = await provider.search('react');

      expect(result.query).toBe('react');
      expect(result.libraryId).toBe('/facebook/react');
      expect(result.results).toHaveLength(1);
      expect(result.results[0].documentation).toContain('React is a JavaScript library');
      expect(result.metadata.type).toBe('library_documentation');

      mockFetch.mockRestore();
    });

    it('should handle search failures gracefully', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ libraries: [] })
      });

      const result = await provider.search('nonexistent-library');

      expect(result.query).toBe('nonexistent-library');
      expect(result.results).toHaveLength(0);
      expect(result.error).toBeDefined();

      mockFetch.mockRestore();
    });
  });

  describe('Library Research', () => {
    it('should conduct comprehensive library research', async () => {
      // Mock library resolution
      const mockResolveResponse = {
        libraries: [{ context7CompatibleLibraryID: '/facebook/react', trustScore: 0.9 }]
      };

      // Mock multiple documentation requests for different topics
      const mockDocsResponse = {
        content: 'Topic-specific documentation content',
        metadata: { tokens: 3000 }
      };

      const mockFetch = spyOn(global, 'fetch')
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResolveResponse)
        });

      // Mock multiple documentation calls
      for (let i = 0; i < 6; i++) {
        mockFetch.mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockDocsResponse)
        });
      }

      const result = await provider.libraryResearch('react');

      expect(result.library).toBe('react');
      expect(result.libraryId).toBe('/facebook/react');
      expect(result.topics).toHaveLength(6); // Default topics
      expect(result.metadata.type).toBe('library_research');

      mockFetch.mockRestore();
    });

    it('should handle partial failures in multi-topic research', async () => {
      // Mock library resolution
      const mockResolveResponse = {
        libraries: [{ context7CompatibleLibraryID: '/facebook/react', trustScore: 0.9 }]
      };

      const mockFetch = spyOn(global, 'fetch')
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockResolveResponse)
        });

      // Mock some successful and some failed documentation calls
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ content: 'Success 1' })
        })
        .mockRejectedValueOnce(new Error('API Error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ content: 'Success 2' })
        });

      const result = await provider.libraryResearch('react', { topics: ['topic1', 'topic2', 'topic3'] });

      // Should have 2 successful results despite 1 failure
      expect(result.topics.length).toBeGreaterThan(0);
      expect(result.metadata.topicsRetrieved).toBeLessThan(result.metadata.topicsRequested);

      mockFetch.mockRestore();
    });
  });

  describe('Code Example Extraction', () => {
    it('should extract code examples from documentation', () => {
      const docs = [
        {
          documentation: `
# React Example

\`\`\`javascript
import React from 'react';

function App() {
  return <div>Hello World</div>;
}
\`\`\`

\`\`\`typescript
interface Props {
  name: string;
}
\`\`\`
          `,
          libraryId: '/facebook/react',
          topic: 'examples'
        }
      ];

      const examples = provider.extractCodeExamples(docs);

      expect(examples).toHaveLength(2);
      expect(examples[0].language).toBe('javascript');
      expect(examples[0].code).toContain('import React from \'react\'');
      expect(examples[1].language).toBe('typescript');
      expect(examples[1].code).toContain('interface Props');
    });

    it('should filter by preferred languages', () => {
      const docs = [
        {
          documentation: `
\`\`\`javascript
console.log('js');
\`\`\`

\`\`\`php
echo 'php';
\`\`\`

\`\`\`python
print('python')
\`\`\`
          `,
          libraryId: '/test/lib'
        }
      ];

      const examples = provider.extractCodeExamples(docs);

      // Should only include preferred languages (javascript, typescript, python)
      const languages = examples.map(ex => ex.language);
      expect(languages).toContain('javascript');
      expect(languages).toContain('python');
      expect(languages).not.toContain('php');
    });
  });

  describe('Statistics and Performance', () => {
    it('should track provider statistics', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ libraries: [] })
      });

      await provider.search('test-library');

      const stats = provider.getStats();
      expect(stats.provider).toBe('Context7');
      expect(stats.totalRequests).toBeGreaterThan(0);

      mockFetch.mockRestore();
    });

    it('should handle rate limiting', async () => {
      const mockCheckLimit = spyOn(provider.rateLimiter, 'checkLimit').mockResolvedValue(false);

      await expect(provider.search('test-library')).rejects.toThrow('Rate limit exceeded');

      mockCheckLimit.mockRestore();
    });
  });
});
