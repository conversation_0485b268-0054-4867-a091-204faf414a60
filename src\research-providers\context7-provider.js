/**
 * Context7 Research Provider
 * Implements technical documentation retrieval, library/framework research,
 * API documentation analysis, and code example extraction using Context7 API
 */

import { BaseResearchProvider } from './base-research-provider.js';

/**
 * Context7-specific documentation filtering and ranking
 */
export class Context7DocumentationFilter {
  constructor(config = {}) {
    this.relevanceThreshold = config.relevanceThreshold || 0.6;
    this.maxTokens = config.maxTokens || 10000;
    this.preferredSources = config.preferredSources || [];
    this.codeSnippetWeight = config.codeSnippetWeight || 0.3;
  }

  /**
   * Filter and rank documentation results
   * @param {Array} docs - Raw Context7 documentation
   * @param {object} context - Search context
   * @returns {Array} Filtered and ranked documentation
   */
  filterDocumentation(docs, context = {}) {
    if (!Array.isArray(docs)) return [];

    return docs
      .map(doc => this.scoreDocument(doc, context))
      .filter(doc => doc.score >= this.relevanceThreshold)
      .sort((a, b) => b.score - a.score);
  }

  /**
   * Score individual documentation based on relevance
   * @param {object} doc - Individual documentation entry
   * @param {object} context - Search context
   * @returns {object} Document with score
   */
  scoreDocument(doc, context) {
    let score = 0.5; // Base score

    // Source preference boost
    if (this.preferredSources.length > 0) {
      const source = this.extractSource(doc);
      if (this.preferredSources.includes(source)) {
        score += 0.2;
      }
    }

    // Code snippet availability boost
    if (doc.code || (doc.content && doc.content.includes('```'))) {
      score += this.codeSnippetWeight;
    }

    // Topic relevance
    if (context.topic && doc.title) {
      const topicWords = context.topic.toLowerCase().split(' ');
      const titleWords = doc.title.toLowerCase();
      const matches = topicWords.filter(word => titleWords.includes(word));
      score += (matches.length / topicWords.length) * 0.25;
    }

    // Content length consideration (prefer comprehensive docs)
    if (doc.content) {
      const contentLength = doc.content.length;
      if (contentLength > 1000) {
        score += 0.1;
      }
      if (contentLength > 5000) {
        score += 0.1;
      }
    }

    // Trust score from Context7
    if (doc.trustScore && doc.trustScore >= 8) {
      score += 0.15;
    }

    return {
      ...doc,
      score: Math.min(score, 1.0)
    };
  }

  extractSource(doc) {
    if (doc.source) return doc.source;
    if (doc.url) {
      try {
        return new URL(doc.url).hostname;
      } catch {
        return '';
      }
    }
    return '';
  }
}

/**
 * Context7 Research Provider
 * Provides technical documentation and library research capabilities
 */
export class Context7Provider extends BaseResearchProvider {
  constructor(config = {}) {
    super({
      name: 'Context7',
      baseUrl: config.baseUrl || 'https://mcp.context7.com',
      ...config
    });

    this.filter = new Context7DocumentationFilter(config.filtering || {});
    
    // Context7-specific configuration
    this.defaultTokens = config.defaultTokens || 10000;
    this.minTokens = config.minTokens || 1000;
    this.preferredLanguages = config.preferredLanguages || ['javascript', 'typescript', 'python'];
    this.includeCodeExamples = config.includeCodeExamples !== false;
  }

  /**
   * Validate Context7 configuration
   * @returns {Promise<boolean>} Configuration validity
   */
  async validateConfig() {
    if (!this.baseUrl) {
      throw new Error('Context7 base URL is required');
    }

    // Test connectivity with a simple library resolution
    try {
      await this.resolveLibraryId('react');
      return true;
    } catch (error) {
      throw new Error(`Context7 API validation failed: ${error.message}`);
    }
  }

  /**
   * Resolve library name to Context7-compatible library ID
   * @param {string} libraryName - Library name to resolve
   * @returns {Promise<string>} Context7-compatible library ID
   */
  async resolveLibraryId(libraryName) {
    try {
      const response = await this.makeRequest('/resolve-library-id', {
        libraryName
      });

      if (response.libraries && response.libraries.length > 0) {
        // Return the best match (highest trust score)
        const bestMatch = response.libraries
          .sort((a, b) => (b.trustScore || 0) - (a.trustScore || 0))[0];
        
        return bestMatch.context7CompatibleLibraryID;
      }

      throw new Error(`No library found for: ${libraryName}`);
    } catch (error) {
      this.emit('resolutionError', { libraryName, error });
      throw new Error(`Library resolution failed: ${error.message}`);
    }
  }

  /**
   * Get library documentation using Context7 ID
   * @param {string} libraryId - Context7-compatible library ID
   * @param {string} topic - Optional topic to focus on
   * @param {number} tokens - Maximum tokens to retrieve
   * @returns {Promise<object>} Library documentation
   */
  async getLibraryDocs(libraryId, topic = '', tokens = null) {
    const requestTokens = Math.max(
      tokens || this.defaultTokens,
      this.minTokens
    );

    try {
      const response = await this.makeRequest('/get-library-docs', {
        context7CompatibleLibraryID: libraryId,
        topic: topic || undefined,
        tokens: requestTokens
      });

      return {
        libraryId,
        topic,
        documentation: response.content || response.docs || response,
        metadata: {
          tokens: requestTokens,
          provider: 'Context7',
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.emit('documentationError', { libraryId, topic, error });
      throw new Error(`Documentation retrieval failed: ${error.message}`);
    }
  }

  /**
   * Search for technical documentation
   * @param {string} query - Search query (library or technology name)
   * @param {object} options - Search options
   * @returns {Promise<object>} Documentation search results
   */
  async search(query, options = {}) {
    try {
      // First, try to resolve as a library
      const libraryId = await this.resolveLibraryId(query);
      
      // Get documentation for the resolved library
      const docs = await this.getLibraryDocs(
        libraryId,
        options.topic || '',
        options.tokens || this.defaultTokens
      );

      return {
        query,
        libraryId,
        results: [docs],
        metadata: {
          type: 'library_documentation',
          provider: 'Context7',
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      // If library resolution fails, return empty results
      return {
        query,
        results: [],
        error: error.message,
        metadata: {
          type: 'library_documentation',
          provider: 'Context7',
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Research specific library or framework
   * @param {string} libraryName - Library/framework name
   * @param {object} options - Research options
   * @returns {Promise<object>} Library research results
   */
  async libraryResearch(libraryName, options = {}) {
    const topics = options.topics || [
      'getting started',
      'installation',
      'configuration',
      'api reference',
      'examples',
      'best practices'
    ];

    try {
      const libraryId = await this.resolveLibraryId(libraryName);
      
      // Get documentation for each topic
      const topicResults = await Promise.allSettled(
        topics.map(topic => 
          this.getLibraryDocs(libraryId, topic, options.tokensPerTopic || 5000)
        )
      );

      const successfulResults = topicResults
        .filter(result => result.status === 'fulfilled')
        .map(result => result.value);

      return {
        library: libraryName,
        libraryId,
        topics: successfulResults,
        codeExamples: this.extractCodeExamples(successfulResults),
        apiReference: this.extractApiReference(successfulResults),
        metadata: {
          type: 'library_research',
          topicsRequested: topics.length,
          topicsRetrieved: successfulResults.length,
          provider: 'Context7',
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.emit('libraryResearchError', { libraryName, error });
      throw new Error(`Library research failed: ${error.message}`);
    }
  }

  /**
   * Analyze API documentation
   * @param {string} libraryName - Library name
   * @param {object} options - Analysis options
   * @returns {Promise<object>} API analysis results
   */
  async apiDocumentationAnalysis(libraryName, options = {}) {
    try {
      const libraryId = await this.resolveLibraryId(libraryName);
      
      const apiDocs = await this.getLibraryDocs(
        libraryId,
        'api reference',
        options.tokens || 15000
      );

      const analysis = {
        library: libraryName,
        libraryId,
        apiStructure: this.analyzeApiStructure(apiDocs.documentation),
        endpoints: this.extractEndpoints(apiDocs.documentation),
        methods: this.extractMethods(apiDocs.documentation),
        parameters: this.extractParameters(apiDocs.documentation),
        examples: this.extractCodeExamples([apiDocs]),
        metadata: {
          type: 'api_documentation_analysis',
          provider: 'Context7',
          timestamp: new Date().toISOString()
        }
      };

      return analysis;
    } catch (error) {
      this.emit('apiAnalysisError', { libraryName, error });
      throw new Error(`API documentation analysis failed: ${error.message}`);
    }
  }

  /**
   * Extract code examples from documentation
   * @param {Array} docs - Documentation results
   * @returns {Array} Extracted code examples
   */
  extractCodeExamples(docs) {
    const examples = [];
    
    docs.forEach(doc => {
      if (doc.documentation) {
        // Extract code blocks from markdown
        const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
        let match;
        
        while ((match = codeBlockRegex.exec(doc.documentation)) !== null) {
          examples.push({
            language: match[1] || 'unknown',
            code: match[2].trim(),
            source: doc.libraryId || 'unknown',
            topic: doc.topic || 'general'
          });
        }
      }
    });

    return examples.filter(example => 
      this.preferredLanguages.length === 0 || 
      this.preferredLanguages.includes(example.language)
    );
  }

  /**
   * Extract API reference information
   * @param {Array} docs - Documentation results
   * @returns {object} API reference structure
   */
  extractApiReference(docs) {
    // Simplified API reference extraction
    // In a real implementation, this would parse documentation structure
    return {
      classes: this.extractClasses(docs),
      functions: this.extractFunctions(docs),
      constants: this.extractConstants(docs),
      types: this.extractTypes(docs)
    };
  }

  /**
   * Analyze API structure from documentation
   * @param {string} documentation - Raw documentation content
   * @returns {object} API structure analysis
   */
  analyzeApiStructure(documentation) {
    // Simplified structure analysis
    return {
      hasClasses: documentation.includes('class '),
      hasFunctions: documentation.includes('function ') || documentation.includes('def '),
      hasTypes: documentation.includes('interface ') || documentation.includes('type '),
      hasExamples: documentation.includes('```'),
      estimatedComplexity: this.estimateComplexity(documentation)
    };
  }

  // Helper methods for extraction (simplified implementations)
  extractEndpoints(docs) { return []; }
  extractMethods(docs) { return []; }
  extractParameters(docs) { return []; }
  extractClasses(docs) { return []; }
  extractFunctions(docs) { return []; }
  extractConstants(docs) { return []; }
  extractTypes(docs) { return []; }
  
  estimateComplexity(documentation) {
    const length = documentation.length;
    if (length < 5000) return 'simple';
    if (length < 20000) return 'moderate';
    return 'complex';
  }

  /**
   * Get provider-specific statistics
   * @returns {object} Enhanced statistics
   */
  getStats() {
    const baseStats = super.getStats();
    return {
      ...baseStats,
      librariesResolved: this.stats.librariesResolved || 0,
      documentationRetrieved: this.stats.documentationRetrieved || 0,
      codeExamplesExtracted: this.stats.codeExamplesExtracted || 0
    };
  }
}

export default Context7Provider;
