/**
 * Research Synthesis Engine (Task 4.1)
 * 
 * Combines research findings with user preferences, applies business context 
 * (budget, timeline, expertise), generates business-friendly recommendations,
 * and presents options with research-backed rationale.
 */

import { performAIOperation } from '../ai-integration/ai-services-unified.js';
import { UserPreferenceManager } from '../workflow-logic/user-preference-manager.js';

/**
 * Business Context Analyzer
 * Analyzes business constraints and context for research synthesis
 */
export class BusinessContextAnalyzer {
  constructor(config = {}) {
    this.config = config;
    this.constraintWeights = {
      budget: 0.3,
      timeline: 0.25,
      expertise: 0.2,
      risk: 0.15,
      scalability: 0.1
    };
  }

  /**
   * Analyze business context from conversation and project data
   * @param {object} projectContext - Project context
   * @param {object} userPreferences - User preferences
   * @returns {object} Business context analysis
   */
  analyzeBusinessContext(projectContext, userPreferences = {}) {
    const context = {
      budget: this.extractBudgetConstraints(projectContext),
      timeline: this.extractTimelineConstraints(projectContext),
      expertise: this.assessExpertiseLevel(projectContext, userPreferences),
      riskTolerance: this.assessRiskTolerance(projectContext, userPreferences),
      scalabilityNeeds: this.assessScalabilityNeeds(projectContext),
      businessModel: projectContext.businessModel || 'unknown',
      targetAudience: projectContext.targetAudience || 'general',
      competitivePosition: projectContext.competitivePosition || 'unknown'
    };

    return {
      ...context,
      contextScore: this.calculateContextScore(context),
      recommendations: this.generateContextRecommendations(context)
    };
  }

  /**
   * Extract budget constraints from project context
   * @param {object} projectContext - Project context
   * @returns {object} Budget analysis
   */
  extractBudgetConstraints(projectContext) {
    const budgetText = projectContext.budget || projectContext.constraints?.budget || '';
    const budgetMatch = budgetText.match(/\$?(\d+(?:,\d{3})*(?:\.\d{2})?)[kK]?/);
    
    let amount = 0;
    let category = 'unknown';
    
    if (budgetMatch) {
      amount = parseFloat(budgetMatch[1].replace(/,/g, ''));
      if (budgetText.toLowerCase().includes('k')) amount *= 1000;
      
      if (amount < 10000) category = 'minimal';
      else if (amount < 50000) category = 'small';
      else if (amount < 200000) category = 'medium';
      else if (amount < 1000000) category = 'large';
      else category = 'enterprise';
    }

    return {
      amount,
      category,
      currency: 'USD',
      confidence: budgetMatch ? 0.8 : 0.2,
      constraints: this.deriveBudgetConstraints(category)
    };
  }

  /**
   * Extract timeline constraints from project context
   * @param {object} projectContext - Project context
   * @returns {object} Timeline analysis
   */
  extractTimelineConstraints(projectContext) {
    const timelineText = projectContext.timeline || projectContext.constraints?.timeline || '';
    const timelineMatch = timelineText.match(/(\d+)\s*(week|month|day)s?/i);
    
    let duration = 0;
    let unit = 'unknown';
    let category = 'unknown';
    
    if (timelineMatch) {
      duration = parseInt(timelineMatch[1]);
      unit = timelineMatch[2].toLowerCase();
      
      // Convert to weeks for standardization
      const weeks = unit === 'day' ? duration / 7 : 
                   unit === 'week' ? duration : 
                   unit === 'month' ? duration * 4 : duration;
      
      if (weeks < 4) category = 'urgent';
      else if (weeks < 12) category = 'fast';
      else if (weeks < 24) category = 'standard';
      else category = 'extended';
    }

    return {
      duration,
      unit,
      category,
      confidence: timelineMatch ? 0.8 : 0.2,
      constraints: this.deriveTimelineConstraints(category)
    };
  }

  /**
   * Assess user expertise level
   * @param {object} projectContext - Project context
   * @param {object} userPreferences - User preferences
   * @returns {object} Expertise assessment
   */
  assessExpertiseLevel(projectContext, userPreferences) {
    const indicators = {
      technical: 0,
      business: 0,
      domain: 0
    };

    // Analyze technical indicators
    if (userPreferences.technology?.frameworks) {
      indicators.technical += Object.keys(userPreferences.technology.frameworks).length * 0.2;
    }
    
    // Analyze business indicators
    if (projectContext.businessModel && projectContext.businessModel !== 'unknown') {
      indicators.business += 0.3;
    }
    
    // Analyze domain indicators
    if (projectContext.domain && projectContext.domain !== 'unknown') {
      indicators.domain += 0.3;
    }

    const overallLevel = Math.min(1.0, (indicators.technical + indicators.business + indicators.domain) / 3);
    
    return {
      technical: Math.min(1.0, indicators.technical),
      business: Math.min(1.0, indicators.business),
      domain: Math.min(1.0, indicators.domain),
      overall: overallLevel,
      category: overallLevel < 0.3 ? 'beginner' : 
               overallLevel < 0.6 ? 'intermediate' : 'advanced',
      recommendations: this.generateExpertiseRecommendations(overallLevel)
    };
  }

  /**
   * Derive budget constraints based on category
   * @param {string} category - Budget category
   * @returns {Array} Budget constraints
   */
  deriveBudgetConstraints(category) {
    const constraints = {
      minimal: ['prefer_free_tools', 'avoid_premium_services', 'minimize_infrastructure'],
      small: ['cost_conscious', 'prefer_open_source', 'gradual_scaling'],
      medium: ['balanced_approach', 'selective_premium', 'planned_scaling'],
      large: ['quality_focus', 'premium_acceptable', 'robust_infrastructure'],
      enterprise: ['best_in_class', 'enterprise_grade', 'full_scaling']
    };
    
    return constraints[category] || [];
  }

  /**
   * Derive timeline constraints based on category
   * @param {string} category - Timeline category
   * @returns {Array} Timeline constraints
   */
  deriveTimelineConstraints(category) {
    const constraints = {
      urgent: ['mvp_focus', 'rapid_prototyping', 'minimal_features'],
      fast: ['agile_approach', 'iterative_development', 'core_features'],
      standard: ['balanced_development', 'quality_focus', 'comprehensive_features'],
      extended: ['thorough_planning', 'robust_architecture', 'full_feature_set']
    };
    
    return constraints[category] || [];
  }

  /**
   * Generate expertise-based recommendations
   * @param {number} expertiseLevel - Overall expertise level (0-1)
   * @returns {Array} Expertise recommendations
   */
  generateExpertiseRecommendations(expertiseLevel) {
    if (expertiseLevel < 0.3) {
      return ['guided_approach', 'managed_services', 'simplified_architecture', 'extensive_documentation'];
    } else if (expertiseLevel < 0.6) {
      return ['balanced_complexity', 'some_automation', 'moderate_customization', 'good_documentation'];
    } else {
      return ['advanced_features', 'custom_solutions', 'complex_architecture', 'minimal_guidance'];
    }
  }

  /**
   * Calculate overall context score
   * @param {object} context - Business context
   * @returns {number} Context score (0-1)
   */
  calculateContextScore(context) {
    const scores = {
      budget: context.budget.confidence,
      timeline: context.timeline.confidence,
      expertise: context.expertise.overall,
      completeness: (context.businessModel !== 'unknown' ? 0.5 : 0) + 
                   (context.targetAudience !== 'general' ? 0.5 : 0)
    };

    return Object.values(scores).reduce((sum, score) => sum + score, 0) / Object.keys(scores).length;
  }

  /**
   * Generate context-based recommendations
   * @param {object} context - Business context
   * @returns {Array} Context recommendations
   */
  generateContextRecommendations(context) {
    const recommendations = [];
    
    // Budget-based recommendations
    if (context.budget.category === 'minimal') {
      recommendations.push('Focus on free and open-source solutions');
    } else if (context.budget.category === 'large') {
      recommendations.push('Consider premium tools for better productivity');
    }
    
    // Timeline-based recommendations
    if (context.timeline.category === 'urgent') {
      recommendations.push('Prioritize MVP approach with core features only');
    } else if (context.timeline.category === 'extended') {
      recommendations.push('Plan for comprehensive feature development');
    }
    
    // Expertise-based recommendations
    if (context.expertise.category === 'beginner') {
      recommendations.push('Choose managed services to reduce complexity');
    } else if (context.expertise.category === 'advanced') {
      recommendations.push('Consider custom solutions for optimal fit');
    }

    return recommendations;
  }

  /**
   * Assess risk tolerance
   * @param {object} projectContext - Project context
   * @param {object} userPreferences - User preferences
   * @returns {object} Risk tolerance assessment
   */
  assessRiskTolerance(projectContext, userPreferences) {
    let riskScore = 0.5; // Default moderate risk tolerance
    
    // Business model affects risk tolerance
    if (projectContext.businessModel === 'startup') riskScore += 0.2;
    if (projectContext.businessModel === 'enterprise') riskScore -= 0.2;
    
    // Timeline affects risk tolerance
    if (projectContext.timeline?.category === 'urgent') riskScore += 0.1;
    if (projectContext.timeline?.category === 'extended') riskScore -= 0.1;
    
    riskScore = Math.max(0, Math.min(1, riskScore));
    
    return {
      score: riskScore,
      category: riskScore < 0.3 ? 'conservative' : 
               riskScore < 0.7 ? 'moderate' : 'aggressive',
      recommendations: this.generateRiskRecommendations(riskScore)
    };
  }

  /**
   * Generate risk-based recommendations
   * @param {number} riskScore - Risk tolerance score (0-1)
   * @returns {Array} Risk recommendations
   */
  generateRiskRecommendations(riskScore) {
    if (riskScore < 0.3) {
      return ['proven_technologies', 'established_vendors', 'conservative_architecture'];
    } else if (riskScore < 0.7) {
      return ['balanced_approach', 'some_innovation', 'calculated_risks'];
    } else {
      return ['cutting_edge_tech', 'innovative_solutions', 'accept_higher_risk'];
    }
  }

  /**
   * Assess scalability needs
   * @param {object} projectContext - Project context
   * @returns {object} Scalability assessment
   */
  assessScalabilityNeeds(projectContext) {
    let scalabilityScore = 0.5; // Default moderate scalability needs
    
    // Business model affects scalability needs
    if (projectContext.businessModel === 'marketplace') scalabilityScore += 0.3;
    if (projectContext.businessModel === 'saas') scalabilityScore += 0.2;
    if (projectContext.businessModel === 'prototype') scalabilityScore -= 0.3;
    
    // Target audience affects scalability
    if (projectContext.targetAudience?.includes('global')) scalabilityScore += 0.2;
    if (projectContext.targetAudience?.includes('local')) scalabilityScore -= 0.1;
    
    scalabilityScore = Math.max(0, Math.min(1, scalabilityScore));
    
    return {
      score: scalabilityScore,
      category: scalabilityScore < 0.3 ? 'minimal' : 
               scalabilityScore < 0.7 ? 'moderate' : 'high',
      recommendations: this.generateScalabilityRecommendations(scalabilityScore)
    };
  }

  /**
   * Generate scalability-based recommendations
   * @param {number} scalabilityScore - Scalability needs score (0-1)
   * @returns {Array} Scalability recommendations
   */
  generateScalabilityRecommendations(scalabilityScore) {
    if (scalabilityScore < 0.3) {
      return ['simple_architecture', 'monolithic_approach', 'basic_infrastructure'];
    } else if (scalabilityScore < 0.7) {
      return ['modular_design', 'cloud_ready', 'horizontal_scaling'];
    } else {
      return ['microservices', 'distributed_architecture', 'auto_scaling'];
    }
  }
}

/**
 * Research Synthesis Engine
 * Main engine that combines research findings with business context
 */
export class ResearchSynthesisEngine {
  constructor(config = {}) {
    this.config = config;
    this.businessAnalyzer = new BusinessContextAnalyzer(config.businessContext);
    this.userPreferenceManager = new UserPreferenceManager(config.projectRoot);
    this.aiModel = config.aiModel || 'research';
    this.synthesisTemplates = this.initializeSynthesisTemplates();
  }

  /**
   * Synthesize research findings with business context
   * @param {object} researchResults - Raw research results
   * @param {object} projectContext - Project context
   * @param {object} options - Synthesis options
   * @returns {Promise<object>} Synthesized recommendations
   */
  async synthesizeResearchWithContext(researchResults, projectContext, options = {}) {
    try {
      console.log('🔬 Starting research synthesis with business context...');

      // Step 1: Load user preferences
      const userPreferences = await this.userPreferenceManager.getUserPreferences();

      // Step 2: Analyze business context
      const businessContext = this.businessAnalyzer.analyzeBusinessContext(projectContext, userPreferences);

      // Step 3: Process research findings
      const processedFindings = this.processResearchFindings(researchResults, businessContext);

      // Step 4: Generate business-friendly recommendations
      const recommendations = await this.generateBusinessRecommendations(
        processedFindings,
        businessContext,
        projectContext,
        options
      );

      // Step 5: Create presentation-ready synthesis
      const synthesis = await this.createPresentationSynthesis(
        recommendations,
        businessContext,
        processedFindings,
        options
      );

      console.log('✅ Research synthesis completed');

      return {
        synthesis,
        businessContext,
        processedFindings,
        recommendations,
        metadata: {
          timestamp: new Date().toISOString(),
          synthesisMethod: 'business_context_enhanced',
          contextScore: businessContext.contextScore,
          recommendationCount: recommendations.length,
          confidenceLevel: this.calculateOverallConfidence(synthesis, businessContext)
        }
      };
    } catch (error) {
      console.error('Research synthesis failed:', error);
      throw error;
    }
  }

  /**
   * Process research findings with business context
   * @param {object} researchResults - Raw research results
   * @param {object} businessContext - Business context analysis
   * @returns {object} Processed findings
   */
  processResearchFindings(researchResults, businessContext) {
    const findings = {
      marketInsights: this.extractMarketInsights(researchResults),
      technicalOptions: this.extractTechnicalOptions(researchResults),
      competitiveAnalysis: this.extractCompetitiveAnalysis(researchResults),
      riskFactors: this.extractRiskFactors(researchResults),
      costImplications: this.extractCostImplications(researchResults),
      timelineImpacts: this.extractTimelineImpacts(researchResults)
    };

    // Apply business context filters
    return this.applyBusinessContextFilters(findings, businessContext);
  }

  /**
   * Generate business-friendly recommendations
   * @param {object} processedFindings - Processed research findings
   * @param {object} businessContext - Business context
   * @param {object} projectContext - Project context
   * @param {object} options - Generation options
   * @returns {Promise<Array>} Business recommendations
   */
  async generateBusinessRecommendations(processedFindings, businessContext, projectContext, options) {
    const synthesisPrompt = this.createBusinessSynthesisPrompt(
      processedFindings,
      businessContext,
      projectContext,
      options
    );

    try {
      const aiResponse = await performAIOperation(this.aiModel, synthesisPrompt, {
        temperature: 0.3,
        maxTokens: 4000
      });

      const recommendations = this.parseRecommendations(aiResponse.text);
      return this.enrichRecommendations(recommendations, businessContext, processedFindings);
    } catch (error) {
      console.error('AI recommendation generation failed, using fallback:', error);
      return this.generateFallbackRecommendations(processedFindings, businessContext);
    }
  }

  /**
   * Create business synthesis prompt
   * @param {object} processedFindings - Processed findings
   * @param {object} businessContext - Business context
   * @param {object} projectContext - Project context
   * @param {object} options - Options
   * @returns {string} Synthesis prompt
   */
  createBusinessSynthesisPrompt(processedFindings, businessContext, projectContext, options) {
    const template = this.synthesisTemplates[options.templateType || 'general'];

    return `${template.systemPrompt}

PROJECT CONTEXT:
- Domain: ${projectContext.domain || 'Not specified'}
- Business Model: ${businessContext.businessModel}
- Target Audience: ${businessContext.targetAudience}
- Budget Category: ${businessContext.budget.category} (${businessContext.budget.amount ? '$' + businessContext.budget.amount.toLocaleString() : 'Not specified'})
- Timeline Category: ${businessContext.timeline.category} (${businessContext.timeline.duration} ${businessContext.timeline.unit})
- Expertise Level: ${businessContext.expertise.category}
- Risk Tolerance: ${businessContext.riskTolerance.category}
- Scalability Needs: ${businessContext.scalabilityNeeds.category}

RESEARCH FINDINGS:
${this.formatFindingsForPrompt(processedFindings)}

BUSINESS CONSTRAINTS:
- Budget Constraints: ${businessContext.budget.constraints.join(', ')}
- Timeline Constraints: ${businessContext.timeline.constraints.join(', ')}
- Expertise Recommendations: ${businessContext.expertise.recommendations.join(', ')}
- Risk Recommendations: ${businessContext.riskTolerance.recommendations.join(', ')}
- Scalability Recommendations: ${businessContext.scalabilityNeeds.recommendations.join(', ')}

${template.taskPrompt}

Please provide recommendations in this JSON format:
{
  "recommendations": [
    {
      "id": "unique_id",
      "title": "Business-friendly title",
      "description": "Clear business description",
      "businessRationale": "Why this makes business sense",
      "researchBacking": "Research evidence supporting this",
      "implementation": {
        "effort": "low|medium|high",
        "timeline": "estimated timeline",
        "cost": "estimated cost range",
        "risks": ["risk1", "risk2"]
      },
      "businessImpact": {
        "revenue": "potential revenue impact",
        "efficiency": "efficiency gains",
        "competitive": "competitive advantage"
      },
      "priority": "high|medium|low",
      "confidence": 0.0-1.0
    }
  ],
  "summary": "Executive summary of key insights",
  "nextSteps": ["step1", "step2", "step3"]
}`;
  }

  /**
   * Initialize synthesis templates
   * @returns {object} Synthesis templates
   */
  initializeSynthesisTemplates() {
    return {
      general: {
        systemPrompt: "You are a business strategy consultant who translates technical research into actionable business recommendations. Focus on business value, ROI, and practical implementation.",
        taskPrompt: "Based on the research findings and business context, provide strategic recommendations that balance technical feasibility with business constraints. Prioritize recommendations that deliver the highest business value within the given constraints."
      },
      market_research: {
        systemPrompt: "You are a market research analyst who provides business insights based on market data. Focus on market opportunities, competitive positioning, and business model validation.",
        taskPrompt: "Analyze the market research findings and provide business recommendations for market entry, positioning, and competitive strategy. Consider the business context and constraints."
      },
      technical_research: {
        systemPrompt: "You are a technical business consultant who translates technical research into business decisions. Focus on technology ROI, implementation feasibility, and business risk assessment.",
        taskPrompt: "Based on the technical research, provide business-focused technology recommendations. Consider implementation costs, timeline impacts, and business risks. Translate technical benefits into business value."
      },
      competitive_analysis: {
        systemPrompt: "You are a competitive intelligence analyst who provides strategic business insights. Focus on competitive positioning, differentiation opportunities, and market strategy.",
        taskPrompt: "Analyze the competitive research and provide strategic recommendations for competitive positioning and differentiation. Consider business constraints and market opportunities."
      }
    };
  }

  /**
   * Parse recommendations from AI response
   * @param {string} aiResponse - AI response text
   * @returns {Array} Parsed recommendations
   */
  parseRecommendations(aiResponse) {
    try {
      // Try to parse JSON response
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return parsed.recommendations || [];
      }
    } catch (error) {
      console.warn('Failed to parse JSON recommendations, using fallback parsing');
    }

    // Fallback parsing for non-JSON responses
    return this.parseTextRecommendations(aiResponse);
  }

  /**
   * Parse text-based recommendations
   * @param {string} text - Response text
   * @returns {Array} Parsed recommendations
   */
  parseTextRecommendations(text) {
    const recommendations = [];
    const sections = text.split(/\n\s*\n/);

    sections.forEach((section, index) => {
      if (section.trim().length > 50) {
        recommendations.push({
          id: `rec_${index + 1}`,
          title: `Recommendation ${index + 1}`,
          description: section.trim().substring(0, 300),
          businessRationale: 'Based on research analysis',
          researchBacking: 'Derived from research findings',
          implementation: {
            effort: 'medium',
            timeline: 'To be determined',
            cost: 'To be estimated',
            risks: []
          },
          businessImpact: {
            revenue: 'Potential positive impact',
            efficiency: 'Expected improvements',
            competitive: 'Strategic advantage'
          },
          priority: 'medium',
          confidence: 0.6
        });
      }
    });

    return recommendations;
  }

  /**
   * Enrich recommendations with business context
   * @param {Array} recommendations - Base recommendations
   * @param {object} businessContext - Business context
   * @param {object} processedFindings - Processed findings
   * @returns {Array} Enriched recommendations
   */
  enrichRecommendations(recommendations, businessContext, processedFindings) {
    return recommendations.map(rec => {
      const enriched = { ...rec };

      // Adjust priority based on business context
      if (businessContext.timeline.category === 'urgent' && rec.implementation?.effort === 'low') {
        enriched.priority = 'high';
      }

      // Adjust confidence based on research backing
      const relevantFindings = this.findRelevantFindings(rec, processedFindings);
      if (relevantFindings.length > 2) {
        enriched.confidence = Math.min(1.0, enriched.confidence + 0.2);
      }

      // Add business context tags
      enriched.contextTags = this.generateContextTags(rec, businessContext);

      // Add implementation guidance
      enriched.implementationGuidance = this.generateImplementationGuidance(rec, businessContext);

      return enriched;
    });
  }

  /**
   * Find relevant findings for a recommendation
   * @param {object} recommendation - Recommendation
   * @param {object} processedFindings - Processed findings
   * @returns {Array} Relevant findings
   */
  findRelevantFindings(recommendation, processedFindings) {
    const relevant = [];
    const recText = (recommendation.description + ' ' + recommendation.businessRationale).toLowerCase();

    Object.values(processedFindings).forEach(findings => {
      findings.forEach(finding => {
        if (this.isRelevantFinding(recText, finding.content.toLowerCase())) {
          relevant.push(finding);
        }
      });
    });

    return relevant;
  }

  /**
   * Check if finding is relevant to recommendation
   * @param {string} recText - Recommendation text
   * @param {string} findingText - Finding text
   * @returns {boolean} Is relevant
   */
  isRelevantFinding(recText, findingText) {
    const keywords = recText.split(' ').filter(word => word.length > 3);
    return keywords.some(keyword => findingText.includes(keyword));
  }

  /**
   * Generate context tags for recommendation
   * @param {object} recommendation - Recommendation
   * @param {object} businessContext - Business context
   * @returns {Array} Context tags
   */
  generateContextTags(recommendation, businessContext) {
    const tags = [];

    tags.push(`budget_${businessContext.budget.category}`);
    tags.push(`timeline_${businessContext.timeline.category}`);
    tags.push(`expertise_${businessContext.expertise.category}`);
    tags.push(`risk_${businessContext.riskTolerance.category}`);

    if (recommendation.implementation?.effort === 'low') tags.push('quick_win');
    if (recommendation.priority === 'high') tags.push('high_priority');
    if (recommendation.confidence > 0.8) tags.push('high_confidence');

    return tags;
  }

  /**
   * Generate implementation guidance
   * @param {object} recommendation - Recommendation
   * @param {object} businessContext - Business context
   * @returns {object} Implementation guidance
   */
  generateImplementationGuidance(recommendation, businessContext) {
    const guidance = {
      nextSteps: [],
      considerations: [],
      resources: []
    };

    // Add expertise-based guidance
    if (businessContext.expertise.category === 'beginner') {
      guidance.considerations.push('Consider hiring external expertise');
      guidance.resources.push('Comprehensive documentation and training');
    }

    // Add budget-based guidance
    if (businessContext.budget.category === 'minimal') {
      guidance.considerations.push('Focus on free and open-source options');
      guidance.nextSteps.push('Research cost-effective alternatives');
    }

    // Add timeline-based guidance
    if (businessContext.timeline.category === 'urgent') {
      guidance.nextSteps.push('Prioritize MVP features');
      guidance.considerations.push('Consider rapid prototyping approaches');
    }

    return guidance;
  }

  /**
   * Generate fallback recommendations
   * @param {object} processedFindings - Processed findings
   * @param {object} businessContext - Business context
   * @returns {Array} Fallback recommendations
   */
  generateFallbackRecommendations(processedFindings, businessContext) {
    const recommendations = [];

    // Generate basic recommendations based on findings
    if (processedFindings.technicalOptions.length > 0) {
      recommendations.push({
        id: 'tech_rec_1',
        title: 'Technology Selection',
        description: 'Based on research findings, consider the technical options that align with your constraints',
        businessRationale: 'Proper technology selection reduces development time and costs',
        researchBacking: `${processedFindings.technicalOptions.length} technical options identified`,
        implementation: {
          effort: 'medium',
          timeline: '2-4 weeks for evaluation',
          cost: 'Evaluation costs only',
          risks: ['Technology learning curve']
        },
        businessImpact: {
          revenue: 'Faster time to market',
          efficiency: 'Improved development efficiency',
          competitive: 'Modern technology stack'
        },
        priority: 'high',
        confidence: 0.7
      });
    }

    if (processedFindings.marketInsights.length > 0) {
      recommendations.push({
        id: 'market_rec_1',
        title: 'Market Positioning',
        description: 'Leverage market insights to position your product effectively',
        businessRationale: 'Market-informed positioning increases success probability',
        researchBacking: `${processedFindings.marketInsights.length} market insights gathered`,
        implementation: {
          effort: 'low',
          timeline: '1-2 weeks',
          cost: 'Minimal',
          risks: ['Market changes']
        },
        businessImpact: {
          revenue: 'Better market fit',
          efficiency: 'Focused marketing efforts',
          competitive: 'Strategic positioning'
        },
        priority: 'medium',
        confidence: 0.6
      });
    }

    return recommendations;
  }
}
