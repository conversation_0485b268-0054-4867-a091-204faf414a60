/**
 * Session Recovery Manager for Guidant
 * Handles session tracking, recovery, and context management
 */

import fs from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import {
  writeJSONFile,
  readJSONFile,
  updateJSONFile,
  withFileLock
} from '../file-management/reliable-file-manager.js';
import {
  SESSIONS,
  USER_FEEDBACK,
  USER_PREFERENCES,
  SESSION_RECOVERY
} from '../constants/paths.js';

/**
 * Session Recovery Manager
 * Provides functionality for session tracking, recovery, and context management
 */
export class SessionRecoveryManager {
  constructor(projectRoot = process.cwd()) {
    this.projectRoot = projectRoot;
    this.sessionsPath = path.join(projectRoot, SESSIONS);
    this.userFeedbackPath = path.join(projectRoot, USER_FEEDBACK);
    this.userPreferencesPath = path.join(projectRoot, USER_PREFERENCES);
    this.sessionRecoveryPath = path.join(projectRoot, SESSION_RECOVERY);
    this.ensureDirectoriesExist();
  }

  /**
   * Ensure required directories exist
   */
  ensureDirectoriesExist() {
    const guidantDir = path.join(this.projectRoot, '.guidant');
    const contextDir = path.join(guidantDir, 'context');

    // Create directories synchronously to ensure they exist before any operations
    try {
      if (!require('fs').existsSync(guidantDir)) {
        require('fs').mkdirSync(guidantDir, { recursive: true });
      }
      if (!require('fs').existsSync(contextDir)) {
        require('fs').mkdirSync(contextDir, { recursive: true });
      }
    } catch (error) {
      console.warn('Failed to create directories:', error.message);
    }
  }

  /**
   * Create a new session
   * @param {Object} sessionData - Initial session data
   * @returns {Promise<Object>} - The created session
   */
  async createSession(sessionData = {}) {
    const sessionId = sessionData.sessionId || uuidv4();
    const now = new Date().toISOString();
    
    const session = {
      sessionId,
      startTime: now,
      lastActive: now,
      status: 'active',
      agentId: sessionData.agentId || '',
      agentName: sessionData.agentName || '',
      projectContext: {
        projectName: sessionData.projectName || '',
        currentPhase: sessionData.currentPhase || '',
        lastAction: '',
        nextAction: ''
      },
      workflowState: {
        currentTask: sessionData.currentTask || '',
        completedTasks: [],
        pendingTasks: [],
        currentProgress: 0
      },
      userContext: {
        lastDecision: '',
        decisionHistory: [],
        currentOrientation: sessionData.currentOrientation || `You're starting a new project${sessionData.projectName ? ': ' + sessionData.projectName : ''}`
      },
      visualState: {
        progressIndicator: 0,
        currentView: 'initial',
        expandedSections: []
      },
      recoveryData: {
        lastMessage: '',
        conversationContext: '',
        resumePoint: '',
        recoveryPrompt: `Welcome to your project${sessionData.projectName ? ' ' + sessionData.projectName : ''}! Let's get started.`
      }
    };

    // Update sessions.json with the new session
    const updateResult = await this.updateSessions(sessions => {
      // Remove any existing session with the same ID
      const filteredSessions = sessions.filter(s => s.sessionId !== sessionId);
      return [...filteredSessions, session];
    });

    if (!updateResult) {
      console.error('Failed to update sessions.json');
      throw new Error('Failed to save session to sessions.json');
    }

    // Update session-recovery.json to track active sessions
    await this.updateSessionRecovery(recoveryData => {
      const activeSessions = recoveryData.activeSessions || [];
      const updatedActiveSessions = activeSessions.filter(s => s.sessionId !== sessionId);
      
      updatedActiveSessions.push({
        sessionId,
        startTime: now,
        lastActive: now,
        agentId: sessionData.agentId || '',
        projectName: sessionData.projectName || ''
      });

      return {
        ...recoveryData,
        activeSessions: updatedActiveSessions
      };
    });

    return session;
  }

  /**
   * Get a session by ID
   * @param {string} sessionId - The session ID
   * @returns {Promise<Object|null>} - The session or null if not found
   */
  async getSession(sessionId) {
    try {
      const result = await readJSONFile(this.sessionsPath);
      if (!result.success) {
        return null;
      }

      const sessions = result.data || [];
      return sessions.find(session => session.sessionId === sessionId) || null;
    } catch (error) {
      console.error('Failed to get session:', error);
      return null;
    }
  }

  /**
   * Update a session
   * @param {string} sessionId - The session ID
   * @param {Function} updateFn - Function that receives the session and returns updated session
   * @returns {Promise<Object|null>} - The updated session or null if failed
   */
  async updateSession(sessionId, updateFn) {
    try {
      let updatedSession = null;

      await this.updateSessions(sessions => {
        const sessionIndex = sessions.findIndex(s => s.sessionId === sessionId);
        if (sessionIndex === -1) {
          return sessions;
        }

        const session = sessions[sessionIndex];
        updatedSession = updateFn({
          ...session,
          lastActive: new Date().toISOString()
        });

        const updatedSessions = [...sessions];
        updatedSessions[sessionIndex] = updatedSession;
        return updatedSessions;
      });

      if (updatedSession) {
        // Also update the session in session-recovery.json
        await this.updateSessionRecovery(recoveryData => {
          const activeSessions = recoveryData.activeSessions || [];
          const sessionIndex = activeSessions.findIndex(s => s.sessionId === sessionId);
          
          if (sessionIndex !== -1) {
            const updatedActiveSessions = [...activeSessions];
            updatedActiveSessions[sessionIndex] = {
              ...activeSessions[sessionIndex],
              lastActive: new Date().toISOString()
            };
            
            return {
              ...recoveryData,
              activeSessions: updatedActiveSessions
            };
          }
          
          return recoveryData;
        });
      }

      return updatedSession;
    } catch (error) {
      console.error('Failed to update session:', error);
      return null;
    }
  }

  /**
   * Close a session
   * @param {string} sessionId - The session ID
   * @returns {Promise<boolean>} - True if successful
   */
  async closeSession(sessionId) {
    try {
      let sessionData = null;

      // First, get the session data
      await this.updateSessions(sessions => {
        const sessionIndex = sessions.findIndex(s => s.sessionId === sessionId);
        if (sessionIndex === -1) {
          return sessions;
        }

        sessionData = sessions[sessionIndex];
        const updatedSession = {
          ...sessionData,
          status: 'inactive',
          lastActive: new Date().toISOString()
        };

        const updatedSessions = [...sessions];
        updatedSessions[sessionIndex] = updatedSession;
        return updatedSessions;
      });

      if (sessionData) {
        // Move from active to history in session-recovery.json
        await this.updateSessionRecovery(recoveryData => {
          const activeSessions = recoveryData.activeSessions || [];
          const sessionHistory = recoveryData.sessionHistory || [];
          
          const updatedActiveSessions = activeSessions.filter(s => s.sessionId !== sessionId);
          
          const historyEntry = activeSessions.find(s => s.sessionId === sessionId);
          if (historyEntry) {
            sessionHistory.push({
              ...historyEntry,
              endTime: new Date().toISOString(),
              status: 'completed'
            });
          }
          
          return {
            ...recoveryData,
            activeSessions: updatedActiveSessions,
            sessionHistory: sessionHistory
          };
        });

        return true;
      }

      return false;
    } catch (error) {
      console.error('Failed to close session:', error);
      return false;
    }
  }

  /**
   * Get recovery information for a session
   * @param {string} sessionId - The session ID
   * @returns {Promise<Object|null>} - Recovery information or null if not found
   */
  async getRecoveryInfo(sessionId) {
    try {
      const session = await this.getSession(sessionId);
      if (!session) {
        return null;
      }
      
      return {
        projectContext: session.projectContext,
        workflowState: session.workflowState,
        userContext: session.userContext,
        recoveryData: session.recoveryData,
        lastActive: session.lastActive
      };
    } catch (error) {
      console.error('Failed to get recovery info:', error);
      return null;
    }
  }

  /**
   * Get orientation information for a session
   * @param {string} sessionId - The session ID
   * @returns {Promise<Object|null>} - Orientation information or null if not found
   */
  async getOrientation(sessionId) {
    try {
      const session = await this.getSession(sessionId);
      if (!session) {
        return null;
      }
      
      return {
        projectName: session.projectContext.projectName,
        currentPhase: session.projectContext.currentPhase,
        currentTask: session.workflowState.currentTask,
        progress: session.workflowState.currentProgress,
        lastAction: session.projectContext.lastAction,
        nextAction: session.projectContext.nextAction
      };
    } catch (error) {
      console.error('Failed to get orientation:', error);
      return null;
    }
  }

  /**
   * Update orientation information for a session
   * @param {string} sessionId - The session ID
   * @param {string} orientation - Orientation message
   * @returns {Promise<boolean>} - True if successful
   */
  async updateOrientation(sessionId, orientation) {
    try {
      await this.updateSession(sessionId, session => ({
        ...session,
        userContext: {
          ...session.userContext,
          currentOrientation: orientation
        }
      }));
      
      return true;
    } catch (error) {
      console.error('Failed to update orientation:', error);
      return false;
    }
  }

  /**
   * Record a user decision
   * @param {string} sessionId - The session ID
   * @param {string} decision - Decision description
   * @returns {Promise<boolean>} - True if successful
   */
  async recordDecision(sessionId, decision) {
    try {
      await this.updateSession(sessionId, session => {
        const decisionHistory = [...(session.userContext.decisionHistory || [])];
        
        // Add new decision with timestamp
        decisionHistory.push({
          timestamp: new Date().toISOString(),
          decision
        });
        
        // Keep only the last 10 decisions
        if (decisionHistory.length > 10) {
          decisionHistory.shift();
        }
        
        return {
          ...session,
          userContext: {
            ...session.userContext,
            lastDecision: decision,
            decisionHistory
          }
        };
      });
      
      return true;
    } catch (error) {
      console.error('Failed to record decision:', error);
      return false;
    }
  }

  /**
   * Update recovery prompt for a session
   * @param {string} sessionId - The session ID
   * @param {string} prompt - Recovery prompt
   * @returns {Promise<boolean>} - True if successful
   */
  async updateRecoveryPrompt(sessionId, prompt) {
    try {
      await this.updateSession(sessionId, session => ({
        ...session,
        recoveryData: {
          ...session.recoveryData,
          recoveryPrompt: prompt
        }
      }));
      
      return true;
    } catch (error) {
      console.error('Failed to update recovery prompt:', error);
      return false;
    }
  }

  /**
   * Update last message and conversation context for a session
   * @param {string} sessionId - The session ID
   * @param {string} message - Last message
   * @param {string} context - Conversation context
   * @returns {Promise<boolean>} - True if successful
   */
  async updateLastMessage(sessionId, message, context = '') {
    try {
      await this.updateSession(sessionId, session => ({
        ...session,
        recoveryData: {
          ...session.recoveryData,
          lastMessage: message,
          conversationContext: context || session.recoveryData.conversationContext
        }
      }));
      
      return true;
    } catch (error) {
      console.error('Failed to update last message:', error);
      return false;
    }
  }

  /**
   * Record a recovery attempt for a session
   * @param {string} sessionId - The session ID
   * @returns {Promise<boolean>} - True if successful
   */
  async recordRecoveryAttempt(sessionId) {
    try {
      // Update session-recovery.json
      await this.updateSessionRecovery(recoveryData => ({
        ...recoveryData,
        lastRecovery: {
          sessionId,
          timestamp: new Date().toISOString()
        }
      }));
      
      // Also update the session
      await this.updateSession(sessionId, session => ({
        ...session,
        status: 'active'
      }));
      
      return true;
    } catch (error) {
      console.error('Failed to record recovery attempt:', error);
      return false;
    }
  }

  /**
   * Get visual progress for a session
   * @param {string} sessionId - The session ID
   * @returns {Promise<Object|null>} - Visual progress information or null if not found
   */
  async getVisualProgress(sessionId) {
    try {
      const session = await this.getSession(sessionId);
      if (!session) {
        return null;
      }
      
      return session.visualState;
    } catch (error) {
      console.error('Failed to get visual progress:', error);
      return null;
    }
  }

  /**
   * Update visual progress for a session
   * @param {string} sessionId - The session ID
   * @param {Object} visualState - Visual state information
   * @returns {Promise<boolean>} - True if successful
   */
  async updateVisualProgress(sessionId, visualState) {
    try {
      await this.updateSession(sessionId, session => ({
        ...session,
        visualState: {
          ...session.visualState,
          ...visualState
        }
      }));
      
      return true;
    } catch (error) {
      console.error('Failed to update visual progress:', error);
      return false;
    }
  }

  /**
   * Helper method to update sessions.json
   * @param {Function} updateFn - Function that receives sessions array and returns updated sessions
   * @returns {Promise<boolean>} - True if successful
   */
  async updateSessions(updateFn) {
    try {
      const result = await updateJSONFile(this.sessionsPath, async (data) => {
        // Ensure we always work with an array for sessions
        const sessions = Array.isArray(data) ? data : [];
        return updateFn(sessions);
      });

      return result.success;
    } catch (error) {
      console.error('Failed to update sessions:', error);
      return false;
    }
  }

  /**
   * Helper method to update session-recovery.json
   * @param {Function} updateFn - Function that receives recovery data and returns updated data
   * @returns {Promise<boolean>} - True if successful
   */
  async updateSessionRecovery(updateFn) {
    try {
      const defaultRecoveryData = {
        version: "1.0",
        activeSessions: [],
        sessionHistory: [],
        lastRecovery: null
      };
      
      const result = await updateJSONFile(this.sessionRecoveryPath, async (recoveryData = defaultRecoveryData) => {
        return updateFn(recoveryData);
      });
      
      return result.success;
    } catch (error) {
      console.error('Failed to update session recovery data:', error);
      return false;
    }
  }
}

/**
 * Create a singleton instance
 */
let sessionRecoveryManagerInstance = null;

/**
 * Get the session recovery manager instance
 * @param {string} projectRoot - Project root directory
 * @returns {SessionRecoveryManager} - Session recovery manager instance
 */
export function getSessionRecoveryManager(projectRoot = process.cwd()) {
  if (!sessionRecoveryManagerInstance) {
    sessionRecoveryManagerInstance = new SessionRecoveryManager(projectRoot);
  }
  return sessionRecoveryManagerInstance;
} 