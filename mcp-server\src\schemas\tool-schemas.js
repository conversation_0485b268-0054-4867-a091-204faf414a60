/**
 * MCP Tool Schemas
 * Centralized schema definitions for MCP tool parameters and validation
 * Provides consistent validation across all consolidated tools
 */

import { z } from 'zod';

/**
 * Base operation schema for all consolidated tools
 */
export const BaseOperationSchema = z.object({
  operation: z.string().min(1).describe("Operation to perform"),
  parameters: z.object({}).optional().describe("Operation-specific parameters"),
  metadata: z.object({}).optional().describe("Optional metadata"),
  context: z.object({}).optional().describe("Execution context")
});

/**
 * Workflow Executor Tool Schema
 */
export const WorkflowExecutorSchema = z.object({
  operation: z.enum([
    "research", "onboarding", "development", "analysis", "optimization",
    "create", "update", "validate", "monitor", "execute", "cancel"
  ]).describe("Type of workflow operation to execute"),
  
  workflow_type: z.string().optional().describe("Specific workflow variant"),
  workflow_id: z.string().optional().describe("Workflow identifier for specific operations"),
  execution_id: z.string().optional().describe("Execution identifier for monitoring"),
  
  context: z.object({
    project_id: z.string().optional(),
    phase: z.string().optional(),
    user_preferences: z.object({}).optional(),
    business_context: z.object({}).optional()
  }).optional().describe("Workflow execution context"),
  
  parameters: z.object({
    template: z.string().optional(),
    priority: z.enum(["low", "medium", "high", "critical"]).optional(),
    timeout: z.number().optional(),
    retry_attempts: z.number().optional(),
    parallel_execution: z.boolean().optional()
  }).optional().describe("Workflow-specific parameters")
});

/**
 * Project Manager Tool Schema
 */
export const ProjectManagerSchema = z.object({
  operation: z.enum([
    "init", "advance_phase", "save_deliverable", "report_progress",
    "make_decision", "complete_onboarding", "update_state", "get_status"
  ]).describe("Project management operation"),
  
  project_id: z.string().optional().describe("Project identifier"),
  phase: z.string().optional().describe("Project phase"),
  deliverable_type: z.string().optional().describe("Type of deliverable"),
  
  project_config: z.object({
    name: z.string().optional(),
    type: z.string().optional(),
    template: z.string().optional(),
    requirements: z.object({}).optional()
  }).optional().describe("Project configuration"),
  
  deliverable_data: z.object({
    content: z.string().optional(),
    metadata: z.object({}).optional(),
    quality_score: z.number().optional(),
    validation_status: z.string().optional()
  }).optional().describe("Deliverable information"),
  
  progress_data: z.object({
    completion_percentage: z.number().optional(),
    milestones_completed: z.array(z.string()).optional(),
    blockers: z.array(z.string()).optional(),
    next_steps: z.array(z.string()).optional()
  }).optional().describe("Progress tracking data"),
  
  decision_context: z.object({
    decision_type: z.string().optional(),
    options: z.array(z.object({})).optional(),
    criteria: z.object({}).optional(),
    stakeholders: z.array(z.string()).optional()
  }).optional().describe("Decision-making context")
});

/**
 * Analytics Engine Tool Schema
 */
export const AnalyticsEngineSchema = z.object({
  operation: z.enum([
    "capabilities", "performance", "trends", "insights",
    "agents", "deliverables", "workflows", "metrics", "benchmarks"
  ]).describe("Type of analysis to perform"),
  
  target: z.string().describe("Analysis target (project_id, agent_id, deliverable_id, workflow_id)"),
  target_type: z.enum(["project", "agent", "deliverable", "workflow", "system"]).optional().describe("Type of target being analyzed"),
  
  analysis_scope: z.enum(["current", "historical", "predictive", "comparative"]).optional().describe("Scope of analysis to perform"),
  timeframe: z.enum(["1h", "24h", "7d", "30d", "90d", "1y", "all"]).optional().describe("Time period for analysis"),
  
  metrics: z.array(z.string()).optional().describe("Specific metrics to analyze"),
  
  filters: z.object({
    phase: z.string().optional(),
    status: z.string().optional(),
    priority: z.string().optional(),
    category: z.string().optional(),
    date_range: z.object({
      start: z.string().optional(),
      end: z.string().optional()
    }).optional()
  }).optional().describe("Analysis filters and criteria"),
  
  options: z.object({
    include_recommendations: z.boolean().optional(),
    include_trends: z.boolean().optional(),
    include_predictions: z.boolean().optional(),
    detail_level: z.enum(["summary", "detailed", "comprehensive"]).optional(),
    export_format: z.enum(["json", "csv", "chart"]).optional()
  }).optional().describe("Analysis options and configuration")
});

/**
 * Quality Validator Tool Schema
 */
export const QualityValidatorSchema = z.object({
  operation: z.enum([
    "deliverable", "history", "statistics", "configure", "rules",
    "score", "feedback", "validate", "analyze", "report"
  ]).describe("Quality validation operation to perform"),
  
  target: z.string().optional().describe("Target identifier (deliverable path, project ID, etc.)"),
  target_type: z.enum(["deliverable", "project", "phase", "component"]).optional().describe("Type of target being validated"),
  
  validation_options: z.object({
    enabled_rules: z.array(z.string()).optional(),
    disabled_rules: z.array(z.string()).optional(),
    custom_thresholds: z.record(z.number()).optional(),
    scoring_algorithm: z.enum(["weighted", "average", "minimum", "custom"]).optional(),
    feedback_level: z.enum(["basic", "detailed", "comprehensive"]).optional(),
    include_history: z.boolean().optional(),
    cache_results: z.boolean().optional(),
    timeout: z.number().optional()
  }).optional().describe("Validation configuration options"),
  
  context: z.object({
    project_type: z.string().optional(),
    phase: z.string().optional(),
    deliverable_type: z.string().optional(),
    quality_requirements: z.object({}).optional(),
    business_context: z.object({}).optional()
  }).optional().describe("Quality validation context"),
  
  parameters: z.object({
    profile: z.string().optional(),
    pass_threshold: z.number().optional(),
    quality_gates: z.object({}).optional(),
    report_type: z.string().optional(),
    period: z.string().optional()
  }).optional().describe("Operation-specific parameters")
});

/**
 * Context Manager Tool Schema
 */
export const ContextManagerSchema = z.object({
  operation: z.enum([
    "answer_question", "recover_session", "manage_preferences", "sync_state",
    "update_context", "restore_workflow", "save_session", "load_session"
  ]).describe("Context management operation"),
  
  session_id: z.string().optional().describe("Session identifier"),
  context_type: z.enum(["user", "project", "workflow", "system"]).optional().describe("Type of context"),
  
  question_data: z.object({
    question: z.string().optional(),
    context: z.object({}).optional(),
    expected_format: z.string().optional()
  }).optional().describe("Question answering data"),
  
  session_data: z.object({
    state: z.object({}).optional(),
    preferences: z.object({}).optional(),
    history: z.array(z.object({})).optional(),
    metadata: z.object({}).optional()
  }).optional().describe("Session management data"),
  
  preferences: z.object({
    ui_preferences: z.object({}).optional(),
    workflow_preferences: z.object({}).optional(),
    notification_preferences: z.object({}).optional(),
    quality_preferences: z.object({}).optional()
  }).optional().describe("User preferences"),
  
  sync_options: z.object({
    force_sync: z.boolean().optional(),
    conflict_resolution: z.enum(["merge", "overwrite", "prompt"]).optional(),
    backup_before_sync: z.boolean().optional()
  }).optional().describe("State synchronization options")
});

/**
 * Common parameter schemas used across tools
 */
export const CommonParameterSchemas = {
  ProjectId: z.string().min(1).describe("Project identifier"),
  Phase: z.enum(["concept", "requirements", "design", "architecture", "implementation", "deployment"]).describe("Project phase"),
  Priority: z.enum(["low", "medium", "high", "critical"]).describe("Priority level"),
  Status: z.enum(["pending", "in_progress", "completed", "failed", "cancelled"]).describe("Status"),
  Timeframe: z.enum(["1h", "24h", "7d", "30d", "90d", "1y", "all"]).describe("Time period"),
  DetailLevel: z.enum(["summary", "detailed", "comprehensive"]).describe("Level of detail"),
  
  Metadata: z.object({
    created_at: z.string().optional(),
    updated_at: z.string().optional(),
    created_by: z.string().optional(),
    tags: z.array(z.string()).optional(),
    version: z.string().optional()
  }).describe("Common metadata structure"),
  
  Context: z.object({
    project_id: z.string().optional(),
    user_id: z.string().optional(),
    session_id: z.string().optional(),
    environment: z.string().optional(),
    locale: z.string().optional()
  }).describe("Common execution context")
};

/**
 * Response schemas for tool outputs
 */
export const ResponseSchemas = {
  SuccessResponse: z.object({
    success: z.literal(true),
    data: z.any(),
    metadata: z.object({
      executedAt: z.string(),
      tool: z.string(),
      operation: z.string(),
      duration: z.number().optional(),
      version: z.string().optional()
    })
  }),
  
  ErrorResponse: z.object({
    success: z.literal(false),
    error: z.string(),
    error_code: z.string().optional(),
    metadata: z.object({
      executedAt: z.string(),
      tool: z.string(),
      operation: z.string().optional(),
      error_details: z.any().optional()
    })
  })
};

/**
 * Tool annotation schemas
 */
export const AnnotationSchemas = {
  ToolAnnotations: z.object({
    title: z.string().describe("Human-readable tool title"),
    description: z.string().describe("Detailed tool description"),
    readOnlyHint: z.boolean().optional().describe("Tool only reads data"),
    destructiveHint: z.boolean().optional().describe("Tool may modify/delete data"),
    idempotentHint: z.boolean().optional().describe("Tool can be safely retried"),
    openWorldHint: z.boolean().optional().describe("Tool interacts with external systems"),
    category: z.string().optional().describe("Tool category"),
    version: z.string().optional().describe("Tool version"),
    deprecated: z.boolean().optional().describe("Tool is deprecated")
  })
};

/**
 * Consolidated tool schemas export
 */
export const ConsolidatedToolSchemas = {
  WorkflowExecutor: WorkflowExecutorSchema,
  ProjectManager: ProjectManagerSchema,
  AnalyticsEngine: AnalyticsEngineSchema,
  QualityValidator: QualityValidatorSchema,
  ContextManager: ContextManagerSchema
};

/**
 * Schema validation utilities
 */
export const SchemaValidators = {
  /**
   * Validate tool parameters against schema
   * @param {string} toolName - Tool name
   * @param {object} parameters - Parameters to validate
   * @returns {object} Validation result
   */
  validateToolParameters(toolName, parameters) {
    const schema = ConsolidatedToolSchemas[toolName];
    if (!schema) {
      return { success: false, error: `Unknown tool: ${toolName}` };
    }
    
    try {
      const validated = schema.parse(parameters);
      return { success: true, data: validated };
    } catch (error) {
      return { 
        success: false, 
        error: error.message,
        details: error.errors || []
      };
    }
  },
  
  /**
   * Get schema for tool
   * @param {string} toolName - Tool name
   * @returns {object} Schema definition
   */
  getToolSchema(toolName) {
    return ConsolidatedToolSchemas[toolName] || null;
  },
  
  /**
   * List all available tool schemas
   * @returns {Array} Tool schema names
   */
  listToolSchemas() {
    return Object.keys(ConsolidatedToolSchemas);
  }
};

export default {
  ConsolidatedToolSchemas,
  CommonParameterSchemas,
  ResponseSchemas,
  AnnotationSchemas,
  SchemaValidators
};
