/**
 * Research Timing Patterns
 * Implements milestone triggers for research:
 * - project initialization → market research
 * - domain specified → competitive analysis  
 * - feature priorities → technical research
 * - decision points → validation research
 */

import { EventEmitter } from 'events';

/**
 * Conversation milestone detector
 */
export class ConversationMilestoneDetector {
  constructor(config = {}) {
    this.config = config;
    this.milestones = new Map([
      ['project_initialization', this.detectProjectInitialization],
      ['domain_specified', this.detectDomainSpecification],
      ['feature_priorities', this.detectFeaturePriorities],
      ['decision_points', this.detectDecisionPoints],
      ['technical_requirements', this.detectTechnicalRequirements],
      ['business_model_clarity', this.detectBusinessModelClarity],
      ['user_feedback_needed', this.detectUserFeedbackNeeds],
      ['implementation_planning', this.detectImplementationPlanning]
    ]);
    
    this.lastAnalysis = null;
    this.milestoneHistory = [];
  }

  /**
   * Detect conversation milestones from recent messages
   * @param {Array} conversationHistory - Full conversation history
   * @param {object} previousContext - Previous context analysis
   * @returns {Array} Detected milestones
   */
  detectMilestones(conversationHistory, previousContext = null) {
    const recentMessages = conversationHistory.slice(-5); // Last 5 messages
    const detectedMilestones = [];

    for (const [milestoneType, detector] of this.milestones) {
      try {
        const milestone = detector.call(this, recentMessages, conversationHistory, previousContext);
        if (milestone) {
          milestone.type = milestoneType;
          milestone.detectedAt = new Date().toISOString();
          detectedMilestones.push(milestone);
        }
      } catch (error) {
        console.error(`Milestone detection failed for ${milestoneType}:`, error);
      }
    }

    // Filter out recently detected milestones to avoid duplicates
    const newMilestones = this.filterNewMilestones(detectedMilestones);
    this.milestoneHistory.push(...newMilestones);

    return newMilestones;
  }

  /**
   * Detect project initialization milestone
   * @param {Array} recentMessages - Recent conversation messages
   * @param {Array} fullHistory - Full conversation history
   * @param {object} previousContext - Previous context
   * @returns {object|null} Milestone if detected
   */
  detectProjectInitialization(recentMessages, fullHistory, previousContext) {
    const initializationKeywords = [
      'new project', 'starting a', 'building a', 'creating a', 'developing a',
      'i want to build', 'i need to create', 'help me with', 'project idea'
    ];

    const recentText = recentMessages.map(msg => msg.content.toLowerCase()).join(' ');
    
    // Check if this is early in conversation (first 10 messages)
    if (fullHistory.length <= 10) {
      const hasInitKeywords = initializationKeywords.some(keyword => 
        recentText.includes(keyword)
      );

      if (hasInitKeywords) {
        return {
          confidence: 0.8,
          trigger: 'project_initialization_detected',
          context: {
            conversationLength: fullHistory.length,
            keywords: initializationKeywords.filter(keyword => recentText.includes(keyword))
          }
        };
      }
    }

    return null;
  }

  /**
   * Detect domain specification milestone
   * @param {Array} recentMessages - Recent conversation messages
   * @param {Array} fullHistory - Full conversation history
   * @param {object} previousContext - Previous context
   * @returns {object|null} Milestone if detected
   */
  detectDomainSpecification(recentMessages, fullHistory, previousContext) {
    const domainKeywords = [
      'restaurant', 'ecommerce', 'healthcare', 'fintech', 'education', 'gaming',
      'retail', 'logistics', 'real estate', 'travel', 'fitness', 'social media',
      'marketplace', 'booking', 'delivery', 'streaming', 'productivity'
    ];

    const recentText = recentMessages.map(msg => msg.content.toLowerCase()).join(' ');
    
    const mentionedDomains = domainKeywords.filter(domain => 
      recentText.includes(domain)
    );

    if (mentionedDomains.length > 0) {
      // Check if this is a new domain mention (not in previous context)
      const isNewDomain = !previousContext || 
        !previousContext.projectContext ||
        previousContext.projectContext.projectDomain === 'unknown' ||
        !mentionedDomains.includes(previousContext.projectContext.projectDomain);

      if (isNewDomain) {
        return {
          confidence: 0.9,
          trigger: 'domain_specification_detected',
          context: {
            domains: mentionedDomains,
            primaryDomain: mentionedDomains[0]
          }
        };
      }
    }

    return null;
  }

  /**
   * Detect feature priorities milestone
   * @param {Array} recentMessages - Recent conversation messages
   * @param {Array} fullHistory - Full conversation history
   * @param {object} previousContext - Previous context
   * @returns {object|null} Milestone if detected
   */
  detectFeaturePriorities(recentMessages, fullHistory, previousContext) {
    const featureKeywords = [
      'features', 'functionality', 'capabilities', 'requirements',
      'user can', 'users should', 'need to', 'must have', 'should include',
      'core features', 'main features', 'key features', 'essential features'
    ];

    const priorityKeywords = [
      'priority', 'important', 'critical', 'essential', 'must have',
      'first', 'primary', 'main', 'core', 'key', 'most important'
    ];

    const recentText = recentMessages.map(msg => msg.content.toLowerCase()).join(' ');
    
    const hasFeatureKeywords = featureKeywords.some(keyword => 
      recentText.includes(keyword)
    );
    
    const hasPriorityKeywords = priorityKeywords.some(keyword => 
      recentText.includes(keyword)
    );

    if (hasFeatureKeywords && hasPriorityKeywords) {
      return {
        confidence: 0.7,
        trigger: 'feature_priorities_detected',
        context: {
          featureKeywords: featureKeywords.filter(keyword => recentText.includes(keyword)),
          priorityKeywords: priorityKeywords.filter(keyword => recentText.includes(keyword))
        }
      };
    }

    return null;
  }

  /**
   * Detect decision points milestone
   * @param {Array} recentMessages - Recent conversation messages
   * @param {Array} fullHistory - Full conversation history
   * @param {object} previousContext - Previous context
   * @returns {object|null} Milestone if detected
   */
  detectDecisionPoints(recentMessages, fullHistory, previousContext) {
    const decisionKeywords = [
      'should i', 'which option', 'what do you think', 'recommend', 'advice',
      'better choice', 'pros and cons', 'compare', 'versus', 'vs',
      'decision', 'choose', 'select', 'pick', 'go with'
    ];

    const uncertaintyKeywords = [
      'not sure', 'unsure', 'confused', 'help me decide', 'torn between',
      'difficult choice', 'hard to decide', 'what would you'
    ];

    const recentText = recentMessages.map(msg => msg.content.toLowerCase()).join(' ');
    
    const hasDecisionKeywords = decisionKeywords.some(keyword => 
      recentText.includes(keyword)
    );
    
    const hasUncertaintyKeywords = uncertaintyKeywords.some(keyword => 
      recentText.includes(keyword)
    );

    if (hasDecisionKeywords || hasUncertaintyKeywords) {
      return {
        confidence: 0.8,
        trigger: 'decision_point_detected',
        context: {
          decisionType: hasUncertaintyKeywords ? 'uncertainty' : 'comparison',
          keywords: [...decisionKeywords, ...uncertaintyKeywords].filter(keyword => 
            recentText.includes(keyword)
          )
        }
      };
    }

    return null;
  }

  /**
   * Detect technical requirements milestone
   * @param {Array} recentMessages - Recent conversation messages
   * @param {Array} fullHistory - Full conversation history
   * @param {object} previousContext - Previous context
   * @returns {object|null} Milestone if detected
   */
  detectTechnicalRequirements(recentMessages, fullHistory, previousContext) {
    const technicalKeywords = [
      'technology', 'framework', 'database', 'api', 'architecture',
      'scalability', 'performance', 'security', 'integration', 'deployment',
      'hosting', 'cloud', 'mobile', 'web', 'backend', 'frontend'
    ];

    const recentText = recentMessages.map(msg => msg.content.toLowerCase()).join(' ');
    
    const mentionedTech = technicalKeywords.filter(tech => 
      recentText.includes(tech)
    );

    if (mentionedTech.length >= 2) {
      return {
        confidence: 0.7,
        trigger: 'technical_requirements_detected',
        context: {
          technologies: mentionedTech,
          complexity: mentionedTech.length > 4 ? 'high' : 'medium'
        }
      };
    }

    return null;
  }

  /**
   * Detect business model clarity milestone
   * @param {Array} recentMessages - Recent conversation messages
   * @param {Array} fullHistory - Full conversation history
   * @param {object} previousContext - Previous context
   * @returns {object|null} Milestone if detected
   */
  detectBusinessModelClarity(recentMessages, fullHistory, previousContext) {
    const businessKeywords = [
      'revenue', 'monetize', 'business model', 'pricing', 'subscription',
      'freemium', 'marketplace', 'commission', 'advertising', 'saas',
      'customers', 'target market', 'value proposition'
    ];

    const recentText = recentMessages.map(msg => msg.content.toLowerCase()).join(' ');
    
    const mentionedBusiness = businessKeywords.filter(keyword => 
      recentText.includes(keyword)
    );

    if (mentionedBusiness.length >= 2) {
      return {
        confidence: 0.8,
        trigger: 'business_model_clarity_detected',
        context: {
          businessConcepts: mentionedBusiness,
          maturity: mentionedBusiness.length > 3 ? 'advanced' : 'basic'
        }
      };
    }

    return null;
  }

  /**
   * Detect user feedback needs milestone
   * @param {Array} recentMessages - Recent conversation messages
   * @param {Array} fullHistory - Full conversation history
   * @param {object} previousContext - Previous context
   * @returns {object|null} Milestone if detected
   */
  detectUserFeedbackNeeds(recentMessages, fullHistory, previousContext) {
    const feedbackKeywords = [
      'user feedback', 'user testing', 'validate', 'market validation',
      'user research', 'survey', 'interview', 'prototype', 'mvp',
      'minimum viable product', 'test with users', 'user acceptance'
    ];

    const recentText = recentMessages.map(msg => msg.content.toLowerCase()).join(' ');
    
    const hasFeedbackKeywords = feedbackKeywords.some(keyword => 
      recentText.includes(keyword)
    );

    if (hasFeedbackKeywords) {
      return {
        confidence: 0.9,
        trigger: 'user_feedback_needed_detected',
        context: {
          feedbackType: 'validation',
          keywords: feedbackKeywords.filter(keyword => recentText.includes(keyword))
        }
      };
    }

    return null;
  }

  /**
   * Detect implementation planning milestone
   * @param {Array} recentMessages - Recent conversation messages
   * @param {Array} fullHistory - Full conversation history
   * @param {object} previousContext - Previous context
   * @returns {object|null} Milestone if detected
   */
  detectImplementationPlanning(recentMessages, fullHistory, previousContext) {
    const implementationKeywords = [
      'how to build', 'implementation', 'development plan', 'roadmap',
      'timeline', 'phases', 'steps', 'start building', 'begin development',
      'next steps', 'action plan', 'getting started'
    ];

    const recentText = recentMessages.map(msg => msg.content.toLowerCase()).join(' ');
    
    const hasImplementationKeywords = implementationKeywords.some(keyword => 
      recentText.includes(keyword)
    );

    if (hasImplementationKeywords) {
      return {
        confidence: 0.8,
        trigger: 'implementation_planning_detected',
        context: {
          planningType: 'development',
          keywords: implementationKeywords.filter(keyword => recentText.includes(keyword))
        }
      };
    }

    return null;
  }

  /**
   * Filter out recently detected milestones to avoid duplicates
   * @param {Array} detectedMilestones - Newly detected milestones
   * @returns {Array} Filtered new milestones
   */
  filterNewMilestones(detectedMilestones) {
    const recentThreshold = 5 * 60 * 1000; // 5 minutes
    const now = new Date();

    return detectedMilestones.filter(milestone => {
      // Check if this milestone type was detected recently
      const recentSimilar = this.milestoneHistory.find(historical => 
        historical.type === milestone.type &&
        (now - new Date(historical.detectedAt)) < recentThreshold
      );

      return !recentSimilar;
    });
  }

  /**
   * Get milestone history
   * @returns {Array} Historical milestones
   */
  getMilestoneHistory() {
    return [...this.milestoneHistory];
  }

  /**
   * Clear milestone history
   */
  clearHistory() {
    this.milestoneHistory = [];
  }
}

/**
 * Research trigger mapping for milestones
 */
export class ResearchTriggerMapper {
  constructor(config = {}) {
    this.config = config;
    this.triggerMap = new Map([
      ['project_initialization', this.createMarketResearchTrigger],
      ['domain_specified', this.createCompetitiveAnalysisTrigger],
      ['feature_priorities', this.createTechnicalResearchTrigger],
      ['decision_points', this.createValidationResearchTrigger],
      ['technical_requirements', this.createTechnicalDocumentationTrigger],
      ['business_model_clarity', this.createBusinessValidationTrigger],
      ['user_feedback_needed', this.createUserResearchTrigger],
      ['implementation_planning', this.createImplementationResearchTrigger]
    ]);
  }

  /**
   * Map milestone to research trigger
   * @param {object} milestone - Detected milestone
   * @param {object} projectContext - Current project context
   * @returns {object} Research trigger
   */
  mapMilestoneToResearch(milestone, projectContext) {
    const mapper = this.triggerMap.get(milestone.type);
    if (!mapper) {
      return null;
    }

    try {
      return mapper.call(this, milestone, projectContext);
    } catch (error) {
      console.error(`Research trigger mapping failed for ${milestone.type}:`, error);
      return null;
    }
  }

  createMarketResearchTrigger(milestone, projectContext) {
    const domain = projectContext.projectDomain || 'general';
    
    return {
      type: 'market_research',
      priority: 'high',
      query: `${domain} market analysis trends 2024 opportunities`,
      context: {
        milestone: milestone.type,
        domain,
        reason: 'Project initialization detected - understanding market landscape'
      },
      timing: 'immediate'
    };
  }

  createCompetitiveAnalysisTrigger(milestone, projectContext) {
    const domain = milestone.context?.primaryDomain || projectContext.projectDomain || 'general';
    
    return {
      type: 'competitive_analysis',
      priority: 'high',
      query: `${domain} competitors market leaders analysis`,
      context: {
        milestone: milestone.type,
        domain,
        reason: 'Domain specified - analyzing competitive landscape'
      },
      timing: 'immediate'
    };
  }

  createTechnicalResearchTrigger(milestone, projectContext) {
    const features = projectContext.keyFeatures || [];
    const query = features.length > 0 
      ? `${features.join(' ')} technical implementation best practices`
      : `${projectContext.projectType || 'application'} technical architecture`;
    
    return {
      type: 'technical_documentation',
      priority: 'medium',
      query,
      context: {
        milestone: milestone.type,
        features,
        reason: 'Feature priorities identified - researching technical implementation'
      },
      timing: 'soon'
    };
  }

  createValidationResearchTrigger(milestone, projectContext) {
    const domain = projectContext.projectDomain || 'general';
    const businessModel = projectContext.businessModel || 'unknown';
    
    return {
      type: 'fact_verification',
      priority: 'medium',
      query: `${domain} ${businessModel} market validation success factors`,
      context: {
        milestone: milestone.type,
        domain,
        businessModel,
        reason: 'Decision point detected - validating assumptions'
      },
      timing: 'soon'
    };
  }

  createTechnicalDocumentationTrigger(milestone, projectContext) {
    const technologies = milestone.context?.technologies || [];
    const query = technologies.length > 0
      ? `${technologies.join(' ')} integration documentation examples`
      : `${projectContext.projectType || 'application'} technical documentation`;
    
    return {
      type: 'technical_documentation',
      priority: 'medium',
      query,
      context: {
        milestone: milestone.type,
        technologies,
        reason: 'Technical requirements discussed - finding documentation'
      },
      timing: 'soon'
    };
  }

  createBusinessValidationTrigger(milestone, projectContext) {
    const businessConcepts = milestone.context?.businessConcepts || [];
    const domain = projectContext.projectDomain || 'general';
    
    return {
      type: 'market_research',
      priority: 'medium',
      query: `${domain} ${businessConcepts.join(' ')} business model validation`,
      context: {
        milestone: milestone.type,
        businessConcepts,
        reason: 'Business model clarity emerging - validating approach'
      },
      timing: 'soon'
    };
  }

  createUserResearchTrigger(milestone, projectContext) {
    const domain = projectContext.projectDomain || 'general';
    
    return {
      type: 'market_research',
      priority: 'low',
      query: `${domain} user research methods validation techniques`,
      context: {
        milestone: milestone.type,
        domain,
        reason: 'User feedback needs identified - researching validation methods'
      },
      timing: 'later'
    };
  }

  createImplementationResearchTrigger(milestone, projectContext) {
    const projectType = projectContext.projectType || 'application';
    const domain = projectContext.projectDomain || 'general';
    
    return {
      type: 'technical_documentation',
      priority: 'low',
      query: `${projectType} ${domain} development roadmap best practices`,
      context: {
        milestone: milestone.type,
        projectType,
        domain,
        reason: 'Implementation planning started - researching development approaches'
      },
      timing: 'later'
    };
  }
}

/**
 * Research Timing Patterns Engine
 * Main orchestrator for milestone-based research triggering
 */
export class ResearchTimingPatterns extends EventEmitter {
  constructor(config = {}) {
    super();
    this.config = config;
    this.milestoneDetector = new ConversationMilestoneDetector(config.milestones || {});
    this.triggerMapper = new ResearchTriggerMapper(config.triggers || {});
    this.activeResearchTriggers = [];
  }

  /**
   * Analyze conversation for research timing patterns
   * @param {Array} conversationHistory - Conversation history
   * @param {object} projectContext - Current project context
   * @param {object} previousContext - Previous context analysis
   * @returns {Array} Research triggers
   */
  analyzeTimingPatterns(conversationHistory, projectContext, previousContext = null) {
    try {
      // Detect conversation milestones
      const milestones = this.milestoneDetector.detectMilestones(
        conversationHistory, 
        previousContext
      );

      // Map milestones to research triggers
      const researchTriggers = milestones
        .map(milestone => this.triggerMapper.mapMilestoneToResearch(milestone, projectContext))
        .filter(trigger => trigger !== null);

      // Store active triggers
      this.activeResearchTriggers.push(...researchTriggers);

      // Emit events for detected patterns
      milestones.forEach(milestone => {
        this.emit('milestoneDetected', milestone);
      });

      researchTriggers.forEach(trigger => {
        this.emit('researchTriggered', trigger);
      });

      return {
        milestones,
        researchTriggers,
        metadata: {
          analyzedAt: new Date().toISOString(),
          conversationLength: conversationHistory.length,
          milestonesDetected: milestones.length,
          triggersGenerated: researchTriggers.length
        }
      };
    } catch (error) {
      console.error('Research timing analysis failed:', error);
      this.emit('analysisError', error);
      return {
        milestones: [],
        researchTriggers: [],
        error: error.message
      };
    }
  }

  /**
   * Get active research triggers
   * @returns {Array} Active research triggers
   */
  getActiveResearchTriggers() {
    return [...this.activeResearchTriggers];
  }

  /**
   * Clear completed research triggers
   * @param {Array} completedTriggerIds - IDs of completed triggers
   */
  clearCompletedTriggers(completedTriggerIds) {
    this.activeResearchTriggers = this.activeResearchTriggers.filter(
      trigger => !completedTriggerIds.includes(trigger.id)
    );
  }

  /**
   * Get milestone detection history
   * @returns {Array} Milestone history
   */
  getMilestoneHistory() {
    return this.milestoneDetector.getMilestoneHistory();
  }

  /**
   * Reset timing patterns state
   */
  reset() {
    this.activeResearchTriggers = [];
    this.milestoneDetector.clearHistory();
    this.emit('reset');
  }
}

export default ResearchTimingPatterns;
