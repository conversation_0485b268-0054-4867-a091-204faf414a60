[{"feedbackId": "", "timestamp": "", "sessionId": "", "context": {"phase": "", "task": "", "decision": ""}, "feedback": {"type": "", "content": "", "rating": 0}, "preferenceIndicators": {"technology": [], "design": [], "timeline": [], "communication": []}, "learningSignals": {"patterns": [], "preferences": [], "rejections": []}, "actionTaken": {"preferencesUpdated": false, "sessionAdjusted": false, "workflowModified": false}}, {"feedbackId": "feedback_1749936289600", "timestamp": "2025-06-14T21:24:49.600Z", "sessionId": "test-session-1749936289492", "context": {"phase": "design", "task": "UI Framework Selection"}, "feedback": {"type": "positive", "content": "I like the material design approach", "rating": 5}, "preferenceIndicators": [{"category": "design", "type": "uiFrameworks", "name": "material-ui", "preference": 1}]}, {"feedbackId": "feedback_1749936307382", "timestamp": "2025-06-14T21:25:07.383Z", "sessionId": "mcp-test-session-1749936307238", "context": {"phase": "design", "task": "UI Framework Selection"}, "feedback": {"type": "positive", "content": "I like the material design approach", "rating": 5}, "preferenceIndicators": [{"category": "design", "type": "uiFrameworks", "name": "material-ui", "preference": 1}]}, {"feedbackId": "feedback_1749936349260", "timestamp": "2025-06-14T21:25:49.260Z", "sessionId": "test-session-1749936349172", "context": {"phase": "design", "task": "UI Framework Selection"}, "feedback": {"type": "positive", "content": "I like the material design approach", "rating": 5}, "preferenceIndicators": [{"category": "design", "type": "uiFrameworks", "name": "material-ui", "preference": 1}]}, {"feedbackId": "feedback_1749936475161", "timestamp": "2025-06-14T21:27:55.161Z", "sessionId": "mcp-test-session-1749936475064", "context": {"phase": "design", "task": "UI Framework Selection"}, "feedback": {"type": "positive", "content": "I like the material design approach", "rating": 5}, "preferenceIndicators": [{"category": "design", "type": "uiFrameworks", "name": "material-ui", "preference": 1}]}, {"feedbackId": "feedback_1749936536871", "timestamp": "2025-06-14T21:28:56.871Z", "sessionId": "test-session-1749936536770", "context": {"phase": "design", "task": "UI Framework Selection"}, "feedback": {"type": "positive", "content": "I like the material design approach", "rating": 5}, "preferenceIndicators": [{"category": "design", "type": "uiFrameworks", "name": "material-ui", "preference": 1}]}, {"feedbackId": "feedback_1749936564102", "timestamp": "2025-06-14T21:29:24.102Z", "sessionId": "test-session-1749936564018", "context": {"phase": "design", "task": "UI Framework Selection"}, "feedback": {"type": "positive", "content": "I like the material design approach", "rating": 5}, "preferenceIndicators": [{"category": "design", "type": "uiFrameworks", "name": "material-ui", "preference": 1}]}, {"feedbackId": "feedback_1749936594705", "timestamp": "2025-06-14T21:29:54.705Z", "sessionId": "mcp-test-session-1749936594585", "context": {"phase": "design", "task": "UI Framework Selection"}, "feedback": {"type": "positive", "content": "I like the material design approach", "rating": 5}, "preferenceIndicators": [{"category": "design", "type": "uiFrameworks", "name": "material-ui", "preference": 1}]}]