/**
 * Research Testing Suite Runner
 * 
 * Comprehensive test runner for all research-related functionality
 * including unit tests, integration tests, performance tests, and acceptance tests.
 */

import { spawn } from 'child_process';
import { existsSync } from 'fs';
import path from 'path';

/**
 * Test suite configuration
 */
const TEST_SUITES = {
  unit: {
    name: 'Unit Tests',
    pattern: 'tests/research-providers/*.test.js',
    timeout: 30000,
    description: 'Tests for individual research provider functionality'
  },
  integration: {
    name: 'Integration Tests',
    pattern: 'tests/integration/research-*.test.js',
    timeout: 60000,
    description: 'End-to-end workflow and provider orchestration tests'
  },
  performance: {
    name: 'Performance Tests',
    pattern: 'tests/performance/*.test.js',
    timeout: 120000,
    description: 'Rate limiting, load testing, and performance validation'
  },
  acceptance: {
    name: 'Acceptance Tests',
    pattern: 'tests/acceptance/research-*.test.js',
    timeout: 90000,
    description: 'User acceptance and research quality validation tests'
  }
};

/**
 * Test execution options
 */
const DEFAULT_OPTIONS = {
  verbose: true,
  coverage: false,
  watch: false,
  bail: false,
  parallel: false
};

/**
 * Color codes for console output
 */
const COLORS = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

/**
 * Log with color
 */
function log(message, color = 'reset') {
  console.log(`${COLORS[color]}${message}${COLORS.reset}`);
}

/**
 * Run a test suite
 */
async function runTestSuite(suiteName, options = {}) {
  const suite = TEST_SUITES[suiteName];
  if (!suite) {
    throw new Error(`Unknown test suite: ${suiteName}`);
  }

  log(`\n${'='.repeat(60)}`, 'cyan');
  log(`Running ${suite.name}`, 'bright');
  log(`Description: ${suite.description}`, 'blue');
  log(`Pattern: ${suite.pattern}`, 'blue');
  log(`${'='.repeat(60)}`, 'cyan');

  const args = ['test'];
  
  // Add test pattern
  args.push(suite.pattern);
  
  // Add options
  if (options.verbose) args.push('--verbose');
  if (options.coverage) args.push('--coverage');
  if (options.watch) args.push('--watch');
  if (options.bail) args.push('--bail');
  
  // Set timeout
  args.push('--timeout', suite.timeout.toString());

  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const process = spawn('bun', args, {
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'test',
        // Load environment variables for testing
        TAVILY_API_KEY: process.env.TAVILY_API_KEY || 'test-key',
        FIRECRAWL_API_KEY: process.env.FIRECRAWL_API_KEY || 'test-key'
      }
    });

    process.on('close', (code) => {
      const endTime = Date.now();
      const duration = ((endTime - startTime) / 1000).toFixed(2);
      
      if (code === 0) {
        log(`\n✅ ${suite.name} completed successfully in ${duration}s`, 'green');
        resolve({ success: true, duration, code });
      } else {
        log(`\n❌ ${suite.name} failed with exit code ${code} after ${duration}s`, 'red');
        resolve({ success: false, duration, code });
      }
    });

    process.on('error', (error) => {
      log(`\n💥 ${suite.name} crashed: ${error.message}`, 'red');
      reject(error);
    });
  });
}

/**
 * Run all test suites
 */
async function runAllTests(options = {}) {
  log('\n🧪 Starting Research Testing Suite', 'bright');
  log(`Options: ${JSON.stringify(options)}`, 'blue');
  
  const results = {};
  const startTime = Date.now();
  let totalPassed = 0;
  let totalFailed = 0;

  for (const [suiteName, suite] of Object.entries(TEST_SUITES)) {
    try {
      const result = await runTestSuite(suiteName, options);
      results[suiteName] = result;
      
      if (result.success) {
        totalPassed++;
      } else {
        totalFailed++;
        if (options.bail) {
          log('\n🛑 Stopping due to --bail option', 'yellow');
          break;
        }
      }
    } catch (error) {
      log(`\n💥 Failed to run ${suite.name}: ${error.message}`, 'red');
      results[suiteName] = { success: false, error: error.message };
      totalFailed++;
      
      if (options.bail) {
        log('\n🛑 Stopping due to --bail option', 'yellow');
        break;
      }
    }
  }

  const endTime = Date.now();
  const totalDuration = ((endTime - startTime) / 1000).toFixed(2);

  // Print summary
  log('\n' + '='.repeat(60), 'cyan');
  log('TEST SUITE SUMMARY', 'bright');
  log('='.repeat(60), 'cyan');
  
  for (const [suiteName, result] of Object.entries(results)) {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    const duration = result.duration ? `(${result.duration}s)` : '';
    log(`${status} ${TEST_SUITES[suiteName].name} ${duration}`, result.success ? 'green' : 'red');
  }
  
  log('\n' + '-'.repeat(60), 'cyan');
  log(`Total: ${totalPassed + totalFailed} suites`, 'blue');
  log(`Passed: ${totalPassed}`, 'green');
  log(`Failed: ${totalFailed}`, totalFailed > 0 ? 'red' : 'blue');
  log(`Duration: ${totalDuration}s`, 'blue');
  log('-'.repeat(60), 'cyan');

  if (totalFailed === 0) {
    log('\n🎉 All research tests passed!', 'green');
    return true;
  } else {
    log(`\n💔 ${totalFailed} test suite(s) failed`, 'red');
    return false;
  }
}

/**
 * Run specific test files
 */
async function runSpecificTests(testFiles, options = {}) {
  log('\n🎯 Running specific test files', 'bright');
  
  for (const testFile of testFiles) {
    if (!existsSync(testFile)) {
      log(`❌ Test file not found: ${testFile}`, 'red');
      continue;
    }
    
    log(`\nRunning: ${testFile}`, 'blue');
    
    const args = ['test', testFile];
    if (options.verbose) args.push('--verbose');
    if (options.coverage) args.push('--coverage');
    
    await new Promise((resolve) => {
      const process = spawn('bun', args, {
        stdio: 'inherit',
        env: {
          ...process.env,
          NODE_ENV: 'test'
        }
      });
      
      process.on('close', resolve);
    });
  }
}

/**
 * Parse command line arguments
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const options = { ...DEFAULT_OPTIONS };
  const suites = [];
  const files = [];
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--verbose':
      case '-v':
        options.verbose = true;
        break;
      case '--coverage':
      case '-c':
        options.coverage = true;
        break;
      case '--watch':
      case '-w':
        options.watch = true;
        break;
      case '--bail':
      case '-b':
        options.bail = true;
        break;
      case '--parallel':
      case '-p':
        options.parallel = true;
        break;
      case '--help':
      case '-h':
        printHelp();
        process.exit(0);
        break;
      default:
        if (TEST_SUITES[arg]) {
          suites.push(arg);
        } else if (arg.endsWith('.test.js')) {
          files.push(arg);
        } else {
          log(`Unknown option: ${arg}`, 'yellow');
        }
    }
  }
  
  return { options, suites, files };
}

/**
 * Print help information
 */
function printHelp() {
  log('\n🧪 Research Testing Suite Runner', 'bright');
  log('\nUsage:', 'blue');
  log('  bun run test:research [options] [suites] [files]', 'cyan');
  
  log('\nTest Suites:', 'blue');
  for (const [name, suite] of Object.entries(TEST_SUITES)) {
    log(`  ${name.padEnd(12)} - ${suite.description}`, 'cyan');
  }
  
  log('\nOptions:', 'blue');
  log('  -v, --verbose    Verbose output', 'cyan');
  log('  -c, --coverage   Generate coverage report', 'cyan');
  log('  -w, --watch      Watch mode', 'cyan');
  log('  -b, --bail       Stop on first failure', 'cyan');
  log('  -p, --parallel   Run tests in parallel', 'cyan');
  log('  -h, --help       Show this help', 'cyan');
  
  log('\nExamples:', 'blue');
  log('  bun run test:research                    # Run all test suites', 'cyan');
  log('  bun run test:research unit integration   # Run specific suites', 'cyan');
  log('  bun run test:research --coverage --bail  # Run with coverage and bail', 'cyan');
  log('  bun run test:research file.test.js       # Run specific test file', 'cyan');
}

/**
 * Main execution
 */
async function main() {
  try {
    const { options, suites, files } = parseArgs();
    
    if (files.length > 0) {
      await runSpecificTests(files, options);
    } else if (suites.length > 0) {
      for (const suite of suites) {
        await runTestSuite(suite, options);
      }
    } else {
      const success = await runAllTests(options);
      process.exit(success ? 0 : 1);
    }
  } catch (error) {
    log(`\n💥 Test runner failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export {
  runTestSuite,
  runAllTests,
  runSpecificTests,
  TEST_SUITES
};
