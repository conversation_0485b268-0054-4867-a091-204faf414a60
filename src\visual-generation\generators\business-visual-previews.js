/**
 * Business-Friendly Visual Previews
 * 
 * Creates simple visual previews that help non-technical users understand
 * what their software will look like and do, without technical complexity.
 * 
 * Focus: Business functionality, not technical implementation
 * Audience: Non-technical stakeholders and users
 * Purpose: Requirement validation and stakeholder communication
 */

export class BusinessVisualPreviewGenerator {
  constructor(config = {}) {
    this.config = {
      showTechnicalDetails: false,
      focusOnBusinessValue: true,
      useSimpleLanguage: true,
      includeUserActions: true,
      ...config
    };
  }

  /**
   * Generate business-friendly visual preview from requirements
   */
  async generatePreview(requirements, options = {}) {
    console.log('🎨 Generating business-friendly visual preview...');

    try {
      // Extract business components from requirements
      const businessComponents = this.extractBusinessComponents(requirements);
      
      // Create simple visual layout
      const visualLayout = this.createVisualLayout(businessComponents);
      
      // Generate business descriptions
      const businessDescriptions = this.generateBusinessDescriptions(businessComponents);
      
      // Create user interaction flows
      const userFlows = this.createUserFlows(businessComponents);

      const preview = {
        title: requirements.title || 'Your Software Preview',
        description: requirements.description || 'Here\'s what your software will look like and do',
        layout: visualLayout,
        components: businessDescriptions,
        userFlows: userFlows,
        businessValue: this.extractBusinessValue(requirements),
        nextSteps: this.generateNextSteps(requirements),
        metadata: {
          generatedAt: new Date().toISOString(),
          purpose: 'business_validation',
          audience: 'non_technical_stakeholders'
        }
      };

      console.log('✅ Business preview generated successfully');
      return preview;

    } catch (error) {
      console.error('❌ Business preview generation failed:', error);
      throw new Error(`Business preview generation failed: ${error.message}`);
    }
  }

  /**
   * Extract business components (what users will see and do)
   */
  extractBusinessComponents(requirements) {
    const components = [];

    // Look for user-facing functionality
    if (requirements.componentSpecs) {
      requirements.componentSpecs.forEach(spec => {
        components.push({
          id: spec.id,
          name: spec.name,
          businessPurpose: this.translateToBusinessPurpose(spec),
          userActions: this.extractUserActions(spec),
          businessValue: this.extractComponentBusinessValue(spec)
        });
      });
    }

    // Look for functional requirements
    if (requirements.functionalRequirements) {
      requirements.functionalRequirements.forEach(req => {
        components.push({
          id: req.id || `req_${components.length}`,
          name: req.title || req.description,
          businessPurpose: req.description,
          userActions: this.extractActionsFromRequirement(req),
          businessValue: req.businessValue || 'Supports business operations'
        });
      });
    }

    return components;
  }

  /**
   * Create simple visual layout description
   */
  createVisualLayout(components) {
    const layout = {
      sections: [],
      description: 'Simple layout showing what users will see'
    };

    // Group components by business function
    const groups = this.groupComponentsByFunction(components);

    Object.entries(groups).forEach(([functionName, groupComponents]) => {
      layout.sections.push({
        name: functionName,
        purpose: `Where users ${functionName.toLowerCase()}`,
        components: groupComponents.map(comp => ({
          name: comp.name,
          purpose: comp.businessPurpose,
          location: this.suggestLocation(comp)
        }))
      });
    });

    return layout;
  }

  /**
   * Generate business-friendly descriptions
   */
  generateBusinessDescriptions(components) {
    return components.map(comp => ({
      name: comp.name,
      whatItDoes: comp.businessPurpose,
      howUsersInteract: comp.userActions.join(', '),
      businessBenefit: comp.businessValue,
      example: this.generateExample(comp)
    }));
  }

  /**
   * Create user interaction flows in business terms
   */
  createUserFlows(components) {
    const flows = [];

    // Create common user journeys
    const journeys = this.identifyUserJourneys(components);
    
    journeys.forEach(journey => {
      flows.push({
        name: journey.name,
        description: journey.description,
        steps: journey.steps.map(step => ({
          action: step.action,
          result: step.result,
          businessValue: step.businessValue
        }))
      });
    });

    return flows;
  }

  /**
   * Translate technical specs to business purpose
   */
  translateToBusinessPurpose(spec) {
    const purposeMap = {
      'form': 'Collect information from users',
      'table': 'Display data in organized rows and columns',
      'button': 'Allow users to take actions',
      'navigation': 'Help users move between different sections',
      'header': 'Show the main title and branding',
      'footer': 'Provide additional links and information',
      'card': 'Display information in organized blocks',
      'modal': 'Show important messages or forms',
      'search': 'Help users find specific information'
    };

    return purposeMap[spec.type] || `Provide ${spec.name.toLowerCase()} functionality`;
  }

  /**
   * Extract user actions from component spec
   */
  extractUserActions(spec) {
    const actions = [];

    switch (spec.type) {
      case 'form':
        actions.push('Fill out information', 'Submit data');
        break;
      case 'button':
        actions.push(`Click to ${spec.name.toLowerCase()}`);
        break;
      case 'navigation':
        actions.push('Navigate between sections');
        break;
      case 'search':
        actions.push('Enter search terms', 'View results');
        break;
      case 'table':
        actions.push('View data', 'Sort information');
        break;
      default:
        actions.push(`Interact with ${spec.name.toLowerCase()}`);
    }

    return actions;
  }

  /**
   * Group components by business function
   */
  groupComponentsByFunction(components) {
    const groups = {
      'Data Entry': [],
      'Information Display': [],
      'Navigation': [],
      'User Actions': []
    };

    components.forEach(comp => {
      if (comp.name.toLowerCase().includes('form') || comp.name.toLowerCase().includes('input')) {
        groups['Data Entry'].push(comp);
      } else if (comp.name.toLowerCase().includes('table') || comp.name.toLowerCase().includes('list')) {
        groups['Information Display'].push(comp);
      } else if (comp.name.toLowerCase().includes('nav') || comp.name.toLowerCase().includes('menu')) {
        groups['Navigation'].push(comp);
      } else {
        groups['User Actions'].push(comp);
      }
    });

    // Remove empty groups
    Object.keys(groups).forEach(key => {
      if (groups[key].length === 0) {
        delete groups[key];
      }
    });

    return groups;
  }

  /**
   * Suggest location for component
   */
  suggestLocation(component) {
    const locationMap = {
      'header': 'Top of the page',
      'navigation': 'Top or side of the page',
      'footer': 'Bottom of the page',
      'form': 'Main content area',
      'table': 'Main content area',
      'button': 'Near related content'
    };

    return locationMap[component.type] || 'Main content area';
  }

  /**
   * Generate example for component
   */
  generateExample(component) {
    const examples = {
      'form': 'Like a contact form where customers enter their name and email',
      'table': 'Like a spreadsheet showing customer orders with dates and amounts',
      'button': 'Like a "Save" or "Submit" button users click to complete actions',
      'navigation': 'Like a menu bar with "Home", "Products", "Contact" links',
      'search': 'Like a search box where users type what they\'re looking for'
    };

    return examples[component.type] || `Similar to ${component.name.toLowerCase()} functionality you've seen on other websites`;
  }

  /**
   * Identify common user journeys
   */
  identifyUserJourneys(components) {
    const journeys = [];

    // Look for form submission journey
    const forms = components.filter(c => c.name.toLowerCase().includes('form'));
    if (forms.length > 0) {
      journeys.push({
        name: 'Submit Information',
        description: 'How users provide information to your system',
        steps: [
          { action: 'User opens the form', result: 'Form is displayed', businessValue: 'User can start providing information' },
          { action: 'User fills out fields', result: 'Information is entered', businessValue: 'System collects needed data' },
          { action: 'User submits form', result: 'Information is saved', businessValue: 'Business receives customer data' }
        ]
      });
    }

    // Look for data viewing journey
    const tables = components.filter(c => c.name.toLowerCase().includes('table') || c.name.toLowerCase().includes('list'));
    if (tables.length > 0) {
      journeys.push({
        name: 'View Information',
        description: 'How users access and review data',
        steps: [
          { action: 'User navigates to data section', result: 'Data is displayed', businessValue: 'User can access needed information' },
          { action: 'User reviews information', result: 'User understands data', businessValue: 'Informed decision making' }
        ]
      });
    }

    return journeys;
  }

  /**
   * Extract business value from requirements
   */
  extractBusinessValue(requirements) {
    const values = [];

    if (requirements.businessGoals) {
      values.push(...requirements.businessGoals);
    }

    if (requirements.description) {
      values.push(`Provides ${requirements.description.toLowerCase()}`);
    }

    if (values.length === 0) {
      values.push('Supports business operations and user needs');
    }

    return values;
  }

  /**
   * Generate next steps for business users
   */
  generateNextSteps(requirements) {
    return [
      'Review this preview with your team',
      'Confirm this matches your business needs',
      'Provide feedback on any changes needed',
      'Approve to proceed with AI-powered development',
      'Receive your working software solution'
    ];
  }

  /**
   * Extract component business value
   */
  extractComponentBusinessValue(spec) {
    if (spec.businessValue) return spec.businessValue;
    if (spec.description) return `Enables ${spec.description.toLowerCase()}`;
    return `Supports ${spec.name.toLowerCase()} functionality`;
  }

  /**
   * Extract actions from functional requirement
   */
  extractActionsFromRequirement(req) {
    const actions = [];
    
    if (req.description) {
      const desc = req.description.toLowerCase();
      if (desc.includes('enter') || desc.includes('input')) actions.push('Enter information');
      if (desc.includes('view') || desc.includes('see')) actions.push('View information');
      if (desc.includes('click') || desc.includes('select')) actions.push('Make selections');
      if (desc.includes('submit') || desc.includes('save')) actions.push('Save changes');
    }

    return actions.length > 0 ? actions : ['Interact with the system'];
  }
}
