/**
 * Project Resources Handler
 * Handles MCP Resource requests for project-related data
 */

import { URI<PERSON><PERSON>plate<PERSON>arser, MCPResourceURI } from '../../utils/uri-template-parser.js';
import { RESOURCE_TEMPLATES } from '../../schemas/resource-schemas.js';

/**
 * Project Resource Handler
 * Converts existing project management tools to MCP Resources
 */
export class ProjectResourceHandler {
  constructor(existingProjectManager) {
    this.projectManager = existingProjectManager;
    this.templates = {
      'project_state': RESOURCE_TEMPLATES.project_state,
      'project_phase': RESOURCE_TEMPLATES.project_phase,
      'current_task': RESOURCE_TEMPLATES.current_task,
      'project_deliverables': RESOURCE_TEMPLATES.project_deliverables
    };
  }

  /**
   * Get resource templates for this handler
   * @returns {Array} Array of resource templates
   */
  getResourceTemplates() {
    return Object.values(this.templates);
  }

  /**
   * Handle resource read request
   * @param {string} uri - Resource URI
   * @returns {object} Resource content
   */
  async handleResourceRead(uri) {
    const parsed = MCPResourceURI.parse(uri);
    if (!parsed || parsed.type !== 'project') {
      throw new Error(`Invalid project resource URI: ${uri}`);
    }

    const { id: projectId, subpath } = parsed;

    try {
      switch (subpath) {
        case 'state':
          return await this.getProjectState(projectId, uri);
        case 'current-task':
          return await this.getCurrentTask(projectId, uri);
        default:
          // Handle parameterized paths
          return await this.handleParameterizedPath(projectId, subpath, uri);
      }
    } catch (error) {
      throw new Error(`Failed to read project resource: ${error.message}`);
    }
  }

  /**
   * Handle parameterized resource paths
   * @param {string} projectId - Project ID
   * @param {string} subpath - Resource subpath
   * @param {string} uri - Full URI
   * @returns {object} Resource content
   */
  async handleParameterizedPath(projectId, subpath, uri) {
    // Handle phase-specific resources
    const phaseMatch = subpath.match(/^phase\/([^/]+)$/);
    if (phaseMatch) {
      const phase = decodeURIComponent(phaseMatch[1]);
      return await this.getPhaseData(projectId, phase, uri);
    }

    // Handle deliverables with optional type filter
    const deliverablesMatch = subpath.match(/^deliverables(?:\/([^/]+))?$/);
    if (deliverablesMatch) {
      const type = deliverablesMatch[1] ? decodeURIComponent(deliverablesMatch[1]) : null;
      return await this.getProjectDeliverables(projectId, type, uri);
    }

    throw new Error(`Unknown project resource path: ${subpath}`);
  }

  /**
   * Get project state data
   * @param {string} projectId - Project ID
   * @param {string} uri - Resource URI
   * @returns {object} Project state resource
   */
  async getProjectState(projectId, uri) {
    try {
      // Use existing project management tool
      const state = await this.projectManager.getProjectState({ projectId });
      
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          projectId,
          name: state.name || 'Unknown Project',
          description: state.description || '',
          phase: state.currentPhase || 'unknown',
          status: state.status || 'active',
          created: state.created || new Date().toISOString(),
          updated: new Date().toISOString(),
          configuration: state.configuration || {},
          metadata: {
            resourceType: 'project_state',
            version: '1.0',
            lastAccessed: new Date().toISOString(),
            ...state.metadata
          }
        }, null, 2)
      };
    } catch (error) {
      // Fallback for missing project
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          projectId,
          name: 'Project Not Found',
          description: 'Project data not available',
          phase: 'unknown',
          status: 'not_found',
          created: new Date().toISOString(),
          updated: new Date().toISOString(),
          configuration: {},
          metadata: {
            resourceType: 'project_state',
            version: '1.0',
            error: error.message,
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    }
  }

  /**
   * Get current task data
   * @param {string} projectId - Project ID
   * @param {string} uri - Resource URI
   * @returns {object} Current task resource
   */
  async getCurrentTask(projectId, uri) {
    try {
      // Use existing workflow control tool
      const task = await this.projectManager.getCurrentTask({ projectId });
      
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          taskId: task.id || 'unknown',
          projectId,
          name: task.name || 'No Active Task',
          description: task.description || '',
          status: task.status || 'pending',
          priority: task.priority || 'medium',
          assignee: task.assignee || 'unassigned',
          created: task.created || new Date().toISOString(),
          updated: task.updated || new Date().toISOString(),
          context: {
            phase: task.phase,
            dependencies: task.dependencies || [],
            progress: task.progress || 0,
            ...task.context
          },
          metadata: {
            resourceType: 'current_task',
            version: '1.0',
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    } catch (error) {
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          taskId: null,
          projectId,
          name: 'No Active Task',
          description: 'No current task available',
          status: 'none',
          priority: 'medium',
          assignee: 'unassigned',
          created: new Date().toISOString(),
          updated: new Date().toISOString(),
          context: {},
          metadata: {
            resourceType: 'current_task',
            version: '1.0',
            error: error.message,
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    }
  }

  /**
   * Get phase-specific data
   * @param {string} projectId - Project ID
   * @param {string} phase - Phase name
   * @param {string} uri - Resource URI
   * @returns {object} Phase data resource
   */
  async getPhaseData(projectId, phase, uri) {
    try {
      // Use existing deliverable analysis tool
      const phaseData = await this.projectManager.analyzePhase({ 
        projectId, 
        phase 
      });
      
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          projectId,
          phase,
          status: phaseData.status || 'in_progress',
          progress: phaseData.progress || 0,
          deliverables: phaseData.deliverables || [],
          tasks: phaseData.tasks || [],
          timeline: {
            started: phaseData.started,
            estimated_completion: phaseData.estimatedCompletion,
            actual_completion: phaseData.actualCompletion
          },
          dependencies: phaseData.dependencies || [],
          insights: phaseData.insights || [],
          metadata: {
            resourceType: 'project_phase',
            version: '1.0',
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    } catch (error) {
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          projectId,
          phase,
          status: 'unknown',
          progress: 0,
          deliverables: [],
          tasks: [],
          timeline: {},
          dependencies: [],
          insights: [],
          metadata: {
            resourceType: 'project_phase',
            version: '1.0',
            error: error.message,
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    }
  }

  /**
   * Get project deliverables
   * @param {string} projectId - Project ID
   * @param {string|null} type - Optional deliverable type filter
   * @param {string} uri - Resource URI
   * @returns {object} Deliverables resource
   */
  async getProjectDeliverables(projectId, type, uri) {
    try {
      // Use existing project management functionality
      const deliverables = await this.projectManager.getDeliverables({ 
        projectId,
        type 
      });
      
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          projectId,
          type: type || 'all',
          count: deliverables.length,
          deliverables: deliverables.map(d => ({
            id: d.id,
            name: d.name,
            type: d.type,
            status: d.status,
            created: d.created,
            updated: d.updated,
            phase: d.phase,
            quality_score: d.qualityScore
          })),
          metadata: {
            resourceType: 'project_deliverables',
            version: '1.0',
            lastAccessed: new Date().toISOString(),
            filter: type ? { type } : null
          }
        }, null, 2)
      };
    } catch (error) {
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          projectId,
          type: type || 'all',
          count: 0,
          deliverables: [],
          metadata: {
            resourceType: 'project_deliverables',
            version: '1.0',
            error: error.message,
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    }
  }

  /**
   * Check if this handler can handle a given URI
   * @param {string} uri - URI to check
   * @returns {boolean} True if this handler can process the URI
   */
  canHandle(uri) {
    const parsed = MCPResourceURI.parse(uri);
    return parsed && parsed.type === 'project';
  }
}

export default ProjectResourceHandler;
