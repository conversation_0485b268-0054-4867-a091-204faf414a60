/**
 * ASCII Layout Algorithm Engine
 * Implements grid-based layout algorithm for ASCII wireframe generation
 */

import { VisualGenerationConfig } from '../config/visual-generation-config.js';

/**
 * ASCII Layout Engine
 */
export class ASCIILayoutEngine {
  constructor(config = {}) {
    this.config = new VisualGenerationConfig(config);
    this.wireframeConfig = this.config.getGeneratorConfig('wireframe');
    this.asciiChars = this.wireframeConfig.asciiCharacters;
    this.gridSystem = this.wireframeConfig.gridSystem;
    this.layout = this.wireframeConfig.layout;
  }

  /**
   * Generate ASCII wireframe layout from components
   */
  generateLayout(components, options = {}) {
    const layoutOptions = {
      breakpoint: options.breakpoint || 'desktop',
      width: options.width || 80, // Fixed width for better readability
      showLabels: options.showLabels !== false,
      ...options
    };

    // Use simple stacked layout instead of complex grid
    const asciiOutput = this.generateSimpleLayout(components, layoutOptions);

    return {
      ascii: asciiOutput,
      components: components,
      metadata: {
        breakpoint: layoutOptions.breakpoint,
        dimensions: { width: layoutOptions.width },
        componentCount: components.length,
        generatedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Generate simple stacked layout
   */
  generateSimpleLayout(components, options) {
    const width = options.width;
    let ascii = '';

    // Add title
    ascii += this.createBox('WIREFRAME - ' + (options.breakpoint || 'desktop').toUpperCase(), width) + '\n\n';

    // Add each component as a simple box
    for (let i = 0; i < components.length; i++) {
      const component = components[i];
      const componentName = component.name || component.type || 'Component';

      ascii += this.createComponentBox(componentName, component.type, width) + '\n';

      if (i < components.length - 1) {
        ascii += '\n'; // Space between components
      }
    }

    return ascii;
  }

  /**
   * Create a simple ASCII box
   */
  createBox(title, width) {
    const innerWidth = width - 4;
    const paddedTitle = title.length > innerWidth ? title.substring(0, innerWidth) : title;
    const padding = Math.max(0, Math.floor((innerWidth - paddedTitle.length) / 2));

    let box = '┌' + '─'.repeat(width - 2) + '┐\n';
    box += '│ ' + ' '.repeat(padding) + paddedTitle + ' '.repeat(innerWidth - padding - paddedTitle.length) + ' │\n';
    box += '└' + '─'.repeat(width - 2) + '┘';

    return box;
  }

  /**
   * Create component-specific ASCII box
   */
  createComponentBox(name, type, width) {
    const innerWidth = width - 4;
    let content = [];

    // Add component-specific content
    switch (type) {
      case 'header':
        content = [
          `[LOGO]  ${name}  [USER MENU ▼]`,
          '─'.repeat(Math.min(innerWidth, 40))
        ];
        break;
      case 'navigation':
        content = [
          '[Home] [Products] [About] [Services] [Contact] [Support]'
        ];
        break;
      case 'form':
        content = [
          `📝 ${name}`,
          '',
          'Email Address: [_________________________]',
          'Password:      [_________________________]',
          '',
          '☐ Remember me    [Forgot Password?]',
          '',
          '[    Login    ] [  Register  ]'
        ];
        break;
      case 'table':
        content = [
          `📊 ${name}`,
          '',
          '┌─────────────┬──────────────────┬─────────┬─────────┐',
          '│ Name        │ Email            │ Status  │ Actions │',
          '├─────────────┼──────────────────┼─────────┼─────────┤',
          '│ John Smith  │ <EMAIL> │ Active  │ [Edit]  │',
          '│ Jane Doe    │ <EMAIL> │ Active  │ [Edit]  │',
          '│ Bob Wilson  │ <EMAIL>  │ Pending │ [Edit]  │',
          '└─────────────┴──────────────────┴─────────┴─────────┘',
          '',
          'Showing 3 of 156 records  [Previous] [1] [2] [3] [Next]'
        ];
        break;
      case 'search':
        content = [
          '🔍 Search & Filter',
          '',
          'Search: [_________________________] [Search]',
          '',
          'Filters: [Category ▼] [Date Range ▼] [Status ▼] [Clear]'
        ];
        break;
      case 'button':
        content = [
          '',
          `[    ${name}    ]`,
          ''
        ];
        break;
      case 'upload':
        content = [
          '📁 File Upload',
          '',
          '┌─────────────────────────────────────────┐',
          '│  📄 Drag & drop files here             │',
          '│      or                                 │',
          '│  [    Browse Files    ]                 │',
          '└─────────────────────────────────────────┘',
          '',
          'Supported: PDF, DOC, JPG, PNG (Max 10MB)'
        ];
        break;
      case 'dashboard':
        content = [
          `📈 ${name}`,
          '',
          '┌─────────────┬─────────────┬─────────────┐',
          '│ Total Users │ Revenue     │ Orders      │',
          '│    1,234    │  $45,678    │    567      │',
          '└─────────────┴─────────────┴─────────────┘',
          '',
          '┌─────────────────────────────────────────┐',
          '│ 📊 Sales Chart                         │',
          '│ ▁▃▅▇█▇▅▃▁ Monthly Performance          │',
          '└─────────────────────────────────────────┘'
        ];
        break;
      case 'sidebar':
        content = [
          '📋 Navigation',
          '',
          '▶ Dashboard',
          '▶ Users',
          '▶ Products',
          '▶ Orders',
          '▶ Reports',
          '▶ Settings',
          '',
          '─────────────',
          '👤 User Profile',
          '[Logout]'
        ];
        break;
      case 'footer':
        content = [
          '© 2024 Company Name. All rights reserved.',
          'Privacy Policy | Terms of Service | Support',
          '📧 <EMAIL> | 📞 +1-555-0123 | 🌐 www.company.com'
        ];
        break;
      case 'content':
        content = [
          `📄 ${name}`,
          '',
          'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
          'Sed do eiusmod tempor incididunt ut labore et dolore magna',
          'aliqua. Ut enim ad minim veniam, quis nostrud exercitation.',
          '',
          '[Read More...] [Share] [Print]'
        ];
        break;
      default:
        content = [
          `🔧 ${name}`,
          '',
          '(Component content will be displayed here)',
          '',
          'This is a placeholder for the component.'
        ];
    }

    // Build the box
    let box = '┌' + '─'.repeat(width - 2) + '┐\n';

    for (const line of content) {
      const truncated = line.length > innerWidth ? line.substring(0, innerWidth) : line;
      const padded = truncated + ' '.repeat(innerWidth - truncated.length);
      box += '│ ' + padded + ' │\n';
    }

    box += '└' + '─'.repeat(width - 2) + '┘';

    return box;
  }

  /**
   * Initialize grid structure
   */
  initializeGrid(options) {
    const rows = Math.ceil(options.height / 20); // 20px per row approximation
    const cols = this.getBreakpointColumns(options.breakpoint);
    
    const grid = {
      rows,
      cols,
      cells: Array(rows).fill(null).map(() => 
        Array(cols).fill(null).map(() => ({
          content: ' ',
          component: null,
          border: { top: false, right: false, bottom: false, left: false },
          occupied: false
        }))
      ),
      metadata: {
        cellWidth: Math.floor(options.width / cols),
        cellHeight: 20,
        totalCells: rows * cols
      }
    };
    
    return grid;
  }

  /**
   * Position components using layout algorithm
   */
  positionComponents(components, options) {
    const positioned = [];
    const cols = this.getBreakpointColumns(options.breakpoint);
    
    // Sort components by priority and type
    const sortedComponents = this.sortComponentsByLayout(components);
    
    let currentRow = 0;
    let currentCol = 0;
    
    for (const component of sortedComponents) {
      const componentLayout = this.calculateComponentLayout(component, options);
      const position = this.findOptimalPosition(
        componentLayout, 
        currentRow, 
        currentCol, 
        cols,
        positioned
      );
      
      const positionedComponent = {
        ...component,
        layout: componentLayout,
        position,
        gridSpan: {
          colStart: position.col,
          colEnd: position.col + componentLayout.colSpan,
          rowStart: position.row,
          rowEnd: position.row + componentLayout.rowSpan
        }
      };
      
      positioned.push(positionedComponent);
      
      // Update current position for next component
      if (component.type === 'header' || component.type === 'navigation') {
        currentRow = position.row + componentLayout.rowSpan;
        currentCol = 0;
      } else if (component.type === 'footer') {
        // Footer goes at bottom, don't update current position
      } else {
        currentCol = position.col + componentLayout.colSpan;
        if (currentCol >= cols) {
          currentRow = position.row + componentLayout.rowSpan;
          currentCol = 0;
        }
      }
    }
    
    return positioned;
  }

  /**
   * Calculate component layout properties
   */
  calculateComponentLayout(component, options) {
    const cols = this.getBreakpointColumns(options.breakpoint);
    const componentType = component.type || 'content';
    
    // Default layout rules based on component type
    const layoutRules = {
      header: { colSpan: cols, rowSpan: 3, priority: 1 },
      navigation: { colSpan: 3, rowSpan: 10, priority: 2 },
      sidebar: { colSpan: 3, rowSpan: 8, priority: 3 },
      content: { colSpan: cols - 3, rowSpan: 6, priority: 4 },
      form: { colSpan: Math.min(6, cols - 3), rowSpan: 8, priority: 5 },
      table: { colSpan: cols - 3, rowSpan: 10, priority: 6 },
      button: { colSpan: 2, rowSpan: 2, priority: 7 },
      footer: { colSpan: cols, rowSpan: 2, priority: 10 }
    };
    
    const defaultLayout = layoutRules[componentType] || layoutRules.content;
    
    // Apply component-specific overrides
    const layout = {
      ...defaultLayout,
      colSpan: component.colSpan || defaultLayout.colSpan,
      rowSpan: component.rowSpan || defaultLayout.rowSpan,
      priority: component.priority || defaultLayout.priority,
      alignment: component.alignment || 'left',
      padding: component.padding || 1
    };
    
    // Adjust for breakpoint
    if (options.breakpoint === 'mobile') {
      layout.colSpan = Math.min(layout.colSpan, cols);
      if (componentType !== 'header' && componentType !== 'footer') {
        layout.colSpan = cols; // Full width on mobile
      }
    } else if (options.breakpoint === 'tablet') {
      layout.colSpan = Math.min(layout.colSpan, Math.floor(cols * 0.8));
    }
    
    return layout;
  }

  /**
   * Find optimal position for component
   */
  findOptimalPosition(componentLayout, startRow, startCol, totalCols, existingComponents) {
    const { colSpan, rowSpan } = componentLayout;
    
    // Try to place at current position first
    if (this.canPlaceComponent(startRow, startCol, colSpan, rowSpan, totalCols, existingComponents)) {
      return { row: startRow, col: startCol };
    }
    
    // Search for next available position
    for (let row = startRow; row < 50; row++) { // Limit search to prevent infinite loop
      for (let col = 0; col <= totalCols - colSpan; col++) {
        if (this.canPlaceComponent(row, col, colSpan, rowSpan, totalCols, existingComponents)) {
          return { row, col };
        }
      }
    }
    
    // Fallback position
    return { row: startRow + 1, col: 0 };
  }

  /**
   * Check if component can be placed at position
   */
  canPlaceComponent(row, col, colSpan, rowSpan, totalCols, existingComponents) {
    // Check bounds
    if (col + colSpan > totalCols || row < 0 || col < 0) {
      return false;
    }
    
    // Check for overlaps with existing components
    for (const existing of existingComponents) {
      if (this.componentsOverlap(
        { row, col, colSpan, rowSpan },
        existing.gridSpan
      )) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Check if two components overlap
   */
  componentsOverlap(comp1, comp2) {
    return !(
      comp1.col + comp1.colSpan <= comp2.colStart ||
      comp2.colEnd <= comp1.col ||
      comp1.row + comp1.rowSpan <= comp2.rowStart ||
      comp2.rowEnd <= comp1.row
    );
  }

  /**
   * Render components to grid
   */
  renderComponentsToGrid(grid, components, options) {
    for (const component of components) {
      this.renderComponentToGrid(grid, component, options);
    }
  }

  /**
   * Render single component to grid
   */
  renderComponentToGrid(grid, component, options) {
    const { position, layout, gridSpan } = component;
    const { rowStart, rowEnd, colStart, colEnd } = gridSpan;
    
    // Draw component border
    this.drawComponentBorder(grid, gridSpan);
    
    // Add component label
    if (options.showLabels && component.name) {
      this.addComponentLabel(grid, component, gridSpan);
    }
    
    // Mark cells as occupied
    for (let row = rowStart; row < Math.min(rowEnd, grid.rows); row++) {
      for (let col = colStart; col < Math.min(colEnd, grid.cols); col++) {
        if (grid.cells[row] && grid.cells[row][col]) {
          grid.cells[row][col].occupied = true;
          grid.cells[row][col].component = component.id || component.name;
        }
      }
    }
  }

  /**
   * Draw component border
   */
  drawComponentBorder(grid, gridSpan) {
    const { rowStart, rowEnd, colStart, colEnd } = gridSpan;
    
    for (let row = rowStart; row < Math.min(rowEnd, grid.rows); row++) {
      for (let col = colStart; col < Math.min(colEnd, grid.cols); col++) {
        if (!grid.cells[row] || !grid.cells[row][col]) continue;
        
        const cell = grid.cells[row][col];
        
        // Set border flags
        if (row === rowStart) cell.border.top = true;
        if (row === rowEnd - 1) cell.border.bottom = true;
        if (col === colStart) cell.border.left = true;
        if (col === colEnd - 1) cell.border.right = true;
      }
    }
  }

  /**
   * Add component label
   */
  addComponentLabel(grid, component, gridSpan) {
    const { rowStart, colStart, colEnd } = gridSpan;
    const label = component.name || component.type || 'Component';
    const labelRow = rowStart + 1; // Place label one row down from top border
    
    if (labelRow >= grid.rows) return;
    
    // Center the label within the component
    const availableWidth = colEnd - colStart - 2; // Account for borders
    const startCol = colStart + 1 + Math.max(0, Math.floor((availableWidth - label.length) / 2));
    
    for (let i = 0; i < label.length && startCol + i < colEnd - 1; i++) {
      if (grid.cells[labelRow] && grid.cells[labelRow][startCol + i]) {
        grid.cells[labelRow][startCol + i].content = label[i];
      }
    }
  }

  /**
   * Convert grid to ASCII string
   */
  gridToASCII(grid, options) {
    const lines = [];
    
    for (let row = 0; row < grid.rows; row++) {
      const line = [];
      
      for (let col = 0; col < grid.cols; col++) {
        const cell = grid.cells[row][col];
        
        if (cell.content !== ' ') {
          line.push(cell.content);
        } else {
          line.push(this.getBorderCharacter(cell, grid, row, col));
        }
      }
      
      lines.push(line.join(''));
    }
    
    return lines.join('\n');
  }

  /**
   * Get appropriate border character for cell
   */
  getBorderCharacter(cell, grid, row, col) {
    const { border } = cell;
    const chars = this.asciiChars;

    // Simple approach: just use the border flags directly
    if (border.top && border.left) return chars.topLeft;
    if (border.top && border.right) return chars.topRight;
    if (border.bottom && border.left) return chars.bottomLeft;
    if (border.bottom && border.right) return chars.bottomRight;
    if (border.top || border.bottom) return chars.horizontal;
    if (border.left || border.right) return chars.vertical;

    return ' ';
  }

  // Helper methods
  sortComponentsByLayout(components) {
    return [...components].sort((a, b) => {
      const priorityA = this.getComponentPriority(a.type);
      const priorityB = this.getComponentPriority(b.type);
      return priorityA - priorityB;
    });
  }

  getComponentPriority(type) {
    const priorities = {
      header: 1,
      navigation: 2,
      sidebar: 3,
      content: 4,
      form: 5,
      table: 6,
      button: 7,
      footer: 10
    };
    return priorities[type] || 5;
  }

  getBreakpointWidth(breakpoint) {
    const breakpoints = this.wireframeConfig.responsiveBreakpoints;
    return breakpoints[breakpoint]?.width || breakpoints.desktop.width;
  }

  getBreakpointColumns(breakpoint) {
    const breakpoints = this.wireframeConfig.responsiveBreakpoints;
    return breakpoints[breakpoint]?.columns || breakpoints.desktop.columns;
  }

  /**
   * Generate responsive layouts for all breakpoints
   */
  generateResponsiveLayouts(components, options = {}) {
    const breakpoints = Object.keys(this.wireframeConfig.responsiveBreakpoints);
    const layouts = {};
    
    for (const breakpoint of breakpoints) {
      layouts[breakpoint] = this.generateLayout(components, {
        ...options,
        breakpoint
      });
    }
    
    return layouts;
  }

  /**
   * Optimize layout for better visual balance
   */
  optimizeLayout(components, options = {}) {
    // Simple optimization: try to balance component distribution
    const optimized = [...components];
    
    // Group related components
    const grouped = this.groupRelatedComponents(optimized);
    
    // Apply spacing optimization
    const spaced = this.optimizeSpacing(grouped, options);
    
    return spaced;
  }

  groupRelatedComponents(components) {
    // Simple grouping by component type and relationships
    const groups = {
      navigation: [],
      content: [],
      forms: [],
      actions: []
    };
    
    for (const component of components) {
      if (component.type === 'navigation' || component.type === 'header') {
        groups.navigation.push(component);
      } else if (component.type === 'form' || component.type === 'input') {
        groups.forms.push(component);
      } else if (component.type === 'button' || component.type === 'action') {
        groups.actions.push(component);
      } else {
        groups.content.push(component);
      }
    }
    
    return [
      ...groups.navigation,
      ...groups.content,
      ...groups.forms,
      ...groups.actions
    ];
  }

  optimizeSpacing(components, options) {
    // Add optimal spacing between components
    return components.map((component, index) => ({
      ...component,
      spacing: {
        top: index === 0 ? 0 : this.calculateOptimalSpacing(component, components[index - 1]),
        bottom: this.calculateOptimalSpacing(component, components[index + 1]),
        left: 1,
        right: 1
      }
    }));
  }

  calculateOptimalSpacing(current, adjacent) {
    if (!adjacent) return 1;
    
    // Different component types need different spacing
    const spacingRules = {
      'header-content': 2,
      'navigation-content': 1,
      'content-content': 2,
      'form-button': 1,
      'content-footer': 3
    };
    
    const key = `${current.type}-${adjacent.type}`;
    return spacingRules[key] || 1;
  }
}
