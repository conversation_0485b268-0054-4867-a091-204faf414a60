/**
 * Research-Informed Choice Presenter (Task 4.3)
 * 
 * Presents research-backed options in business terms, provides rationale based on 
 * market research and technical analysis, and ensures choices are appropriate for 
 * user expertise level.
 */

import { OptionPresenter } from './option-presenter.js';
import { ResearchEnhancedDecisionTranslator } from './research-enhanced-decision-translator.js';
import { UserPreferenceManager } from '../workflow-logic/user-preference-manager.js';
import chalk from 'chalk';
import boxen from 'boxen';

/**
 * Research-Informed Choice Presenter
 * Enhances the existing OptionPresenter with research-backed presentations
 */
export class ResearchInformedChoicePresenter extends OptionPresenter {
  constructor(projectRoot = '.') {
    super(projectRoot);
    this.researchTranslator = new ResearchEnhancedDecisionTranslator(projectRoot);
    this.userPreferenceManager = new UserPreferenceManager(projectRoot);
    this.presentationTemplates = this.initializePresentationTemplates();
  }

  /**
   * Get research-informed decision options with appropriate presentation
   * @param {string} context - Decision context
   * @param {object} projectContext - Project context
   * @param {object} options - Presentation options
   * @returns {Promise<object>} Research-informed presentation
   */
  async getResearchInformedDecisionOptions(context, projectContext = {}, options = {}) {
    try {
      console.log(`🎯 Creating research-informed presentation for: ${context}`);

      // Step 1: Get user preferences and expertise level
      const userPreferences = await this.userPreferenceManager.getUserPreferences();
      const expertiseLevel = this.determineExpertiseLevel(projectContext, userPreferences);

      // Step 2: Get research-enhanced decision options
      const enhancedDecision = await this.researchTranslator.translateDecisionOptionsWithResearch(
        context,
        projectContext,
        {
          enableResearch: options.enableResearch !== false,
          researchProviders: options.researchProviders || ['tavily'],
          maxResults: options.maxResults || 5
        }
      );

      // Step 3: Create expertise-appropriate presentation
      const presentation = await this.createExpertiseAppropriatePresentation(
        enhancedDecision,
        expertiseLevel,
        projectContext,
        options
      );

      // Step 4: Add research rationale and backing
      const presentationWithRationale = await this.addResearchRationale(
        presentation,
        enhancedDecision,
        expertiseLevel
      );

      // Step 5: Format for final presentation
      const finalPresentation = this.formatForPresentation(
        presentationWithRationale,
        expertiseLevel,
        options
      );

      console.log(`✅ Research-informed presentation created`);
      return finalPresentation;
    } catch (error) {
      console.error('Research-informed presentation failed:', error);
      // Fallback to base presentation
      return await super.getDecisionOptions(context);
    }
  }

  /**
   * Determine user expertise level from context and preferences
   * @param {object} projectContext - Project context
   * @param {object} userPreferences - User preferences
   * @returns {string} Expertise level (novice, intermediate, advanced)
   */
  determineExpertiseLevel(projectContext, userPreferences) {
    // Check explicit expertise from project context
    if (projectContext.expertise?.category) {
      return projectContext.expertise.category;
    }

    // Infer from user preferences
    let expertiseScore = 0;

    // Technical preferences indicate higher expertise
    if (userPreferences.preferences?.technology) {
      const techPrefs = userPreferences.preferences.technology;
      expertiseScore += Object.keys(techPrefs.frameworks || {}).length * 0.1;
      expertiseScore += Object.keys(techPrefs.languages || {}).length * 0.1;
      expertiseScore += Object.keys(techPrefs.databases || {}).length * 0.1;
    }

    // Communication preferences
    if (userPreferences.preferences?.communication?.technicalTerms) {
      expertiseScore += 0.3;
    }

    // Determine level
    if (expertiseScore < 0.3) return 'novice';
    if (expertiseScore < 0.7) return 'intermediate';
    return 'advanced';
  }

  /**
   * Create expertise-appropriate presentation
   * @param {object} enhancedDecision - Enhanced decision options
   * @param {string} expertiseLevel - User expertise level
   * @param {object} projectContext - Project context
   * @param {object} options - Presentation options
   * @returns {Promise<object>} Expertise-appropriate presentation
   */
  async createExpertiseAppropriatePresentation(enhancedDecision, expertiseLevel, projectContext, options) {
    const template = this.presentationTemplates[expertiseLevel];
    
    // Adapt options based on expertise level
    const adaptedOptions = enhancedDecision.options.map(option => {
      const adapted = { ...option };

      // Adjust description complexity
      adapted.description = this.adaptDescriptionForExpertise(option.description, expertiseLevel);

      // Add or remove technical details
      if (expertiseLevel === 'novice') {
        delete adapted.technicalDetails;
        adapted.focusAreas = ['business_impact', 'ease_of_use', 'cost'];
      } else if (expertiseLevel === 'intermediate') {
        adapted.focusAreas = ['business_impact', 'technical_feasibility', 'timeline'];
        if (adapted.technicalDetails) {
          adapted.technicalDetails = this.simplifyTechnicalDetails(adapted.technicalDetails);
        }
      } else {
        adapted.focusAreas = ['technical_details', 'scalability', 'performance', 'business_impact'];
      }

      // Add research confidence indicators appropriate for expertise
      if (option.confidence) {
        adapted.confidenceIndicator = this.formatConfidenceForExpertise(option.confidence, expertiseLevel);
      }

      return adapted;
    });

    return {
      ...enhancedDecision,
      options: adaptedOptions,
      presentationStyle: expertiseLevel,
      template: template.name,
      adaptations: {
        expertiseLevel,
        focusAreas: template.focusAreas,
        complexityLevel: template.complexityLevel
      }
    };
  }

  /**
   * Add research rationale and backing to presentation
   * @param {object} presentation - Base presentation
   * @param {object} enhancedDecision - Enhanced decision with research
   * @param {string} expertiseLevel - User expertise level
   * @returns {Promise<object>} Presentation with research rationale
   */
  async addResearchRationale(presentation, enhancedDecision, expertiseLevel) {
    const withRationale = { ...presentation };

    // Add overall research summary
    if (enhancedDecision.researchBacking) {
      withRationale.researchSummary = this.formatResearchSummaryForExpertise(
        enhancedDecision.researchBacking,
        expertiseLevel
      );
    }

    // Add research rationale to each option
    withRationale.options = presentation.options.map(option => {
      const enhanced = { ...option };

      // Add research insights if available
      if (option.researchInsights && option.researchInsights.length > 0) {
        enhanced.researchRationale = this.formatResearchInsightsForExpertise(
          option.researchInsights,
          expertiseLevel
        );
      }

      // Add market context if available
      if (enhancedDecision.recommendations && option.id === enhancedDecision.recommendations.primary?.id) {
        enhanced.marketContext = this.formatMarketContextForExpertise(
          enhancedDecision.recommendations.researchBacking,
          expertiseLevel
        );
      }

      return enhanced;
    });

    // Add recommendations with research backing
    if (enhancedDecision.recommendations) {
      withRationale.recommendations = this.formatRecommendationsForExpertise(
        enhancedDecision.recommendations,
        expertiseLevel
      );
    }

    return withRationale;
  }

  /**
   * Format final presentation based on expertise level and options
   * @param {object} presentation - Presentation with rationale
   * @param {string} expertiseLevel - User expertise level
   * @param {object} options - Formatting options
   * @returns {object} Final formatted presentation
   */
  formatForPresentation(presentation, expertiseLevel, options = {}) {
    const formatted = {
      ...presentation,
      metadata: {
        ...presentation.metadata,
        presentationStyle: expertiseLevel,
        researchInformed: true,
        formattedAt: new Date().toISOString()
      }
    };

    // Add visual formatting if requested
    if (options.includeVisualFormatting) {
      formatted.visualPresentation = this.createVisualPresentation(presentation, expertiseLevel);
    }

    // Add CLI formatting if requested
    if (options.includeCLIFormatting) {
      formatted.cliPresentation = this.createCLIPresentation(presentation, expertiseLevel);
    }

    // Add summary for quick decision making
    formatted.quickSummary = this.createQuickSummary(presentation, expertiseLevel);

    return formatted;
  }

  /**
   * Adapt description complexity for expertise level
   * @param {string} description - Original description
   * @param {string} expertiseLevel - User expertise level
   * @returns {string} Adapted description
   */
  adaptDescriptionForExpertise(description, expertiseLevel) {
    if (expertiseLevel === 'novice') {
      // Simplify technical terms and focus on business benefits
      return description
        .replace(/API/g, 'interface')
        .replace(/framework/g, 'foundation')
        .replace(/library/g, 'tool set')
        .replace(/scalable/g, 'grows with your business')
        .replace(/performance/g, 'speed');
    } else if (expertiseLevel === 'intermediate') {
      // Keep some technical terms but add explanations
      return description.replace(/(\w+API\w*)/g, '$1 (programming interface)');
    }
    
    // Advanced users get full technical detail
    return description;
  }

  /**
   * Format confidence indicator for expertise level
   * @param {number} confidence - Confidence score (0-1)
   * @param {string} expertiseLevel - User expertise level
   * @returns {string} Formatted confidence indicator
   */
  formatConfidenceForExpertise(confidence, expertiseLevel) {
    const percentage = Math.round(confidence * 100);
    
    if (expertiseLevel === 'novice') {
      if (confidence > 0.8) return '✅ Highly recommended by research';
      if (confidence > 0.6) return '👍 Supported by research';
      return '⚠️ Limited research backing';
    } else if (expertiseLevel === 'intermediate') {
      return `📊 Research confidence: ${percentage}% (${confidence > 0.7 ? 'Strong' : confidence > 0.5 ? 'Moderate' : 'Limited'} backing)`;
    } else {
      return `Research confidence: ${percentage}% (n=${confidence > 0.8 ? 'high' : confidence > 0.6 ? 'medium' : 'low'} evidence quality)`;
    }
  }

  /**
   * Format research summary for expertise level
   * @param {object} researchBacking - Research backing information
   * @param {string} expertiseLevel - User expertise level
   * @returns {string} Formatted research summary
   */
  formatResearchSummaryForExpertise(researchBacking, expertiseLevel) {
    if (expertiseLevel === 'novice') {
      return `Based on analysis of current market trends and industry best practices. ${Math.round(researchBacking.confidence * 100)}% confidence in recommendations.`;
    } else if (expertiseLevel === 'intermediate') {
      return `Research analysis of ${researchBacking.sources} sources with ${Math.round(researchBacking.confidence * 100)}% confidence. Findings based on current market data and technical evaluations.`;
    } else {
      return `Comprehensive research synthesis: ${researchBacking.sources} sources analyzed, ${Math.round(researchBacking.confidence * 100)}% confidence interval. Methodology: market analysis + technical evaluation + adoption metrics.`;
    }
  }

  /**
   * Create quick summary for decision making
   * @param {object} presentation - Full presentation
   * @param {string} expertiseLevel - User expertise level
   * @returns {object} Quick summary
   */
  createQuickSummary(presentation, expertiseLevel) {
    const topOption = presentation.options
      .filter(opt => opt.confidence)
      .sort((a, b) => b.confidence - a.confidence)[0];

    if (!topOption) {
      return {
        recommendation: 'No clear research-backed recommendation available',
        reasoning: 'Insufficient research data for confident recommendation',
        confidence: 'low'
      };
    }

    const summary = {
      recommendation: topOption.title,
      reasoning: this.generateQuickReasoning(topOption, presentation, expertiseLevel),
      confidence: topOption.confidence > 0.7 ? 'high' : topOption.confidence > 0.5 ? 'medium' : 'low',
      keyBenefits: this.extractKeyBenefits(topOption, expertiseLevel)
    };

    return summary;
  }

  /**
   * Generate quick reasoning for top recommendation
   * @param {object} option - Top recommended option
   * @param {object} presentation - Full presentation
   * @param {string} expertiseLevel - User expertise level
   * @returns {string} Quick reasoning
   */
  generateQuickReasoning(option, presentation, expertiseLevel) {
    if (expertiseLevel === 'novice') {
      return `${option.title} is recommended because it offers the best balance of ease of use and business value for your project.`;
    } else if (expertiseLevel === 'intermediate') {
      return `${option.title} provides optimal technical feasibility and business impact based on current market research and your project requirements.`;
    } else {
      return `${option.title} demonstrates superior technical merit and market validation with ${Math.round(option.confidence * 100)}% research confidence.`;
    }
  }

  /**
   * Extract key benefits appropriate for expertise level
   * @param {object} option - Option to extract benefits from
   * @param {string} expertiseLevel - User expertise level
   * @returns {Array} Key benefits
   */
  extractKeyBenefits(option, expertiseLevel) {
    const benefits = [];

    if (expertiseLevel === 'novice') {
      if (option.businessImpact) benefits.push('Strong business value');
      if (option.confidence > 0.7) benefits.push('Well-researched choice');
      if (option.focusAreas?.includes('ease_of_use')) benefits.push('Easy to implement');
    } else if (expertiseLevel === 'intermediate') {
      if (option.businessImpact) benefits.push('Proven business impact');
      if (option.researchInsights?.length > 0) benefits.push('Research-backed');
      if (option.focusAreas?.includes('technical_feasibility')) benefits.push('Technically sound');
    } else {
      if (option.technicalDetails) benefits.push('Comprehensive technical solution');
      if (option.confidence > 0.8) benefits.push('High-confidence research backing');
      if (option.focusAreas?.includes('scalability')) benefits.push('Scalable architecture');
    }

    return benefits;
  }

  /**
   * Initialize presentation templates for different expertise levels
   * @returns {object} Presentation templates
   */
  initializePresentationTemplates() {
    return {
      novice: {
        name: 'Business-Focused',
        complexityLevel: 'low',
        focusAreas: ['business_impact', 'ease_of_use', 'cost', 'timeline'],
        technicalDetails: false,
        researchDetails: 'simplified',
        language: 'business_friendly'
      },
      intermediate: {
        name: 'Balanced Technical-Business',
        complexityLevel: 'medium',
        focusAreas: ['business_impact', 'technical_feasibility', 'timeline', 'scalability'],
        technicalDetails: 'simplified',
        researchDetails: 'moderate',
        language: 'mixed'
      },
      advanced: {
        name: 'Technical-Detailed',
        complexityLevel: 'high',
        focusAreas: ['technical_details', 'performance', 'scalability', 'architecture', 'business_impact'],
        technicalDetails: true,
        researchDetails: 'comprehensive',
        language: 'technical'
      }
    };
  }

  /**
   * Simplify technical details for intermediate users
   * @param {string} technicalDetails - Original technical details
   * @returns {string} Simplified technical details
   */
  simplifyTechnicalDetails(technicalDetails) {
    return technicalDetails
      .replace(/\b(API|REST|GraphQL)\b/g, 'programming interface')
      .replace(/\b(microservices|monolithic)\b/g, 'architecture approach')
      .replace(/\b(scalability|horizontal scaling)\b/g, 'growth capability')
      .replace(/\b(performance optimization)\b/g, 'speed improvements')
      .substring(0, 200) + (technicalDetails.length > 200 ? '...' : '');
  }

  /**
   * Format research insights for expertise level
   * @param {Array} researchInsights - Research insights
   * @param {string} expertiseLevel - User expertise level
   * @returns {string} Formatted research insights
   */
  formatResearchInsightsForExpertise(researchInsights, expertiseLevel) {
    if (expertiseLevel === 'novice') {
      const topInsight = researchInsights[0];
      return `Research shows: ${topInsight.insight.substring(0, 100)}...`;
    } else if (expertiseLevel === 'intermediate') {
      const insights = researchInsights.slice(0, 2).map(insight =>
        `• ${insight.insight.substring(0, 80)}...`
      ).join('\n');
      return `Key research findings:\n${insights}`;
    } else {
      const insights = researchInsights.map(insight =>
        `• ${insight.insight} (confidence: ${Math.round(insight.confidence * 100)}%)`
      ).join('\n');
      return `Research insights:\n${insights}`;
    }
  }

  /**
   * Format market context for expertise level
   * @param {string} marketContext - Market context information
   * @param {string} expertiseLevel - User expertise level
   * @returns {string} Formatted market context
   */
  formatMarketContextForExpertise(marketContext, expertiseLevel) {
    if (!marketContext) return '';

    if (expertiseLevel === 'novice') {
      return `Market insight: ${marketContext.substring(0, 100)}...`;
    } else if (expertiseLevel === 'intermediate') {
      return `Market analysis: ${marketContext.substring(0, 200)}...`;
    } else {
      return `Market research context: ${marketContext}`;
    }
  }

  /**
   * Format recommendations for expertise level
   * @param {object} recommendations - Recommendations object
   * @param {string} expertiseLevel - User expertise level
   * @returns {object} Formatted recommendations
   */
  formatRecommendationsForExpertise(recommendations, expertiseLevel) {
    const formatted = { ...recommendations };

    if (expertiseLevel === 'novice') {
      formatted.reasoning = recommendations.reasoning?.substring(0, 150) + '...';
      formatted.businessJustification = recommendations.businessJustification?.substring(0, 150) + '...';
      // Simplify alternatives to just titles
      formatted.alternatives = recommendations.alternatives?.map(alt => ({
        title: alt.title,
        reason: 'Alternative option worth considering'
      }));
    } else if (expertiseLevel === 'intermediate') {
      formatted.reasoning = recommendations.reasoning?.substring(0, 250) + '...';
      formatted.businessJustification = recommendations.businessJustification?.substring(0, 250) + '...';
      // Include basic alternative info
      formatted.alternatives = recommendations.alternatives?.map(alt => ({
        title: alt.title,
        description: alt.description?.substring(0, 100) + '...',
        confidence: alt.confidence
      }));
    }
    // Advanced users get full recommendations as-is

    return formatted;
  }

  /**
   * Create visual presentation formatting
   * @param {object} presentation - Presentation data
   * @param {string} expertiseLevel - User expertise level
   * @returns {object} Visual presentation formatting
   */
  createVisualPresentation(presentation, expertiseLevel) {
    const visual = {
      title: presentation.title,
      style: expertiseLevel,
      sections: []
    };

    // Add research summary section
    if (presentation.researchSummary) {
      visual.sections.push({
        type: 'research_summary',
        title: '🔍 Research Insights',
        content: presentation.researchSummary,
        style: expertiseLevel === 'novice' ? 'simple' : expertiseLevel === 'intermediate' ? 'balanced' : 'detailed'
      });
    }

    // Add options section
    visual.sections.push({
      type: 'options',
      title: '⚖️ Your Options',
      content: presentation.options.map(option => ({
        title: option.title,
        description: option.description,
        confidence: option.confidenceIndicator,
        highlights: option.focusAreas
      })),
      style: expertiseLevel
    });

    // Add recommendations section
    if (presentation.recommendations) {
      visual.sections.push({
        type: 'recommendations',
        title: '🎯 Recommended Choice',
        content: {
          primary: presentation.recommendations.primary?.title,
          reasoning: presentation.recommendations.reasoning,
          alternatives: presentation.recommendations.alternatives?.map(alt => alt.title)
        },
        style: expertiseLevel
      });
    }

    return visual;
  }

  /**
   * Create CLI presentation formatting
   * @param {object} presentation - Presentation data
   * @param {string} expertiseLevel - User expertise level
   * @returns {string} CLI formatted presentation
   */
  createCLIPresentation(presentation, expertiseLevel) {
    let output = '';

    // Title
    output += boxen(chalk.bold.blue(presentation.title), {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: 'blue'
    }) + '\n';

    // Research summary
    if (presentation.researchSummary) {
      output += chalk.gray('🔍 Research Insights:\n');
      output += chalk.white(presentation.researchSummary) + '\n\n';
    }

    // Options
    output += chalk.bold.yellow('⚖️ Your Options:\n\n');
    presentation.options.forEach((option, index) => {
      const optionNumber = chalk.bold.cyan(`${index + 1}.`);
      const optionTitle = chalk.bold.white(option.title);
      const confidence = option.confidenceIndicator ? chalk.green(option.confidenceIndicator) : '';

      output += `${optionNumber} ${optionTitle}\n`;
      output += chalk.gray(`   ${option.description}\n`);
      if (confidence) output += `   ${confidence}\n`;

      if (expertiseLevel !== 'novice' && option.researchRationale) {
        output += chalk.dim(`   Research: ${option.researchRationale.substring(0, 100)}...\n`);
      }
      output += '\n';
    });

    // Recommendations
    if (presentation.recommendations) {
      output += chalk.bold.green('🎯 Recommended Choice:\n');
      output += chalk.white(`${presentation.recommendations.primary?.title}\n`);
      output += chalk.gray(`${presentation.recommendations.reasoning}\n\n`);
    }

    // Quick summary
    if (presentation.quickSummary) {
      output += boxen(
        chalk.bold('Quick Decision Summary:\n') +
        chalk.white(`Recommendation: ${presentation.quickSummary.recommendation}\n`) +
        chalk.gray(`Confidence: ${presentation.quickSummary.confidence}\n`) +
        chalk.dim(`Key Benefits: ${presentation.quickSummary.keyBenefits?.join(', ') || 'None specified'}`),
        {
          padding: 1,
          borderStyle: 'single',
          borderColor: 'green'
        }
      );
    }

    return output;
  }

  /**
   * Get presentation statistics
   * @returns {object} Presentation statistics
   */
  getPresentationStats() {
    return {
      cacheStats: this.researchTranslator.getResearchCacheStats(),
      templatesAvailable: Object.keys(this.presentationTemplates).length,
      expertiseLevels: Object.keys(this.presentationTemplates)
    };
  }

  /**
   * Clear presentation cache
   */
  clearCache() {
    this.researchTranslator.clearResearchCache();
  }
}

/**
 * Factory function to create research-informed choice presenter
 * @param {string} projectRoot - Project root path
 * @returns {ResearchInformedChoicePresenter} Presenter instance
 */
export function createResearchInformedChoicePresenter(projectRoot = '.') {
  return new ResearchInformedChoicePresenter(projectRoot);
}

/**
 * Get research-informed decision options (convenience function)
 * @param {object} params - Parameters
 * @returns {Promise<object>} Research-informed decision options
 */
export async function getResearchInformedDecisionOptions(params) {
  const {
    context,
    projectContext = {},
    projectRoot = '.',
    options = {}
  } = params;

  const presenter = new ResearchInformedChoicePresenter(projectRoot);
  return await presenter.getResearchInformedDecisionOptions(context, projectContext, options);
}

export default ResearchInformedChoicePresenter;
