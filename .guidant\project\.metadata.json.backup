{"targetUsers": [], "businessGoals": [], "technicalRequirements": [], "constraints": [], "workflow": {"projectType": "product", "complexity": "standard", "phases": ["concept", "requirements", "design", "architecture", "implementation", "deployment"], "estimatedDuration": "2-6 months", "qualityLevel": "enterprise", "researchRequirement": "comprehensive", "generated": "2025-06-18T16:12:06.260Z", "adaptive": true}, "researchRequirements": {"mandatory": ["market_research", "competitor_analysis", "user_research"], "checkpoints": [{"name": "concept_validation", "required": true, "status": "pending", "description": "Research validation checkpoint: concept validation"}, {"name": "requirements_validation", "required": true, "status": "pending", "description": "Research validation checkpoint: requirements validation"}, {"name": "design_validation", "required": true, "status": "pending", "description": "Research validation checkpoint: design validation"}, {"name": "architecture_validation", "required": true, "status": "pending", "description": "Research validation checkpoint: architecture validation"}], "depth": "comprehensive", "enforced": true}}