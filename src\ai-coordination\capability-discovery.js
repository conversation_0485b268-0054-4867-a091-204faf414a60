/**
 * AI Capability Discovery System
 * Discovers what tools AI assistant has available and maps them to appropriate tasks
 */

import { readProjectFile, writeProjectFile } from '../file-management/project-structure.js';
import { AI_CAPABILITIES } from '../constants/paths.js';

/**
 * Standard tool categories and their requirements
 */
export const TOOL_CATEGORIES = {
  research: {
    required: ['tavily-search', 'tavily-extract', 'tavily-crawl', 'get-library-docs', 'resolve-library-id', 'web_search', 'read_web_page'],
    optional: [],
    capabilities: ['market_research', 'competitor_analysis', 'user_research', 'technical_research', 'library_documentation']
  },
  
  design: {
    required: ['create_file', 'edit_file', 'mermaid'],
    optional: ['read_file', 'tavily-search'],
    capabilities: ['wireframing', 'user_flows', 'component_specs', 'system_diagrams', 'architecture_design']
  },
  
  development: {
    required: ['create_file', 'edit_file', 'read_file', 'Bash'],
    optional: ['git', 'get_diagnostics', 'format_file'],
    capabilities: ['code_generation', 'testing', 'debugging', 'build_management', 'version_control', 'code_quality']
  },
  
  testing: {
    required: ['create_file', 'edit_file', 'Bash'],
    optional: ['playwright', 'get_diagnostics', 'git'],
    capabilities: ['unit_testing', 'e2e_testing', 'browser_automation', 'test_frameworks', 'quality_assurance']
  },
  
  deployment: {
    required: ['create_file', 'edit_file', 'Bash'],
    optional: ['git', 'web_search', 'get_diagnostics'],
    capabilities: ['environment_setup', 'hosting_configuration', 'monitoring_setup', 'ci_cd', 'deployment_automation']
  }
};

import { discoverCapabilitiesWithAI } from '../ai-integration/capability-analyzer.js';

/**
 * Discover and analyze AI assistant capabilities with AI enhancement
 */
export async function discoverCapabilities(availableTools, projectRoot = process.cwd(), options = {}) {
  const toolNames = availableTools.map(tool => typeof tool === 'string' ? tool : tool.name);

  // Only skip AI analysis in explicit test environments or when explicitly disabled
  const skipAI = options.skipAI || (
    (process.env.NODE_ENV === 'test' ||
     process.env.GUIDANT_TEST_MODE === 'true' ||
     process.env.CI === 'true') &&
    process.env.GUIDANT_FORCE_AI !== 'true'
  );

  if (!skipAI) {
    // Try AI-enhanced capability discovery first
    const aiResult = await discoverCapabilitiesWithAI(toolNames, projectRoot);

    if (aiResult.success) {
      // Convert AI format to legacy format for compatibility
      const enhancedCapabilities = convertAICapabilitiesToLegacyFormat(aiResult.capabilities, toolNames);

      // Store capabilities for future reference
      await writeProjectFile(AI_CAPABILITIES, enhancedCapabilities, projectRoot);

      return enhancedCapabilities;
    }
  }

  // Fallback to original logic (used in tests or when AI fails)
  const capabilities = {
    tools: availableTools,
    toolNames,
    roles: analyzeRoleCapabilities(toolNames),
    limitations: identifyLimitations(toolNames),
    discoveredAt: new Date().toISOString(),
    aiEnhanced: false
  };

  // Store capabilities for future reference
  await writeProjectFile(AI_CAPABILITIES, capabilities, projectRoot);

  return capabilities;
}

/**
 * Convert AI-discovered capabilities to legacy format for compatibility
 */
function convertAICapabilitiesToLegacyFormat(aiCapabilities, toolNames) {
  const roles = {};
  
  Object.entries(aiCapabilities.roleCapabilities).forEach(([roleName, roleData]) => {
    roles[roleName] = {
      canFulfill: roleData.canFulfill,
      confidence: roleData.confidence,
      missingRequired: [], // AI analysis doesn't use this concept
      availableOptional: roleData.coreTools || [],
      capabilities: roleData.capabilities,
      reasoning: roleData.reasoning
    };
  });

  return {
    tools: toolNames,
    toolNames,
    roles,
    limitations: aiCapabilities.overallCapability.limitations,
    discoveredAt: new Date().toISOString(),
    aiEnhanced: true,
    overallCapability: aiCapabilities.overallCapability,
    recommendations: aiCapabilities.recommendations
  };
}

/**
 * Analyze which roles the AI can fulfill based on available tools
 */
function analyzeRoleCapabilities(toolNames) {
  const roleCapabilities = {};
  
  for (const [category, requirements] of Object.entries(TOOL_CATEGORIES)) {
    const hasRequired = requirements.required.every(tool => toolNames.includes(tool));
    const optionalCount = requirements.optional.filter(tool => toolNames.includes(tool)).length;
    
    // Calculate confidence, handling case where there are no optional tools
    const optionalRatio = requirements.optional.length > 0 ?
      (optionalCount / requirements.optional.length) : 1.0;

    roleCapabilities[category] = {
      canFulfill: hasRequired,
      confidence: hasRequired ? (0.5 + optionalRatio * 0.5) : 0,
      missingRequired: requirements.required.filter(tool => !toolNames.includes(tool)),
      availableOptional: requirements.optional.filter(tool => toolNames.includes(tool)),
      capabilities: hasRequired ? requirements.capabilities : []
    };
  }
  
  return roleCapabilities;
}

/**
 * Identify specific limitations based on missing tools
 */
function identifyLimitations(toolNames) {
  const limitations = [];
  
  // Research limitations
  if (!toolNames.includes('tavily-search') && !toolNames.includes('web_search')) {
    limitations.push({
      category: 'research',
      issue: 'No advanced search capabilities',
      impact: 'Limited to basic web research'
    });
  }
  
  // Development limitations  
  if (!toolNames.includes('Bash')) {
    limitations.push({
      category: 'development',
      issue: 'No command line access',
      impact: 'Cannot run build tools or package managers'
    });
  }
  
  // Design limitations
  if (!toolNames.includes('mermaid')) {
    limitations.push({
      category: 'design',
      issue: 'No diagram generation',
      impact: 'Limited to text-based design documentation'
    });
  }
  
  return limitations;
}

/**
 * Generate task recommendations based on capabilities
 */
export function generateTaskRecommendations(capabilities) {
  const recommendations = {
    strongSuits: [],
    possibleWithLimitations: [],
    notRecommended: []
  };
  
  for (const [role, analysis] of Object.entries(capabilities.roles)) {
    if (analysis.confidence >= 0.8) {
      recommendations.strongSuits.push({
        role,
        reason: `High capability with ${analysis.availableOptional.length} optional tools available`
      });
    } else if (analysis.canFulfill && analysis.confidence >= 0.5) {
      recommendations.possibleWithLimitations.push({
        role,
        reason: `Can fulfill basic requirements but missing ${analysis.availableOptional.length - analysis.missingRequired.length} optional tools`
      });
    } else {
      recommendations.notRecommended.push({
        role,
        reason: `Missing required tools: ${analysis.missingRequired.join(', ')}`
      });
    }
  }
  
  return recommendations;
}

/**
 * Create tool-specific task instructions
 */
export function generateToolInstructions(taskType, availableTools) {
  const toolNames = availableTools.map(tool => typeof tool === 'string' ? tool : tool.name);
  
  const instructions = {
    taskType,
    availableTools: toolNames,
    steps: [],
    alternatives: []
  };
  
  // Generate specific instructions based on task type and available tools
  switch (taskType) {
    case 'market_research':
      if (toolNames.includes('tavily-search')) {
        instructions.steps.push('Use tavily-search for comprehensive market analysis');
        instructions.steps.push('Search for competitor analysis and market trends');
      } else if (toolNames.includes('web_search')) {
        instructions.steps.push('Use web_search for basic market research');
        instructions.alternatives.push('Consider upgrading to Tavily for more comprehensive results');
      }
      break;
      
    case 'wireframe_creation':
      instructions.steps.push('Use create_file to generate ASCII wireframes');
      instructions.steps.push('Create detailed component specifications in markdown');
      if (toolNames.includes('mermaid')) {
        instructions.steps.push('Use mermaid for user flow diagrams');
      } else {
        instructions.alternatives.push('User flows will be text-based without diagram tool');
      }
      break;
      
    case 'code_implementation':
      instructions.steps.push('Use create_file and edit_file for code generation');
      if (toolNames.includes('Bash')) {
        instructions.steps.push('Use Bash for package management and build processes');
      } else {
        instructions.alternatives.push('Manual dependency management - provide installation instructions');
      }
      break;
  }
  
  return instructions;
}

/**
 * Validate if AI can handle a specific task
 */
export function validateTaskCapability(taskType, capabilities) {
  const categoryMap = {
    'market_research': 'research',
    'competitor_analysis': 'research', 
    'wireframe_creation': 'design',
    'user_flow_design': 'design',
    'code_implementation': 'development',
    'testing_setup': 'development',
    'deployment_setup': 'deployment'
  };
  
  const category = categoryMap[taskType];
  if (!category) {
    return { canHandle: false, reason: 'Unknown task type' };
  }
  
  const roleCapability = capabilities.roles[category];
  if (!roleCapability) {
    return { canHandle: false, reason: 'Category not analyzed' };
  }
  
  return {
    canHandle: roleCapability.canFulfill,
    confidence: roleCapability.confidence,
    reason: roleCapability.canFulfill 
      ? `Can handle with ${Math.round(roleCapability.confidence * 100)}% confidence`
      : `Missing required tools: ${roleCapability.missingRequired.join(', ')}`
  };
}

/**
 * Capability Discovery System for Guidant
 * Implements the "Handshake" protocol for AI agent capability discovery and gap analysis
 */

import { AgentDiscovery, discoverCurrentAgent } from '../agent-registry/agent-discovery.js';
import { GapAnalyzer, analyzeAgentGaps } from '../agent-registry/gap-analysis.js';
import { ToolConfigurator, getConfigurationSuggestions } from '../agent-registry/tool-configurator.js';
import { ROLE_REQUIREMENTS } from './tool-registry.js';

/**
 * The Handshake protocol orchestrates agent discovery, gap analysis, and tool configuration
 */
export class HandshakeProtocol {
  constructor() {
    this.agentDiscovery = new AgentDiscovery();
    this.gapAnalyzer = new GapAnalyzer();
    this.toolConfigurator = new ToolConfigurator();
  }

  /**
   * Perform the complete handshake process with an AI agent
   * @param {Object} communicationMethod - Method to communicate with the agent
   * @param {string} [agentId] - Optional agent identifier
   * @param {Object} [projectContext] - Optional project context for targeted analysis
   * @returns {Promise<Object>} - Complete handshake result with agent card, gap analysis, and configuration recommendations
   */
  async performHandshake(communicationMethod, agentId = null, projectContext = null) {
    // Step 1: Discover agent capabilities
    const agentCard = await this.agentDiscovery.discoverAgent(communicationMethod, agentId);
    
    if (agentCard.status !== 'discovered') {
      return {
        success: false,
        stage: 'discovery',
        error: `Agent discovery failed: ${agentCard.error || 'Unknown error'}`,
        agentCard
      };
    }
    
    // Step 2: Analyze capability gaps
    const targetProjects = projectContext ? [projectContext] : [];
    const gapAnalysis = await this.gapAnalyzer.analyzeCapabilityGaps(agentCard, targetProjects);
    
    // Step 3: Generate configuration recommendations
    const configRecommendations = await this.toolConfigurator.suggestOptimalConfiguration(
      agentCard, 
      projectContext
    );
    
    return {
      success: true,
      agentCard,
      gapAnalysis,
      configRecommendations,
      summary: this.generateHandshakeSummary(agentCard, gapAnalysis, configRecommendations)
    };
  }

  /**
   * Generate a user-friendly summary of the handshake results
   * @param {Object} agentCard - The discovered agent capabilities
   * @param {Object} gapAnalysis - The gap analysis results
   * @param {Object} configRecommendations - The configuration recommendations
   * @returns {Object} - A structured summary of the handshake results
   */
  generateHandshakeSummary(agentCard, gapAnalysis, configRecommendations) {
    const summary = {
      agentName: agentCard.identity.name || agentCard.id,
      discoveredTools: agentCard.tools.length,
      normalizedTools: agentCard.normalizedTools.length,
      unknownTools: agentCard.tools.filter(tool => !agentCard.normalizedTools.includes(tool)).length,
      capableRoles: [],
      gapScore: gapAnalysis.totalGapScore,
      criticalGaps: [],
      topRecommendations: []
    };
    
    // Identify roles the agent can fulfill
    Object.entries(gapAnalysis.roleGaps).forEach(([role, analysis]) => {
      if (analysis.canCurrentlyFulfill) {
        summary.capableRoles.push({
          role,
          confidence: analysis.currentConfidence
        });
      } else if (analysis.missingEssentialCategories.length > 0) {
        summary.criticalGaps.push({
          role,
          missingCategories: analysis.missingEssentialCategories
        });
      }
    });
    
    // Sort capable roles by confidence
    summary.capableRoles.sort((a, b) => b.confidence - a.confidence);
    
    // Get top recommendations
    summary.topRecommendations = configRecommendations.recommendations
      .slice(0, 5)
      .map(rec => ({
        tool: rec.tool,
        priority: rec.priority,
        reasoning: rec.reasoning
      }));
    
    return summary;
  }
}

/**
 * Perform a handshake with the current AI agent
 * @param {Object} [projectContext] - Optional project context for targeted analysis
 * @returns {Promise<Object>} - Complete handshake result
 */
export async function performHandshakeWithCurrentAgent(projectContext = null) {
  const protocol = new HandshakeProtocol();
  
  // Discover the current agent using the best available method
  const currentAgent = await discoverCurrentAgent();
  
  // Use the discovered agent info to perform the full handshake
  return protocol.performHandshake(
    { type: 'config', config: currentAgent },
    currentAgent.id,
    projectContext
  );
}

/**
 * Generate a structured gap report for presentation to the user
 * @param {Object} handshakeResult - The result from performHandshake
 * @returns {Object} - A user-friendly gap report
 */
export function generateGapReport(handshakeResult) {
  if (!handshakeResult.success) {
    return {
      title: 'Agent Capability Analysis Failed',
      error: handshakeResult.error,
      recommendations: ['Try restarting the agent', 'Check agent connectivity']
    };
  }
  
  const { agentCard, gapAnalysis, configRecommendations, summary } = handshakeResult;
  
  const report = {
    title: `AI Agent Capability Analysis: ${summary.agentName}`,
    timestamp: new Date().toISOString(),
    overview: {
      discoveredCapabilities: summary.discoveredTools,
      gapScore: summary.gapScore,
      capableRoles: summary.capableRoles.map(r => r.role),
      criticalGaps: summary.criticalGaps.length
    },
    roleAnalysis: Object.entries(gapAnalysis.roleGaps).map(([role, analysis]) => ({
      role,
      canFulfill: analysis.canCurrentlyFulfill,
      confidence: analysis.currentConfidence,
      missingEssentials: analysis.missingEssentialCategories,
      missingImportant: analysis.missingImportantCategories
    })),
    recommendations: configRecommendations.recommendations.slice(0, 10).map(rec => ({
      tool: rec.tool,
      priority: rec.priority,
      reasoning: rec.reasoning,
      alternatives: rec.alternatives || []
    })),
    implementationPlan: configRecommendations.implementationPlan
  };
  
  return report;
}

/**
 * Check if the current agent can fulfill a specific role
 * @param {string} roleName - The role to check
 * @returns {Promise<Object>} - Role capability assessment
 */
export async function checkAgentRoleCapability(roleName) {
  if (!ROLE_REQUIREMENTS[roleName]) {
    return {
      success: false,
      error: `Unknown role: ${roleName}`
    };
  }
  
  const currentAgent = await discoverCurrentAgent();
  const gapAnalyzer = new GapAnalyzer();
  
  const roleGap = gapAnalyzer.analyzeRoleGap(
    currentAgent.normalizedTools || currentAgent.tools || [],
    roleName
  );
  
  return {
    success: true,
    role: roleName,
    canFulfill: roleGap.canCurrentlyFulfill,
    confidence: roleGap.currentConfidence,
    missingEssentials: roleGap.missingEssentialCategories,
    missingImportant: roleGap.missingImportantCategories,
    recommendations: roleGap.recommendedTools.slice(0, 5)
  };
}
