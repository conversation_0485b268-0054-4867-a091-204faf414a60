/**
 * Tests for Configurable Research System
 */

import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { VisualResearchInterface } from '../../src/visual-generation/research/visual-research-interface.js';
import { ResearchConfigManager } from '../../src/visual-generation/config/research-config-manager.js';
import fs from 'fs/promises';
import path from 'path';

describe('Configurable Research System', () => {
  let researchInterface;
  let configManager;
  let testConfigPath;

  beforeEach(async () => {
    // Create test config manager with temporary path
    configManager = new ResearchConfigManager();
    testConfigPath = path.join(process.cwd(), '.guidant/test-research-config.json');
    configManager.userConfigPath = testConfigPath;
    
    researchInterface = new VisualResearchInterface({
      cacheEnabled: false
    });
  });

  afterEach(async () => {
    // Clean up test config file
    try {
      await fs.unlink(testConfigPath);
    } catch (error) {
      // File might not exist - that's okay
    }
  });

  describe('ResearchConfigManager', () => {
    it('should load default configuration', async () => {
      const config = await configManager.loadConfig();
      
      expect(config).toBeDefined();
      expect(config.domains).toBeDefined();
      expect(config.libraries).toBeDefined();
      expect(config.domains.ui_practices).toBeInstanceOf(Array);
      expect(config.libraries.react).toBeInstanceOf(Array);
    });

    it('should add custom domains', async () => {
      const customDomains = ['ui.shadcn.com', 'daisyui.com'];
      const success = await configManager.addDomains('ui_practices', customDomains);
      
      expect(success).toBe(true);
      
      const config = await configManager.getCurrentConfig();
      expect(config.domains.ui_practices).toContain('ui.shadcn.com');
      expect(config.domains.ui_practices).toContain('daisyui.com');
    });

    it('should remove domains', async () => {
      // First add some domains
      await configManager.addDomains('ui_practices', ['test1.com', 'test2.com']);
      
      // Then remove one
      const success = await configManager.removeDomains('ui_practices', ['test1.com']);
      expect(success).toBe(true);
      
      const config = await configManager.getCurrentConfig();
      expect(config.domains.ui_practices).not.toContain('test1.com');
    });

    it('should add custom libraries', async () => {
      const customLibraries = ['shadcn/ui', 'radix-ui'];
      const success = await configManager.addLibraries('react', customLibraries);
      
      expect(success).toBe(true);
      
      const config = await configManager.getCurrentConfig();
      expect(config.libraries.react).toContain('shadcn/ui');
      expect(config.libraries.react).toContain('radix-ui');
    });

    it('should handle custom frameworks', async () => {
      const customLibraries = ['company-ui-kit', 'internal-components'];
      const success = await configManager.addLibraries('internal', customLibraries);
      
      expect(success).toBe(true);
      
      const config = await configManager.getCurrentConfig();
      expect(config.libraries.internal).toContain('company-ui-kit');
      expect(config.libraries.internal).toContain('internal-components');
    });

    it('should merge configurations correctly', () => {
      const baseConfig = {
        domains: {
          ui_practices: ['material.io', 'chakra-ui.com'],
          accessibility: ['w3.org']
        },
        libraries: {
          react: ['react', 'mui']
        }
      };

      const userConfig = {
        domains: {
          ui_practices: {
            add: ['ui.shadcn.com'],
            remove: ['material.io']
          },
          custom_type: {
            add: ['custom.com']
          }
        },
        libraries: {
          react: {
            add: ['shadcn/ui']
          },
          vue: {
            add: ['vuetify']
          }
        }
      };

      const merged = configManager.mergeConfigurations(baseConfig, userConfig);
      
      // Should have added shadcn and removed material.io
      expect(merged.domains.ui_practices).toContain('ui.shadcn.com');
      expect(merged.domains.ui_practices).toContain('chakra-ui.com');
      expect(merged.domains.ui_practices).not.toContain('material.io');
      
      // Should have added custom type
      expect(merged.domains.custom_type).toContain('custom.com');
      
      // Should have added shadcn to react
      expect(merged.libraries.react).toContain('shadcn/ui');
      expect(merged.libraries.react).toContain('mui');
      
      // Should have added vue
      expect(merged.libraries.vue).toContain('vuetify');
    });

    it('should reset to defaults', async () => {
      // Add some custom config
      await configManager.addDomains('ui_practices', ['custom.com']);
      
      // Reset
      const success = await configManager.resetToDefaults();
      expect(success).toBe(true);
      
      // Should not have custom domain anymore
      const config = await configManager.getCurrentConfig();
      expect(config.domains.ui_practices).not.toContain('custom.com');
    });
  });

  describe('VisualResearchInterface with Custom Config', () => {
    it('should use custom domains for research', async () => {
      // Add custom domain
      await configManager.addDomains('ui_practices', ['ui.shadcn.com']);
      
      // Create research interface with custom config
      const customResearchInterface = new VisualResearchInterface({
        cacheEnabled: false
      });
      
      await customResearchInterface.initialize();
      
      const domains = customResearchInterface.getRelevantDomains('ui_practices');
      expect(domains).toContain('ui.shadcn.com');
    });

    it('should use custom libraries for component research', async () => {
      // Add custom library
      await configManager.addLibraries('react', ['shadcn/ui']);
      
      const customResearchInterface = new VisualResearchInterface({
        cacheEnabled: false
      });
      
      await customResearchInterface.initialize();
      
      const libraries = customResearchInterface.getComponentLibraries('react');
      expect(libraries).toContain('shadcn/ui');
    });

    it('should handle missing configuration gracefully', async () => {
      const researchInterface = new VisualResearchInterface({
        cacheEnabled: false
      });
      
      // Don't initialize - should handle gracefully
      const domains = researchInterface.getRelevantDomains('ui_practices');
      expect(domains).toEqual([]);
      
      const libraries = researchInterface.getComponentLibraries('react');
      expect(libraries).toEqual([]);
    });

    it('should generate queries with custom domains', () => {
      const queries = researchInterface.generateUIBestPracticesQueries('form', {
        industry: 'healthcare'
      });
      
      expect(queries).toBeInstanceOf(Array);
      expect(queries.length).toBeGreaterThan(0);
      expect(queries.some(q => q.includes('form'))).toBe(true);
      expect(queries.some(q => q.includes('healthcare'))).toBe(true);
    });
  });

  describe('Configuration Validation', () => {
    it('should validate correct configuration', () => {
      const validConfig = {
        domains: {
          ui_practices: ['material.io', 'chakra-ui.com']
        },
        libraries: {
          react: ['react', 'mui']
        }
      };

      const validation = configManager.validateConfig(validConfig);
      expect(validation.valid).toBe(true);
      expect(validation.errors).toEqual([]);
    });

    it('should detect invalid configuration', () => {
      const invalidConfig = {
        domains: {
          ui_practices: 'not-an-array'
        },
        libraries: {
          react: 'also-not-an-array'
        }
      };

      const validation = configManager.validateConfig(invalidConfig);
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Real-world Configuration Examples', () => {
    it('should handle enterprise configuration', async () => {
      const enterpriseConfig = {
        domains: {
          ui_practices: {
            add: ['company-design-system.com', 'internal-guidelines.com']
          },
          compliance: {
            add: ['sox-compliance.org', 'gdpr-guidelines.eu']
          }
        },
        libraries: {
          react: {
            add: ['company-ui-kit', '@company/components']
          },
          internal: {
            add: ['legacy-components', 'shared-ui']
          }
        }
      };

      await configManager.saveUserConfig(enterpriseConfig);
      const config = await configManager.getCurrentConfig();
      
      expect(config.domains.ui_practices).toContain('company-design-system.com');
      expect(config.domains.compliance).toContain('sox-compliance.org');
      expect(config.libraries.react).toContain('company-ui-kit');
      expect(config.libraries.internal).toContain('legacy-components');
    });

    it('should handle startup configuration', async () => {
      const startupConfig = {
        domains: {
          ui_practices: {
            add: ['ui.shadcn.com', 'daisyui.com', 'tailwindui.com'],
            remove: ['material.io'] // Don't want Google's design system
          }
        },
        libraries: {
          react: {
            add: ['shadcn/ui', 'radix-ui', 'headlessui'],
            remove: ['mui', 'ant-design'] // Too heavy for startup
          }
        }
      };

      await configManager.saveUserConfig(startupConfig);
      const config = await configManager.getCurrentConfig();
      
      expect(config.domains.ui_practices).toContain('ui.shadcn.com');
      expect(config.domains.ui_practices).not.toContain('material.io');
      expect(config.libraries.react).toContain('shadcn/ui');
      expect(config.libraries.react).not.toContain('mui');
    });

    it('should handle agency configuration', async () => {
      const agencyConfig = {
        domains: {
          ui_practices: {
            add: ['client1-design.com', 'client2-brand.com']
          },
          accessibility: {
            add: ['wcag-validator.com', 'a11y-checker.org']
          }
        },
        libraries: {
          react: { add: ['styled-components', 'emotion'] },
          vue: { add: ['nuxt', 'vuetify'] },
          angular: { add: ['angular-material', 'primeng'] }
        }
      };

      await configManager.saveUserConfig(agencyConfig);
      const config = await configManager.getCurrentConfig();
      
      expect(config.domains.ui_practices).toContain('client1-design.com');
      expect(config.domains.accessibility).toContain('wcag-validator.com');
      expect(config.libraries.react).toContain('styled-components');
      expect(config.libraries.vue).toContain('nuxt');
    });
  });
});
