/**
 * Tests for Research-Enhanced Visual Generation
 */

import { describe, it, expect, beforeEach } from 'bun:test';
import { ComponentMapper } from '../../src/visual-generation/generators/component-mapper.js';
import { UIBestPracticesIntelligence } from '../../src/visual-generation/research/ui-best-practices-intelligence.js';
import { VisualResearchInterface } from '../../src/visual-generation/research/visual-research-interface.js';

describe('Research-Enhanced Visual Generation', () => {
  let componentMapper;
  let intelligence;
  let researchInterface;

  beforeEach(() => {
    // Initialize with research disabled for testing (to avoid API calls)
    componentMapper = new ComponentMapper({
      enableResearch: false,
      intelligence: {
        enableResearch: false
      }
    });
    
    intelligence = new UIBestPracticesIntelligence({
      enableResearch: false
    });
    
    researchInterface = new VisualResearchInterface({
      cacheEnabled: false
    });
  });

  describe('VisualResearchInterface', () => {
    it('should initialize without research providers', async () => {
      await researchInterface.initialize();
      expect(researchInterface.providers).toBeDefined();
    });

    it('should generate UI best practices queries', () => {
      const queries = researchInterface.generateUIBestPracticesQueries('form', {
        industry: 'healthcare',
        platform: 'web'
      });
      
      expect(queries).toBeInstanceOf(Array);
      expect(queries.length).toBeGreaterThan(0);
      expect(queries.some(q => q.includes('form'))).toBe(true);
      expect(queries.some(q => q.includes('healthcare'))).toBe(true);
    });

    it('should provide fallback component patterns', () => {
      const patterns = researchInterface.getFallbackComponentPatterns('form', 'react');
      
      expect(patterns.componentType).toBe('form');
      expect(patterns.framework).toBe('react');
      expect(patterns.recommendations).toBeInstanceOf(Array);
      expect(patterns.metadata.provider).toBe('fallback');
    });

    it('should provide fallback accessibility guidelines', () => {
      const guidelines = researchInterface.getFallbackAccessibilityGuidelines('table');
      
      expect(guidelines.componentType).toBe('table');
      expect(guidelines.guidelines).toBeInstanceOf(Array);
      expect(guidelines.recommendations).toBeInstanceOf(Array);
      expect(guidelines.metadata.provider).toBe('fallback');
    });
  });

  describe('UIBestPracticesIntelligence', () => {
    it('should enhance component with basic best practices', async () => {
      const component = {
        type: 'form',
        name: 'Login Form',
        asciiRepresentation: '┌─ Form ─┐\n│ Content │\n└─────────┘'
      };

      const enhanced = await intelligence.enhanceComponentWithBestPractices(component);
      
      expect(enhanced.bestPractices).toBeDefined();
      expect(enhanced.accessibility).toBeDefined();
      expect(enhanced.metadata.intelligenceApplied).toBe(false); // Research disabled
      expect(enhanced.metadata.fallbackUsed).toBe(true);
    });

    it('should generate form-specific intelligence', () => {
      const component = { type: 'form', name: 'Contact Form' };
      const practices = { bestPractices: [], confidence: 0.8 };
      
      const intelligence_recommendations = intelligence.getFormIntelligence(component, practices);
      
      expect(intelligence_recommendations).toBeInstanceOf(Array);
      expect(intelligence_recommendations.length).toBeGreaterThan(0);
      expect(intelligence_recommendations.some(r => r.includes('validation'))).toBe(true);
      expect(intelligence_recommendations.some(r => r.includes('labels'))).toBe(true);
    });

    it('should generate table-specific intelligence', () => {
      const component = { type: 'table', name: 'Data Table' };
      const practices = { bestPractices: [], confidence: 0.8 };
      
      const recommendations = intelligence.getTableIntelligence(component, practices);
      
      expect(recommendations).toBeInstanceOf(Array);
      expect(recommendations.some(r => r.includes('sortable') || r.includes('sorting'))).toBe(true);
      expect(recommendations.some(r => r.includes('pagination'))).toBe(true);
    });

    it('should generate ARIA attributes for different component types', () => {
      const formAria = intelligence.generateAriaAttributes('form', {});
      const tableAria = intelligence.generateAriaAttributes('table', {});
      const navAria = intelligence.generateAriaAttributes('navigation', {});
      
      expect(formAria).toContain('aria-label');
      expect(formAria).toContain('aria-required');
      expect(tableAria).toContain('aria-sort');
      expect(navAria).toContain('aria-current');
    });

    it('should generate keyboard navigation guidelines', () => {
      const formKeyboard = intelligence.generateKeyboardNavigation('form');
      const tableKeyboard = intelligence.generateKeyboardNavigation('table');
      
      expect(formKeyboard).toBeInstanceOf(Array);
      expect(formKeyboard.some(k => k.includes('Tab'))).toBe(true);
      expect(tableKeyboard.some(k => k.includes('Arrow'))).toBe(true);
    });

    it('should generate industry-specific intelligence', () => {
      const healthcareRules = intelligence.getIndustrySpecificIntelligence('form', 'healthcare');
      const financeRules = intelligence.getIndustrySpecificIntelligence('form', 'finance');
      
      expect(healthcareRules.some(r => r.includes('HIPAA'))).toBe(true);
      expect(financeRules.some(r => r.includes('PCI DSS'))).toBe(true);
    });
  });

  describe('Enhanced Component Mapping', () => {
    it('should map requirements to components with intelligence (research disabled)', async () => {
      const requirements = [
        'User login functionality with email and password',
        'Product catalog with search and filtering',
        'Shopping cart with checkout process'
      ];

      const components = await componentMapper.mapRequirementsToComponents(
        requirements, 
        [], 
        { industry: 'ecommerce' }
      );
      
      expect(components.length).toBeGreaterThan(0);
      
      // Check that components have basic intelligence applied
      const formComponent = components.find(c => c.type === 'form');
      if (formComponent) {
        expect(formComponent.bestPractices).toBeDefined();
        expect(formComponent.accessibility).toBeDefined();
      }
    });

    it('should enhance components with accessibility indicators', async () => {
      const component = {
        type: 'form',
        name: 'Registration Form',
        asciiRepresentation: '┌─ Form ─┐\n│ Content │\n└─────────┘',
        asciiTemplate: '┌─ {name} ─┐\n│ Content │\n└─────────┘',
        dimensions: { width: 20, height: 3 }
      };

      await componentMapper.enhanceComponentsWithIntelligence([component], {});

      expect(component.asciiRepresentation).toBeDefined();
      // Should have intelligence applied (even with research disabled, fallback is used)
      expect(component.bestPractices).toBeDefined();
      expect(component.accessibility).toBeDefined();
    });

    it('should generate intelligent ASCII with best practices', () => {
      const component = {
        type: 'form',
        name: 'Contact Form',
        bestPractices: [
          { practice: 'Use clear labels', priority: 3 },
          { practice: 'Implement validation', priority: 2 }
        ],
        accessibility: {
          ariaAttributes: ['aria-label', 'aria-required'],
          keyboardNavigation: ['Tab navigation', 'Enter to submit']
        },
        asciiRepresentation: '┌─ Form ─┐\n│ Content │\n└─────────┘',
        asciiTemplate: '┌─ {name} ─┐\n│ Content │\n└─────────┘',
        dimensions: { width: 20, height: 3 },
        interactions: []
      };

      const intelligentASCII = componentMapper.generateIntelligentASCII(component);

      expect(intelligentASCII).toContain('✨'); // Intelligence indicator
      expect(intelligentASCII).toContain('♿'); // Accessibility indicator
      expect(intelligentASCII).toContain('Best Practices Applied');
      expect(intelligentASCII).toContain('Accessibility Features');
    });

    it('should handle component enhancement errors gracefully', async () => {
      const component = {
        type: 'invalid_type',
        name: 'Invalid Component'
      };

      // Should not throw error
      await componentMapper.enhanceComponentsWithIntelligence([component], {});
      
      // Component should still exist
      expect(component.type).toBe('invalid_type');
      expect(component.name).toBe('Invalid Component');
    });
  });

  describe('Integration Tests', () => {
    it('should create complete research-enhanced wireframe', async () => {
      const requirements = [
        'Healthcare patient portal with secure login',
        'Medical records table with search functionality',
        'Appointment booking form with validation'
      ];

      const components = await componentMapper.mapRequirementsToComponents(
        requirements,
        [],
        { 
          industry: 'healthcare',
          platform: 'web',
          accessibility: 'WCAG_AA'
        }
      );
      
      expect(components.length).toBeGreaterThan(0);
      
      // Check for healthcare-specific enhancements
      const formComponent = components.find(c => c.type === 'form');
      if (formComponent) {
        expect(formComponent.bestPractices).toBeDefined();
        expect(formComponent.accessibility).toBeDefined();
        expect(formComponent.metadata.fallbackUsed).toBe(true); // Research disabled
      }
      
      // Check for table component with intelligence
      const tableComponent = components.find(c => c.type === 'table');
      if (tableComponent) {
        expect(tableComponent.bestPractices).toBeDefined();
        expect(tableComponent.accessibility.ariaAttributes).toContain('aria-sort');
      }
    });

    it('should maintain backward compatibility', async () => {
      // Test that old synchronous method still works
      const requirements = ['Simple contact form'];
      
      // This should work without await (backward compatibility)
      const components = componentMapper.mapRequirementsToComponents(requirements);
      
      // Should return a promise that resolves to components
      expect(components).toBeInstanceOf(Promise);
      
      const resolvedComponents = await components;
      expect(resolvedComponents.length).toBeGreaterThan(0);
    });

    it('should cache research results when enabled', async () => {
      const researchInterface = new VisualResearchInterface({
        cacheEnabled: true,
        cacheDuration: 1000
      });

      // First call (will use fallback since no providers)
      const result1 = await researchInterface.researchUIBestPractices('form');

      expect(result1).toBeDefined();
      expect(result1.metadata).toBeDefined();
    });
  });
});
