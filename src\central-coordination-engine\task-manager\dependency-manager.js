import { TaskPersistenceService } from '../../task-management/task-persistence-service.js';
import { TASK_STATUSES } from '../../task-management/task-schema.js';
import { log } from '../../utils/logger-utils.js'; // Assuming logger exists similar to claude-task-master

const taskService = new TaskPersistenceService(); // Assuming default project root or configure as needed

/**
 * Represents the structure of a node in the task graph.
 * @typedef {Object} TaskGraphNode
 * @property {Object} task - The task object itself.
 * @property {Set<string>} successors - Set of IDs of tasks that depend on this task.
 * @property {Set<string>} predecessors - Set of IDs of tasks this task depends on.
 * @property {number} [duration] - The estimated effort or duration of the task.
 * @property {number} [est] - Earliest Start Time.
 * @property {number} [eft] - Earliest Finish Time.
 * @property {number} [lst] - Latest Start Time.
 * @property {number} [lft] - Latest Finish Time.
 * @property {number} [slack] - Slack time for the task.
 */

/**
 * Represents the task graph.
 * @typedef {Map<string, TaskGraphNode>} TaskGraph
 */

/**
 * Builds an in-memory task graph (adjacency list) from all tasks.
 * Tasks are nodes, and dependencies are directed edges.
 * @returns {Promise<TaskGraph>} A promise that resolves to the constructed task graph.
 */
export async function buildTaskGraph() {
  const tasks = await taskService.getAllTasks();
  /** @type {TaskGraph} */
  const graph = new Map();

  // Initialize graph nodes
  for (const task of tasks) {
    graph.set(task.id, {
      task,
      successors: new Set(),
      predecessors: new Set(),
      // Attempt to parse estimatedHours as duration.
      // Default to 1 if not present or invalid for critical path calculation.
      duration: (typeof task.estimatedHours === 'number' && task.estimatedHours > 0) ? task.estimatedHours : 1,
    });
  }

  // Populate successors and predecessors
  for (const task of tasks) {
    if (task.dependencies && task.dependencies.length > 0) {
      for (const depId of task.dependencies) {
        if (graph.has(depId) && graph.has(task.id)) {
          graph.get(task.id).predecessors.add(depId);
          graph.get(depId).successors.add(task.id);
        } else {
          // This indicates a missing dependency or an issue with task data integrity
          log('warn', `Dependency link issue: Task ${task.id} depends on ${depId}, but one or both not found in graph init.`);
        }
      }
    }
  }
  return graph;
}

/**
 * Adds a dependency to a task.
 * @param {string} taskId - The ID of the task to add the dependency to.
 * @param {string} dependencyId - The ID of the task that `taskId` will depend on.
 * @returns {Promise<Object|null>} The updated task object or null if an error occurred.
 * @throws {Error} if tasks not found, self-dependency, or circular dependency.
 */
export async function addDependency(taskId, dependencyId) {
  log('info', `Attempting to add dependency: Task ${taskId} depends on ${dependencyId}`);

  if (taskId === dependencyId) {
    throw new Error(`Task ${taskId} cannot depend on itself.`);
  }

  const task = await taskService.getTaskById(taskId);
  const dependencyTask = await taskService.getTaskById(dependencyId);

  if (!task) {
    throw new Error(`Task with ID ${taskId} not found.`);
  }
  if (!dependencyTask) {
    throw new Error(`Dependency task with ID ${dependencyId} not found.`);
  }

  // Check for circular dependencies before adding
  // Temporarily add dependency to check for cycles
  const tempGraph = await buildTaskGraph();
  if (tempGraph.has(taskId) && tempGraph.has(dependencyId)) {
    // Simulate adding the dependency for cycle check
    tempGraph.get(taskId).predecessors.add(dependencyId);
    tempGraph.get(dependencyId).successors.add(taskId);

    if (detectCycleInGraph(tempGraph, taskId)) {
      throw new Error(`Adding dependency ${dependencyId} to task ${taskId} would create a circular dependency.`);
    }
  } else {
     log('warn', `Could not build temporary graph for cycle check for ${taskId} -> ${dependencyId}`);
     // Fallback: use a simpler check if graph building is problematic, or proceed with caution
  }


  const updatedDependencies = new Set(task.dependencies || []);
  if (updatedDependencies.has(dependencyId)) {
    log('info', `Dependency ${dependencyId} already exists for task ${taskId}.`);
    return task; // No change needed
  }
  updatedDependencies.add(dependencyId);

  const result = await taskService.updateTask(taskId, { dependencies: Array.from(updatedDependencies) });
  if (result) {
    log('success', `Successfully added dependency: Task ${taskId} now depends on ${dependencyId}`);
  }
  return result;
}

/**
 * Removes a dependency from a task.
 * @param {string} taskId - The ID of the task to remove the dependency from.
 * @param {string} dependencyId - The ID of the dependency to remove.
 * @returns {Promise<Object|null>} The updated task object or null if an error occurred.
 */
export async function removeDependency(taskId, dependencyId) {
  log('info', `Attempting to remove dependency: Task ${taskId} no longer depends on ${dependencyId}`);
  const task = await taskService.getTaskById(taskId);

  if (!task) {
    throw new Error(`Task with ID ${taskId} not found.`);
  }

  if (!task.dependencies || !task.dependencies.includes(dependencyId)) {
    log('info', `Dependency ${dependencyId} not found on task ${taskId}.`);
    return task; // No change needed
  }

  const updatedDependencies = (task.dependencies || []).filter(dep => dep !== dependencyId);
  const result = await taskService.updateTask(taskId, { dependencies: updatedDependencies });
   if (result) {
    log('success', `Successfully removed dependency: Task ${taskId} no longer depends on ${dependencyId}`);
  }
  return result;
}

/**
 * Detects if a cycle exists in the task graph starting from a specific node or in the whole graph.
 * Uses Depth First Search.
 * @param {TaskGraph} graph - The task graph.
 * @param {string} [startNodeId] - Optional. If provided, checks for cycles reachable from this node.
 *                                If not, checks all nodes.
 * @returns {boolean} True if a cycle is detected, false otherwise.
 */
export function detectCycleInGraph(graph, startNodeId = null) {
  const visited = new Set(); // Nodes whose processing is complete
  const recursionStack = new Set(); // Nodes currently in the recursion stack for the current DFS path

  /**
   * @param {string} nodeId
   * @returns {boolean}
   */
  function dfs(nodeId) {
    if (!graph.has(nodeId)) return false; // Node not in graph

    visited.add(nodeId);
    recursionStack.add(nodeId);

    const node = graph.get(nodeId);
    if (node.successors) { // Successors are tasks that depend on this one
      for (const successorId of node.successors) {
        if (!graph.has(successorId)) continue; // Successor not in graph (should not happen if graph is consistent)

        if (!visited.has(successorId)) {
          if (dfs(successorId)) {
            return true; // Cycle detected in a deeper call
          }
        } else if (recursionStack.has(successorId)) {
          // If successor is in recursionStack, it means we've found a back edge -> cycle
          log('warn', `Cycle detected: ${nodeId} -> ${successorId} (back edge)`);
          return true;
        }
      }
    }

    recursionStack.delete(nodeId); // Remove node from recursion stack before returning
    return false;
  }

  if (startNodeId) {
    if (!graph.has(startNodeId)) return false; // Start node not in graph
    return dfs(startNodeId);
  } else {
    // If no startNodeId, check all nodes in the graph
    for (const nodeId of graph.keys()) {
      if (!visited.has(nodeId)) {
        if (dfs(nodeId)) {
          return true; // Cycle detected
        }
      }
    }
  }
  return false; // No cycles detected
}


/**
 * Performs a topological sort of the task graph.
 * @param {TaskGraph} graph - The task graph.
 * @returns {Array<string>} An array of task IDs in topological order.
 * @throws {Error} if the graph has a cycle.
 */
function topologicalSort(graph) {
  const sortedOrder = [];
  const inDegree = new Map();
  const queue = [];

  // Initialize in-degrees and find all source nodes (in-degree 0)
  for (const [nodeId, nodeDetails] of graph.entries()) {
    inDegree.set(nodeId, nodeDetails.predecessors.size);
    if (nodeDetails.predecessors.size === 0) {
      queue.push(nodeId);
    }
  }

  while (queue.length > 0) {
    const u = queue.shift();
    sortedOrder.push(u);

    const nodeU = graph.get(u);
    if (nodeU && nodeU.successors) {
      for (const v of nodeU.successors) {
        if (inDegree.has(v)) {
          inDegree.set(v, inDegree.get(v) - 1);
          if (inDegree.get(v) === 0) {
            queue.push(v);
          }
        }
      }
    }
  }

  if (sortedOrder.length !== graph.size) {
    // This indicates a cycle, as not all nodes were included in the sort.
    // The detectCycleInGraph should ideally catch this, but this is a good safeguard.
    throw new Error("Graph has a cycle, topological sort failed. Cycle detected during sort.");
  }

  return sortedOrder;
}

/**
 * Calculates the critical path of the project.
 * Assumes task durations are available on graph nodes (e.g., node.duration).
 * @returns {Promise<Array<string>>} A promise that resolves to an array of task IDs forming a critical path.
 *                                   Returns an empty array if graph is empty or critical path cannot be determined.
 */
export async function getCriticalPath() {
  const graph = await buildTaskGraph();
  if (graph.size === 0) return [];

  if (detectCycleInGraph(graph)) {
      log('error', "Cannot calculate critical path: Cycle detected in the task graph.");
      return []; // Or throw an error
  }

  const sortedNodes = topologicalSort(graph);

  // Initialize EST and EFT
  for (const nodeId of graph.keys()) {
    const node = graph.get(nodeId);
    node.est = 0;
    node.eft = 0;
  }

  // Calculate EST and EFT (forward pass)
  for (const nodeId of sortedNodes) {
    const node = graph.get(nodeId);
    node.est = 0; // Default for source nodes
    for (const predId of node.predecessors) {
      const predNode = graph.get(predId);
      node.est = Math.max(node.est, predNode.eft);
    }
    node.eft = node.est + node.duration;
  }

  // Project finish time is the max EFT of all tasks
  let projectFinishTime = 0;
  for (const node of graph.values()) {
    projectFinishTime = Math.max(projectFinishTime, node.eft);
  }

  // Initialize LST and LFT
  for (const nodeId of graph.keys()) {
    const node = graph.get(nodeId);
    node.lft = projectFinishTime;
    node.lst = 0;
  }

  // Calculate LFT and LST (backward pass)
  for (let i = sortedNodes.length - 1; i >= 0; i--) {
    const nodeId = sortedNodes[i];
    const node = graph.get(nodeId);
    node.lft = projectFinishTime; // Default for sink nodes
    for (const succId of node.successors) {
      const succNode = graph.get(succId);
      node.lft = Math.min(node.lft, succNode.lst);
    }
    node.lst = node.lft - node.duration;
  }

  // Calculate slack and identify critical path
  const criticalPath = [];
  for (const nodeId of sortedNodes) {
    const node = graph.get(nodeId);
    node.slack = node.lst - node.est;
    // A task is critical if its slack is 0 (or very close to 0 due to float precision if applicable)
    if (Math.abs(node.slack) < 0.001) { // Using a small epsilon for float comparison
      criticalPath.push(nodeId);
    }
  }
  log('info', `Critical Path calculated: ${criticalPath.join(' -> ')}`);
  return criticalPath;
}


/**
 * Recommends the next tasks that can be started.
 * A task is a candidate if it's not completed and all its dependencies are met (completed).
 * @returns {Promise<Array<Object>>} A promise that resolves to an array of recommended task objects, sorted by priority and complexity.
 */
export async function getNextTasks() {
  const allTasks = await taskService.getAllTasks();
  const taskMap = new Map(allTasks.map(task => [task.id, task]));
  const availableTasks = [];

  for (const task of allTasks) {
    // Consider tasks that are not yet completed or deferred
    if (task.status === TASK_STATUSES.DONE || task.status === TASK_STATUSES.DEFERRED) {
      continue;
    }

    let allDependenciesMet = true;
    if (task.dependencies && task.dependencies.length > 0) {
      for (const depId of task.dependencies) {
        const depTask = taskMap.get(depId);
        // A dependency is met if the dependent task is DONE.
        if (!depTask || depTask.status !== TASK_STATUSES.DONE) {
          allDependenciesMet = false;
          break;
        }
      }
    }

    if (allDependenciesMet) {
      availableTasks.push(task);
    }
  }

  // Sort available tasks:
  // 1. Priority (High > Medium > Low)
  // 2. Business Impact (Higher score is more impactful, preferred - assuming 1-10 scale)
  // 3. Complexity Score (Lower score is less complex, preferred)
  // 4. Creation Date (Older first as a tie-breaker)
  const priorityOrder = { 'high': 1, 'medium': 2, 'low': 3 };

  availableTasks.sort((a, b) => {
    // Priority
    const priorityA = priorityOrder[a.priority?.toLowerCase()] || 3;
    const priorityB = priorityOrder[b.priority?.toLowerCase()] || 3;
    if (priorityA !== priorityB) {
      return priorityA - priorityB;
    }

    // Business Impact (higher is better, so sort descending)
    const impactA = typeof a.businessContext?.businessImpact === 'number' ? a.businessContext.businessImpact : 0;
    const impactB = typeof b.businessContext?.businessImpact === 'number' ? b.businessContext.businessImpact : 0;
    if (impactA !== impactB) {
      return impactB - impactA; // Sort descending for impact
    }

    // Complexity Score (lower is better)
    const complexityA = typeof a.complexityScore === 'number' ? a.complexityScore : Infinity;
    const complexityB = typeof b.complexityScore === 'number' ? b.complexityScore : Infinity;
    if (complexityA !== complexityB) {
      return complexityA - complexityB;
    }

    // Creation Date (older first)
    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
  });

  log('info', `Recommended next tasks (sorted): ${availableTasks.map(t => `${t.id} (P:${t.priority || 'N/A'}, BI:${t.businessContext?.businessImpact || 'N/A'}, C:${t.complexityScore || 'N/A'})`).join(', ')}`);
  return availableTasks;
}

/**
 * Builds the task graph and annotates each node with a calculated status
 * (e.g., 'blocked', 'ready-to-start', 'in-progress', 'done').
 * This is useful for visualization or higher-level planning.
 * @returns {Promise<Array<Object>>} A promise that resolves to an array of task nodes,
 *                                   each including the task object and its calculated graph status.
 */
export async function getTaskGraphWithStatuses() {
  const graph = await buildTaskGraph();
  const tasksWithStatuses = [];

  for (const [nodeId, nodeDetails] of graph.entries()) {
    let calculatedStatus = nodeDetails.task.status; // Start with the task's own status

    if (nodeDetails.task.status !== TASK_STATUSES.DONE && nodeDetails.task.status !== TASK_STATUSES.DEFERRED) {
      // Check dependencies only if the task itself is not already done or deferred
      let allDepsMet = true;
      if (nodeDetails.predecessors.size > 0) {
        for (const predId of nodeDetails.predecessors) {
          const predNode = graph.get(predId);
          if (!predNode || predNode.task.status !== TASK_STATUSES.DONE) {
            allDepsMet = false;
            break;
          }
        }
      }

      if (!allDepsMet) {
        calculatedStatus = 'blocked';
      } else {
        // If all dependencies are met, and it's not done/deferred, it's ready or in progress
        if (nodeDetails.task.status === TASK_STATUSES.PENDING) {
          calculatedStatus = 'ready-to-start';
        } else {
          // Could be IN_PROGRESS or any other active status
          calculatedStatus = nodeDetails.task.status;
        }
      }
    }

    tasksWithStatuses.push({
      ...nodeDetails.task, // Spread the original task properties
      graphStatus: calculatedStatus,
      // Optionally include successor/predecessor IDs if needed for visualization
      // successors: Array.from(nodeDetails.successors),
      // predecessors: Array.from(nodeDetails.predecessors),
    });
  }

  // Optionally sort or further process tasksWithStatuses if needed
  // For example, sort by topological order for some visualizations
  try {
    const sortedIds = topologicalSort(graph);
    tasksWithStatuses.sort((a, b) => sortedIds.indexOf(a.id) - sortedIds.indexOf(b.id));
  } catch (error) {
    log('warn', `Could not topologically sort graph for status visualization: ${error.message}`);
    // Proceed with unsorted list if sort fails (e.g., due to undetected cycle if checks are bypassed)
  }
  
  log('info', `Task graph with statuses generated for ${tasksWithStatuses.length} tasks.`);
  return tasksWithStatuses;
}