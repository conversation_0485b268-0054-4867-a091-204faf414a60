/**
 * Visual Asset Data Models
 * Defines data structures for visual assets with metadata, versioning, and validation
 */

/**
 * Base Visual Asset class with common properties
 */
export class VisualAsset {
  constructor(data = {}) {
    this.id = data.id || this.generateId();
    this.type = data.type || 'unknown';
    this.title = data.title || '';
    this.description = data.description || '';
    this.content = data.content || '';
    this.metadata = new AssetMetadata(data.metadata);
    this.version = new AssetVersion(data.version);
    this.businessIntelligence = new BusinessIntelligence(data.businessIntelligence);
    this.createdAt = data.createdAt || new Date().toISOString();
    this.updatedAt = data.updatedAt || new Date().toISOString();
  }

  generateId() {
    return `va_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Update asset content and metadata
   */
  update(updates) {
    Object.assign(this, updates);
    this.updatedAt = new Date().toISOString();
    this.version.increment();
  }

  /**
   * Validate asset structure and content
   */
  validate() {
    const errors = [];
    
    if (!this.id) errors.push('Asset ID is required');
    if (!this.type) errors.push('Asset type is required');
    if (!this.title) errors.push('Asset title is required');
    if (!this.content) errors.push('Asset content is required');
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Export asset to JSON
   */
  toJSON() {
    return {
      id: this.id,
      type: this.type,
      title: this.title,
      description: this.description,
      content: this.content,
      metadata: this.metadata.toJSON(),
      version: this.version.toJSON(),
      businessIntelligence: this.businessIntelligence.toJSON(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}

/**
 * ASCII Wireframe Asset
 */
export class WireframeAsset extends VisualAsset {
  constructor(data = {}) {
    super({ ...data, type: 'wireframe' });
    this.layout = data.layout || {};
    this.components = data.components || [];
    this.interactions = data.interactions || [];
    this.breakpoints = data.breakpoints || ['desktop'];
    this.gridSystem = data.gridSystem || { columns: 12, gutters: 16 };
    this.asciiContent = data.asciiContent || '';
  }

  /**
   * Add component to wireframe
   */
  addComponent(component) {
    this.components.push({
      id: `comp_${this.components.length + 1}`,
      ...component,
      addedAt: new Date().toISOString()
    });
    this.update({});
  }

  /**
   * Generate ASCII representation
   */
  generateASCII() {
    // This will be implemented by the ASCII generator
    return this.asciiContent;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      layout: this.layout,
      components: this.components,
      interactions: this.interactions,
      breakpoints: this.breakpoints,
      gridSystem: this.gridSystem,
      asciiContent: this.asciiContent
    };
  }
}

/**
 * Mermaid Diagram Asset
 */
export class DiagramAsset extends VisualAsset {
  constructor(data = {}) {
    super({ ...data, type: 'diagram' });
    this.diagramType = data.diagramType || 'flowchart'; // flowchart, sequence, state
    this.mermaidSyntax = data.mermaidSyntax || '';
    this.nodes = data.nodes || [];
    this.edges = data.edges || [];
    this.userFlow = data.userFlow || {};
    this.decisionPoints = data.decisionPoints || [];
    this.errorPaths = data.errorPaths || [];
  }

  /**
   * Add node to diagram
   */
  addNode(node) {
    this.nodes.push({
      id: `node_${this.nodes.length + 1}`,
      ...node,
      addedAt: new Date().toISOString()
    });
    this.update({});
  }

  /**
   * Add edge between nodes
   */
  addEdge(fromNodeId, toNodeId, label = '') {
    this.edges.push({
      id: `edge_${this.edges.length + 1}`,
      from: fromNodeId,
      to: toNodeId,
      label,
      addedAt: new Date().toISOString()
    });
    this.update({});
  }

  /**
   * Generate Mermaid syntax
   */
  generateMermaidSyntax() {
    // This will be implemented by the Mermaid generator
    return this.mermaidSyntax;
  }

  toJSON() {
    return {
      ...super.toJSON(),
      diagramType: this.diagramType,
      mermaidSyntax: this.mermaidSyntax,
      nodes: this.nodes,
      edges: this.edges,
      userFlow: this.userFlow,
      decisionPoints: this.decisionPoints,
      errorPaths: this.errorPaths
    };
  }
}

/**
 * HTML Prototype Asset
 */
export class PrototypeAsset extends VisualAsset {
  constructor(data = {}) {
    super({ ...data, type: 'prototype' });
    this.htmlContent = data.htmlContent || '';
    this.cssContent = data.cssContent || '';
    this.jsContent = data.jsContent || '';
    this.components = data.components || [];
    this.interactions = data.interactions || [];
    this.designSystem = data.designSystem || {};
    this.responsiveBreakpoints = data.responsiveBreakpoints || {};
    this.accessibilityFeatures = data.accessibilityFeatures || [];
  }

  /**
   * Add interactive component
   */
  addComponent(component) {
    this.components.push({
      id: `proto_comp_${this.components.length + 1}`,
      ...component,
      addedAt: new Date().toISOString()
    });
    this.update({});
  }

  /**
   * Generate complete HTML prototype
   */
  generateHTML() {
    // Check if htmlContent is already a complete document
    if (this.htmlContent.includes('<!DOCTYPE html>')) {
      // Already a complete document, enhance it if needed
      let html = this.htmlContent;

      // If Tailwind is enabled in metadata, enhance the HTML
      if (this.metadata?.tailwindEnabled && this.metadata?.tailwindData) {
        const tailwindData = this.metadata.tailwindData;

        // Add Tailwind config script if needed
        if (tailwindData.tailwindConfig) {
          const configScript = `
<script>
  tailwind.config = ${JSON.stringify(tailwindData.tailwindConfig, null, 2)};
</script>`;
          html = html.replace('</head>', `${configScript}\n</head>`);
        }
      }

      return html;
    } else {
      // Content only, wrap in complete document
      const tailwindCDN = this.metadata?.tailwindEnabled ?
        '<script src="https://cdn.tailwindcss.com"></script>' : '';

      return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${this.title}</title>
  ${tailwindCDN}
  <style>${this.cssContent}</style>
</head>
<body>
  ${this.htmlContent}
  <script>${this.jsContent}</script>
</body>
</html>`;
    }
  }

  toJSON() {
    return {
      ...super.toJSON(),
      htmlContent: this.htmlContent,
      cssContent: this.cssContent,
      jsContent: this.jsContent,
      components: this.components,
      interactions: this.interactions,
      designSystem: this.designSystem,
      responsiveBreakpoints: this.responsiveBreakpoints,
      accessibilityFeatures: this.accessibilityFeatures
    };
  }
}

/**
 * Asset Metadata
 */
export class AssetMetadata {
  constructor(data = {}) {
    this.sourceRequirements = data.sourceRequirements || [];
    this.generationMethod = data.generationMethod || 'manual';
    this.generatorVersion = data.generatorVersion || '1.0.0';
    this.validationStatus = data.validationStatus || 'pending';
    this.tags = data.tags || [];
    this.category = data.category || 'general';
    this.priority = data.priority || 'medium';
    this.complexity = data.complexity || 'medium';

    // Preserve all custom properties from data
    Object.keys(data).forEach(key => {
      if (!this.hasOwnProperty(key)) {
        this[key] = data[key];
      }
    });
  }

  toJSON() {
    return { ...this };
  }
}

/**
 * Asset Version Control
 */
export class AssetVersion {
  constructor(data = {}) {
    this.major = data.major || 1;
    this.minor = data.minor || 0;
    this.patch = data.patch || 0;
    this.history = data.history || [];
  }

  increment(type = 'patch') {
    const previousVersion = this.toString();
    
    switch (type) {
      case 'major':
        this.major++;
        this.minor = 0;
        this.patch = 0;
        break;
      case 'minor':
        this.minor++;
        this.patch = 0;
        break;
      default:
        this.patch++;
    }

    this.history.push({
      version: previousVersion,
      timestamp: new Date().toISOString(),
      type
    });
  }

  toString() {
    return `${this.major}.${this.minor}.${this.patch}`;
  }

  toJSON() {
    return {
      major: this.major,
      minor: this.minor,
      patch: this.patch,
      history: this.history,
      current: this.toString()
    };
  }
}

/**
 * Business Intelligence Tracking
 */
export class BusinessIntelligence {
  constructor(data = {}) {
    this.usageCount = data.usageCount || 0;
    this.userFeedback = data.userFeedback || [];
    this.performanceMetrics = data.performanceMetrics || {};
    this.businessValue = data.businessValue || 'unknown';
    this.stakeholderApproval = data.stakeholderApproval || 'pending';
    this.iterationCount = data.iterationCount || 0;
    this.lastAccessed = data.lastAccessed || null;
  }

  recordUsage() {
    this.usageCount++;
    this.lastAccessed = new Date().toISOString();
  }

  addFeedback(feedback) {
    this.userFeedback.push({
      id: `feedback_${this.userFeedback.length + 1}`,
      ...feedback,
      timestamp: new Date().toISOString()
    });
  }

  toJSON() {
    return { ...this };
  }
}
