/**
 * Conversation Intelligence Integration Example (Task 3.4)
 * 
 * Demonstrates how to integrate conversation intelligence with WLR-004 conversation management
 * for autonomous research orchestration during conversational flows.
 */

import { ConversationIntelligenceManager } from '../src/conversation-intelligence/conversation-management-integration.js';
import { getSessionRecoveryManager } from '../src/workflow-logic/session-recovery-manager.js';

/**
 * Example: Restaurant App Conversation with Intelligence
 */
async function restaurantAppConversationExample() {
  console.log('🤖 Starting Restaurant App Conversation with Intelligence...\n');

  // Initialize conversation intelligence manager
  const intelligenceManager = new ConversationIntelligenceManager({
    projectRoot: process.cwd(),
    contextAnalysis: {
      confidenceThreshold: 0.7
    },
    researchTiming: {
      enableMilestoneDetection: true
    },
    research: {
      maxConcurrent: 3,
      enableBackgroundProcessing: true
    }
  });

  // Create a new session using WLR-004
  const sessionManager = getSessionRecoveryManager();
  const session = await sessionManager.createSession({
    projectName: 'Restaurant Discovery App',
    agentId: 'conversation-intelligence-demo',
    currentOrientation: 'Starting a new restaurant app project'
  });

  const sessionId = session.sessionId;
  console.log(`📱 Created session: ${sessionId}`);

  // Initialize conversation intelligence for the session
  await intelligenceManager.initializeConversationIntelligence(sessionId, {
    enableIntelligence: true,
    enableBackgroundResearch: true
  });

  console.log('🧠 Conversation intelligence initialized\n');

  // Setup event listeners to monitor intelligence activities
  intelligenceManager.on('milestoneProcessed', ({ milestone }) => {
    console.log(`🎯 Milestone detected: ${milestone.type} - ${milestone.description}`);
  });

  intelligenceManager.on('backgroundResearchQueued', ({ jobId }) => {
    console.log(`🔍 Background research queued: ${jobId}`);
  });

  intelligenceManager.on('backgroundResearchCompleted', ({ result }) => {
    console.log(`✅ Background research completed: ${result.result?.summary || 'Research finished'}`);
  });

  // Simulate conversation flow
  const conversationFlow = [
    {
      sender: 'user',
      content: 'I want to build a restaurant discovery app for my local area',
      type: 'project_initialization'
    },
    {
      sender: 'assistant',
      content: 'Great idea! A restaurant discovery app can help people find great dining experiences. What specific features are you thinking about?',
      type: 'clarification'
    },
    {
      sender: 'user',
      content: 'I want users to be able to search restaurants by cuisine, read reviews, make reservations, and get directions',
      type: 'feature_specification'
    },
    {
      sender: 'assistant',
      content: 'Those are excellent core features. Are you targeting a specific geographic area or planning to start local and expand?',
      type: 'scope_clarification'
    },
    {
      sender: 'user',
      content: 'I want to start in San Francisco and then expand to other major cities. My budget is around $50,000 and I need to launch in 6 months',
      type: 'business_constraints'
    },
    {
      sender: 'assistant',
      content: 'Perfect! With that timeline and budget, we should focus on an MVP approach. Let me help you prioritize features and create a development plan.',
      type: 'planning_response'
    }
  ];

  // Process each message in the conversation
  for (let i = 0; i < conversationFlow.length; i++) {
    const message = conversationFlow[i];
    console.log(`\n💬 ${message.sender}: ${message.content}`);

    // Process message with conversation intelligence
    const result = await intelligenceManager.processConversationMessage(sessionId, message);

    if (result.intelligenceResults) {
      const { contextAnalysis, timingAnalysis } = result.intelligenceResults;
      
      console.log(`   📊 Context: ${contextAnalysis.projectContext.domain || 'analyzing...'}`);
      console.log(`   🎯 Milestones: ${timingAnalysis.milestonesDetected || 0}`);
      console.log(`   🔍 Research triggers: ${timingAnalysis.triggersGenerated || 0}`);
      
      if (result.researchTriggered) {
        console.log(`   🚀 Background research initiated: ${result.backgroundJobs.length} jobs`);
      }
    }

    // Maintain conversation continuity
    const continuity = await intelligenceManager.maintainConversationContinuity(sessionId);
    if (continuity.activeBackgroundJobs > 0) {
      console.log(`   ⏳ Background activity: ${continuity.activeBackgroundJobs} jobs running`);
    }

    // Small delay to simulate real conversation timing
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Get final conversation intelligence status
  console.log('\n📈 Final Conversation Intelligence Status:');
  const status = intelligenceManager.getConversationIntelligenceStatus(sessionId);
  console.log(JSON.stringify(status, null, 2));

  // Get research results
  console.log('\n🔬 Research Results:');
  const researchResults = intelligenceManager.getResearchResults(sessionId);
  researchResults.forEach((result, index) => {
    console.log(`${index + 1}. Job ${result.jobId}: ${result.job?.trigger?.type || 'Unknown'}`);
  });

  // Cleanup
  await intelligenceManager.cleanupConversationIntelligence(sessionId);
  await sessionManager.closeSession(sessionId);
  intelligenceManager.stop();

  console.log('\n✅ Conversation intelligence integration example completed!');
}

/**
 * Example: E-commerce Platform Conversation with Intelligence
 */
async function ecommercePlatformConversationExample() {
  console.log('\n🛒 Starting E-commerce Platform Conversation with Intelligence...\n');

  const intelligenceManager = new ConversationIntelligenceManager({
    projectRoot: process.cwd()
  });

  const sessionManager = getSessionRecoveryManager();
  const session = await sessionManager.createSession({
    projectName: 'E-commerce Platform',
    agentId: 'ecommerce-intelligence-demo'
  });

  const sessionId = session.sessionId;
  await intelligenceManager.initializeConversationIntelligence(sessionId);

  // Simulate rapid conversation with multiple decision points
  const messages = [
    { sender: 'user', content: 'I need to build an e-commerce platform for selling handmade crafts' },
    { sender: 'user', content: 'I want to compete with Etsy but focus on local artisans' },
    { sender: 'user', content: 'Budget is $100k, need to launch in 4 months' },
    { sender: 'user', content: 'Should I use Shopify or build custom?' }
  ];

  for (const message of messages) {
    console.log(`💬 ${message.sender}: ${message.content}`);
    
    const result = await intelligenceManager.processConversationMessage(sessionId, message);
    
    if (result.intelligenceResults) {
      console.log(`   🧠 Intelligence: ${result.intelligenceResults.contextAnalysis.projectContext.confidence || 0} confidence`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // Show how conversation intelligence maintains state across messages
  const finalStatus = intelligenceManager.getConversationIntelligenceStatus(sessionId);
  console.log('\n📊 Final Status:', finalStatus);

  await intelligenceManager.cleanupConversationIntelligence(sessionId);
  await sessionManager.closeSession(sessionId);
  intelligenceManager.stop();

  console.log('✅ E-commerce conversation intelligence example completed!');
}

/**
 * Example: Error Handling and Recovery
 */
async function errorHandlingExample() {
  console.log('\n⚠️  Testing Error Handling and Recovery...\n');

  const intelligenceManager = new ConversationIntelligenceManager({
    projectRoot: process.cwd()
  });

  // Test with non-existent session
  try {
    await intelligenceManager.processConversationMessage('nonexistent-session', {
      content: 'test message'
    });
  } catch (error) {
    console.log(`✅ Correctly handled error: ${error.message}`);
  }

  // Test initialization with invalid session
  const result = await intelligenceManager.initializeConversationIntelligence('invalid-session');
  console.log(`✅ Initialization with invalid session: ${result ? 'unexpected success' : 'correctly failed'}`);

  // Test status for non-existent conversation
  const status = intelligenceManager.getConversationIntelligenceStatus('nonexistent');
  console.log(`✅ Status for non-existent conversation: ${status === null ? 'correctly null' : 'unexpected result'}`);

  intelligenceManager.stop();
  console.log('✅ Error handling example completed!');
}

/**
 * Run all examples
 */
async function runExamples() {
  try {
    await restaurantAppConversationExample();
    await ecommercePlatformConversationExample();
    await errorHandlingExample();
    
    console.log('\n🎉 All conversation intelligence integration examples completed successfully!');
  } catch (error) {
    console.error('❌ Example failed:', error);
    process.exit(1);
  }
}

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runExamples();
}

export {
  restaurantAppConversationExample,
  ecommercePlatformConversationExample,
  errorHandlingExample,
  runExamples
};
