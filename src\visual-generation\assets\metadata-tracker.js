/**
 * Metadata Tracking System for Visual Assets
 * Tracks generation, validation, iteration history, and user feedback
 */

import { writeJ<PERSON>NF<PERSON>, readJSONFile, updateJSONFile } from '../../file-management/reliable-file-manager.js';
import path from 'path';

/**
 * Visual Asset Metadata Tracker
 */
export class MetadataTracker {
  constructor(projectRoot = process.cwd()) {
    this.projectRoot = projectRoot;
    this.metadataDir = path.join(projectRoot, '.guidant/deliverables/wireframes/.metadata');
  }

  /**
   * Initialize metadata tracking system
   */
  async initialize() {
    // Create metadata directory structure
    await this.ensureDirectoryExists(this.metadataDir);
    
    // Initialize tracking files
    const trackingFiles = [
      'generation-history.json',
      'validation-results.json', 
      'iteration-history.json',
      'user-feedback.json',
      'performance-metrics.json'
    ];
    
    for (const file of trackingFiles) {
      const filePath = path.join(this.metadataDir, file);
      try {
        await readJSONFile(filePath);
      } catch {
        await this.initializeTrackingFile(file);
      }
    }
    
    return {
      initialized: true,
      metadataDirectory: this.metadataDir,
      trackingFiles
    };
  }

  /**
   * Track asset generation event
   */
  async trackGeneration(assetId, generationData) {
    const historyPath = path.join(this.metadataDir, 'generation-history.json');
    const history = await readJSONFile(historyPath);
    
    const generationEvent = {
      id: `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      assetId,
      timestamp: new Date().toISOString(),
      generatorType: generationData.generatorType,
      generatorVersion: generationData.generatorVersion || '1.0.0',
      sourceRequirements: generationData.sourceRequirements || [],
      generationMethod: generationData.method || 'automatic',
      parameters: generationData.parameters || {},
      duration: generationData.duration || 0,
      success: generationData.success !== false,
      errors: generationData.errors || [],
      warnings: generationData.warnings || [],
      metadata: {
        userAgent: generationData.userAgent || 'Guidant Visual Generator',
        environment: generationData.environment || 'development',
        researchEnhanced: generationData.researchEnhanced || false,
        iterationCount: generationData.iterationCount || 1
      }
    };
    
    history.events.push(generationEvent);
    history.totalGenerations++;
    history.lastGeneration = generationEvent.timestamp;
    
    // Update statistics
    if (!history.statistics.byGenerator[generationData.generatorType]) {
      history.statistics.byGenerator[generationData.generatorType] = 0;
    }
    history.statistics.byGenerator[generationData.generatorType]++;
    
    if (generationEvent.success) {
      history.statistics.successfulGenerations++;
    } else {
      history.statistics.failedGenerations++;
    }
    
    await writeJSONFile(historyPath, history);
    return generationEvent.id;
  }

  /**
   * Track validation results
   */
  async trackValidation(assetId, validationData) {
    const validationPath = path.join(this.metadataDir, 'validation-results.json');
    const validations = await readJSONFile(validationPath);
    
    const validationEvent = {
      id: `val_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      assetId,
      timestamp: new Date().toISOString(),
      validationType: validationData.type || 'general',
      validator: validationData.validator || 'system',
      status: validationData.status, // 'passed', 'failed', 'warning'
      score: validationData.score || null,
      results: {
        errors: validationData.errors || [],
        warnings: validationData.warnings || [],
        suggestions: validationData.suggestions || [],
        metrics: validationData.metrics || {}
      },
      criteria: validationData.criteria || {},
      autoFixed: validationData.autoFixed || false,
      fixedIssues: validationData.fixedIssues || []
    };
    
    validations.events.push(validationEvent);
    validations.totalValidations++;
    validations.lastValidation = validationEvent.timestamp;
    
    // Update statistics
    if (!validations.statistics.byType[validationData.type]) {
      validations.statistics.byType[validationData.type] = { passed: 0, failed: 0, warnings: 0 };
    }
    validations.statistics.byType[validationData.type][validationEvent.status]++;
    
    await writeJSONFile(validationPath, validations);
    return validationEvent.id;
  }

  /**
   * Track iteration/refinement history
   */
  async trackIteration(assetId, iterationData) {
    const iterationPath = path.join(this.metadataDir, 'iteration-history.json');
    const iterations = await readJSONFile(iterationPath);
    
    const iterationEvent = {
      id: `iter_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      assetId,
      timestamp: new Date().toISOString(),
      iterationType: iterationData.type || 'refinement', // refinement, enhancement, fix
      trigger: iterationData.trigger || 'manual', // manual, feedback, validation, research
      previousVersion: iterationData.previousVersion,
      newVersion: iterationData.newVersion,
      changes: {
        description: iterationData.description || '',
        changedElements: iterationData.changedElements || [],
        addedElements: iterationData.addedElements || [],
        removedElements: iterationData.removedElements || [],
        modifiedProperties: iterationData.modifiedProperties || []
      },
      reasoning: iterationData.reasoning || '',
      userFeedback: iterationData.userFeedback || null,
      researchInsights: iterationData.researchInsights || [],
      qualityImprovement: iterationData.qualityImprovement || null
    };
    
    iterations.events.push(iterationEvent);
    iterations.totalIterations++;
    iterations.lastIteration = iterationEvent.timestamp;
    
    // Update asset iteration count
    if (!iterations.assetIterations[assetId]) {
      iterations.assetIterations[assetId] = 0;
    }
    iterations.assetIterations[assetId]++;
    
    await writeJSONFile(iterationPath, iterations);
    return iterationEvent.id;
  }

  /**
   * Track user feedback
   */
  async trackUserFeedback(assetId, feedbackData) {
    const feedbackPath = path.join(this.metadataDir, 'user-feedback.json');
    const feedback = await readJSONFile(feedbackPath);
    
    const feedbackEvent = {
      id: `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      assetId,
      timestamp: new Date().toISOString(),
      userId: feedbackData.userId || 'anonymous',
      feedbackType: feedbackData.type || 'general', // general, usability, visual, functional
      rating: feedbackData.rating || null, // 1-5 scale
      sentiment: feedbackData.sentiment || 'neutral', // positive, neutral, negative
      content: {
        text: feedbackData.text || '',
        suggestions: feedbackData.suggestions || [],
        issues: feedbackData.issues || [],
        praise: feedbackData.praise || []
      },
      context: {
        userRole: feedbackData.userRole || 'stakeholder',
        sessionId: feedbackData.sessionId || null,
        deviceType: feedbackData.deviceType || 'unknown',
        browserInfo: feedbackData.browserInfo || null
      },
      actionable: feedbackData.actionable !== false,
      priority: feedbackData.priority || 'medium', // low, medium, high, critical
      status: 'new', // new, reviewed, addressed, dismissed
      tags: feedbackData.tags || []
    };
    
    feedback.events.push(feedbackEvent);
    feedback.totalFeedback++;
    feedback.lastFeedback = feedbackEvent.timestamp;
    
    // Update statistics
    if (!feedback.statistics.byType[feedbackData.type]) {
      feedback.statistics.byType[feedbackData.type] = 0;
    }
    feedback.statistics.byType[feedbackData.type]++;
    
    if (!feedback.statistics.byRating[feedbackData.rating]) {
      feedback.statistics.byRating[feedbackData.rating] = 0;
    }
    feedback.statistics.byRating[feedbackData.rating]++;
    
    await writeJSONFile(feedbackPath, feedback);
    return feedbackEvent.id;
  }

  /**
   * Track performance metrics
   */
  async trackPerformance(assetId, performanceData) {
    const performancePath = path.join(this.metadataDir, 'performance-metrics.json');
    const performance = await readJSONFile(performancePath);
    
    const performanceEvent = {
      id: `perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      assetId,
      timestamp: new Date().toISOString(),
      operation: performanceData.operation || 'generation',
      metrics: {
        duration: performanceData.duration || 0,
        memoryUsage: performanceData.memoryUsage || 0,
        cpuUsage: performanceData.cpuUsage || 0,
        assetSize: performanceData.assetSize || 0,
        complexity: performanceData.complexity || 'medium'
      },
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        memoryTotal: performanceData.memoryTotal || 0
      },
      optimizations: performanceData.optimizations || [],
      bottlenecks: performanceData.bottlenecks || []
    };
    
    performance.events.push(performanceEvent);
    performance.totalMeasurements++;
    performance.lastMeasurement = performanceEvent.timestamp;
    
    // Update averages
    const operationMetrics = performance.averages[performanceData.operation] || {
      count: 0,
      totalDuration: 0,
      averageDuration: 0
    };
    
    operationMetrics.count++;
    operationMetrics.totalDuration += performanceData.duration || 0;
    operationMetrics.averageDuration = operationMetrics.totalDuration / operationMetrics.count;
    
    performance.averages[performanceData.operation] = operationMetrics;
    
    await writeJSONFile(performancePath, performance);
    return performanceEvent.id;
  }

  /**
   * Get metadata summary for an asset
   */
  async getAssetMetadataSummary(assetId) {
    const [generation, validation, iteration, feedback, performance] = await Promise.all([
      this.getAssetGenerationHistory(assetId),
      this.getAssetValidationHistory(assetId),
      this.getAssetIterationHistory(assetId),
      this.getAssetFeedback(assetId),
      this.getAssetPerformanceMetrics(assetId)
    ]);
    
    return {
      assetId,
      summary: {
        totalGenerations: generation.length,
        totalValidations: validation.length,
        totalIterations: iteration.length,
        totalFeedback: feedback.length,
        totalPerformanceMeasurements: performance.length,
        lastActivity: this.getLastActivityTimestamp([
          ...generation,
          ...validation,
          ...iteration,
          ...feedback,
          ...performance
        ]),
        qualityScore: this.calculateQualityScore(validation),
        userSatisfaction: this.calculateUserSatisfaction(feedback),
        performanceScore: this.calculatePerformanceScore(performance)
      },
      details: {
        generation,
        validation,
        iteration,
        feedback,
        performance
      }
    };
  }

  /**
   * Get comprehensive metadata analytics
   */
  async getMetadataAnalytics() {
    const [generation, validation, iteration, feedback, performance] = await Promise.all([
      readJSONFile(path.join(this.metadataDir, 'generation-history.json')),
      readJSONFile(path.join(this.metadataDir, 'validation-results.json')),
      readJSONFile(path.join(this.metadataDir, 'iteration-history.json')),
      readJSONFile(path.join(this.metadataDir, 'user-feedback.json')),
      readJSONFile(path.join(this.metadataDir, 'performance-metrics.json'))
    ]);
    
    return {
      overview: {
        totalAssets: Object.keys(iteration.assetIterations).length,
        totalGenerations: generation.totalGenerations,
        totalValidations: validation.totalValidations,
        totalIterations: iteration.totalIterations,
        totalFeedback: feedback.totalFeedback,
        totalPerformanceMeasurements: performance.totalMeasurements
      },
      trends: {
        generationTrends: this.calculateTrends(generation.events, 'timestamp'),
        validationTrends: this.calculateTrends(validation.events, 'timestamp'),
        feedbackTrends: this.calculateTrends(feedback.events, 'timestamp')
      },
      quality: {
        averageValidationScore: this.calculateAverageValidationScore(validation.events),
        successRate: generation.statistics.successfulGenerations / generation.totalGenerations,
        userSatisfactionScore: this.calculateOverallUserSatisfaction(feedback.events)
      },
      performance: {
        averageGenerationTime: performance.averages.generation?.averageDuration || 0,
        performanceOptimizations: this.getPerformanceOptimizations(performance.events)
      }
    };
  }

  // Helper methods
  async initializeTrackingFile(filename) {
    const filePath = path.join(this.metadataDir, filename);
    const initialData = this.getInitialDataForFile(filename);
    await writeJSONFile(filePath, initialData);
  }

  getInitialDataForFile(filename) {
    const baseStructure = {
      version: '1.0.0',
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      events: []
    };

    switch (filename) {
      case 'generation-history.json':
        return {
          ...baseStructure,
          totalGenerations: 0,
          lastGeneration: null,
          statistics: {
            successfulGenerations: 0,
            failedGenerations: 0,
            byGenerator: {}
          }
        };
      
      case 'validation-results.json':
        return {
          ...baseStructure,
          totalValidations: 0,
          lastValidation: null,
          statistics: {
            byType: {}
          }
        };
      
      case 'iteration-history.json':
        return {
          ...baseStructure,
          totalIterations: 0,
          lastIteration: null,
          assetIterations: {}
        };
      
      case 'user-feedback.json':
        return {
          ...baseStructure,
          totalFeedback: 0,
          lastFeedback: null,
          statistics: {
            byType: {},
            byRating: {}
          }
        };
      
      case 'performance-metrics.json':
        return {
          ...baseStructure,
          totalMeasurements: 0,
          lastMeasurement: null,
          averages: {}
        };
      
      default:
        return baseStructure;
    }
  }

  async ensureDirectoryExists(dirPath) {
    const fs = await import('fs/promises');
    await fs.mkdir(dirPath, { recursive: true });
  }

  async getAssetGenerationHistory(assetId) {
    const history = await readJSONFile(path.join(this.metadataDir, 'generation-history.json'));
    return history.events.filter(event => event.assetId === assetId);
  }

  async getAssetValidationHistory(assetId) {
    const validation = await readJSONFile(path.join(this.metadataDir, 'validation-results.json'));
    return validation.events.filter(event => event.assetId === assetId);
  }

  async getAssetIterationHistory(assetId) {
    const iteration = await readJSONFile(path.join(this.metadataDir, 'iteration-history.json'));
    return iteration.events.filter(event => event.assetId === assetId);
  }

  async getAssetFeedback(assetId) {
    const feedback = await readJSONFile(path.join(this.metadataDir, 'user-feedback.json'));
    return feedback.events.filter(event => event.assetId === assetId);
  }

  async getAssetPerformanceMetrics(assetId) {
    const performance = await readJSONFile(path.join(this.metadataDir, 'performance-metrics.json'));
    return performance.events.filter(event => event.assetId === assetId);
  }

  getLastActivityTimestamp(events) {
    if (events.length === 0) return null;
    return events.reduce((latest, event) => 
      event.timestamp > latest ? event.timestamp : latest, 
      events[0].timestamp
    );
  }

  calculateQualityScore(validationEvents) {
    if (validationEvents.length === 0) return null;
    const scores = validationEvents.filter(e => e.score !== null).map(e => e.score);
    return scores.length > 0 ? scores.reduce((sum, score) => sum + score, 0) / scores.length : null;
  }

  calculateUserSatisfaction(feedbackEvents) {
    if (feedbackEvents.length === 0) return null;
    const ratings = feedbackEvents.filter(e => e.rating !== null).map(e => e.rating);
    return ratings.length > 0 ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length : null;
  }

  calculatePerformanceScore(performanceEvents) {
    if (performanceEvents.length === 0) return null;
    // Simple performance score based on duration (lower is better)
    const durations = performanceEvents.map(e => e.metrics.duration);
    const avgDuration = durations.reduce((sum, duration) => sum + duration, 0) / durations.length;
    // Convert to score (assuming 1000ms = 50 points, scale accordingly)
    return Math.max(0, 100 - (avgDuration / 10));
  }

  calculateTrends(events, timeField) {
    // Simple trend calculation - could be enhanced with more sophisticated analysis
    const timeGroups = {};
    events.forEach(event => {
      const date = new Date(event[timeField]).toISOString().split('T')[0];
      timeGroups[date] = (timeGroups[date] || 0) + 1;
    });
    return timeGroups;
  }

  calculateAverageValidationScore(validationEvents) {
    const scores = validationEvents.filter(e => e.score !== null).map(e => e.score);
    return scores.length > 0 ? scores.reduce((sum, score) => sum + score, 0) / scores.length : null;
  }

  calculateOverallUserSatisfaction(feedbackEvents) {
    const ratings = feedbackEvents.filter(e => e.rating !== null).map(e => e.rating);
    return ratings.length > 0 ? ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length : null;
  }

  getPerformanceOptimizations(performanceEvents) {
    const optimizations = [];
    performanceEvents.forEach(event => {
      optimizations.push(...event.optimizations);
    });
    return [...new Set(optimizations)]; // Remove duplicates
  }
}
