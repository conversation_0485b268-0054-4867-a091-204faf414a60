
# MCP Tool Architecture Overhaul Implementation Guide

**Document Version**: 1.0  
**Created**: 2025-06-15  
**Author**: <PERSON> 4 (Augment Agent)  
**Related Task**: WLR-017 - MCP Tool Architecture Overhaul  

## Executive Summary

This document provides a comprehensive implementation guide for consolidating Guidant's MCP tool architecture from 48 tools to 30-35 tools (25-30% reduction) while adding enhanced task management functionality. The approach is based on FastMCP best practices and current industry standards for MCP tool consolidation.

## Problem Statement

### Current State
- **48 MCP tools** across 10 categories
- **Tool proliferation risk**: Adding 5 new task management tools would push to 53 tools
- **Cognitive overload**: AI agents struggle with tool discovery and selection
- **Maintenance burden**: Each tool requires individual testing, documentation, and updates

### Target State
- **30-35 MCP tools** (25-30% reduction)
- **Enhanced functionality**: Better task management without tool proliferation
- **Improved discoverability**: Logical tool grouping and operation-based parameters
- **Maintainable architecture**: Consolidated error handling and testing

## Research Foundation

### FastMCP Best Practices (2025)
Based on comprehensive research of FastMCP documentation and current industry practices:

1. **Operation-Based Tool Design**: Single tools with operation parameters instead of multiple single-purpose tools
2. **Server Composition Patterns**: Mount specialized servers as modules using `mcp.mount()`
3. **Smart Tool Routing**: Context-aware tool routing with unified error handling
4. **Proxy Consolidation**: Use `FastMCP.as_proxy()` for aggregating multiple servers

### Industry Standards
- **Tool Registry Management**: Centralized discovery and routing
- **Hierarchical Organization**: Group related functionality into tool families
- **Backward Compatibility**: Maintain existing interfaces during transition

## MCP Protocol Foundation: JSON-RPC 2.0

### Core JSON Usage in MCP
MCP is built on **JSON-RPC 2.0**, meaning all communication uses JSON format. Understanding this is crucial for our tool consolidation strategy.

#### 1. Message Structure (JSON-RPC 2.0)
All MCP communication follows JSON-RPC 2.0 format:

```json
{
  "jsonrpc": "2.0",
  "id": "req-001",
  "method": "tools/call",
  "params": {
    "name": "guidant_manage_orchestration",
    "arguments": {
      "operation": "execute",
      "workflow_id": "123",
      "parameters": {"key": "value"}
    }
  }
}
```

#### 2. Tool Schema Definition (JSON Schema)
Tool parameters are validated using JSON Schema:

```json
{
  "name": "guidant_manage_orchestration",
  "description": "Unified workflow orchestration management",
  "inputSchema": {
    "type": "object",
    "properties": {
      "operation": {
        "type": "string",
        "enum": ["execute", "status", "cancel", "list", "create", "validate"],
        "description": "Operation to perform"
      },
      "workflow_id": {
        "type": "string",
        "description": "Workflow identifier (required for specific operations)"
      },
      "parameters": {
        "type": "object",
        "description": "Operation-specific parameters"
      }
    },
    "required": ["operation"],
    "additionalProperties": false
  }
}
```

#### 3. Response Format (JSON)
All tool responses must be valid JSON:

```json
{
  "jsonrpc": "2.0",
  "id": "req-001",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "Workflow executed successfully"
      }
    ],
    "metadata": {
      "operation": "execute",
      "workflow_id": "123",
      "executedAt": "2025-06-15T10:30:00Z"
    }
  }
}
```

### Impact on Tool Consolidation

#### 1. Parameter Mapping Strategy
Our smart tool router must handle JSON-to-JSON parameter transformation:

```javascript
// Deprecated tool mapping
const PARAMETER_MAPPINGS = {
  'guidant_execute_workflow': {
    newTool: 'guidant_manage_orchestration',
    transform: (oldParams) => ({
      operation: 'execute',
      workflow_id: oldParams.workflowId,
      parameters: oldParams.parameters || {}
    })
  },
  'guidant_get_workflow_status': {
    newTool: 'guidant_manage_orchestration',
    transform: (oldParams) => ({
      operation: 'status',
      workflow_id: oldParams.workflowId
    })
  }
};
```

#### 2. Schema Validation for Consolidated Tools
Each consolidated tool needs comprehensive JSON Schema validation:

```javascript
const CONSOLIDATED_TOOL_SCHEMAS = {
  'guidant_manage_orchestration': {
    type: 'object',
    properties: {
      operation: {
        type: 'string',
        enum: ['execute', 'status', 'cancel', 'list', 'create', 'validate']
      },
      workflow_id: { type: 'string' },
      template_id: { type: 'string' },
      parameters: { type: 'object' },
      filters: { type: 'object' }
    },
    required: ['operation'],
    additionalProperties: false
  }
};
```

#### 3. Standardized Response Format
All consolidated tools should return consistent JSON structure:

```javascript
// Standardized response format
const createToolResponse = (operation, data, metadata = {}) => ({
  success: true,
  operation,
  data,
  metadata: {
    executedAt: new Date().toISOString(),
    tool: 'guidant_manage_orchestration',
    ...metadata
  }
});
```

## Implementation Strategy

### Phase 1: Smart Tool Consolidation (Week 1)

#### 1.1 Tool Orchestration Consolidation
**Current**: 8 separate orchestration tools  
**Target**: 2-3 consolidated tools

```javascript
// Enhanced consolidated tool
guidant_manage_orchestration({
  operation: 'execute|status|cancel|list|create|validate',
  workflow_id?: string,
  template_id?: string,
  parameters?: object
})

guidant_orchestration_metrics({
  operation: 'performance|usage|health',
  timeframe?: string,
  filters?: object
})
```

#### 1.2 Tool Analytics Consolidation
**Current**: 9 separate analytics tools  
**Target**: 3-4 consolidated tools

```javascript
guidant_analyze_project({
  operation: 'metrics|usage|performance|export|category',
  analysis_type: string,
  parameters: object
})

guidant_analytics_query({
  operation: 'filter|aggregate|compare|trend',
  query: object,
  format?: 'json|csv|chart'
})
```

#### 1.3 Quality Validation Consolidation
**Current**: 4 separate validation tools  
**Target**: 2 consolidated tools

```javascript
guidant_validate_quality({
  operation: 'deliverable|history|statistics|configure',
  validation_type: string,
  parameters: object
})
```

### Phase 2: Task Management Integration (Week 2)

#### 2.1 Enhance Existing Tools (No New Tool Proliferation)

```javascript
// Enhanced guidant_get_current_task
guidant_get_current_task({
  include_dependencies?: boolean,
  include_recommendations?: boolean,
  include_subtasks?: boolean,
  format?: 'simple|rich|detailed'
})

// Enhanced guidant_report_progress
guidant_report_progress({
  task_operation?: 'complete|update|breakdown|prioritize',
  task_reference?: string,
  completion_notes?: string,
  next_steps?: string[]
})
```

#### 2.2 Add Single Task Intelligence Tool

```javascript
// New unified task intelligence tool (replaces 5 separate tools)
guidant_task_intelligence({
  operation: 'analyze_dependencies|recommend_next|breakdown_task|check_conflicts|prioritize_tasks',
  task_id?: string,
  context?: object,
  business_context?: object
})
```

### Phase 3: Smart Tool Router Implementation

#### 3.1 Router Architecture

```javascript
class SmartToolRouter {
  constructor() {
    this.toolMap = new Map();
    this.deprecatedTools = new Map();
    this.usageAnalytics = new Map();
  }

  registerTool(name, handler, operations = []) {
    this.toolMap.set(name, { 
      handler, 
      operations, 
      category: this.inferCategory(name),
      registeredAt: new Date()
    });
  }

  addDeprecatedAlias(oldName, newName, migrationGuide) {
    this.deprecatedTools.set(oldName, { 
      newName, 
      migrationGuide,
      deprecatedAt: new Date()
    });
  }

  async executeTool(name, params) {
    // Track usage analytics
    this.trackUsage(name, params);

    // Handle deprecated tools
    if (this.deprecatedTools.has(name)) {
      return this.handleDeprecatedTool(name, params);
    }

    // Execute consolidated tool
    const tool = this.toolMap.get(name);
    if (!tool) {
      throw new Error(`Tool '${name}' not found`);
    }

    return this.executeWithErrorHandling(tool, params);
  }

  private handleDeprecatedTool(oldName, params) {
    const { newName, migrationGuide } = this.deprecatedTools.get(oldName);

    console.warn(`⚠️  Tool '${oldName}' is deprecated. Use '${newName}'. ${migrationGuide}`);

    // Automatic JSON parameter mapping
    const mappedParams = this.mapParameters(oldName, newName, params);

    return this.executeTool(newName, mappedParams);
  }

  private mapParameters(oldName, newName, params) {
    const mapping = PARAMETER_MAPPINGS[oldName];
    if (!mapping || !mapping.transform) {
      return params; // No transformation needed
    }

    try {
      // Validate input JSON structure
      this.validateJsonStructure(params, oldName);

      // Transform parameters using mapping function
      const transformedParams = mapping.transform(params);

      // Validate output JSON structure
      this.validateJsonStructure(transformedParams, newName);

      return transformedParams;
    } catch (error) {
      throw new Error(`Parameter mapping failed for ${oldName} → ${newName}: ${error.message}`);
    }
  }

  private validateJsonStructure(params, toolName) {
    // Ensure parameters are valid JSON-serializable objects
    try {
      JSON.stringify(params);
    } catch (error) {
      throw new Error(`Invalid JSON structure for tool '${toolName}': ${error.message}`);
    }

    // Additional schema validation if available
    const schema = CONSOLIDATED_TOOL_SCHEMAS[toolName];
    if (schema) {
      this.validateAgainstSchema(params, schema, toolName);
    }
  }

  private validateAgainstSchema(params, schema, toolName) {
    // JSON Schema validation implementation
    // This would use a library like ajv for proper JSON Schema validation
    const requiredFields = schema.required || [];
    const missingFields = requiredFields.filter(field => !(field in params));

    if (missingFields.length > 0) {
      throw new Error(`Missing required fields for '${toolName}': ${missingFields.join(', ')}`);
    }
  }

  private async executeWithErrorHandling(tool, params) {
    try {
      const result = await tool.handler(params);
      return {
        success: true,
        data: result,
        tool: tool.name,
        executedAt: new Date()
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        tool: tool.name,
        executedAt: new Date()
      };
    }
  }
}
```

### Phase 4: Migration and Cleanup

#### 4.1 Backward Compatibility Strategy

```javascript
// Maintain existing tool interfaces during transition
const DEPRECATED_TOOL_MAPPINGS = {
  'guidant_execute_workflow': {
    newTool: 'guidant_manage_orchestration',
    parameterMapping: (params) => ({
      operation: 'execute',
      workflow_id: params.workflowId,
      parameters: params
    }),
    migrationGuide: 'Use guidant_manage_orchestration with operation="execute"'
  },
  
  'guidant_get_workflow_status': {
    newTool: 'guidant_manage_orchestration',
    parameterMapping: (params) => ({
      operation: 'status',
      workflow_id: params.workflowId
    }),
    migrationGuide: 'Use guidant_manage_orchestration with operation="status"'
  }
  // ... additional mappings
};
```

#### 4.2 Gradual Rollout Timeline

**Week 1-2: Implementation**
- Create consolidated tools alongside existing ones
- Implement smart routing logic
- Add deprecation warnings

**Week 3-4: Migration**
- Update internal tool calls to use new tools
- Provide migration documentation
- Monitor usage analytics

**Month 2-3: Cleanup**
- Remove deprecated tools after sufficient warning period
- Update all documentation and examples
- Finalize tool registry

## Technical Implementation Details

### Schema Design Principles for Extensibility

#### Core Design Philosophy
To properly implement WLR-017, our JSON schemas must be designed for **extensibility and composability**. This means any new tool can be easily added or integrated without requiring schema redesign.

#### 1. Modular Schema Architecture
Design schemas using composable building blocks:

```javascript
// Base operation schema (reusable)
const BaseOperationSchema = {
  type: 'object',
  properties: {
    operation: {
      type: 'string',
      description: 'Operation to perform'
    },
    parameters: {
      type: 'object',
      description: 'Operation-specific parameters',
      additionalProperties: true
    },
    metadata: {
      type: 'object',
      description: 'Optional metadata',
      additionalProperties: true
    }
  },
  required: ['operation'],
  additionalProperties: false
};

// Workflow-specific operations (extends base)
const WorkflowOperations = {
  enum: ['execute', 'status', 'cancel', 'list', 'create', 'validate', 'pause', 'resume'],
  description: 'Workflow management operations'
};

// Analytics-specific operations (extends base)
const AnalyticsOperations = {
  enum: ['metrics', 'usage', 'performance', 'export', 'category', 'trend', 'compare'],
  description: 'Analytics and reporting operations'
};
```

#### 2. Extensible Tool Categories
Design tool schemas that can easily accommodate new operations:

```javascript
// Schema factory for creating extensible tool schemas
const createToolSchema = (toolCategory, operations, additionalProperties = {}) => ({
  ...BaseOperationSchema,
  properties: {
    ...BaseOperationSchema.properties,
    operation: {
      ...BaseOperationSchema.properties.operation,
      enum: operations
    },
    ...additionalProperties
  }
});

// Easy to extend - just add new operations
const WorkflowSchema = createToolSchema('workflow', WorkflowOperations, {
  workflow_id: { type: 'string', description: 'Workflow identifier' },
  template_id: { type: 'string', description: 'Template identifier' }
});

// Adding new operations is simple
const ExtendedWorkflowOperations = [
  ...WorkflowOperations.enum,
  'schedule',    // New operation
  'duplicate',   // New operation
  'archive'      // New operation
];
```

#### 3. Future-Proof Parameter Handling
Design parameter schemas that can evolve:

```javascript
// Flexible parameter schema with operation-specific validation
const ParameterSchemas = {
  execute: {
    type: 'object',
    properties: {
      workflow_id: { type: 'string', required: true },
      input_data: { type: 'object' },
      options: { type: 'object' }
    }
  },

  // Easy to add new operation parameters
  schedule: {
    type: 'object',
    properties: {
      workflow_id: { type: 'string', required: true },
      schedule_time: { type: 'string', format: 'date-time' },
      recurrence: { type: 'object' }
    }
  }
};

// Dynamic parameter validation based on operation
const validateParameters = (operation, parameters) => {
  const schema = ParameterSchemas[operation];
  if (schema) {
    return validateAgainstSchema(parameters, schema);
  }
  return true; // Allow unknown operations (forward compatibility)
};
```

### JSON Schema Management

#### 1. Consolidated Tool Schemas
Each consolidated tool uses the extensible schema architecture:

```javascript
// mcp-server/src/schemas/consolidated-tool-schemas.js
export const CONSOLIDATED_TOOL_SCHEMAS = {
  'guidant_manage_orchestration': {
    type: 'object',
    properties: {
      operation: {
        type: 'string',
        enum: ['execute', 'status', 'cancel', 'list', 'create', 'validate'],
        description: 'Operation to perform on workflow'
      },
      workflow_id: {
        type: 'string',
        description: 'Workflow identifier (required for specific operations)'
      },
      template_id: {
        type: 'string',
        description: 'Template identifier for create operations'
      },
      parameters: {
        type: 'object',
        description: 'Operation-specific parameters'
      }
    },
    required: ['operation'],
    additionalProperties: false
  },

  'guidant_analyze_project': {
    type: 'object',
    properties: {
      operation: {
        type: 'string',
        enum: ['metrics', 'usage', 'performance', 'export', 'category'],
        description: 'Type of analysis to perform'
      },
      analysis_type: {
        type: 'string',
        description: 'Specific analysis subtype'
      },
      parameters: {
        type: 'object',
        description: 'Analysis parameters'
      },
      format: {
        type: 'string',
        enum: ['json', 'csv', 'chart'],
        default: 'json'
      }
    },
    required: ['operation', 'analysis_type'],
    additionalProperties: false
  }
};
```

#### 2. Parameter Mapping Definitions
JSON-to-JSON transformation rules for deprecated tools:

```javascript
// mcp-server/src/mappings/parameter-mappings.js
export const PARAMETER_MAPPINGS = {
  'guidant_execute_workflow': {
    newTool: 'guidant_manage_orchestration',
    transform: (params) => ({
      operation: 'execute',
      workflow_id: params.workflowId || params.workflow_id,
      parameters: params.parameters || {}
    }),
    migrationGuide: 'Use guidant_manage_orchestration with operation="execute"'
  },

  'guidant_get_workflow_status': {
    newTool: 'guidant_manage_orchestration',
    transform: (params) => ({
      operation: 'status',
      workflow_id: params.workflowId || params.workflow_id
    }),
    migrationGuide: 'Use guidant_manage_orchestration with operation="status"'
  },

  'guidant_get_project_metrics': {
    newTool: 'guidant_analyze_project',
    transform: (params) => ({
      operation: 'metrics',
      analysis_type: params.metricType || 'general',
      parameters: params.filters || {},
      format: params.format || 'json'
    }),
    migrationGuide: 'Use guidant_analyze_project with operation="metrics"'
  }
};
```

#### 4. Schema Evolution Strategy
Design for seamless tool addition and integration:

```javascript
// Schema registry for dynamic tool management
class SchemaRegistry {
  constructor() {
    this.toolSchemas = new Map();
    this.operationSchemas = new Map();
    this.parameterSchemas = new Map();
  }

  // Register a new tool category with extensible operations
  registerToolCategory(category, baseOperations, additionalProperties = {}) {
    const schema = createToolSchema(category, baseOperations, additionalProperties);
    this.toolSchemas.set(category, schema);

    // Register operation-specific parameter schemas
    baseOperations.forEach(operation => {
      const operationKey = `${category}_${operation}`;
      this.operationSchemas.set(operationKey, this.getDefaultOperationSchema());
    });
  }

  // Add new operations to existing tool category (no breaking changes)
  extendToolCategory(category, newOperations, newParameterSchemas = {}) {
    const existingSchema = this.toolSchemas.get(category);
    if (!existingSchema) {
      throw new Error(`Tool category '${category}' not found`);
    }

    // Extend operations enum
    const currentOperations = existingSchema.properties.operation.enum;
    const extendedOperations = [...currentOperations, ...newOperations];

    existingSchema.properties.operation.enum = extendedOperations;

    // Register parameter schemas for new operations
    newOperations.forEach(operation => {
      const operationKey = `${category}_${operation}`;
      const paramSchema = newParameterSchemas[operation] || this.getDefaultOperationSchema();
      this.operationSchemas.set(operationKey, paramSchema);
    });
  }

  // Add completely new tool (follows same pattern)
  addNewTool(toolName, operations, schema, parameterSchemas = {}) {
    this.toolSchemas.set(toolName, schema);

    operations.forEach(operation => {
      const operationKey = `${toolName}_${operation}`;
      const paramSchema = parameterSchemas[operation] || this.getDefaultOperationSchema();
      this.operationSchemas.set(operationKey, paramSchema);
    });
  }

  getDefaultOperationSchema() {
    return {
      type: 'object',
      additionalProperties: true // Allow any parameters for forward compatibility
    };
  }

  // Validate tool call against registered schemas
  validateToolCall(toolName, operation, parameters) {
    const toolSchema = this.toolSchemas.get(toolName);
    const operationKey = `${toolName}_${operation}`;
    const paramSchema = this.operationSchemas.get(operationKey);

    // Validate tool structure
    if (toolSchema) {
      this.validateAgainstSchema({ operation, parameters }, toolSchema, toolName);
    }

    // Validate operation-specific parameters
    if (paramSchema) {
      this.validateAgainstSchema(parameters, paramSchema, operationKey);
    }

    return true;
  }
}

// Usage example - easy tool addition
const registry = new SchemaRegistry();

// Register initial tool categories
registry.registerToolCategory('workflow', ['execute', 'status', 'cancel'], {
  workflow_id: { type: 'string' }
});

registry.registerToolCategory('analytics', ['metrics', 'usage', 'performance'], {
  analysis_type: { type: 'string' }
});

// Later: Add new operations without breaking existing tools
registry.extendToolCategory('workflow', ['schedule', 'duplicate'], {
  schedule: {
    type: 'object',
    properties: {
      schedule_time: { type: 'string', format: 'date-time' }
    }
  }
});

// Later: Add completely new tool category
registry.addNewTool('task_intelligence', ['analyze_dependencies', 'recommend_next'], {
  type: 'object',
  properties: {
    operation: { type: 'string', enum: ['analyze_dependencies', 'recommend_next'] },
    task_id: { type: 'string' },
    parameters: { type: 'object' }
  }
});
```

#### 5. Benefits of Extensible Schema Design

**Easy Tool Addition:**
- New tools follow established patterns
- No need to redesign existing schemas
- Automatic validation for new operations

**Forward Compatibility:**
- Unknown operations don't break existing tools
- Parameter schemas can evolve independently
- Graceful handling of new features

**Maintenance Efficiency:**
- Single source of truth for schema patterns
- Consistent validation across all tools
- Automated schema generation for new tools

### File Structure Changes

```
mcp-server/src/tools/
├── core/
│   ├── workflow-control.js (enhanced)
│   └── smart-tool-router.js (new)
├── consolidated/
│   ├── orchestration-tools.js (new)
│   ├── analytics-tools.js (new)
│   ├── quality-tools.js (new)
│   └── task-intelligence.js (new)
├── schemas/
│   └── consolidated-tool-schemas.js (new)
├── mappings/
│   └── parameter-mappings.js (new)
├── deprecated/
│   └── legacy-tool-mappings.js (new)
└── index.js (updated registration)
```

### Tool Registration Updates

```javascript
// mcp-server/src/tools/index.js
import { SmartToolRouter } from './core/smart-tool-router.js';
import { registerOrchestrationTools } from './consolidated/orchestration-tools.js';
import { registerAnalyticsTools } from './consolidated/analytics-tools.js';
import { registerTaskIntelligence } from './consolidated/task-intelligence.js';
import { DEPRECATED_TOOL_MAPPINGS } from './deprecated/legacy-tool-mappings.js';

const router = new SmartToolRouter();

// Register consolidated tools
registerOrchestrationTools(router);
registerAnalyticsTools(router);
registerTaskIntelligence(router);

// Register deprecated tool mappings
Object.entries(DEPRECATED_TOOL_MAPPINGS).forEach(([oldName, mapping]) => {
  router.addDeprecatedAlias(oldName, mapping.newTool, mapping.migrationGuide);
});

export { router as toolRouter };
```

## Success Metrics

### Quantitative Targets
- **Tool Count**: 48 → 30-35 tools (25-30% reduction)
- **Tool Discovery Time**: <5 seconds for AI agents
- **Tool Usage Distribution**: No single category >20% of total tools
- **Backward Compatibility**: 100% of existing workflows continue working

### Qualitative Improvements
- **Improved AI Agent Performance**: Better tool selection accuracy
- **Reduced Cognitive Load**: Clearer tool organization and documentation
- **Enhanced Maintainability**: Consolidated error handling and testing
- **Better User Experience**: More intuitive tool interfaces

## Risk Mitigation

### Potential Risks
1. **Breaking Changes**: Existing workflows might break during transition
2. **Performance Impact**: Tool routing might introduce latency
3. **Complexity**: Consolidated tools might become too complex

### Mitigation Strategies
1. **Comprehensive Testing**: Full regression testing before deployment
2. **Gradual Migration**: Phased rollout with rollback capability
3. **Performance Monitoring**: Track tool execution times and optimize
4. **Clear Documentation**: Detailed migration guides and examples

## Testing Strategy

### Unit Testing
- Test each consolidated tool with all operation variants
- Test deprecated tool mapping and parameter conversion
- Test error handling and edge cases

### Integration Testing
- Test tool router with real MCP client interactions
- Test backward compatibility with existing workflows
- Test performance under load

### User Acceptance Testing
- Validate AI agent tool selection accuracy
- Confirm user workflow continuity
- Gather feedback on tool discoverability

## Documentation Updates Required

1. **Tool Reference Documentation**: Update all tool descriptions and examples
2. **Migration Guide**: Step-by-step guide for updating existing integrations
3. **Architecture Documentation**: Document new tool organization and patterns
4. **API Documentation**: Update MCP tool schemas and parameter definitions

## Conclusion

This comprehensive overhaul will transform Guidant's MCP tool architecture from a proliferating collection of single-purpose tools into a well-organized, maintainable system of intelligent, operation-based tools. The approach balances functionality enhancement with complexity reduction, ensuring sustainable growth while improving user experience.

The implementation follows industry best practices and leverages FastMCP's built-in consolidation features, providing a solid foundation for future development and scaling.
