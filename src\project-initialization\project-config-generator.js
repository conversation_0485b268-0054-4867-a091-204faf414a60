/**
 * Project Configuration Generator
 * 
 * Generates project configuration based on user responses from the
 * interactive onboarding process.
 */

/**
 * Generate project configuration based on collected information
 * 
 * @param {Object} options - Configuration options
 * @param {string} options.projectName - Project name
 * @param {string} options.projectDescription - Project description
 * @param {string} options.projectType - Project type (general, e-commerce, saas, community)
 * @param {Object} options.classification - Project classification results
 * @param {Object} options.answers - User answers from onboarding
 * @returns {Object} Project configuration
 */
export async function generateProjectConfiguration({
  projectName,
  projectDescription,
  projectType,
  classification,
  answers
}) {
  // Base configuration
  const config = {
    name: projectName,
    description: projectDescription,
    version: '0.1.0',
    created: new Date().toISOString(),
    lastModified: new Date().toISOString(),
    projectType,
    classification: classification || {},
    techStack: [],
    features: [],
    nextSteps: []
  };
  
  // Process answers based on project type
  switch (projectType) {
    case 'e-commerce':
      return processECommerceConfig(config, answers);
    case 'saas':
      return processSaasConfig(config, answers);
    case 'community':
      return processCommunityConfig(config, answers);
    case 'general':
    default:
      return processGeneralConfig(config, answers);
  }
}

/**
 * Process general project configuration
 * 
 * @param {Object} config - Base configuration
 * @param {Object} answers - User answers
 * @returns {Object} Enhanced configuration
 */
function processGeneralConfig(config, answers) {
  // Extract tech stack preferences
  if (answers.tech_stack?.value) {
    config.techStack = Array.isArray(answers.tech_stack.value) 
      ? answers.tech_stack.value 
      : [answers.tech_stack.value];
  }
  
  // Extract features
  if (answers.features?.value) {
    config.features = Array.isArray(answers.features.value)
      ? answers.features.value
      : [answers.features.value];
  }
  
  // Set project goals
  if (answers.project_goals?.value) {
    config.goals = answers.project_goals.value;
  }
  
  // Set target audience
  if (answers.target_audience?.value) {
    config.targetAudience = answers.target_audience.value;
  }
  
  // Set timeline approach
  if (answers.timeline_approach?.value) {
    config.timelineApproach = answers.timeline_approach.value;
  }
  
  // Generate next steps
  config.nextSteps = [
    'Set up development environment',
    'Create project structure',
    'Implement core features',
    'Test functionality',
    'Deploy initial version'
  ];
  
  return config;
}

/**
 * Process e-commerce project configuration
 * 
 * @param {Object} config - Base configuration
 * @param {Object} answers - User answers
 * @returns {Object} Enhanced configuration
 */
function processECommerceConfig(config, answers) {
  // E-commerce specific configuration
  config.ecommerce = {
    productTypes: answers.product_types?.value || '',
    targetAudience: answers.target_audience?.value || '',
    paymentGateways: answers.payment_gateways?.value || [],
    shippingRegions: answers.shipping_regions?.value || 'local',
    productCount: answers.product_count?.value || 'small',
    businessModel: answers.business_model?.value || 'b2c'
  };
  
  // Extract features
  if (answers.feature_priorities?.value) {
    config.features = Array.isArray(answers.feature_priorities.value)
      ? answers.feature_priorities.value
      : [answers.feature_priorities.value];
  }
  
  // Set competitor inspiration
  if (answers.competitor_urls?.value) {
    config.competitorUrls = answers.competitor_urls.value;
  }
  
  // Generate next steps
  config.nextSteps = [
    'Set up product catalog structure',
    'Configure payment gateways',
    'Implement shopping cart functionality',
    'Set up user accounts and checkout process',
    'Configure shipping options'
  ];
  
  return config;
}

/**
 * Process SaaS project configuration
 * 
 * @param {Object} config - Base configuration
 * @param {Object} answers - User answers
 * @returns {Object} Enhanced configuration
 */
function processSaasConfig(config, answers) {
  // SaaS specific configuration
  config.saas = {
    problemSolved: answers.problem_solved?.value || '',
    targetAudience: answers.target_audience?.value || '',
    subscriptionModel: answers.subscription_model?.value || 'subscription',
    pricingTiers: answers.pricing_tiers?.value || 'basic_premium',
    userRoles: answers.user_roles?.value || [],
    dataSensitivity: answers.data_sensitivity?.value || 'business',
    scalingNeeds: answers.scaling_needs?.value || 'startup'
  };
  
  // Extract features
  if (answers.core_features?.value) {
    config.features = Array.isArray(answers.core_features.value)
      ? answers.core_features.value
      : [answers.core_features.value];
  }
  
  // Set competitor inspiration
  if (answers.competitor_urls?.value) {
    config.competitorUrls = answers.competitor_urls.value;
  }
  
  // Generate next steps
  config.nextSteps = [
    'Set up user authentication and authorization',
    'Implement subscription management',
    'Create core application features',
    'Develop dashboard and reporting',
    'Set up monitoring and analytics'
  ];
  
  return config;
}

/**
 * Process community platform configuration
 * 
 * @param {Object} config - Base configuration
 * @param {Object} answers - User answers
 * @returns {Object} Enhanced configuration
 */
function processCommunityConfig(config, answers) {
  // Community platform specific configuration
  config.community = {
    purpose: answers.community_purpose?.value || '',
    targetAudience: answers.target_audience?.value || '',
    communitySize: answers.community_size?.value || 'small',
    contentTypes: answers.content_types?.value || [],
    profileFeatures: answers.user_profile_features?.value || [],
    communityStructure: answers.community_structure?.value || 'forums',
    moderationApproach: answers.moderation_approach?.value || 'community',
    monetization: answers.monetization?.value || [],
    privacyLevel: answers.privacy_level?.value || 'public'
  };
  
  // Set competitor inspiration
  if (answers.competitor_urls?.value) {
    config.competitorUrls = answers.competitor_urls.value;
  }
  
  // Generate next steps
  config.nextSteps = [
    'Set up user authentication and profiles',
    'Implement content creation and sharing',
    'Create community structure and navigation',
    'Set up moderation tools',
    'Configure privacy and permission settings'
  ];
  
  return config;
} 