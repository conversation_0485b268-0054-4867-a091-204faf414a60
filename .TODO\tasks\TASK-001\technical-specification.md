# TASK-001 Technical Specification: MCP-Native Architecture

## Architecture Overview

### MCP Primitive-Based Design

```
┌─────────────────────────────────────────────────────────────┐
│                    MCP Client (Host)                        │
├─────────────────────────────────────────────────────────────┤
│                  JSON-RPC 2.0 Transport                    │
├─────────────────────────────────────────────────────────────┤
│                   Guidant MCP Server                       │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │  Resources  │    Tools    │   Prompts   │  Sampling   │  │
│  │ (App-Ctrl)  │ (Model-Ctrl)│ (User-Ctrl) │ (Serv-Ctrl) │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│              Primitive Router & Orchestrator               │
├─────────────────────────────────────────────────────────────┤
│                  Existing Infrastructure                   │
│         (Tool Orchestrator, Workflow DSL, etc.)           │
└─────────────────────────────────────────────────────────────┘
```

## File Structure Implementation

### New Directory Organization

```
mcp-server/src/
├── primitives/
│   ├── resources/
│   │   ├── index.js                    # Resource registry
│   │   ├── project-resources.js        # Project state, tasks, phases
│   │   ├── deliverable-resources.js    # Deliverable analysis, insights
│   │   ├── analytics-resources.js      # Metrics, performance data
│   │   └── workflow-resources.js       # Workflow definitions, executions
│   ├── tools/
│   │   ├── index.js                    # Tool registry with MCP annotations
│   │   ├── workflow-executor.js        # Consolidated workflow operations
│   │   ├── project-manager.js          # Project lifecycle management
│   │   ├── analytics-engine.js         # Analysis and insights
│   │   └── context-manager.js          # Session and context management
│   ├── prompts/
│   │   ├── index.js                    # Prompt registry
│   │   ├── analysis-prompts.js         # Analysis templates
│   │   ├── onboarding-prompts.js       # Onboarding workflows
│   │   ├── decision-prompts.js         # Decision frameworks
│   │   └── quality-prompts.js          # Quality assessment guides
│   └── sampling/
│       ├── index.js                    # Sampling capabilities
│       ├── intelligent-decisions.js    # Adaptive decision making
│       ├── content-generation.js       # Contextual content creation
│       └── predictive-analysis.js      # Predictive insights
├── orchestration/
│   ├── primitive-router.js             # MCP-aware routing logic
│   ├── mcp-compliance.js               # Standards validation
│   ├── backward-compatibility.js       # Legacy tool mapping
│   └── tool-orchestrator.js            # Enhanced existing orchestrator
├── schemas/
│   ├── resource-schemas.js             # Resource URI and content schemas
│   ├── tool-schemas.js                 # Tool parameter and annotation schemas
│   ├── prompt-schemas.js               # Prompt argument schemas
│   └── sampling-schemas.js             # Sampling request schemas
├── utils/
│   ├── uri-template-parser.js          # RFC 6570 URI template handling
│   ├── mcp-message-handler.js          # JSON-RPC 2.0 message processing
│   └── annotation-processor.js         # MCP annotation handling
└── index.js                            # Main MCP server registration
```

## Resource Implementation Specification

### URI Template Design (RFC 6570)

```javascript
// Resource Templates Registry
const RESOURCE_TEMPLATES = {
  // Project Resources
  project_state: {
    uriTemplate: "guidant://project/{projectId}/state",
    name: "Project State",
    description: "Current project state and configuration",
    mimeType: "application/json"
  },
  
  project_phase: {
    uriTemplate: "guidant://project/{projectId}/phase/{phase}",
    name: "Project Phase Data",
    description: "Phase-specific information and deliverables",
    mimeType: "application/json"
  },
  
  current_task: {
    uriTemplate: "guidant://project/{projectId}/current-task",
    name: "Current Task",
    description: "Active task information and context",
    mimeType: "application/json"
  },
  
  // Analytics Resources
  project_insights: {
    uriTemplate: "guidant://analytics/{projectId}/insights/{timeframe?}",
    name: "Project Insights",
    description: "Analytics insights for specified timeframe",
    mimeType: "application/json"
  },
  
  workflow_metrics: {
    uriTemplate: "guidant://workflow/{workflowId}/metrics/{metric?}",
    name: "Workflow Metrics",
    description: "Workflow performance and execution metrics",
    mimeType: "application/json"
  },
  
  // Deliverable Resources
  deliverable_analysis: {
    uriTemplate: "guidant://deliverable/{deliverableId}/analysis",
    name: "Deliverable Analysis",
    description: "Comprehensive deliverable analysis and quality assessment",
    mimeType: "application/json"
  }
};
```

### Resource Handler Implementation

```javascript
// project-resources.js
export class ProjectResourceHandler {
  async handleResourceRead(uri, params) {
    const { projectId, phase } = params;
    
    switch (uri.path) {
      case 'state':
        return await this.getProjectState(projectId);
      case 'phase':
        return await this.getPhaseData(projectId, phase);
      case 'current-task':
        return await this.getCurrentTask(projectId);
      default:
        throw new Error(`Resource not found: ${uri.path}`);
    }
  }
  
  async getProjectState(projectId) {
    // Implementation using existing infrastructure
    const state = await existingProjectManager.getState(projectId);
    return {
      uri: `guidant://project/${projectId}/state`,
      mimeType: "application/json",
      text: JSON.stringify(state, null, 2)
    };
  }
}
```

## Tool Consolidation Specification

### Consolidated Tool Definitions

```javascript
// workflow-executor.js
export const WORKFLOW_EXECUTOR_TOOL = {
  name: "guidant_execute_workflow",
  description: "Execute workflow operations with intelligent routing and context awareness",
  inputSchema: {
    type: "object",
    properties: {
      operation: {
        type: "string",
        enum: ["research", "onboarding", "development", "analysis", "optimization"],
        description: "Primary workflow operation type"
      },
      workflow_type: {
        type: "string", 
        enum: ["market", "competitive", "technical", "user_feedback", "quality"],
        description: "Specific workflow variant or specialization"
      },
      context: {
        type: "object",
        description: "Workflow execution context and parameters"
      },
      parameters: {
        type: "object",
        description: "Operation-specific parameters"
      },
      workflow_template: {
        type: "string",
        description: "Optional predefined workflow template ID"
      }
    },
    required: ["operation"]
  },
  annotations: {
    title: "Workflow Executor",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: true,
    openWorldHint: true
  }
};

// Implementation with smart routing
export class WorkflowExecutor {
  async execute({ operation, workflow_type, context, parameters, workflow_template }) {
    // Route to appropriate handler based on operation
    const handler = this.getOperationHandler(operation, workflow_type);
    
    // Execute with existing orchestration infrastructure
    const result = await this.orchestrator.executeWorkflow({
      handler,
      context,
      parameters,
      template: workflow_template
    });
    
    return this.formatMCPResponse(result);
  }
  
  getOperationHandler(operation, type) {
    // Smart routing logic using existing tool mappings
    const routingKey = `${operation}_${type || 'default'}`;
    return this.operationHandlers.get(routingKey) || this.defaultHandler;
  }
}
```

### Tool Annotation Processing

```javascript
// annotation-processor.js
export class MCPAnnotationProcessor {
  static processToolAnnotations(tool) {
    const annotations = tool.annotations || {};
    
    return {
      // MCP standard annotations
      title: annotations.title || tool.name,
      readOnlyHint: annotations.readOnlyHint || false,
      destructiveHint: annotations.destructiveHint !== false, // Default true
      idempotentHint: annotations.idempotentHint || false,
      openWorldHint: annotations.openWorldHint !== false, // Default true
      
      // Custom Guidant annotations
      category: annotations.category,
      priority: annotations.priority || 'normal',
      cacheable: annotations.cacheable || false,
      requiresAuth: annotations.requiresAuth || false
    };
  }
  
  static validateAnnotations(annotations) {
    // Validate against MCP specification
    const validKeys = ['title', 'readOnlyHint', 'destructiveHint', 'idempotentHint', 'openWorldHint'];
    const invalidKeys = Object.keys(annotations).filter(key => !validKeys.includes(key));
    
    if (invalidKeys.length > 0) {
      console.warn(`Invalid MCP annotations: ${invalidKeys.join(', ')}`);
    }
    
    return annotations;
  }
}
```

## Prompt Implementation Specification

### Prompt Template Definitions

```javascript
// analysis-prompts.js
export const ANALYSIS_PROMPTS = {
  "analyze-project-phase": {
    name: "analyze-project-phase",
    description: "Analyze project phase progress and provide actionable recommendations",
    arguments: [
      {
        name: "phase",
        description: "Project phase to analyze (requirements, design, implementation, testing, deployment)",
        required: true
      },
      {
        name: "focus_area", 
        description: "Specific area to focus analysis on (progress, quality, risks, timeline)",
        required: false
      },
      {
        name: "deliverables",
        description: "Specific deliverables to include in analysis",
        required: false
      },
      {
        name: "timeframe",
        description: "Analysis timeframe (last_week, last_month, project_start)",
        required: false
      }
    ]
  }
};

// Prompt handler implementation
export class AnalysisPromptHandler {
  async getPrompt(name, arguments) {
    if (name === "analyze-project-phase") {
      return this.generatePhaseAnalysisPrompt(arguments);
    }
    throw new Error(`Prompt not found: ${name}`);
  }
  
  async generatePhaseAnalysisPrompt({ phase, focus_area, deliverables, timeframe }) {
    // Get project data from resources
    const projectData = await this.resourceManager.getResource(
      `guidant://project/${this.currentProjectId}/phase/${phase}`
    );
    
    // Generate contextual prompt
    return {
      description: `Analyze ${phase} phase progress and provide recommendations`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: this.buildAnalysisPrompt(phase, focus_area, projectData)
          }
        },
        {
          role: "user",
          content: {
            type: "resource",
            resource: {
              uri: `guidant://project/${this.currentProjectId}/phase/${phase}`,
              text: JSON.stringify(projectData, null, 2),
              mimeType: "application/json"
            }
          }
        }
      ]
    };
  }
}
```

## Sampling Implementation Specification

### Intelligent Decision Making

```javascript
// intelligent-decisions.js
export class IntelligentDecisionSampler {
  async requestDecision(context, options) {
    const samplingRequest = {
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: this.buildDecisionPrompt(context, options)
          }
        }
      ],
      modelPreferences: {
        hints: [{ name: "claude-3" }],
        intelligencePriority: 0.8,
        costPriority: 0.3,
        speedPriority: 0.5
      },
      systemPrompt: "You are an intelligent project management assistant. Analyze the context and provide a reasoned decision with clear rationale.",
      includeContext: "thisServer",
      temperature: 0.3,
      maxTokens: 500
    };
    
    return await this.mcpClient.sampling.createMessage(samplingRequest);
  }
  
  buildDecisionPrompt(context, options) {
    return `
Based on the current project context, I need to make a decision about: ${context.decision_type}

Current situation:
${JSON.stringify(context.current_state, null, 2)}

Available options:
${options.map((opt, i) => `${i + 1}. ${opt.name}: ${opt.description}`).join('\n')}

Please analyze the situation and recommend the best option with clear reasoning.
    `.trim();
  }
}
```

## Backward Compatibility Implementation

### Legacy Tool Mapping

```javascript
// backward-compatibility.js
export class BackwardCompatibilityLayer {
  constructor(primitiveRouter) {
    this.router = primitiveRouter;
    this.legacyMappings = this.buildLegacyMappings();
  }
  
  buildLegacyMappings() {
    return {
      // Resource mappings
      'guidant_get_project_state': {
        type: 'resource',
        uri: 'guidant://project/{projectId}/state'
      },
      'guidant_get_current_task': {
        type: 'resource', 
        uri: 'guidant://project/{projectId}/current-task'
      },
      
      // Tool mappings
      'guidant_research_market': {
        type: 'tool',
        name: 'guidant_execute_workflow',
        parameters: { operation: 'research', workflow_type: 'market' }
      },
      'guidant_init_project': {
        type: 'tool',
        name: 'guidant_manage_project',
        parameters: { operation: 'init' }
      }
    };
  }
  
  async handleLegacyCall(toolName, parameters) {
    const mapping = this.legacyMappings[toolName];
    if (!mapping) {
      throw new Error(`Legacy tool not found: ${toolName}`);
    }
    
    switch (mapping.type) {
      case 'resource':
        return await this.router.handleResourceRequest(mapping.uri, parameters);
      case 'tool':
        return await this.router.handleToolCall(mapping.name, {
          ...mapping.parameters,
          ...parameters
        });
      case 'prompt':
        return await this.router.handlePromptRequest(mapping.name, parameters);
      default:
        throw new Error(`Unknown mapping type: ${mapping.type}`);
    }
  }
}
```

## Performance & Monitoring

### Metrics Collection

```javascript
// Performance monitoring for MCP primitives
export class MCPPerformanceMonitor {
  trackPrimitiveUsage(primitive, operation, duration, success) {
    this.metrics.record({
      primitive_type: primitive, // 'resource', 'tool', 'prompt', 'sampling'
      operation,
      duration_ms: duration,
      success,
      timestamp: Date.now()
    });
  }
  
  generatePerformanceReport() {
    return {
      primitive_performance: this.getPerformanceByPrimitive(),
      consolidation_impact: this.getConsolidationMetrics(),
      mcp_compliance: this.getComplianceMetrics()
    };
  }
}
```

## Testing Strategy

### MCP Compliance Testing

```javascript
// Test MCP primitive compliance
describe('MCP Primitive Compliance', () => {
  test('Resource URI templates follow RFC 6570', () => {
    // Validate URI template syntax
  });
  
  test('Tool annotations match MCP specification', () => {
    // Validate annotation structure and types
  });
  
  test('Prompt arguments follow MCP schema', () => {
    // Validate prompt argument definitions
  });
  
  test('Sampling requests use correct format', () => {
    // Validate sampling message structure
  });
});
```

## Migration Timeline

### Phase Implementation Schedule

1. **Week 1**: Resource layer implementation
2. **Week 2**: Tool consolidation (2 tools)
3. **Week 3**: Tool consolidation (2 tools) + Prompts
4. **Week 4**: Sampling + Integration testing

### Success Criteria

- [ ] All 48 tools mapped to 15 MCP primitives
- [ ] 100% backward compatibility maintained
- [ ] Performance within 5% of current system
- [ ] Full MCP specification compliance
- [ ] Comprehensive test coverage (>90%)
