/**
 * Default Configuration for Guidant
 *
 * This file contains the static, default configuration for the application.
 * It follows the TaskMaster pattern of separating default data from the
 * configuration loading logic. This object is the lowest priority in the
 * configuration hierarchy.
 */

export const DEFAULT_CONFIG = {
  providers: {
    'vertex-ai': {
        name: 'Google Vertex AI',
        apiKeyEnv: 'GOOGLE_APPLICATION_CREDENTIALS',
        projectIdEnv: 'GOOGLE_CLOUD_PROJECT',
        location: 'us-central1', // Default location
        description: 'Direct access to Gemini models via Vertex AI',
        // These settings are specific to our successful tests
        safetySettings: [
            { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_NONE' },
            { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_NONE' },
            { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_NONE' },
            { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_NONE' }
        ],
        thinkingConfig: {
            thinkingBudget: -1 // Unlimited thinking
        }
    },
    openrouter: {
      name: 'OpenRouter',
      baseUrl: 'https://openrouter.ai/api/v1',
      apiKeyEnv: 'OPENROUTER_API_KEY',
      description: 'Multi-model API gateway with competitive pricing'
    },
    perplexity: {
      name: 'Perplexity',
      baseUrl: 'https://api.perplexity.ai',
      apiKeyEnv: 'PERPLEXITY_API_KEY',
      description: 'Research-focused models with web search capabilities'
    },
    anthropic: {
        name: 'Anthropic',
        baseUrl: 'https://api.anthropic.com',
        apiKeyEnv: 'ANTHROPIC_API_KEY',
        description: 'Direct access to Claude models'
    },
    mistral: {
        name: 'Mistral AI',
        baseUrl: 'https://api.mistral.ai',
        apiKeyEnv: 'MISTRAL_API_KEY',
        description: 'Direct access to Mistral models'
    }
    // Add other providers here as needed
  },
  models: {
    // Default parameters for all roles unless overridden
    default: {
        temperature: 0.7,
        maxTokens: 200000,
    },
    // Role-specific configurations
    main: {
      provider: 'vertex-ai',
      modelId: 'gemini-2.5-flash-preview-05-20',
      supportsThinking: true,
      description: 'Primary model for general, fast AI tasks.'
    },
    analysis: {
      provider: 'vertex-ai',
      modelId: 'gemini-2.5-pro-preview-06-05',
      supportsThinking: false,
      temperature: 0.2,
      maxTokens: 1000000,
      description: 'High-context model for code analysis and complex reasoning.'
    },
    generation: {
      provider: 'vertex-ai',
      modelId: 'gemini-2.5-flash-preview-05-20',
      supportsThinking: true,
      temperature: 0.8,
      maxTokens: 1000000,
      description: 'Model for creative content and code generation.'
    },
    research: {
      provider: 'perplexity',
      modelId: 'sonar-pro',
      temperature: 0.1,
      maxTokens: 8000,
      description: 'Model for research and web-search-augmented tasks.'
    },
    fallback: {
      provider: 'vertex-ai',
      modelId: 'gemini-2.5-flash-preview-05-20',
      supportsThinking: true,
      maxTokens: 1000000,
      description: 'Fallback model for when primary models fail.'
    }
  },
  research: {
    providers: {
      tavily: {
        apiKey: "${TAVILY_API_KEY}",
        baseUrl: "https://api.tavily.com",
        rateLimit: {
          requestsPerMinute: 100,
          burstLimit: 10,
          retryAfter: 60,
          exponentialBackoff: true,
          maxRetries: 3
        },
        defaultOptions: {
          searchDepth: "basic",
          maxResults: 10,
          includeImages: false,
          includeAnswer: true,
          includeRawContent: false
        },
        ranking: {
          relevanceThreshold: 0.7,
          maxAge: 30,
          domainWeights: {
            "statista.com": 0.25,
            "mckinsey.com": 0.25,
            "deloitte.com": 0.2,
            "pwc.com": 0.2,
            "bcg.com": 0.2,
            "bain.com": 0.2,
            "gartner.com": 0.25,
            "forrester.com": 0.2,
            "reuters.com": 0.2,
            "bloomberg.com": 0.2,
            "wsj.com": 0.18,
            "ft.com": 0.18,
            "github.com": 0.15,
            "stackoverflow.com": 0.18
          }
        }
      },
      context7: {
        baseUrl: "https://mcp.context7.com",
        fallbackUrls: ["https://backup.context7.com", "https://api.context7.io"],
        rateLimit: {
          requestsPerMinute: 60,
          burstLimit: 15,
          retryAfter: 60,
          exponentialBackoff: true,
          maxRetries: 3
        },
        defaultTokens: 10000,
        minTokens: 1000,
        maxTokens: 50000,
        preferredLanguages: [
          "javascript", "typescript", "python"
        ],
        filtering: {
          relevanceThreshold: 0.6,
          codeSnippetWeight: 0.3,
          officialDocWeight: 0.4,
          preferredSources: [
            "docs.microsoft.com", "developer.mozilla.org", "docs.python.org", "docs.oracle.com", "golang.org", "rust-lang.org",
            "reactjs.org", "vuejs.org", "angular.io", "nodejs.org", "spring.io", "kubernetes.io", "docker.com",
            "docs.aws.amazon.com", "cloud.google.com", "pytorch.org", "tensorflow.org"
          ]
        }
      },
      firecrawl: {
        apiKey: "${FIRECRAWL_API_KEY}",
        baseUrl: "https://api.firecrawl.dev",
        fallbackUrls: ["https://backup.firecrawl.dev", "https://api-eu.firecrawl.dev"],
        rateLimit: {
          requestsPerMinute: 100,
          concurrentBrowsers: 5,
          burstLimit: 20,
          retryAfter: 60,
          exponentialBackoff: true,
          maxRetries: 3
        },
        defaultOptions: {
          formats: ["markdown", "html", "structured"],
          onlyMainContent: true,
          timeout: 30000,
          waitFor: 2000,
          removeBase64Images: true
        },
        processing: {
          maxContentLength: 100000,
          preferredFormats: ["markdown", "structured", "html"],
          contentFilters: {
            minWordCount: 50,
            maxWordCount: 50000,
            removeBoilerplate: true,
            removeDuplicates: true,
            qualityThreshold: 0.6
          }
        }
      }
    },
    orchestration: {
      cacheEnabled: true,
      cacheTTL: 3600,
      maxConcurrentRequests: 5,
      timeoutMs: 30000,
      synthesisModel: "research",
      intelligentRouting: {
        enabled: true,
        loadBalancing: true,
        failoverEnabled: true,
        contextualRouting: {
          market_research: ["tavily"],
          competitive_analysis: ["tavily", "firecrawl"],
          technical_documentation: ["context7"],
          library_research: ["context7"],
          web_content_extraction: ["firecrawl"],
          news_monitoring: ["tavily"],
          fact_verification: ["tavily"],
          api_documentation: ["context7"],
          website_analysis: ["firecrawl"],
          general_search: ["tavily"]
        }
      },
      qualityControl: {
        enabled: true,
        minimumSources: 2,
        crossValidation: true,
        confidenceThreshold: 0.7,
        factChecking: true,
        sourceCredibility: true
      },
      performance: {
        adaptiveTimeout: true,
        circuitBreaker: {
          enabled: true,
          failureThreshold: 5,
          recoveryTimeout: 60000
        },
        resourceManagement: {
          memoryLimit: "2GB",
          cpuLimit: "80%",
          diskCacheLimit: "1GB"
        }
      },
      analytics: {
        enabled: true,
        trackUsage: true,
        trackPerformance: true,
        trackQuality: true,
        reportingInterval: 3600,
        retentionPeriod: 2592000
      },
      security: {
        apiKeyRotation: true,
        encryptCache: true,
        sanitizeInputs: true,
        validateSources: true,
        contentFiltering: true
      }
    }
  },
  global: {
    logLevel: "info",
    debug: false,
    projectName: "Guidant Project",
    defaultRole: "main",
    retryAttempts: 3,
    timeout: 60000
  }
};