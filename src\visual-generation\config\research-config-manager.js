/**
 * Research Configuration Manager
 * Manages dynamic configuration for research domains and libraries
 */

import fs from 'fs/promises';
import path from 'path';

/**
 * Research Configuration Manager
 * Allows users to customize research domains and libraries dynamically
 */
export class ResearchConfigManager {
  constructor() {
    this.configPath = path.join(process.cwd(), 'src/visual-generation/config/research-domains.json');
    this.userConfigPath = path.join(process.cwd(), '.guidant/research-config.json');
    this.cache = new Map();
  }

  /**
   * Load configuration with user overrides
   */
  async loadConfig() {
    try {
      // Load base configuration
      const baseConfig = await this.loadBaseConfig();
      
      // Load user overrides if they exist
      const userConfig = await this.loadUserConfig();
      
      // Merge configurations
      const mergedConfig = this.mergeConfigurations(baseConfig, userConfig);
      
      return mergedConfig;
    } catch (error) {
      console.warn('Failed to load research configuration:', error.message);
      return this.getFallbackConfig();
    }
  }

  /**
   * Load base configuration from file
   */
  async loadBaseConfig() {
    const configData = await fs.readFile(this.configPath, 'utf8');
    return JSON.parse(configData);
  }

  /**
   * Load user configuration overrides
   */
  async loadUserConfig() {
    try {
      const userConfigData = await fs.readFile(this.userConfigPath, 'utf8');
      return JSON.parse(userConfigData);
    } catch (error) {
      // User config doesn't exist or is invalid - that's okay
      return {};
    }
  }

  /**
   * Merge base and user configurations
   */
  mergeConfigurations(baseConfig, userConfig) {
    const merged = JSON.parse(JSON.stringify(baseConfig)); // Deep clone

    // Merge domains
    if (userConfig.domains) {
      for (const [type, domains] of Object.entries(userConfig.domains)) {
        if (userConfig.domains[type]?.mode === 'replace') {
          // Replace entire domain list
          merged.domains[type] = domains.list || [];
        } else {
          // Add to existing domains (default behavior)
          if (merged.domains[type]) {
            merged.domains[type] = [...new Set([...merged.domains[type], ...(domains.add || [])])];
            
            // Remove domains if specified
            if (domains.remove) {
              merged.domains[type] = merged.domains[type].filter(d => !domains.remove.includes(d));
            }
          } else {
            merged.domains[type] = domains.add || [];
          }
        }
      }
    }

    // Merge libraries
    if (userConfig.libraries) {
      for (const [framework, libraries] of Object.entries(userConfig.libraries)) {
        if (userConfig.libraries[framework]?.mode === 'replace') {
          // Replace entire library list
          merged.libraries[framework] = libraries.list || [];
        } else {
          // Add to existing libraries (default behavior)
          if (merged.libraries[framework]) {
            merged.libraries[framework] = [...new Set([...merged.libraries[framework], ...(libraries.add || [])])];
            
            // Remove libraries if specified
            if (libraries.remove) {
              merged.libraries[framework] = merged.libraries[framework].filter(l => !libraries.remove.includes(l));
            }
          } else {
            merged.libraries[framework] = libraries.add || [];
          }
        }
      }
    }

    // Merge other settings
    if (userConfig.research_priorities) {
      merged.research_priorities = { ...merged.research_priorities, ...userConfig.research_priorities };
    }

    if (userConfig.cache_settings) {
      merged.cache_settings = { ...merged.cache_settings, ...userConfig.cache_settings };
    }

    return merged;
  }

  /**
   * Save user configuration
   */
  async saveUserConfig(userConfig) {
    try {
      // Ensure directory exists
      const dir = path.dirname(this.userConfigPath);
      await fs.mkdir(dir, { recursive: true });
      
      // Save configuration
      await fs.writeFile(this.userConfigPath, JSON.stringify(userConfig, null, 2));
      
      // Clear cache
      this.cache.clear();
      
      return true;
    } catch (error) {
      console.error('Failed to save user configuration:', error.message);
      return false;
    }
  }

  /**
   * Add domains to a specific type
   */
  async addDomains(type, domains) {
    const userConfig = await this.loadUserConfig();
    
    if (!userConfig.domains) {
      userConfig.domains = {};
    }
    
    if (!userConfig.domains[type]) {
      userConfig.domains[type] = { add: [] };
    }
    
    if (!userConfig.domains[type].add) {
      userConfig.domains[type].add = [];
    }
    
    userConfig.domains[type].add = [...new Set([...userConfig.domains[type].add, ...domains])];
    
    return await this.saveUserConfig(userConfig);
  }

  /**
   * Remove domains from a specific type
   */
  async removeDomains(type, domains) {
    const userConfig = await this.loadUserConfig();
    
    if (!userConfig.domains) {
      userConfig.domains = {};
    }
    
    if (!userConfig.domains[type]) {
      userConfig.domains[type] = { remove: [] };
    }
    
    if (!userConfig.domains[type].remove) {
      userConfig.domains[type].remove = [];
    }
    
    userConfig.domains[type].remove = [...new Set([...userConfig.domains[type].remove, ...domains])];
    
    return await this.saveUserConfig(userConfig);
  }

  /**
   * Add libraries to a specific framework
   */
  async addLibraries(framework, libraries) {
    const userConfig = await this.loadUserConfig();
    
    if (!userConfig.libraries) {
      userConfig.libraries = {};
    }
    
    if (!userConfig.libraries[framework]) {
      userConfig.libraries[framework] = { add: [] };
    }
    
    if (!userConfig.libraries[framework].add) {
      userConfig.libraries[framework].add = [];
    }
    
    userConfig.libraries[framework].add = [...new Set([...userConfig.libraries[framework].add, ...libraries])];
    
    return await this.saveUserConfig(userConfig);
  }

  /**
   * Remove libraries from a specific framework
   */
  async removeLibraries(framework, libraries) {
    const userConfig = await this.loadUserConfig();
    
    if (!userConfig.libraries) {
      userConfig.libraries = {};
    }
    
    if (!userConfig.libraries[framework]) {
      userConfig.libraries[framework] = { remove: [] };
    }
    
    if (!userConfig.libraries[framework].remove) {
      userConfig.libraries[framework].remove = [];
    }
    
    userConfig.libraries[framework].remove = [...new Set([...userConfig.libraries[framework].remove, ...libraries])];
    
    return await this.saveUserConfig(userConfig);
  }

  /**
   * Get current effective configuration
   */
  async getCurrentConfig() {
    const cacheKey = 'current_config';
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    const config = await this.loadConfig();
    this.cache.set(cacheKey, config);
    
    return config;
  }

  /**
   * Reset to default configuration
   */
  async resetToDefaults() {
    try {
      // Remove user config file
      await fs.unlink(this.userConfigPath);
      
      // Clear cache
      this.cache.clear();
      
      return true;
    } catch (error) {
      // File might not exist - that's okay
      return true;
    }
  }

  /**
   * Get fallback configuration
   */
  getFallbackConfig() {
    return {
      domains: {
        ui_practices: ['material.io', 'ui.shadcn.com', 'chakra-ui.com'],
        accessibility: ['w3.org', 'webaim.org', 'a11y.org'],
        design_system: ['designsystems.com', 'material.io'],
        component_libraries: ['storybook.js.org', 'github.com']
      },
      libraries: {
        react: ['react', 'mui', 'chakra-ui', 'shadcn/ui'],
        vue: ['vue', 'vuetify', 'quasar'],
        angular: ['angular', 'angular-material'],
        svelte: ['svelte', 'svelteui']
      },
      research_priorities: {
        ui_practices: { weight: 0.3, max_results: 5 },
        accessibility: { weight: 0.4, max_results: 3 },
        design_system: { weight: 0.2, max_results: 4 }
      }
    };
  }

  /**
   * Validate configuration
   */
  validateConfig(config) {
    const errors = [];
    
    // Validate domains
    if (config.domains) {
      for (const [type, domains] of Object.entries(config.domains)) {
        if (!Array.isArray(domains)) {
          errors.push(`domains.${type} must be an array`);
        }
      }
    }
    
    // Validate libraries
    if (config.libraries) {
      for (const [framework, libraries] of Object.entries(config.libraries)) {
        if (!Array.isArray(libraries)) {
          errors.push(`libraries.${framework} must be an array`);
        }
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// Export singleton instance
export const researchConfigManager = new ResearchConfigManager();
