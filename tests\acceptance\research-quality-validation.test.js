/**
 * Research Quality Validation Tests
 * 
 * User acceptance tests for research quality, business decision accuracy,
 * and end-user experience validation.
 */

import { describe, it, expect, beforeEach, afterEach, mock, spyOn } from 'bun:test';
import { performResearchOperation } from '../../src/ai-integration/ai-services-unified.js';
import { ResearchInformedChoicePresenter } from '../../src/business-decisions/research-informed-choice-presenter.js';
import { ResearchSynthesisEngine } from '../../src/research-synthesis/research-synthesis-engine.js';

describe('Research Quality Validation', () => {
  let mockHighQualityData;
  let mockLowQualityData;

  beforeEach(() => {
    // High-quality research data mock
    mockHighQualityData = {
      results: [
        {
          title: 'Comprehensive Market Analysis: Restaurant Discovery Apps 2024',
          content: 'According to recent industry reports, the restaurant discovery market has grown by 23% year-over-year, driven by increased mobile adoption and changing consumer dining habits. Key trends include AI-powered recommendations, integration with delivery services, and social dining features.',
          url: 'https://authoritative-research.com/restaurant-market-2024',
          score: 0.95,
          metadata: {
            publishDate: '2024-01-15',
            authorCredibility: 'high',
            sourceType: 'industry_report'
          }
        },
        {
          title: 'Technical Architecture Best Practices for Food Apps',
          content: 'Modern restaurant discovery applications benefit from microservices architecture, real-time data synchronization, and robust caching strategies. Performance benchmarks show that apps with sub-2-second load times achieve 40% higher user retention.',
          url: 'https://tech-insights.com/food-app-architecture',
          score: 0.88,
          metadata: {
            publishDate: '2024-02-01',
            authorCredibility: 'high',
            sourceType: 'technical_analysis'
          }
        }
      ]
    };

    // Low-quality research data mock
    mockLowQualityData = {
      results: [
        {
          title: 'Random Blog Post About Food',
          content: 'I think restaurants are cool and apps are useful.',
          url: 'https://random-blog.com/food-thoughts',
          score: 0.2,
          metadata: {
            publishDate: '2020-01-01',
            authorCredibility: 'unknown',
            sourceType: 'blog'
          }
        }
      ]
    };
  });

  afterEach(() => {
    mock.restore();
  });

  describe('Research Content Quality', () => {
    it('should identify high-quality research sources', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockHighQualityData)
      });

      const mockPerformAI = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performAIOperation')
        .mockResolvedValue({
          content: JSON.stringify({
            summary: 'High-quality research indicates strong market growth and technical best practices for restaurant discovery applications.',
            recommendations: [
              'Implement microservices architecture for scalability',
              'Focus on sub-2-second load times for better retention',
              'Integrate AI-powered recommendation systems'
            ],
            confidence: 0.92,
            sources: ['https://authoritative-research.com/restaurant-market-2024', 'https://tech-insights.com/food-app-architecture']
          })
        });

      const result = await performResearchOperation({
        type: 'market_research',
        query: 'restaurant discovery app market analysis',
        providers: ['tavily'],
        maxResults: 5
      });

      // Quality indicators
      expect(result.synthesis.confidence).toBeGreaterThan(0.8);
      expect(result.synthesis.recommendations.length).toBeGreaterThan(2);
      expect(result.synthesis.summary.length).toBeGreaterThan(100);
      expect(result.synthesis.sources.length).toBeGreaterThan(1);

      // Content quality checks
      expect(result.synthesis.summary).toMatch(/market|growth|technical|best practices/i);
      expect(result.synthesis.recommendations[0]).toMatch(/architecture|scalability|performance/i);

      mockFetch.mockRestore();
      mockPerformAI.mockRestore();
    });

    it('should filter out low-quality research sources', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockLowQualityData)
      });

      const mockPerformAI = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performAIOperation')
        .mockResolvedValue({
          content: JSON.stringify({
            summary: 'Limited research available with low confidence.',
            recommendations: [],
            confidence: 0.3,
            sources: []
          })
        });

      const result = await performResearchOperation({
        type: 'market_research',
        query: 'restaurant discovery app analysis',
        providers: ['tavily'],
        maxResults: 5
      });

      // Should indicate low quality
      expect(result.synthesis.confidence).toBeLessThan(0.5);
      expect(result.synthesis.recommendations.length).toBeLessThan(2);
      expect(result.synthesis.summary).toMatch(/limited|low confidence/i);

      mockFetch.mockRestore();
      mockPerformAI.mockRestore();
    });

    it('should provide source credibility assessment', async () => {
      const synthesisEngine = new ResearchSynthesisEngine();
      
      const researchData = [
        {
          provider: 'tavily',
          results: mockHighQualityData.results,
          metadata: { provider: 'tavily', timestamp: new Date().toISOString() }
        }
      ];

      const synthesis = await synthesisEngine.synthesizeResearchWithContext(researchData, {
        includeSourceAnalysis: true,
        qualityThreshold: 0.7
      });

      expect(synthesis.sourceAnalysis).toBeDefined();
      expect(synthesis.sourceAnalysis.highQualitySources).toBeGreaterThan(0);
      expect(synthesis.sourceAnalysis.averageCredibility).toBeGreaterThan(0.7);
      expect(synthesis.sourceAnalysis.sourceTypes).toContain('industry_report');
    });
  });

  describe('Business Decision Quality', () => {
    it('should provide actionable business recommendations', async () => {
      const mockResearchResult = {
        synthesis: {
          summary: 'Market research shows strong demand for AI-powered restaurant discovery with focus on mobile experience and real-time data.',
          recommendations: [
            {
              title: 'Implement AI Recommendations',
              description: 'Use machine learning for personalized restaurant suggestions',
              businessImpact: { revenue: 'Increase user engagement by 35%' },
              implementation: { effort: 'medium', timeline: '3-4 months' },
              confidence: 0.9
            },
            {
              title: 'Mobile-First Design',
              description: 'Prioritize mobile user experience and performance',
              businessImpact: { revenue: 'Improve conversion rates by 25%' },
              implementation: { effort: 'low', timeline: '1-2 months' },
              confidence: 0.85
            }
          ],
          sources: ['https://market-research.com/ai-restaurants']
        },
        metadata: { confidenceLevel: 0.88 }
      };

      const mockPerformResearch = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performResearchOperation')
        .mockResolvedValue(mockResearchResult);

      const presenter = new ResearchInformedChoicePresenter();
      
      const presentation = await presenter.getResearchInformedDecisionOptions(
        'framework_selection',
        {
          domain: 'restaurant_discovery',
          businessModel: 'marketplace',
          expertise: { category: 'intermediate' },
          budget: { category: 'medium' }
        },
        { enableResearch: true }
      );

      // Business decision quality checks
      expect(presentation.recommendations).toBeDefined();
      expect(presentation.recommendations.primary).toBeDefined();
      expect(presentation.recommendations.businessJustification).toBeDefined();
      expect(presentation.recommendations.businessJustification.length).toBeGreaterThan(50);

      // Should have clear business impact
      expect(presentation.recommendations.businessJustification).toMatch(/revenue|cost|efficiency|growth/i);

      mockPerformResearch.mockRestore();
    });

    it('should adapt recommendations to user expertise level', async () => {
      const mockResearchResult = {
        synthesis: {
          summary: 'Technical analysis of framework options',
          recommendations: [],
          sources: []
        },
        metadata: { confidenceLevel: 0.8 }
      };

      const mockPerformResearch = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performResearchOperation')
        .mockResolvedValue(mockResearchResult);

      const presenter = new ResearchInformedChoicePresenter();

      // Test novice presentation
      const novicePresentation = await presenter.getResearchInformedDecisionOptions(
        'database_selection',
        { expertise: { category: 'novice' } },
        { enableResearch: true }
      );

      // Test advanced presentation
      const advancedPresentation = await presenter.getResearchInformedDecisionOptions(
        'database_selection',
        { expertise: { category: 'advanced' } },
        { enableResearch: true }
      );

      // Novice should have simplified language
      const noviceOption = novicePresentation.options[0];
      expect(noviceOption.description).not.toMatch(/API|microservices|scalability/);
      expect(noviceOption.focusAreas).toContain('ease_of_use');

      // Advanced should have technical details
      const advancedOption = advancedPresentation.options[0];
      expect(advancedOption.focusAreas).toContain('technical_details');

      mockPerformResearch.mockRestore();
    });

    it('should provide confidence indicators for decisions', async () => {
      const mockResearchResult = {
        synthesis: {
          summary: 'Research-backed analysis',
          recommendations: [
            { title: 'React', confidence: 0.9, businessImpact: { revenue: 'High' } },
            { title: 'Vue', confidence: 0.7, businessImpact: { revenue: 'Medium' } }
          ],
          sources: ['https://framework-comparison.com']
        },
        metadata: { confidenceLevel: 0.85 }
      };

      const mockPerformResearch = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performResearchOperation')
        .mockResolvedValue(mockResearchResult);

      const presenter = new ResearchInformedChoicePresenter();
      
      const presentation = await presenter.getResearchInformedDecisionOptions(
        'framework_selection',
        { expertise: { category: 'intermediate' } },
        { enableResearch: true }
      );

      // Should have confidence indicators
      expect(presentation.researchBacking.confidence).toBe(0.85);
      
      const reactOption = presentation.options.find(opt => opt.title === 'React');
      if (reactOption) {
        expect(reactOption.confidence).toBeGreaterThan(0.8);
        expect(reactOption.confidenceIndicator).toContain('Research confidence');
      }

      mockPerformResearch.mockRestore();
    });
  });

  describe('User Experience Quality', () => {
    it('should provide clear and understandable explanations', async () => {
      const mockResearchResult = {
        synthesis: {
          summary: 'Research shows React is widely adopted with strong community support and excellent performance characteristics.',
          recommendations: [],
          sources: []
        },
        metadata: { confidenceLevel: 0.8 }
      };

      const mockPerformResearch = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performResearchOperation')
        .mockResolvedValue(mockResearchResult);

      const presenter = new ResearchInformedChoicePresenter();
      
      const presentation = await presenter.getResearchInformedDecisionOptions(
        'framework_selection',
        { expertise: { category: 'novice' } },
        { enableResearch: true }
      );

      // Check explanation quality
      expect(presentation.researchSummary).toBeDefined();
      expect(presentation.researchSummary.length).toBeGreaterThan(50);
      expect(presentation.researchSummary).not.toMatch(/API|REST|GraphQL|microservices/); // No technical jargon for novice

      // Should have quick summary for decision making
      expect(presentation.quickSummary).toBeDefined();
      expect(presentation.quickSummary.recommendation).toBeDefined();
      expect(presentation.quickSummary.reasoning).toBeDefined();

      mockPerformResearch.mockRestore();
    });

    it('should provide actionable next steps', async () => {
      const mockResearchResult = {
        synthesis: {
          summary: 'Framework analysis complete',
          recommendations: [
            {
              title: 'React',
              implementation: {
                nextSteps: ['Set up development environment', 'Create project structure', 'Install dependencies'],
                timeline: '1-2 weeks',
                effort: 'medium'
              }
            }
          ],
          sources: []
        },
        metadata: { confidenceLevel: 0.8 }
      };

      const mockPerformResearch = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performResearchOperation')
        .mockResolvedValue(mockResearchResult);

      const presenter = new ResearchInformedChoicePresenter();
      
      const presentation = await presenter.getResearchInformedDecisionOptions(
        'framework_selection',
        { expertise: { category: 'intermediate' } },
        { enableResearch: true }
      );

      // Should provide clear next steps
      expect(presentation.recommendations).toBeDefined();
      expect(presentation.recommendations.primary).toBeDefined();
      
      if (presentation.recommendations.primary.implementation) {
        expect(presentation.recommendations.primary.implementation.nextSteps).toBeDefined();
        expect(presentation.recommendations.primary.implementation.timeline).toBeDefined();
      }

      mockPerformResearch.mockRestore();
    });

    it('should handle edge cases gracefully', async () => {
      // Test with no research results
      const mockPerformResearch = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performResearchOperation')
        .mockRejectedValue(new Error('Research service unavailable'));

      const presenter = new ResearchInformedChoicePresenter();
      
      const presentation = await presenter.getResearchInformedDecisionOptions(
        'framework_selection',
        { expertise: { category: 'intermediate' } },
        { enableResearch: true }
      );

      // Should still provide a presentation, even without research
      expect(presentation.title).toBeDefined();
      expect(presentation.options.length).toBeGreaterThan(0);
      expect(presentation.researchBacking).toBeNull();

      mockPerformResearch.mockRestore();
    });
  });

  describe('Research Accuracy Validation', () => {
    it('should validate research against known facts', async () => {
      const knownFacts = {
        'React': {
          maintainer: 'Facebook/Meta',
          type: 'JavaScript library',
          primaryUse: 'user interfaces'
        },
        'Vue': {
          maintainer: 'Evan You',
          type: 'JavaScript framework',
          primaryUse: 'web applications'
        }
      };

      const mockResearchData = {
        results: [
          {
            title: 'React Overview',
            content: 'React is a JavaScript library for building user interfaces, maintained by Facebook.',
            score: 0.9
          },
          {
            title: 'Vue.js Guide',
            content: 'Vue.js is a progressive JavaScript framework created by Evan You for building web applications.',
            score: 0.85
          }
        ]
      };

      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockResearchData)
      });

      const mockPerformAI = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performAIOperation')
        .mockResolvedValue({
          content: JSON.stringify({
            summary: 'React is a JavaScript library maintained by Facebook for building user interfaces. Vue.js is a progressive framework created by Evan You.',
            recommendations: [],
            confidence: 0.9,
            sources: []
          })
        });

      const result = await performResearchOperation({
        type: 'technical_research',
        query: 'React vs Vue comparison',
        providers: ['tavily']
      });

      // Validate against known facts
      const summary = result.synthesis.summary.toLowerCase();
      expect(summary).toContain('react');
      expect(summary).toContain('javascript');
      expect(summary).toContain('facebook');
      expect(summary).toContain('vue');
      expect(summary).toContain('evan you');

      mockFetch.mockRestore();
      mockPerformAI.mockRestore();
    });

    it('should identify and flag potentially outdated information', async () => {
      const outdatedData = {
        results: [
          {
            title: 'Old Framework Comparison',
            content: 'Angular 1.x is the most popular framework in 2015.',
            score: 0.6,
            metadata: {
              publishDate: '2015-01-01',
              lastUpdated: '2015-06-01'
            }
          }
        ]
      };

      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(outdatedData)
      });

      const mockPerformAI = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performAIOperation')
        .mockResolvedValue({
          content: JSON.stringify({
            summary: 'Limited current information available. Some sources may be outdated.',
            recommendations: [],
            confidence: 0.4,
            sources: [],
            warnings: ['Some information may be outdated (2015)']
          })
        });

      const result = await performResearchOperation({
        type: 'technical_research',
        query: 'current framework trends',
        providers: ['tavily']
      });

      // Should flag low confidence due to outdated information
      expect(result.synthesis.confidence).toBeLessThan(0.6);
      expect(result.synthesis.summary).toMatch(/limited|outdated/i);

      mockFetch.mockRestore();
      mockPerformAI.mockRestore();
    });
  });
});
