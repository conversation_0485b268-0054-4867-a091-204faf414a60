{"current": "requirements", "phases": {"concept": {"status": "optional", "required": false, "reason": "Not required for feature projects"}, "requirements": {"status": "active", "startedAt": "2025-06-18T16:17:22.318Z", "required": true}, "design": {"status": "pending", "required": true}, "architecture": {"status": "optional", "required": false, "reason": "Not required for feature projects"}, "implementation": {"status": "pending", "required": true}, "deployment": {"status": "pending", "required": true}}}