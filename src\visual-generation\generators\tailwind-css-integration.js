/**
 * Tailwind CSS Integration
 * Implements Tailwind CSS integration for rapid prototyping with design system tokens,
 * responsive utilities, and component styling based on generated design system specifications
 */

import { VisualIntelligenceCache } from '../research/visual-intelligence-cache.js';

/**
 * Tailwind CSS Integration Engine
 * Provides Tailwind utility classes and design system integration
 */
export class TailwindCSSIntegration {
  constructor(config = {}) {
    this.config = {
      enableDesignTokens: config.enableDesignTokens !== false,
      enableResponsiveUtilities: config.enableResponsiveUtilities !== false,
      enableCustomComponents: config.enableCustomComponents !== false,
      enableDarkMode: config.enableDarkMode || false,
      enableCaching: config.enableCaching !== false,
      tailwindVersion: config.tailwindVersion || '3.4.0',
      cdnMode: config.cdnMode !== false, // Use CDN by default for prototyping
      customTheme: config.customTheme || {},
      ...config
    };

    // Initialize visual intelligence cache if enabled
    if (this.config.enableCaching) {
      this.visualCache = new VisualIntelligenceCache({
        maxCacheSize: config.maxCacheSize || 50,
        enableQualityScoring: true,
        enablePerformanceMonitoring: true
      });
    }

    this.integrationStats = {
      totalGenerations: 0,
      cacheHits: 0,
      averageGenerationTime: 0,
      designTokensUsed: 0
    };

    // Initialize Tailwind utility mappings
    this.utilityMappings = this.initializeUtilityMappings();
    this.designTokens = this.initializeDesignTokens();
    this.componentClasses = this.initializeComponentClasses();
  }

  /**
   * Generate Tailwind CSS classes for component specifications
   */
  async generateTailwindClasses(componentSpecs, designSystem = {}, options = {}) {
    const startTime = Date.now();
    this.integrationStats.totalGenerations++;

    try {
      // Generate cache key
      const cacheKey = this.generateCacheKey(componentSpecs, designSystem, options);

      // Try cache first if enabled
      if (this.config.enableCaching && this.visualCache) {
        const cached = await this.visualCache.get(cacheKey, options);
        if (cached) {
          this.integrationStats.cacheHits++;
          return cached;
        }
      }

      // Generate Tailwind classes for each component
      const tailwindClasses = {};
      
      for (const spec of componentSpecs) {
        tailwindClasses[spec.id] = await this.generateComponentTailwindClasses(spec, designSystem, options);
      }

      // Generate global utility classes
      const globalClasses = this.generateGlobalUtilityClasses(designSystem, options);

      // Generate responsive classes
      const responsiveClasses = this.config.enableResponsiveUtilities ? 
        this.generateResponsiveClasses(componentSpecs, options) : {};

      // Generate design token classes
      const designTokenClasses = this.config.enableDesignTokens ? 
        this.generateDesignTokenClasses(designSystem) : {};

      const result = {
        componentClasses: tailwindClasses,
        globalClasses,
        responsiveClasses,
        designTokenClasses,
        tailwindConfig: this.generateTailwindConfig(designSystem, options),
        cdnLink: this.generateTailwindCDNLink(),
        customCSS: this.generateCustomTailwindCSS(designSystem, options),
        metadata: {
          generatedAt: new Date().toISOString(),
          tailwindVersion: this.config.tailwindVersion,
          componentsCount: componentSpecs.length,
          designTokensUsed: this.integrationStats.designTokensUsed
        }
      };

      // Update performance stats
      const generationTime = Date.now() - startTime;
      this.integrationStats.averageGenerationTime = 
        (this.integrationStats.averageGenerationTime * (this.integrationStats.totalGenerations - 1) + generationTime) / 
        this.integrationStats.totalGenerations;

      // Cache the result
      if (this.config.enableCaching && this.visualCache) {
        await this.visualCache.set(cacheKey, result, {
          requestType: 'tailwind_generation',
          provider: 'tailwind_css_integration',
          timestamp: Date.now()
        });
      }

      return result;

    } catch (error) {
      console.error('Tailwind CSS generation failed:', error);
      throw new Error(`Tailwind CSS generation failed: ${error.message}`);
    }
  }

  /**
   * Generate Tailwind classes for individual component
   */
  async generateComponentTailwindClasses(spec, designSystem, options) {
    const baseClasses = this.componentClasses[spec.type] || this.componentClasses.default;
    const customClasses = this.applyDesignSystemToClasses(baseClasses, designSystem);
    const responsiveClasses = this.applyResponsiveClasses(customClasses, spec, options);
    const accessibilityClasses = this.applyAccessibilityClasses(responsiveClasses, spec);

    return {
      base: baseClasses,
      custom: customClasses,
      responsive: responsiveClasses,
      accessibility: accessibilityClasses,
      final: this.mergeTailwindClasses([baseClasses, customClasses, responsiveClasses, accessibilityClasses])
    };
  }

  /**
   * Generate global utility classes
   */
  generateGlobalUtilityClasses(designSystem, options) {
    return {
      container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
      section: 'py-8 sm:py-12 lg:py-16',
      card: 'bg-white rounded-lg shadow-md border border-gray-200',
      button: {
        primary: 'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200',
        secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-md transition-colors duration-200',
        outline: 'border border-blue-600 text-blue-600 hover:bg-blue-50 font-medium py-2 px-4 rounded-md transition-colors duration-200'
      },
      form: {
        input: 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
        label: 'block text-sm font-medium text-gray-700 mb-1',
        error: 'text-red-600 text-sm mt-1'
      },
      typography: {
        h1: 'text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900',
        h2: 'text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900',
        h3: 'text-xl sm:text-2xl lg:text-3xl font-semibold text-gray-900',
        body: 'text-base text-gray-600 leading-relaxed',
        small: 'text-sm text-gray-500'
      }
    };
  }

  /**
   * Generate responsive classes
   */
  generateResponsiveClasses(componentSpecs, options) {
    return {
      breakpoints: {
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1536px'
      },
      grid: {
        mobile: 'grid-cols-1',
        tablet: 'sm:grid-cols-2 md:grid-cols-3',
        desktop: 'lg:grid-cols-4 xl:grid-cols-6'
      },
      spacing: {
        mobile: 'p-4 space-y-4',
        tablet: 'sm:p-6 sm:space-y-6',
        desktop: 'lg:p-8 lg:space-y-8'
      },
      typography: {
        mobile: 'text-sm',
        tablet: 'sm:text-base',
        desktop: 'lg:text-lg'
      }
    };
  }

  /**
   * Generate design token classes
   */
  generateDesignTokenClasses(designSystem) {
    if (!designSystem || !designSystem.colorPalette) {
      return this.getDefaultDesignTokens();
    }

    this.integrationStats.designTokensUsed++;

    const tokens = {
      colors: this.mapColorsToTailwind(designSystem.colorPalette),
      spacing: this.mapSpacingToTailwind(designSystem.spacing),
      typography: this.mapTypographyToTailwind(designSystem.typography),
      shadows: this.mapShadowsToTailwind(designSystem.shadows),
      borderRadius: this.mapBorderRadiusToTailwind(designSystem.borderRadius)
    };

    return tokens;
  }

  /**
   * Map design system colors to Tailwind classes
   */
  mapColorsToTailwind(colorPalette) {
    const colorMap = {};
    
    if (colorPalette.primary) {
      colorMap.primary = {
        50: this.lightenColor(colorPalette.primary[0], 0.9),
        100: this.lightenColor(colorPalette.primary[0], 0.8),
        200: this.lightenColor(colorPalette.primary[0], 0.6),
        300: this.lightenColor(colorPalette.primary[0], 0.4),
        400: this.lightenColor(colorPalette.primary[0], 0.2),
        500: colorPalette.primary[0],
        600: colorPalette.primary[1] || this.darkenColor(colorPalette.primary[0], 0.1),
        700: colorPalette.primary[2] || this.darkenColor(colorPalette.primary[0], 0.2),
        800: this.darkenColor(colorPalette.primary[0], 0.3),
        900: this.darkenColor(colorPalette.primary[0], 0.4)
      };
    }

    if (colorPalette.secondary) {
      colorMap.secondary = {
        500: colorPalette.secondary[0],
        600: colorPalette.secondary[1] || this.darkenColor(colorPalette.secondary[0], 0.1),
        700: colorPalette.secondary[2] || this.darkenColor(colorPalette.secondary[0], 0.2)
      };
    }

    return colorMap;
  }

  /**
   * Generate Tailwind config object
   */
  generateTailwindConfig(designSystem, options) {
    const config = {
      content: ['./src/**/*.{html,js}'],
      theme: {
        extend: {}
      },
      plugins: []
    };

    // Add custom colors from design system
    if (designSystem?.colorPalette) {
      config.theme.extend.colors = this.mapColorsToTailwind(designSystem.colorPalette);
    }

    // Add custom spacing
    if (designSystem?.spacing) {
      config.theme.extend.spacing = this.mapSpacingToTailwind(designSystem.spacing);
    }

    // Add custom typography
    if (designSystem?.typography) {
      config.theme.extend.fontFamily = this.mapTypographyToTailwind(designSystem.typography);
    }

    // Add dark mode support if enabled
    if (this.config.enableDarkMode) {
      config.darkMode = 'class';
    }

    // Merge with custom theme
    if (this.config.customTheme) {
      config.theme.extend = { ...config.theme.extend, ...this.config.customTheme };
    }

    return config;
  }

  /**
   * Generate Tailwind CDN link
   */
  generateTailwindCDNLink() {
    if (!this.config.cdnMode) {
      return null;
    }

    return `<script src="https://cdn.tailwindcss.com"></script>`;
  }

  /**
   * Generate custom Tailwind CSS
   */
  generateCustomTailwindCSS(designSystem, options) {
    let customCSS = '';

    // Add custom component styles
    if (this.config.enableCustomComponents) {
      customCSS += this.generateCustomComponentStyles(designSystem);
    }

    // Add design system specific styles
    if (designSystem) {
      customCSS += this.generateDesignSystemStyles(designSystem);
    }

    // Add accessibility enhancements
    customCSS += this.generateAccessibilityStyles();

    return customCSS;
  }

  /**
   * Initialize utility mappings for components
   */
  initializeUtilityMappings() {
    return {
      spacing: {
        xs: 'p-1',
        sm: 'p-2',
        md: 'p-4',
        lg: 'p-6',
        xl: 'p-8'
      },
      colors: {
        primary: 'text-blue-600',
        secondary: 'text-gray-600',
        success: 'text-green-600',
        warning: 'text-yellow-600',
        error: 'text-red-600'
      },
      backgrounds: {
        primary: 'bg-blue-600',
        secondary: 'bg-gray-100',
        success: 'bg-green-600',
        warning: 'bg-yellow-600',
        error: 'bg-red-600'
      },
      borders: {
        none: 'border-0',
        thin: 'border',
        thick: 'border-2',
        rounded: 'rounded',
        'rounded-lg': 'rounded-lg',
        'rounded-full': 'rounded-full'
      }
    };
  }

  /**
   * Initialize design tokens
   */
  initializeDesignTokens() {
    return {
      colors: {
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a'
        },
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827'
        }
      },
      spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
        '2xl': '3rem'
      },
      typography: {
        fontFamily: {
          sans: ['Inter', 'system-ui', 'sans-serif'],
          serif: ['Georgia', 'serif'],
          mono: ['Monaco', 'monospace']
        },
        fontSize: {
          xs: '0.75rem',
          sm: '0.875rem',
          base: '1rem',
          lg: '1.125rem',
          xl: '1.25rem',
          '2xl': '1.5rem',
          '3xl': '1.875rem',
          '4xl': '2.25rem'
        }
      }
    };
  }

  /**
   * Initialize component classes
   */
  initializeComponentClasses() {
    return {
      header: 'bg-white shadow-sm border-b border-gray-200 px-4 py-6 sm:px-6 lg:px-8',
      navigation: 'flex space-x-4 sm:space-x-6 lg:space-x-8',
      form: 'space-y-4 sm:space-y-6 max-w-md mx-auto',
      table: 'min-w-full divide-y divide-gray-200 shadow-sm border border-gray-200 rounded-lg overflow-hidden',
      search: 'relative max-w-md mx-auto',
      button: 'inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2',
      sidebar: 'w-64 bg-gray-50 border-r border-gray-200 px-4 py-6',
      footer: 'bg-gray-50 border-t border-gray-200 px-4 py-8 sm:px-6 lg:px-8',
      card: 'bg-white overflow-hidden shadow-sm border border-gray-200 rounded-lg',
      modal: 'fixed inset-0 z-50 overflow-y-auto',
      section: 'py-8 sm:py-12 lg:py-16',
      default: 'block'
    };
  }

  /**
   * Apply design system to classes
   */
  applyDesignSystemToClasses(baseClasses, designSystem) {
    if (!designSystem || !designSystem.colorPalette) {
      return baseClasses;
    }

    let customClasses = baseClasses;

    // Replace default colors with design system colors
    if (designSystem.colorPalette.primary) {
      customClasses = customClasses.replace(/blue-/g, 'primary-');
    }

    // Apply custom spacing if available
    if (designSystem.spacing) {
      // Apply custom spacing logic here
    }

    return customClasses;
  }

  /**
   * Apply responsive classes
   */
  applyResponsiveClasses(baseClasses, spec, options) {
    if (!this.config.enableResponsiveUtilities) {
      return baseClasses;
    }

    const responsiveClasses = [];

    // Add mobile-first responsive classes
    switch (spec.type) {
      case 'navigation':
        responsiveClasses.push('flex-col sm:flex-row');
        break;
      case 'form':
        responsiveClasses.push('w-full sm:max-w-md');
        break;
      case 'table':
        responsiveClasses.push('overflow-x-auto');
        break;
      case 'card':
        responsiveClasses.push('mx-4 sm:mx-0');
        break;
      case 'modal':
        responsiveClasses.push('p-4 sm:p-6 lg:p-8');
        break;
    }

    return `${baseClasses} ${responsiveClasses.join(' ')}`.trim();
  }

  /**
   * Apply accessibility classes
   */
  applyAccessibilityClasses(baseClasses, spec) {
    const accessibilityClasses = [];

    // Add focus styles
    accessibilityClasses.push('focus:outline-none focus:ring-2 focus:ring-blue-500');

    // Add specific accessibility classes based on component type
    switch (spec.type) {
      case 'button':
        accessibilityClasses.push('focus:ring-offset-2');
        break;
      case 'form':
        accessibilityClasses.push('focus-within:ring-1 focus-within:ring-blue-500');
        break;
      case 'navigation':
        accessibilityClasses.push('focus-visible:ring-2');
        break;
    }

    return `${baseClasses} ${accessibilityClasses.join(' ')}`.trim();
  }

  /**
   * Merge multiple Tailwind class strings
   */
  mergeTailwindClasses(classArrays) {
    const allClasses = classArrays.filter(Boolean).join(' ');

    // Remove duplicate classes
    const uniqueClasses = [...new Set(allClasses.split(' '))];

    return uniqueClasses.join(' ').trim();
  }

  /**
   * Generate custom component styles
   */
  generateCustomComponentStyles(designSystem) {
    return `
/* Custom Component Styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-900 font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .card-elevated {
    @apply bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden;
  }

  .nav-link {
    @apply text-gray-600 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
  }
}
`;
  }

  /**
   * Generate design system specific styles
   */
  generateDesignSystemStyles(designSystem) {
    if (!designSystem.colorPalette) {
      return '';
    }

    let styles = '\n/* Design System Styles */\n@layer base {\n';

    // Add custom color variables
    if (designSystem.colorPalette.primary) {
      styles += '  :root {\n';
      styles += `    --color-primary: ${designSystem.colorPalette.primary[0]};\n`;
      styles += `    --color-primary-dark: ${designSystem.colorPalette.primary[1] || this.darkenColor(designSystem.colorPalette.primary[0], 0.1)};\n`;
      styles += '  }\n';
    }

    styles += '}\n';

    return styles;
  }

  /**
   * Generate accessibility styles
   */
  generateAccessibilityStyles() {
    return `
/* Accessibility Enhancements */
@layer utilities {
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .focus-visible {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  .skip-link {
    @apply sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-primary {
    @apply border-2 border-current;
  }

  .form-input {
    @apply border-2 border-gray-900;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
`;
  }

  /**
   * Map spacing to Tailwind
   */
  mapSpacingToTailwind(spacing) {
    if (!spacing) return {};

    return {
      'xs': spacing.xs || '0.25rem',
      'sm': spacing.sm || '0.5rem',
      'md': spacing.md || '1rem',
      'lg': spacing.lg || '1.5rem',
      'xl': spacing.xl || '2rem'
    };
  }

  /**
   * Map typography to Tailwind
   */
  mapTypographyToTailwind(typography) {
    if (!typography) return {};

    return {
      'sans': typography.fontFamily?.sans || ['Inter', 'system-ui', 'sans-serif'],
      'serif': typography.fontFamily?.serif || ['Georgia', 'serif'],
      'mono': typography.fontFamily?.mono || ['Monaco', 'monospace']
    };
  }

  /**
   * Map shadows to Tailwind
   */
  mapShadowsToTailwind(shadows) {
    if (!shadows) return {};

    return {
      'sm': shadows.sm || '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      'md': shadows.md || '0 4px 6px -1px rgb(0 0 0 / 0.1)',
      'lg': shadows.lg || '0 10px 15px -3px rgb(0 0 0 / 0.1)',
      'xl': shadows.xl || '0 20px 25px -5px rgb(0 0 0 / 0.1)'
    };
  }

  /**
   * Map border radius to Tailwind
   */
  mapBorderRadiusToTailwind(borderRadius) {
    if (!borderRadius) return {};

    return {
      'sm': borderRadius.sm || '0.125rem',
      'md': borderRadius.md || '0.375rem',
      'lg': borderRadius.lg || '0.5rem',
      'xl': borderRadius.xl || '0.75rem'
    };
  }

  /**
   * Get default design tokens
   */
  getDefaultDesignTokens() {
    return {
      colors: this.designTokens.colors,
      spacing: this.designTokens.spacing,
      typography: this.designTokens.typography
    };
  }

  /**
   * Lighten a color
   */
  lightenColor(color, amount) {
    // Simple color lightening - in production, use a proper color manipulation library
    const hex = color.replace('#', '');
    const num = parseInt(hex, 16);
    const r = Math.min(255, Math.floor((num >> 16) + (255 - (num >> 16)) * amount));
    const g = Math.min(255, Math.floor(((num >> 8) & 0x00FF) + (255 - ((num >> 8) & 0x00FF)) * amount));
    const b = Math.min(255, Math.floor((num & 0x0000FF) + (255 - (num & 0x0000FF)) * amount));
    return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
  }

  /**
   * Darken a color
   */
  darkenColor(color, amount) {
    // Simple color darkening - in production, use a proper color manipulation library
    const hex = color.replace('#', '');
    const num = parseInt(hex, 16);
    const r = Math.max(0, Math.floor((num >> 16) * (1 - amount)));
    const g = Math.max(0, Math.floor(((num >> 8) & 0x00FF) * (1 - amount)));
    const b = Math.max(0, Math.floor((num & 0x0000FF) * (1 - amount)));
    return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
  }

  /**
   * Generate cache key
   */
  generateCacheKey(componentSpecs, designSystem, options) {
    const keyData = {
      componentsHash: this.hashObject(componentSpecs),
      designSystemHash: this.hashObject(designSystem),
      optionsHash: this.hashObject(options),
      configHash: this.hashObject(this.config)
    };

    return `tailwind_${JSON.stringify(keyData)}`.replace(/[^a-zA-Z0-9_]/g, '_');
  }

  /**
   * Simple hash function for objects
   */
  hashObject(obj) {
    return JSON.stringify(obj).split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
  }

  /**
   * Get integration statistics
   */
  getIntegrationStats() {
    return {
      ...this.integrationStats,
      cacheHitRate: this.integrationStats.totalGenerations > 0 ?
        this.integrationStats.cacheHits / this.integrationStats.totalGenerations : 0,
      designTokenUsageRate: this.integrationStats.totalGenerations > 0 ?
        this.integrationStats.designTokensUsed / this.integrationStats.totalGenerations : 0
    };
  }
}
