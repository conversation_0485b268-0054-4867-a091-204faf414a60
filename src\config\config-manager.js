/**
 * Configuration Manager for Guidant
 *
 * This class is responsible for loading, merging, and providing access to the
 * application's configuration. It follows TaskMaster's pattern of a clear
 * precedence hierarchy:
 * 1. Environment Variables (highest priority)
 * 2. User-defined config file (.guidant/config.json)
 * 3. Default configuration (lowest priority)
 *
 * This manager is intended to be a singleton, instantiated once and used
 * throughout the application to ensure consistent configuration access.
 */
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import { DEFAULT_CONFIG } from './defaults.js';

// Using __dirname in ES modules
const __dirname = path.dirname(fileURLToPath(import.meta.url));

class ConfigManager {
  constructor(projectRoot = null) {
    this.projectRoot = projectRoot;
    this.config = this.loadConfiguration();
    this.supportedModels = this.loadSupportedModels();
  }

  /**
   * Loads the final, merged configuration following the precedence hierarchy.
   */
  loadConfiguration() {
    let config = this.deepClone(DEFAULT_CONFIG);
    const fileConfig = this.loadConfigurationFile();
    if (fileConfig) {
      config = this.deepMerge(config, fileConfig);
    }
    config = this.applyEnvironmentOverrides(config);
    return config;
  }

  /**
   * Loads the user-defined configuration file (.guidant/config.json).
   */
  loadConfigurationFile() {
    try {
      const configPath = this.findConfigurationFile();
      if (configPath && fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf-8');
        return JSON.parse(configData);
      }
      return null;
    } catch (error) {
      console.warn(`Warning: Could not load or parse configuration file: ${error.message}`);
      return null;
    }
  }
  
  /**
   * Loads the registry of supported models from an external JSON file.
   */
  loadSupportedModels() {
    try {
        const modelsPath = path.join(__dirname, 'supported-models.json');
        if (fs.existsSync(modelsPath)) {
            const modelsData = fs.readFileSync(modelsPath, 'utf-8');
            return JSON.parse(modelsData);
        }
        console.warn(`Warning: supported-models.json not found at ${modelsPath}`);
        return {};
    } catch (error) {
        console.warn('Warning: Could not load supported-models.json:', error.message);
        return {};
    }
  }

  /**
   * Searches for the configuration file in standard locations.
   */
  findConfigurationFile() {
    const searchPaths = [
      this.projectRoot && path.join(this.projectRoot, '.guidant', 'config.json'),
      path.join(process.cwd(), '.guidant', 'config.json'),
    ].filter(Boolean);

    for (const configPath of searchPaths) {
      if (fs.existsSync(configPath)) {
        return configPath;
      }
    }
    return null;
  }

  /**
   * Overrides configuration with values from environment variables.
   */
  applyEnvironmentOverrides(config) {
    const newConfig = this.deepClone(config);
    
    // Override model roles
    if (newConfig.models) {
        Object.keys(newConfig.models).forEach(role => {
            const envPrefix = `AI_${role.toUpperCase()}`;
            if (process.env[`${envPrefix}_PROVIDER`]) {
                newConfig.models[role].provider = process.env[`${envPrefix}_PROVIDER`];
            }
            if (process.env[`${envPrefix}_MODEL`]) {
                newConfig.models[role].modelId = process.env[`${envPrefix}_MODEL`];
            }
        });
    }

    return newConfig;
  }

  /**
   * Returns the full configuration for a specific AI provider.
   */
  getProviderConfig(providerName) {
    return this.config.providers?.[providerName] || null;
  }

  /**
   * Returns the configuration for a specific model role (e.g., 'main', 'analysis').
   * It merges the role-specific settings with the provider-level settings.
   */
  getRoleConfig(role = 'main') {
    const roleDefaults = this.config.models.default;
    const roleSpecific = this.config.models[role];
    if (!roleSpecific) {
      throw new Error(`Configuration for role '${role}' not found.`);
    }

    const modelConfig = { ...roleDefaults, ...roleSpecific };
    const providerConfig = this.getProviderConfig(modelConfig.provider);

    if (!providerConfig) {
        throw new Error(`Provider configuration for '${modelConfig.provider}' not found.`);
    }

    // Merge provider settings into the final role config
    return this.deepMerge(providerConfig, modelConfig);
  }

  /**
   * Get research services configuration
   * @returns {Object} Research configuration
   */
  getResearchConfig() {
    const researchConfig = this.config.research;
    if (!researchConfig) {
      throw new Error('No research configuration found');
    }

    // Apply environment variable substitutions for API keys
    const processedConfig = this.processEnvironmentVariables(researchConfig);

    return {
      ...processedConfig,
      ai: this.getRoleConfig('research') // Include AI model config for synthesis
    };
  }

  /**
   * Process environment variable substitutions in configuration
   * @param {Object} config - Configuration object to process
   * @returns {Object} Processed configuration
   */
  processEnvironmentVariables(config) {
    const processed = JSON.parse(JSON.stringify(config)); // Deep clone

    const processValue = (value) => {
      if (typeof value === 'string' && value.startsWith('${') && value.endsWith('}')) {
        const envVar = value.slice(2, -1);
        return process.env[envVar] || value;
      }
      return value;
    };

    const processObject = (obj) => {
      for (const key in obj) {
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          processObject(obj[key]);
        } else {
          obj[key] = processValue(obj[key]);
        }
      }
    };

    processObject(processed);
    return processed;
  }

  /**
   * Utility for deep cloning an object.
   */
  deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
  }

  /**
   * Utility for deeply merging two objects.
   */
  deepMerge(target, source) {
    const output = { ...target };
    if (this.isObject(target) && this.isObject(source)) {
      Object.keys(source).forEach(key => {
        if (this.isObject(source[key])) {
          if (!(key in target)) {
            Object.assign(output, { [key]: source[key] });
          } else {
            output[key] = this.deepMerge(target[key], source[key]);
          }
        } else {
          Object.assign(output, { [key]: source[key] });
        }
      });
    }
    return output;
  }
  
  isObject(item) {
    return (item && typeof item === 'object' && !Array.isArray(item));
  }
}

// Create and export a singleton instance
const configManager = new ConfigManager();
export { configManager }; 