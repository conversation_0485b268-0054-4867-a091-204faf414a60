/**
 * Component Library Research
 * Advanced component library research capability using Context7 for finding relevant
 * component examples, design patterns, and implementation best practices
 */

import { Context7Provider } from '../../research-providers/context7-provider.js';
import { VisualIntelligenceCache } from './visual-intelligence-cache.js';
import { configManager } from '../../config/config-manager.js';
import fs from 'fs/promises';
import path from 'path';

/**
 * Component Pattern Analyzer
 * Analyzes and scores component patterns from multiple libraries
 */
export class ComponentPatternAnalyzer {
  constructor(config = {}) {
    this.config = {
      minPatternScore: config.minPatternScore || 0.6,
      maxPatternsPerLibrary: config.maxPatternsPerLibrary || 5,
      patternWeights: {
        popularity: 0.3,
        documentation: 0.25,
        examples: 0.25,
        maintenance: 0.2
      },
      ...config
    };
  }

  /**
   * Analyze patterns from library research results
   */
  analyzePatterns(libraryResults, componentType) {
    const patterns = [];

    for (const result of libraryResults) {
      if (result.status === 'fulfilled' && result.value) {
        const libraryPatterns = this.extractLibraryPatterns(result.value, componentType);
        patterns.push(...libraryPatterns);
      }
    }

    return this.scoreAndRankPatterns(patterns, componentType);
  }

  /**
   * Extract patterns from library research result
   */
  extractLibraryPatterns(libraryResult, componentType) {
    const patterns = [];
    const { library, topics, codeExamples, metadata } = libraryResult;

    // Extract patterns from documentation topics
    for (const topic of topics || []) {
      if (topic.documentation) {
        const extractedPatterns = this.extractPatternsFromDocumentation(
          topic.documentation, 
          componentType, 
          library
        );
        patterns.push(...extractedPatterns);
      }
    }

    // Extract patterns from code examples
    for (const example of codeExamples || []) {
      const patternFromCode = this.extractPatternFromCode(example, componentType, library);
      if (patternFromCode) {
        patterns.push(patternFromCode);
      }
    }

    return patterns;
  }

  /**
   * Extract patterns from documentation text
   */
  extractPatternsFromDocumentation(documentation, componentType, library) {
    const patterns = [];
    const patternIndicators = [
      /pattern[s]?:?\s*([^.!?\n]+)/gi,
      /approach[es]?:?\s*([^.!?\n]+)/gi,
      /implementation[s]?:?\s*([^.!?\n]+)/gi,
      /usage[s]?:?\s*([^.!?\n]+)/gi
    ];

    for (const indicator of patternIndicators) {
      const matches = documentation.matchAll(indicator);
      for (const match of matches) {
        if (match[1] && match[1].length > 10 && match[1].length < 200) {
          patterns.push({
            type: 'documentation',
            pattern: match[1].trim(),
            library,
            componentType,
            source: 'documentation',
            confidence: 0.7
          });
        }
      }
    }

    return patterns;
  }

  /**
   * Extract pattern from code example
   */
  extractPatternFromCode(codeExample, componentType, library) {
    const { code, language } = codeExample;
    
    if (!code || !this.isRelevantCode(code, componentType)) {
      return null;
    }

    return {
      type: 'code',
      pattern: this.summarizeCodePattern(code, componentType),
      library,
      componentType,
      source: 'code_example',
      language,
      codeSnippet: code.slice(0, 500), // First 500 chars
      confidence: 0.8
    };
  }

  /**
   * Check if code is relevant to component type
   */
  isRelevantCode(code, componentType) {
    const codeText = code.toLowerCase();
    const componentKeywords = [
      componentType.toLowerCase(),
      `${componentType.toLowerCase()}component`,
      `use${componentType.toLowerCase()}`,
      `create${componentType.toLowerCase()}`
    ];

    return componentKeywords.some(keyword => codeText.includes(keyword));
  }

  /**
   * Summarize code pattern for display
   */
  summarizeCodePattern(code, componentType) {
    // Extract key patterns from code
    const lines = code.split('\n').filter(line => line.trim());
    const keyLines = lines.filter(line => {
      const lowerLine = line.toLowerCase();
      return lowerLine.includes(componentType.toLowerCase()) ||
             lowerLine.includes('props') ||
             lowerLine.includes('state') ||
             lowerLine.includes('hook') ||
             lowerLine.includes('component');
    });

    if (keyLines.length > 0) {
      return `${componentType} implementation using ${keyLines[0].trim().slice(0, 100)}...`;
    }

    return `${componentType} implementation pattern`;
  }

  /**
   * Score and rank patterns
   */
  scoreAndRankPatterns(patterns, componentType) {
    const scoredPatterns = patterns.map(pattern => ({
      ...pattern,
      score: this.calculatePatternScore(pattern, componentType)
    }));

    return scoredPatterns
      .filter(pattern => pattern.score >= this.config.minPatternScore)
      .sort((a, b) => b.score - a.score)
      .slice(0, this.config.maxPatternsPerLibrary * 3); // Allow more patterns across libraries
  }

  /**
   * Calculate pattern score based on multiple factors
   */
  calculatePatternScore(pattern, componentType) {
    let score = pattern.confidence || 0.5;

    // Boost for exact component type match
    if (pattern.pattern.toLowerCase().includes(componentType.toLowerCase())) {
      score += 0.2;
    }

    // Boost for code examples
    if (pattern.type === 'code') {
      score += 0.15;
    }

    // Boost for popular libraries
    const popularLibraries = ['react', 'mui', 'chakra-ui', 'ant-design', 'mantine'];
    if (popularLibraries.includes(pattern.library.toLowerCase())) {
      score += 0.1;
    }

    return Math.min(score, 1);
  }
}

/**
 * Code Example Processor
 * Processes and analyzes code examples from component libraries
 */
export class CodeExampleProcessor {
  constructor(config = {}) {
    this.config = {
      maxExamplesPerLibrary: config.maxExamplesPerLibrary || 3,
      minCodeLength: config.minCodeLength || 50,
      maxCodeLength: config.maxCodeLength || 2000,
      preferredLanguages: config.preferredLanguages || ['javascript', 'typescript', 'jsx', 'tsx'],
      ...config
    };
  }

  /**
   * Process code examples from library research results
   */
  processCodeExamples(libraryResults, componentType) {
    const allExamples = [];

    for (const result of libraryResults) {
      if (result.status === 'fulfilled' && result.value?.codeExamples) {
        const processedExamples = this.processLibraryExamples(
          result.value.codeExamples,
          result.value.library,
          componentType
        );
        allExamples.push(...processedExamples);
      }
    }

    return this.rankAndFilterExamples(allExamples, componentType);
  }

  /**
   * Process examples from a single library
   */
  processLibraryExamples(examples, library, componentType) {
    return examples
      .filter(example => this.isValidExample(example, componentType))
      .map(example => this.enhanceExample(example, library, componentType))
      .slice(0, this.config.maxExamplesPerLibrary);
  }

  /**
   * Check if example is valid for processing
   */
  isValidExample(example, componentType) {
    const { code, language } = example;
    
    if (!code || code.length < this.config.minCodeLength || code.length > this.config.maxCodeLength) {
      return false;
    }

    if (this.config.preferredLanguages.length > 0 && 
        !this.config.preferredLanguages.includes(language)) {
      return false;
    }

    return code.toLowerCase().includes(componentType.toLowerCase());
  }

  /**
   * Enhance example with additional metadata
   */
  enhanceExample(example, library, componentType) {
    return {
      ...example,
      library,
      componentType,
      complexity: this.assessComplexity(example.code),
      features: this.extractFeatures(example.code, componentType),
      usagePattern: this.identifyUsagePattern(example.code),
      relevanceScore: this.calculateRelevanceScore(example.code, componentType)
    };
  }

  /**
   * Assess code complexity
   */
  assessComplexity(code) {
    const lines = code.split('\n').length;
    const hasHooks = /use[A-Z]/.test(code);
    const hasState = /state|setState/.test(code);
    const hasProps = /props\./.test(code);

    if (lines > 50 || (hasHooks && hasState)) return 'high';
    if (lines > 20 || hasHooks || hasState || hasProps) return 'medium';
    return 'low';
  }

  /**
   * Extract features from code
   */
  extractFeatures(code, componentType) {
    const features = [];
    const codeText = code.toLowerCase();

    const featurePatterns = {
      'state-management': /state|usestate|setstate/,
      'event-handling': /onclick|onchange|onevent|handle/,
      'styling': /style|css|classname|styled/,
      'validation': /valid|error|required/,
      'accessibility': /aria|role|tabindex/,
      'responsive': /responsive|mobile|breakpoint/,
      'animation': /animate|transition|motion/
    };

    for (const [feature, pattern] of Object.entries(featurePatterns)) {
      if (pattern.test(codeText)) {
        features.push(feature);
      }
    }

    return features;
  }

  /**
   * Identify usage pattern
   */
  identifyUsagePattern(code) {
    if (/function.*component|const.*=.*\(/i.test(code)) return 'functional';
    if (/class.*extends.*component/i.test(code)) return 'class';
    if (/hook|use[A-Z]/i.test(code)) return 'hook';
    return 'unknown';
  }

  /**
   * Calculate relevance score
   */
  calculateRelevanceScore(code, componentType) {
    let score = 0.5;
    const codeText = code.toLowerCase();
    const componentText = componentType.toLowerCase();

    // Exact component name match
    if (codeText.includes(componentText)) score += 0.3;
    
    // Component-related keywords
    if (codeText.includes(`${componentText}component`)) score += 0.2;
    if (codeText.includes(`use${componentText}`)) score += 0.2;
    
    // Quality indicators
    if (codeText.includes('proptypes') || codeText.includes('typescript')) score += 0.1;
    if (codeText.includes('aria') || codeText.includes('accessibility')) score += 0.1;

    return Math.min(score, 1);
  }

  /**
   * Rank and filter examples
   */
  rankAndFilterExamples(examples, componentType) {
    return examples
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 10); // Top 10 examples across all libraries
  }
}

/**
 * Implementation Recommendation Engine
 * Generates actionable implementation recommendations based on research
 */
export class ImplementationRecommendationEngine {
  constructor(config = {}) {
    this.config = {
      maxRecommendations: config.maxRecommendations || 8,
      minConfidence: config.minConfidence || 0.6,
      priorityWeights: {
        accessibility: 0.3,
        performance: 0.25,
        maintainability: 0.25,
        usability: 0.2
      },
      ...config
    };
  }

  /**
   * Generate implementation recommendations
   */
  generateRecommendations(patterns, codeExamples, componentType, framework) {
    const recommendations = [];

    // Pattern-based recommendations
    recommendations.push(...this.generatePatternRecommendations(patterns, componentType));

    // Code example-based recommendations
    recommendations.push(...this.generateCodeRecommendations(codeExamples, componentType));

    // Framework-specific recommendations
    recommendations.push(...this.generateFrameworkRecommendations(framework, componentType));

    return this.scoreAndRankRecommendations(recommendations);
  }

  /**
   * Generate recommendations from patterns
   */
  generatePatternRecommendations(patterns, componentType) {
    const recommendations = [];

    for (const pattern of patterns.slice(0, 5)) {
      recommendations.push({
        type: 'pattern',
        title: `Follow ${pattern.library} ${componentType} pattern`,
        description: pattern.pattern,
        library: pattern.library,
        confidence: pattern.score,
        priority: this.calculatePriority(pattern, 'pattern'),
        implementation: this.generatePatternImplementation(pattern, componentType),
        source: 'pattern_analysis'
      });
    }

    return recommendations;
  }

  /**
   * Generate recommendations from code examples
   */
  generateCodeRecommendations(codeExamples, componentType) {
    const recommendations = [];

    // Group examples by features
    const featureGroups = this.groupExamplesByFeatures(codeExamples);

    for (const [feature, examples] of Object.entries(featureGroups)) {
      if (examples.length >= 2) { // Feature appears in multiple examples
        recommendations.push({
          type: 'feature',
          title: `Implement ${feature} for ${componentType}`,
          description: `Multiple libraries implement ${feature} for ${componentType} components`,
          examples: examples.slice(0, 2),
          confidence: Math.min(examples.length / 5, 1),
          priority: this.calculateFeaturePriority(feature),
          implementation: this.generateFeatureImplementation(feature, componentType),
          source: 'code_analysis'
        });
      }
    }

    return recommendations;
  }

  /**
   * Generate framework-specific recommendations
   */
  generateFrameworkRecommendations(framework, componentType) {
    const frameworkPatterns = {
      react: [
        'Use functional components with hooks',
        'Implement proper prop validation',
        'Follow React naming conventions',
        'Use React.memo for performance optimization'
      ],
      vue: [
        'Use composition API for complex logic',
        'Implement proper prop definitions',
        'Follow Vue naming conventions',
        'Use computed properties for derived state'
      ],
      angular: [
        'Use OnPush change detection strategy',
        'Implement proper input/output decorators',
        'Follow Angular style guide',
        'Use trackBy functions for lists'
      ]
    };

    const patterns = frameworkPatterns[framework.toLowerCase()] || frameworkPatterns.react;

    return patterns.map(pattern => ({
      type: 'framework',
      title: `${framework} best practice for ${componentType}`,
      description: pattern,
      framework,
      confidence: 0.8,
      priority: 'medium',
      implementation: `Apply ${pattern} to ${componentType} component`,
      source: 'framework_guidelines'
    }));
  }

  /**
   * Group examples by features
   */
  groupExamplesByFeatures(codeExamples) {
    const groups = {};

    for (const example of codeExamples) {
      for (const feature of example.features || []) {
        if (!groups[feature]) {
          groups[feature] = [];
        }
        groups[feature].push(example);
      }
    }

    return groups;
  }

  /**
   * Calculate recommendation priority
   */
  calculatePriority(item, type) {
    if (type === 'pattern') {
      if (item.score >= 0.8) return 'high';
      if (item.score >= 0.6) return 'medium';
      return 'low';
    }

    return 'medium';
  }

  /**
   * Calculate feature priority
   */
  calculateFeaturePriority(feature) {
    const highPriorityFeatures = ['accessibility', 'validation', 'state-management'];
    const mediumPriorityFeatures = ['event-handling', 'styling', 'responsive'];

    if (highPriorityFeatures.includes(feature)) return 'high';
    if (mediumPriorityFeatures.includes(feature)) return 'medium';
    return 'low';
  }

  /**
   * Generate pattern implementation guidance
   */
  generatePatternImplementation(pattern, componentType) {
    return `Implement ${componentType} following ${pattern.library} patterns: ${pattern.pattern}`;
  }

  /**
   * Generate feature implementation guidance
   */
  generateFeatureImplementation(feature, componentType) {
    const implementations = {
      'accessibility': `Add ARIA attributes and keyboard navigation to ${componentType}`,
      'validation': `Implement input validation and error handling for ${componentType}`,
      'state-management': `Use appropriate state management for ${componentType} data`,
      'event-handling': `Implement proper event handlers for ${componentType} interactions`,
      'styling': `Apply consistent styling and theming to ${componentType}`,
      'responsive': `Ensure ${componentType} works across different screen sizes`
    };

    return implementations[feature] || `Implement ${feature} for ${componentType}`;
  }

  /**
   * Score and rank recommendations
   */
  scoreAndRankRecommendations(recommendations) {
    const scored = recommendations.map(rec => ({
      ...rec,
      finalScore: this.calculateFinalScore(rec)
    }));

    return scored
      .filter(rec => rec.confidence >= this.config.minConfidence)
      .sort((a, b) => b.finalScore - a.finalScore)
      .slice(0, this.config.maxRecommendations);
  }

  /**
   * Calculate final recommendation score
   */
  calculateFinalScore(recommendation) {
    let score = recommendation.confidence || 0.5;

    // Priority boost
    const priorityBoost = {
      high: 0.3,
      medium: 0.15,
      low: 0.05
    };
    score += priorityBoost[recommendation.priority] || 0;

    // Type boost
    const typeBoost = {
      pattern: 0.2,
      feature: 0.15,
      framework: 0.1
    };
    score += typeBoost[recommendation.type] || 0;

    return Math.min(score, 1);
  }
}

/**
 * Component Library Research
 * Main research engine for component library analysis and recommendations
 */
export class ComponentLibraryResearch {
  constructor(config = {}) {
    this.config = {
      enableResearch: config.enableResearch !== false,
      cacheResults: config.cacheResults !== false,
      maxLibrariesPerFramework: config.maxLibrariesPerFramework || 4,
      researchTimeout: config.researchTimeout || 30000, // 30 seconds
      parallelResearch: config.parallelResearch !== false,
      ...config
    };

    this.context7Provider = null;
    this.patternAnalyzer = new ComponentPatternAnalyzer(config.patternAnalysis || {});
    this.codeProcessor = new CodeExampleProcessor(config.codeProcessing || {});
    this.recommendationEngine = new ImplementationRecommendationEngine(config.recommendations || {});

    // Initialize enhanced visual intelligence cache
    this.visualCache = new VisualIntelligenceCache({
      maxCacheSize: config.maxCacheSize || 300,
      enableQualityScoring: config.enableQualityScoring !== false,
      enableIntelligentInvalidation: config.enableIntelligentInvalidation !== false,
      enableAPIOptimization: config.enableAPIOptimization !== false,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring !== false
    });

    // Legacy cache for backward compatibility (deprecated)
    this.cache = new Map();
    this.libraryConfig = null;
    this.researchMetrics = {
      totalResearches: 0,
      cacheHits: 0,
      librariesResearched: 0,
      patternsFound: 0,
      examplesProcessed: 0
    };
  }

  /**
   * Initialize the research system
   */
  async initialize() {
    if (!this.config.enableResearch) {
      console.log('Component library research disabled');
      return;
    }

    try {
      // Initialize Context7 provider
      const researchConfig = configManager.getResearchConfig();
      this.context7Provider = new Context7Provider(researchConfig.providers.context7);

      // Load library configuration
      await this.loadLibraryConfiguration();

      console.log('Component library research initialized successfully');
    } catch (error) {
      console.warn('Component library research initialization failed:', error.message);
      this.config.enableResearch = false;
    }
  }

  /**
   * Research component libraries for specific component type
   */
  async researchComponentLibraries(componentType, framework = 'react', context = {}) {
    if (!this.config.enableResearch) {
      return this.getFallbackResearch(componentType, framework);
    }

    this.researchMetrics.totalResearches++;

    // Use enhanced visual intelligence cache
    const cacheKey = this.visualCache.generateCacheKey(
      'component_library_research',
      componentType,
      framework,
      context
    );

    // Try enhanced cache first
    const cached = await this.visualCache.get(cacheKey, context);
    if (cached) {
      this.researchMetrics.cacheHits++;
      return cached;
    }

    try {
      await this.initialize();

      // Get relevant libraries for framework
      const libraries = this.getRelevantLibraries(framework, componentType);

      // Research libraries in parallel or sequentially
      const libraryResults = this.config.parallelResearch ?
        await this.researchLibrariesParallel(libraries, componentType, context) :
        await this.researchLibrariesSequential(libraries, componentType, context);

      // Analyze patterns and examples
      const patterns = this.patternAnalyzer.analyzePatterns(libraryResults, componentType);
      const codeExamples = this.codeProcessor.processCodeExamples(libraryResults, componentType);

      // Generate recommendations
      const recommendations = this.recommendationEngine.generateRecommendations(
        patterns, codeExamples, componentType, framework
      );

      // Compile comprehensive research result
      const research = {
        componentType,
        framework,
        libraries: libraries.slice(0, this.config.maxLibrariesPerFramework),
        patterns,
        codeExamples,
        recommendations,
        insights: this.generateInsights(patterns, codeExamples, recommendations),
        metadata: {
          librariesResearched: libraryResults.filter(r => r.status === 'fulfilled').length,
          patternsFound: patterns.length,
          examplesProcessed: codeExamples.length,
          recommendationsGenerated: recommendations.length,
          researchDate: new Date().toISOString(),
          provider: 'Context7',
          confidence: this.calculateOverallConfidence(patterns, codeExamples, recommendations)
        }
      };

      // Update metrics
      this.researchMetrics.librariesResearched += research.metadata.librariesResearched;
      this.researchMetrics.patternsFound += research.metadata.patternsFound;
      this.researchMetrics.examplesProcessed += research.metadata.examplesProcessed;

      // Cache result using enhanced visual intelligence cache
      await this.visualCache.set(cacheKey, research, {
        requestType: componentType,
        provider: 'component_library_research',
        timestamp: Date.now()
      });

      return research;

    } catch (error) {
      console.warn('Component library research failed:', error.message);
      return this.getFallbackResearch(componentType, framework);
    }
  }

  /**
   * Research libraries in parallel for better performance
   */
  async researchLibrariesParallel(libraries, componentType, context) {
    const researchPromises = libraries.map(library =>
      this.researchSingleLibrary(library, componentType, context)
    );

    return await Promise.allSettled(researchPromises);
  }

  /**
   * Research libraries sequentially to avoid rate limits
   */
  async researchLibrariesSequential(libraries, componentType, context) {
    const results = [];

    for (const library of libraries) {
      try {
        const result = await this.researchSingleLibrary(library, componentType, context);
        results.push({ status: 'fulfilled', value: result });
      } catch (error) {
        results.push({ status: 'rejected', reason: error });
      }
    }

    return results;
  }

  /**
   * Research a single library
   */
  async researchSingleLibrary(library, componentType, context) {
    if (!this.context7Provider) {
      throw new Error('Context7 provider not initialized');
    }

    const topics = [
      componentType,
      `${componentType} component`,
      `${componentType} examples`,
      `${componentType} best practices`,
      'api reference'
    ];

    try {
      const research = await Promise.race([
        this.context7Provider.libraryResearch(library, {
          topics,
          tokensPerTopic: 3000
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Research timeout')), this.config.researchTimeout)
        )
      ]);

      return {
        ...research,
        library,
        componentType,
        context
      };
    } catch (error) {
      console.warn(`Library research failed for ${library}:`, error.message);
      throw error;
    }
  }

  /**
   * Get relevant libraries for framework and component type
   */
  getRelevantLibraries(framework, componentType) {
    if (!this.libraryConfig) {
      return this.getFallbackLibraries(framework);
    }

    const frameworkLibraries = this.libraryConfig[framework.toLowerCase()] ||
                              this.libraryConfig.react || [];

    // Prioritize libraries based on component type
    const prioritized = this.prioritizeLibrariesForComponent(frameworkLibraries, componentType);

    return prioritized.slice(0, this.config.maxLibrariesPerFramework);
  }

  /**
   * Prioritize libraries based on component type
   */
  prioritizeLibrariesForComponent(libraries, componentType) {
    const componentPriorities = {
      form: ['react-hook-form', 'formik', 'mui', 'ant-design'],
      table: ['react-table', 'ag-grid', 'mui', 'ant-design'],
      chart: ['recharts', 'd3', 'chart.js', 'victory'],
      navigation: ['react-router', 'reach-router', 'mui', 'ant-design'],
      modal: ['react-modal', 'mui', 'chakra-ui', 'ant-design'],
      button: ['mui', 'chakra-ui', 'ant-design', 'mantine'],
      input: ['mui', 'chakra-ui', 'ant-design', 'react-hook-form']
    };

    const priorities = componentPriorities[componentType.toLowerCase()] || [];
    const prioritized = [];
    const remaining = [...libraries];

    // Add prioritized libraries first
    for (const priority of priorities) {
      const index = remaining.findIndex(lib =>
        lib.toLowerCase().includes(priority.toLowerCase())
      );
      if (index !== -1) {
        prioritized.push(remaining.splice(index, 1)[0]);
      }
    }

    // Add remaining libraries
    prioritized.push(...remaining);

    return prioritized;
  }

  /**
   * Generate insights from research results
   */
  generateInsights(patterns, codeExamples, recommendations) {
    const insights = [];

    // Pattern insights
    if (patterns.length > 0) {
      const topPattern = patterns[0];
      insights.push({
        type: 'pattern',
        title: `Most common pattern: ${topPattern.library}`,
        description: `${topPattern.library} provides the most relevant pattern for this component`,
        confidence: topPattern.score,
        actionable: true
      });
    }

    // Code complexity insights
    const complexityDistribution = this.analyzeComplexityDistribution(codeExamples);
    if (complexityDistribution.recommendation) {
      insights.push({
        type: 'complexity',
        title: 'Implementation complexity recommendation',
        description: complexityDistribution.recommendation,
        confidence: 0.8,
        actionable: true
      });
    }

    // Feature insights
    const commonFeatures = this.identifyCommonFeatures(codeExamples);
    if (commonFeatures.length > 0) {
      insights.push({
        type: 'features',
        title: `Common features: ${commonFeatures.slice(0, 3).join(', ')}`,
        description: `Most implementations include these features`,
        confidence: 0.7,
        actionable: true
      });
    }

    return insights;
  }

  /**
   * Analyze complexity distribution of code examples
   */
  analyzeComplexityDistribution(codeExamples) {
    if (codeExamples.length === 0) {
      return { recommendation: null };
    }

    const complexities = codeExamples.map(ex => ex.complexity);
    const distribution = {
      low: complexities.filter(c => c === 'low').length,
      medium: complexities.filter(c => c === 'medium').length,
      high: complexities.filter(c => c === 'high').length
    };

    const total = complexities.length;
    const lowPercent = distribution.low / total;
    const mediumPercent = distribution.medium / total;

    if (lowPercent > 0.6) {
      return { recommendation: 'Start with a simple implementation - most examples are low complexity' };
    } else if (mediumPercent > 0.5) {
      return { recommendation: 'Medium complexity implementation recommended - balance features and simplicity' };
    } else {
      return { recommendation: 'Consider advanced features - many examples show complex implementations' };
    }
  }

  /**
   * Identify common features across examples
   */
  identifyCommonFeatures(codeExamples) {
    const featureCounts = {};

    for (const example of codeExamples) {
      for (const feature of example.features || []) {
        featureCounts[feature] = (featureCounts[feature] || 0) + 1;
      }
    }

    const threshold = Math.max(2, Math.floor(codeExamples.length * 0.3));

    return Object.entries(featureCounts)
      .filter(([_, count]) => count >= threshold)
      .sort((a, b) => b[1] - a[1])
      .map(([feature, _]) => feature);
  }

  /**
   * Calculate overall research confidence
   */
  calculateOverallConfidence(patterns, codeExamples, recommendations) {
    let confidence = 0;
    let factors = 0;

    if (patterns.length > 0) {
      confidence += patterns.reduce((sum, p) => sum + p.score, 0) / patterns.length * 0.4;
      factors++;
    }

    if (codeExamples.length > 0) {
      confidence += codeExamples.reduce((sum, ex) => sum + ex.relevanceScore, 0) / codeExamples.length * 0.3;
      factors++;
    }

    if (recommendations.length > 0) {
      confidence += recommendations.reduce((sum, rec) => sum + rec.confidence, 0) / recommendations.length * 0.3;
      factors++;
    }

    return factors > 0 ? Math.min(confidence, 1) : 0.5;
  }

  /**
   * Load library configuration
   */
  async loadLibraryConfiguration() {
    try {
      const configPath = path.join(process.cwd(), 'src/visual-generation/config/research-domains.json');
      const configData = await fs.readFile(configPath, 'utf8');
      const config = JSON.parse(configData);
      this.libraryConfig = config.libraries;
    } catch (error) {
      console.warn('Could not load library config, using fallback');
      this.libraryConfig = this.getFallbackLibraryConfig();
    }
  }

  /**
   * Generate cache key for research results
   */
  generateCacheKey(componentType, framework, context) {
    const contextHash = JSON.stringify({
      componentType,
      framework,
      industry: context.industry,
      complexity: context.complexity
    });
    return `component_research_${Buffer.from(contextHash).toString('base64').slice(0, 16)}`;
  }

  /**
   * Check if cached result is still valid
   */
  isCacheValid(cached) {
    const maxAge = 3600000; // 1 hour
    return Date.now() - cached.timestamp < maxAge;
  }

  /**
   * Get fallback research when main research fails
   */
  getFallbackResearch(componentType, framework) {
    return {
      componentType,
      framework,
      libraries: this.getFallbackLibraries(framework),
      patterns: [],
      codeExamples: [],
      recommendations: this.getFallbackRecommendations(componentType, framework),
      insights: [{
        type: 'fallback',
        title: 'Using fallback recommendations',
        description: 'Research providers unavailable, using basic recommendations',
        confidence: 0.3,
        actionable: false
      }],
      metadata: {
        librariesResearched: 0,
        patternsFound: 0,
        examplesProcessed: 0,
        recommendationsGenerated: 1,
        researchDate: new Date().toISOString(),
        provider: 'fallback',
        confidence: 0.3
      }
    };
  }

  /**
   * Get fallback libraries
   */
  getFallbackLibraries(framework) {
    const fallbackLibraries = {
      react: ['react', 'mui', 'chakra-ui', 'ant-design'],
      vue: ['vue', 'vuetify', 'quasar', 'element-plus'],
      angular: ['angular', 'angular-material', 'ng-bootstrap'],
      svelte: ['svelte', 'svelteui', 'carbon-components-svelte']
    };

    return fallbackLibraries[framework.toLowerCase()] || fallbackLibraries.react;
  }

  /**
   * Get fallback recommendations
   */
  getFallbackRecommendations(componentType, framework) {
    return [{
      type: 'fallback',
      title: `Basic ${componentType} implementation`,
      description: `Use standard ${framework} patterns for ${componentType} component`,
      confidence: 0.5,
      priority: 'medium',
      implementation: `Follow ${framework} documentation for ${componentType} implementation`,
      source: 'fallback'
    }];
  }

  /**
   * Get fallback library configuration
   */
  getFallbackLibraryConfig() {
    return {
      react: ['react', 'mui', 'chakra-ui', 'ant-design', 'mantine'],
      vue: ['vue', 'vuetify', 'quasar', 'element-plus'],
      angular: ['angular', 'angular-material', 'ng-bootstrap'],
      svelte: ['svelte', 'svelteui', 'carbon-components-svelte']
    };
  }

  /**
   * Get research metrics
   */
  getResearchMetrics() {
    return { ...this.researchMetrics };
  }
}
