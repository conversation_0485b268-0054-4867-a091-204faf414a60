/**
 * Intelligent Decision Sampler
 * Handles server-controlled LLM completions for intelligent decision making
 */

/**
 * Intelligent Decision Sampler
 * Uses LLM completions to make intelligent decisions and optimizations
 */
export class IntelligentDecisionSampler {
  constructor(existingInfrastructure = {}) {
    this.llmProvider = existingInfrastructure.llmProvider;
    this.contextManager = existingInfrastructure.contextManager;
    this.decisionHistory = existingInfrastructure.decisionHistory;
    
    this.supportedPurposes = [
      'project_guidance_decision',
      'technology_recommendation_decision',
      'feature_prioritization_decision',
      'business_strategy_decision',
      'user_experience_decision',
      'implementation_approach_decision',
      'resource_planning_decision',
      'risk_assessment_decision',
      'timeline_optimization_decision',
      'quality_assurance_decision',
      'stakeholder_communication_decision',
      'project_milestone_decision',
      'vendor_selection_decision',
      'architecture_guidance_decision',
      'performance_optimization_decision',
      'cost_benefit_analysis_decision'
    ];
  }

  /**
   * Request LLM completion for decision making
   * @param {object} context - Decision context
   * @param {object} options - Sampling options
   * @returns {object} Decision completion result
   */
  async requestCompletion(context, options = {}) {
    try {
      // Enhance context with relevant historical data
      const enhancedContext = await this.enhanceDecisionContext(context);
      
      // Generate decision prompt
      const prompt = this.generateDecisionPrompt(enhancedContext, options);
      
      // Request LLM completion
      const completion = await this.requestLLMCompletion(prompt, options);
      
      // Process and structure the decision
      const structuredDecision = this.processDecisionCompletion(completion, enhancedContext);
      
      // Store decision for future reference
      await this.storeDecision(structuredDecision, enhancedContext);
      
      return structuredDecision;
    } catch (error) {
      console.error('Decision sampling error:', error);
      return this.generateFallbackDecision(context, error);
    }
  }

  /**
   * Enhance decision context with relevant data
   * @param {object} context - Original context
   * @returns {object} Enhanced context
   */
  async enhanceDecisionContext(context) {
    const enhanced = { ...context };

    try {
      // Add relevant historical decisions
      if (this.decisionHistory && context.decision_category) {
        enhanced.recent_decisions = await this.decisionHistory.getRecentDecisions(
          context.decision_category, 
          5
        );
      }

      // Add contextual information
      if (this.contextManager) {
        enhanced.relevant_context = await this.contextManager.getRelevantContext(
          'decision_making',
          context
        );
      }

      // Add performance baselines
      enhanced.performance_baseline = this.getPerformanceBaseline(context);
      
      // Add decision constraints
      enhanced.constraints = this.identifyDecisionConstraints(context);
      
      // Add success criteria
      enhanced.success_criteria = this.defineSuccessCriteria(context);

    } catch (error) {
      console.warn('Context enhancement failed:', error.message);
    }

    return enhanced;
  }

  /**
   * Generate decision prompt for LLM
   * @param {object} context - Enhanced context
   * @param {object} options - Sampling options
   * @returns {string} Decision prompt
   */
  generateDecisionPrompt(context, options) {
    const {
      decision_type = 'project_guidance',
      current_state = {},
      goals = {},
      constraints = {},
      options_available = [],
      user_context = {}
    } = context;

    return `# Guidant AI Orchestration Decision: ${decision_type}

## Decision Context
You are Guidant's intelligent decision-making AI, specializing in guiding non-technical users through complex project decisions. Your role is to provide clear, business-focused recommendations that align with user goals while considering their technical comfort level and business constraints.

## User Context
- **Technical Expertise**: ${user_context.technical_level || 'beginner'}
- **Business Domain**: ${user_context.business_domain || 'general'}
- **Project Type**: ${user_context.project_type || 'software development'}
- **Decision Urgency**: ${user_context.urgency || 'normal'}
- **Risk Tolerance**: ${user_context.risk_tolerance || 'moderate'}

## Current Project Situation
${JSON.stringify(current_state, null, 2)}

## Business Goals and Success Criteria
${JSON.stringify(goals, null, 2)}

## Business and Technical Constraints
${JSON.stringify(constraints, null, 2)}

## Available Options for Consideration
${options_available.length > 0 ? JSON.stringify(options_available, null, 2) : 'Generate business-appropriate options based on context'}

## Relevant Project History
${context.recent_decisions ? `Previous project decisions: ${JSON.stringify(context.recent_decisions, null, 2)}` : 'No previous decisions available for this project'}

## Performance and Success Baselines
${context.performance_baseline ? JSON.stringify(context.performance_baseline, null, 2) : 'Establishing baseline metrics for this project'}

## Guidant Decision Framework
Please provide a comprehensive decision that includes:

1. **Business Recommendation**: Clear, non-technical recommendation in business terms
2. **Business Rationale**: Why this is the best choice for the user's business goals
3. **Expected Business Outcomes**: Specific business benefits and value delivery
4. **Implementation Approach**: Step-by-step approach appropriate for user's technical level
5. **Success Measurement**: Business-focused metrics and milestones
6. **Risk Management**: Business risks and practical mitigation strategies
7. **Alternative Approaches**: Other viable options with trade-off analysis
8. **Confidence Assessment**: Your confidence in this recommendation (1-10) with reasoning
9. **User Communication**: How to present this decision to the user in their language
10. **Next Steps**: Immediate actionable next steps for the user

## Response Requirements
- Use business-friendly language appropriate for ${user_context.technical_level || 'beginner'} users
- Focus on business value and outcomes rather than technical details
- Provide clear reasoning that builds user confidence
- Consider the user's risk tolerance and decision-making style
- Include fallback options if the primary recommendation doesn't work

## Response Format
Respond with a JSON object containing all the above elements, structured for easy processing and user presentation.

Focus on decisions that empower the user, build their confidence, and deliver clear business value while respecting their technical comfort level and business constraints.`;
  }

  /**
   * Request LLM completion
   * @param {string} prompt - Decision prompt
   * @param {object} options - Sampling options
   * @returns {object} LLM completion
   */
  async requestLLMCompletion(prompt, options) {
    if (!this.llmProvider) {
      throw new Error('No LLM provider available for decision sampling');
    }

    const completionOptions = {
      max_tokens: options.max_tokens || 1000,
      temperature: options.temperature || 0.7,
      top_p: options.top_p || 0.9,
      frequency_penalty: options.frequency_penalty || 0.1,
      presence_penalty: options.presence_penalty || 0.1,
      ...options
    };

    return await this.llmProvider.requestCompletion(prompt, completionOptions);
  }

  /**
   * Process and structure decision completion
   * @param {object} completion - Raw LLM completion
   * @param {object} context - Decision context
   * @returns {object} Structured decision
   */
  processDecisionCompletion(completion, context) {
    try {
      // Try to parse JSON response
      const decisionData = JSON.parse(completion.text);

      return {
        decision_id: `guidant-decision-${Date.now()}`,
        timestamp: new Date().toISOString(),
        decision_type: context.decision_type || 'project_guidance',
        user_context: context.user_context || {},

        // Core Guidant decision elements
        business_recommendation: decisionData.business_recommendation || decisionData.recommended_action,
        business_rationale: decisionData.business_rationale || decisionData.rationale,
        expected_business_outcomes: decisionData.expected_business_outcomes || decisionData.expected_outcomes,
        implementation_approach: decisionData.implementation_approach || decisionData.implementation_steps,
        success_measurement: decisionData.success_measurement || decisionData.success_metrics,
        risk_management: decisionData.risk_management || decisionData.risk_assessment,
        alternative_approaches: decisionData.alternative_approaches || decisionData.alternative_options,
        confidence_assessment: decisionData.confidence_assessment || decisionData.confidence_level || 7,
        user_communication: decisionData.user_communication || 'Present recommendation clearly',
        next_steps: decisionData.next_steps || ['Review recommendation', 'Gather feedback', 'Proceed with implementation'],

        // Metadata
        context_used: context,
        llm_metadata: {
          model: completion.model,
          tokens_used: completion.tokens_used,
          finish_reason: completion.finish_reason
        },
        guidant_metadata: {
          decision_category: this.categorizeDecision(context),
          business_impact: this.assessBusinessImpact(context),
          technical_complexity: this.assessTechnicalComplexity(context),
          user_readiness: this.assessUserReadiness(context)
        }
      };
    } catch (parseError) {
      // Fallback to text parsing if JSON parsing fails
      return this.parseGuidantTextDecision(completion.text, context);
    }
  }

  /**
   * Parse text-based Guidant decision response
   * @param {string} text - LLM response text
   * @param {object} context - Decision context
   * @returns {object} Structured decision
   */
  parseGuidantTextDecision(text, context) {
    // Extract key information from text response
    const lines = text.split('\n').filter(line => line.trim());

    return {
      decision_id: `guidant-decision-${Date.now()}`,
      timestamp: new Date().toISOString(),
      decision_type: context.decision_type || 'project_guidance',
      user_context: context.user_context || {},

      // Core Guidant decision elements
      business_recommendation: this.extractSection(lines, 'recommendation') || 'See full response',
      business_rationale: this.extractSection(lines, 'rationale') || 'See full response',
      expected_business_outcomes: this.extractSection(lines, 'outcomes') || 'See full response',
      implementation_approach: this.extractSection(lines, 'implementation') || 'See full response',
      success_measurement: this.extractSection(lines, 'success') || 'See full response',
      risk_management: this.extractSection(lines, 'risk') || 'See full response',
      alternative_approaches: this.extractSection(lines, 'alternatives') || 'See full response',
      confidence_assessment: this.extractConfidence(text) || 7,
      user_communication: this.extractSection(lines, 'communication') || 'Present clearly to user',
      next_steps: this.extractNextSteps(text) || ['Review recommendation', 'Proceed with implementation'],

      // Metadata
      full_response: text,
      context_used: context,
      parsing_method: 'guidant_text_extraction',
      guidant_metadata: {
        decision_category: this.categorizeDecision(context),
        business_impact: this.assessBusinessImpact(context),
        technical_complexity: this.assessTechnicalComplexity(context),
        user_readiness: this.assessUserReadiness(context)
      }
    };
  }

  /**
   * Categorize decision for Guidant context
   * @param {object} context - Decision context
   * @returns {string} Decision category
   */
  categorizeDecision(context) {
    const decisionType = context.decision_type || 'project_guidance';

    if (decisionType.includes('technology') || decisionType.includes('architecture')) {
      return 'technical';
    } else if (decisionType.includes('business') || decisionType.includes('strategy')) {
      return 'business';
    } else if (decisionType.includes('user') || decisionType.includes('experience')) {
      return 'user_experience';
    } else if (decisionType.includes('project') || decisionType.includes('planning')) {
      return 'project_management';
    }

    return 'general_guidance';
  }

  /**
   * Assess business impact of decision
   * @param {object} context - Decision context
   * @returns {string} Business impact level
   */
  assessBusinessImpact(context) {
    const userContext = context.user_context || {};
    const urgency = userContext.urgency || 'normal';
    const riskTolerance = userContext.risk_tolerance || 'moderate';

    if (urgency === 'high' || riskTolerance === 'low') {
      return 'high';
    } else if (urgency === 'low' && riskTolerance === 'high') {
      return 'low';
    }

    return 'medium';
  }

  /**
   * Assess technical complexity of decision
   * @param {object} context - Decision context
   * @returns {string} Technical complexity level
   */
  assessTechnicalComplexity(context) {
    const userContext = context.user_context || {};
    const technicalLevel = userContext.technical_level || 'beginner';
    const decisionType = context.decision_type || 'project_guidance';

    if (decisionType.includes('architecture') || decisionType.includes('technology')) {
      return technicalLevel === 'advanced' ? 'medium' : 'high';
    } else if (decisionType.includes('business') || decisionType.includes('strategy')) {
      return 'low';
    }

    return 'medium';
  }

  /**
   * Assess user readiness for decision
   * @param {object} context - Decision context
   * @returns {string} User readiness level
   */
  assessUserReadiness(context) {
    const userContext = context.user_context || {};
    const technicalLevel = userContext.technical_level || 'beginner';
    const complexity = this.assessTechnicalComplexity(context);

    if (technicalLevel === 'advanced' && complexity === 'low') {
      return 'high';
    } else if (technicalLevel === 'beginner' && complexity === 'high') {
      return 'low';
    }

    return 'medium';
  }

  /**
   * Extract next steps from text
   * @param {string} text - Response text
   * @returns {Array} Next steps
   */
  extractNextSteps(text) {
    const lines = text.split('\n').filter(line => line.trim());
    const nextSteps = [];

    let inNextStepsSection = false;
    for (const line of lines) {
      const lowerLine = line.toLowerCase();

      if (lowerLine.includes('next steps') || lowerLine.includes('next actions')) {
        inNextStepsSection = true;
        continue;
      }

      if (inNextStepsSection) {
        if (line.match(/^\d+\.|\*|-/) && line.trim().length > 5) {
          nextSteps.push(line.replace(/^\d+\.\s*|\*\s*|-\s*/g, '').trim());
        } else if (line.trim() === '' || line.match(/^#/)) {
          break;
        }
      }
    }

    return nextSteps.length > 0 ? nextSteps : null;
  }

  /**
   * Extract section from text lines
   * @param {Array} lines - Text lines
   * @param {string} section - Section to extract
   * @returns {string|null} Extracted section
   */
  extractSection(lines, section) {
    const sectionKeywords = {
      action: ['action', 'recommendation', 'decision'],
      rationale: ['rationale', 'reasoning', 'why'],
      outcomes: ['outcomes', 'results', 'benefits'],
      steps: ['steps', 'implementation', 'how'],
      metrics: ['metrics', 'measure', 'success'],
      risks: ['risks', 'challenges', 'concerns'],
      alternatives: ['alternatives', 'options', 'other']
    };

    const keywords = sectionKeywords[section] || [section];
    
    for (const line of lines) {
      const lowerLine = line.toLowerCase();
      if (keywords.some(keyword => lowerLine.includes(keyword))) {
        return line.replace(/^\d+\.\s*|\*\s*|-\s*/g, '').trim();
      }
    }
    
    return null;
  }

  /**
   * Extract confidence level from text
   * @param {string} text - Response text
   * @returns {number|null} Confidence level
   */
  extractConfidence(text) {
    const confidenceMatch = text.match(/confidence[:\s]*(\d+)/i);
    if (confidenceMatch) {
      return parseInt(confidenceMatch[1]);
    }
    
    // Look for percentage confidence
    const percentMatch = text.match(/(\d+)%\s*confident/i);
    if (percentMatch) {
      return Math.round(parseInt(percentMatch[1]) / 10);
    }
    
    return null;
  }

  /**
   * Store decision for future reference
   * @param {object} decision - Structured decision
   * @param {object} context - Decision context
   */
  async storeDecision(decision, context) {
    try {
      if (this.decisionHistory) {
        await this.decisionHistory.storeDecision(decision, context);
      }
    } catch (error) {
      console.warn('Failed to store decision:', error.message);
    }
  }

  /**
   * Generate fallback decision when LLM fails
   * @param {object} context - Original context
   * @param {Error} error - Error that occurred
   * @returns {object} Fallback decision
   */
  generateFallbackDecision(context, error) {
    const userContext = context.user_context || {};
    const technicalLevel = userContext.technical_level || 'beginner';

    return {
      decision_id: `guidant-fallback-${Date.now()}`,
      timestamp: new Date().toISOString(),
      decision_type: context.decision_type || 'project_guidance',
      user_context: userContext,

      // Guidant-specific fallback decision
      business_recommendation: 'Take a conservative, step-by-step approach while we gather more information',
      business_rationale: 'When AI guidance is temporarily unavailable, the safest approach is to proceed carefully with proven best practices',
      expected_business_outcomes: 'Continued progress with minimal risk while maintaining project momentum',
      implementation_approach: [
        'Review current project status and recent progress',
        'Identify the most critical next step that can be taken safely',
        'Proceed with small, reversible actions',
        'Document decisions for future review',
        'Seek additional input when AI guidance becomes available'
      ],
      success_measurement: [
        'Project continues moving forward',
        'No major setbacks or risks introduced',
        'Team remains engaged and productive',
        'Clear documentation of interim decisions'
      ],
      risk_management: 'Very low risk due to conservative approach and focus on reversible actions',
      alternative_approaches: [
        'Pause project until AI guidance is restored',
        'Seek external expert consultation',
        'Focus on planning and documentation activities'
      ],
      confidence_assessment: 6,
      user_communication: `We're experiencing a temporary issue with our AI guidance system. Don't worry - this is a normal part of technology. We recommend taking a careful, step-by-step approach while the system recovers. This actually gives us a good opportunity to review our progress and plan our next moves thoughtfully.`,
      next_steps: [
        'Review what we\'ve accomplished so far',
        'Identify safe next actions we can take',
        'Continue with planning and preparation activities',
        'Check back for AI guidance availability'
      ],

      // Metadata
      fallback_reason: error.message,
      context_used: context,
      guidant_metadata: {
        decision_category: 'fallback_guidance',
        business_impact: 'low',
        technical_complexity: 'low',
        user_readiness: 'high'
      }
    };
  }

  /**
   * Get performance baseline for context
   * @param {object} context - Decision context
   * @returns {object} Performance baseline
   */
  getPerformanceBaseline(context) {
    // Mock baseline - would integrate with actual performance data
    return {
      current_performance: context.current_performance || 75,
      target_performance: context.target_performance || 85,
      benchmark: context.benchmark || 'industry_average',
      measurement_period: '30_days'
    };
  }

  /**
   * Identify decision constraints
   * @param {object} context - Decision context
   * @returns {object} Decision constraints
   */
  identifyDecisionConstraints(context) {
    return {
      budget: context.budget_constraint || 'standard',
      timeline: context.timeline_constraint || 'flexible',
      resources: context.resource_constraint || 'current_team',
      technology: context.technology_constraint || 'existing_stack',
      compliance: context.compliance_constraint || 'standard_requirements'
    };
  }

  /**
   * Define success criteria
   * @param {object} context - Decision context
   * @returns {object} Success criteria
   */
  defineSuccessCriteria(context) {
    return {
      performance_improvement: context.target_improvement || '10%',
      timeline_adherence: 'within_planned_schedule',
      resource_efficiency: 'no_additional_resources',
      quality_maintenance: 'maintain_or_improve',
      stakeholder_satisfaction: 'positive_feedback'
    };
  }

  /**
   * Get supported purposes
   * @returns {Array} Supported purposes
   */
  getSupportedPurposes() {
    return this.supportedPurposes;
  }

  /**
   * Get purpose description
   * @param {string} purpose - Purpose name
   * @returns {string} Purpose description
   */
  getPurposeDescription(purpose) {
    const descriptions = {
      'project_guidance_decision': 'Guide non-technical users through complex project decisions',
      'technology_recommendation_decision': 'Recommend appropriate technologies for business needs',
      'feature_prioritization_decision': 'Help prioritize features based on business value',
      'business_strategy_decision': 'Make strategic business decisions for project success',
      'user_experience_decision': 'Optimize user experience and interface decisions',
      'implementation_approach_decision': 'Choose the best implementation approach for user capabilities',
      'resource_planning_decision': 'Plan resources effectively within business constraints',
      'risk_assessment_decision': 'Assess and mitigate project risks for business continuity',
      'timeline_optimization_decision': 'Optimize project timelines for realistic delivery',
      'quality_assurance_decision': 'Ensure quality standards align with business objectives',
      'stakeholder_communication_decision': 'Guide effective stakeholder communication strategies',
      'project_milestone_decision': 'Define and manage project milestones effectively',
      'vendor_selection_decision': 'Select vendors and partners that align with business needs',
      'architecture_guidance_decision': 'Provide architecture guidance appropriate for technical level',
      'performance_optimization_decision': 'Optimize performance while maintaining business focus',
      'cost_benefit_analysis_decision': 'Analyze costs and benefits for informed business decisions'
    };

    return descriptions[purpose] || purpose;
  }

  /**
   * Validate sampling request
   * @param {string} purpose - Sampling purpose
   * @param {object} context - Request context
   * @param {object} options - Sampling options
   * @returns {object} Validation result
   */
  validateRequest(purpose, context, options) {
    if (!this.supportedPurposes.includes(purpose)) {
      return {
        valid: false,
        error: `Unsupported decision purpose: ${purpose}`
      };
    }

    if (!context || typeof context !== 'object') {
      return {
        valid: false,
        error: 'Context must be a valid object'
      };
    }

    return { valid: true };
  }
}

export default IntelligentDecisionSampler;
