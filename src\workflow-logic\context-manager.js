/**
 * Context Manager for Guidant
 * Provides orientation features and context management for users
 */

import path from 'path';
import { getSessionRecoveryManager } from './session-recovery-manager.js';
import { getUserPreferenceManager } from './user-preference-manager.js';

/**
 * Context Manager
 * Provides orientation features and context management for users
 */
export class ContextManager {
  constructor(projectRoot = process.cwd()) {
    this.projectRoot = projectRoot;
    this.sessionManager = getSessionRecoveryManager(projectRoot);
    this.preferenceManager = getUserPreferenceManager(projectRoot);
  }

  /**
   * Get "Where am I?" orientation information
   * @param {string} sessionId - Session ID
   * @returns {Promise<Object|null>} - Orientation information or null if not found
   */
  async getOrientation(sessionId) {
    return this.sessionManager.getOrientation(sessionId);
  }

  /**
   * Generate orientation message
   * @param {string} sessionId - Session ID
   * @returns {Promise<string|null>} - Orientation message or null if failed
   */
  async generateOrientationMessage(sessionId) {
    const orientation = await this.getOrientation(sessionId);
    if (!orientation) {
      return null;
    }

    const { projectName, currentPhase, currentTask, progress, lastAction, nextAction } = orientation;

    let message = `You're working on ${projectName || 'your project'}`;
    
    if (currentPhase) {
      message += ` in the ${currentPhase} phase`;
    }
    
    if (currentTask) {
      message += `. Current task: ${currentTask}`;
    }
    
    if (typeof progress === 'number') {
      message += `. Progress: ${Math.round(progress * 100)}%`;
    }
    
    if (lastAction) {
      message += `\nLast action: ${lastAction}`;
    }
    
    if (nextAction) {
      message += `\nNext action: ${nextAction}`;
    }
    
    return message;
  }

  /**
   * Update orientation message
   * @param {string} sessionId - Session ID
   * @param {Object} context - Context information
   * @returns {Promise<boolean>} - True if successful
   */
  async updateOrientation(sessionId, context) {
    const { projectName, currentPhase, currentTask, progress, lastAction, nextAction } = context;
    
    let orientationMessage = `You're working on ${projectName || 'your project'}`;
    
    if (currentPhase) {
      orientationMessage += ` in the ${currentPhase} phase`;
    }
    
    if (currentTask) {
      orientationMessage += `. Current task: ${currentTask}`;
    }
    
    if (typeof progress === 'number') {
      orientationMessage += `. Progress: ${Math.round(progress * 100)}%`;
    }
    
    if (lastAction) {
      orientationMessage += `\nLast action: ${lastAction}`;
    }
    
    if (nextAction) {
      orientationMessage += `\nNext action: ${nextAction}`;
    }
    
    return this.sessionManager.updateOrientation(sessionId, orientationMessage);
  }

  /**
   * Generate recovery prompt for returning user
   * @param {string} sessionId - Session ID
   * @returns {Promise<string|null>} - Recovery prompt or null if failed
   */
  async generateRecoveryPrompt(sessionId) {
    const recoveryInfo = await this.sessionManager.getRecoveryInfo(sessionId);
    if (!recoveryInfo) {
      return null;
    }

    const { projectContext, workflowState, recoveryData, lastActive } = recoveryInfo;
    
    // If there's an existing recovery prompt, use it
    if (recoveryData.recoveryPrompt) {
      return recoveryData.recoveryPrompt;
    }
    
    // Calculate time away
    const timeAway = this.getTimeAway(lastActive);
    
    let prompt = `Welcome back to ${projectContext.projectName || 'your project'}! `;
    
    if (timeAway) {
      prompt += `It's been ${timeAway} since your last session. `;
    }
    
    prompt += `Here's where we left off:\n`;
    
    if (projectContext.currentPhase) {
      prompt += `- You're in the ${projectContext.currentPhase} phase\n`;
    }
    
    if (workflowState.currentTask) {
      prompt += `- Current task: ${workflowState.currentTask}\n`;
    }
    
    if (projectContext.lastAction) {
      prompt += `- Last action: ${projectContext.lastAction}\n`;
    }
    
    if (projectContext.nextAction) {
      prompt += `- Next step: ${projectContext.nextAction}\n`;
    }
    
    return prompt;
  }

  /**
   * Get time away description
   * @param {string} lastActiveIso - Last active timestamp (ISO string)
   * @returns {string} - Time away description
   */
  getTimeAway(lastActiveIso) {
    if (!lastActiveIso) {
      return '';
    }
    
    const lastActive = new Date(lastActiveIso);
    const now = new Date();
    const diffMs = now - lastActive;
    
    // Less than a minute
    if (diffMs < 60000) {
      return 'less than a minute';
    }
    
    // Less than an hour
    if (diffMs < 3600000) {
      const minutes = Math.floor(diffMs / 60000);
      return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'}`;
    }
    
    // Less than a day
    if (diffMs < 86400000) {
      const hours = Math.floor(diffMs / 3600000);
      return `${hours} ${hours === 1 ? 'hour' : 'hours'}`;
    }
    
    // Less than a week
    if (diffMs < 604800000) {
      const days = Math.floor(diffMs / 86400000);
      return `${days} ${days === 1 ? 'day' : 'days'}`;
    }
    
    // Less than a month
    if (diffMs < 2592000000) {
      const weeks = Math.floor(diffMs / 604800000);
      return `${weeks} ${weeks === 1 ? 'week' : 'weeks'}`;
    }
    
    // More than a month
    const months = Math.floor(diffMs / 2592000000);
    return `${months} ${months === 1 ? 'month' : 'months'}`;
  }

  /**
   * Prepare context for AI agent handoff
   * @param {string} sessionId - Session ID
   * @param {string} targetAgentId - Target AI agent ID
   * @returns {Promise<Object|null>} - Handoff context or null if failed
   */
  async prepareAgentHandoff(sessionId, targetAgentId) {
    try {
      const session = await this.sessionManager.getSession(sessionId);
      if (!session) {
        return null;
      }
      
      // Get user preferences
      const preferences = await this.preferenceManager.getUserPreferences();
      
      // Create handoff context
      const handoffContext = {
        sessionId,
        projectContext: session.projectContext,
        workflowState: session.workflowState,
        userContext: {
          orientation: session.userContext.currentOrientation,
          lastDecision: session.userContext.lastDecision,
          recentDecisions: session.userContext.decisionHistory.slice(-3) // Last 3 decisions
        },
        userPreferences: {
          communication: preferences.preferences.communication,
          // Include other relevant preferences
        },
        sourceAgentId: session.agentId,
        targetAgentId,
        handoffTimestamp: new Date().toISOString()
      };
      
      // Update session with handoff information
      await this.sessionManager.updateSession(sessionId, session => ({
        ...session,
        projectContext: {
          ...session.projectContext,
          lastAction: `Handoff to agent ${targetAgentId}`
        },
        agentId: targetAgentId
      }));
      
      return handoffContext;
    } catch (error) {
      console.error('Failed to prepare agent handoff:', error);
      return null;
    }
  }

  /**
   * Receive context from another AI agent
   * @param {Object} handoffContext - Handoff context
   * @returns {Promise<string|null>} - New session ID or null if failed
   */
  async receiveAgentHandoff(handoffContext) {
    try {
      const { sessionId, projectContext, workflowState, userContext, sourceAgentId, targetAgentId } = handoffContext;
      
      // Check if this agent is the intended target
      if (targetAgentId && targetAgentId !== 'current') {
        console.warn(`Handoff intended for agent ${targetAgentId}`);
      }
      
      // Check if session exists
      const existingSession = await this.sessionManager.getSession(sessionId);
      
      if (existingSession) {
        // Update existing session
        await this.sessionManager.updateSession(sessionId, session => ({
          ...session,
          lastActive: new Date().toISOString(),
          agentId: 'current', // Current agent
          projectContext: {
            ...projectContext,
            lastAction: `Handoff received from agent ${sourceAgentId}`
          },
          workflowState,
          userContext: {
            ...session.userContext,
            currentOrientation: userContext.orientation || session.userContext.currentOrientation
          },
          recoveryData: {
            ...session.recoveryData,
            recoveryPrompt: `Continuing from where agent ${sourceAgentId} left off.`
          }
        }));
        
        return sessionId;
      } else {
        // Create new session
        const newSession = await this.sessionManager.createSession({
          sessionId,
          agentId: 'current',
          agentName: 'Current Agent',
          projectName: projectContext.projectName,
          currentPhase: projectContext.currentPhase,
          currentTask: workflowState.currentTask,
          currentOrientation: userContext.orientation
        });
        
        return newSession.sessionId;
      }
    } catch (error) {
      console.error('Failed to receive agent handoff:', error);
      return null;
    }
  }

  /**
   * Get visual progress for a session
   * @param {string} sessionId - Session ID
   * @returns {Promise<Object|null>} - Visual progress information or null if not found
   */
  async getVisualProgress(sessionId) {
    try {
      return this.sessionManager.getVisualProgress(sessionId);
    } catch (error) {
      console.error('Failed to get visual progress:', error);
      return null;
    }
  }

  /**
   * Update visual progress for a session
   * @param {string} sessionId - Session ID
   * @param {Object} visualState - Visual state information
   * @returns {Promise<boolean>} - True if successful
   */
  async updateVisualProgress(sessionId, visualState) {
    try {
      return this.sessionManager.updateVisualProgress(sessionId, visualState);
    } catch (error) {
      console.error('Failed to update visual progress:', error);
      return false;
    }
  }
}

/**
 * Create a singleton instance
 */
let contextManagerInstance = null;

/**
 * Get the context manager instance
 * @param {string} projectRoot - Project root directory
 * @returns {ContextManager} - Context manager instance
 */
export function getContextManager(projectRoot = process.cwd()) {
  if (!contextManagerInstance) {
    contextManagerInstance = new ContextManager(projectRoot);
  }
  return contextManagerInstance;
} 