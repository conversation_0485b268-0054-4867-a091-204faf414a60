# MCP Primitive Mapping Analysis

## Current Tool Inventory → MCP Primitive Classification

### 🔍 Analysis Methodology

Each tool analyzed against MCP control paradigms:
- **Resources** (Application-Controlled): Data exposure, read operations
- **Tools** (Model-Controlled): Actions, state changes, executable functions
- **Prompts** (User-Controlled): Templates, guided workflows, reusable patterns
- **Sampling** (Server-Controlled): LLM completion requests, intelligent decisions

---

## 📊 RESOURCES (Application-Controlled Data Exposure)

### Current Tools → Resource URI Templates

| Current Tool | Function | New Resource URI | MIME Type |
|-------------|----------|------------------|-----------|
| `guidant_get_project_state` | Project state data | `guidant://project/{id}/state` | `application/json` |
| `guidant_get_current_task` | Current task info | `guidant://project/{id}/current-task` | `application/json` |
| `guidant_get_session_state` | Session information | `guidant://session/{id}/state` | `application/json` |
| `guidant_analyze_deliverable` | Deliverable analysis | `guidant://deliverable/{id}/analysis` | `application/json` |
| `guidant_analyze_phase` | Phase analysis data | `guidant://project/{id}/phase/{phase}/analysis` | `application/json` |
| `guidant_extract_insights` | Project insights | `guidant://project/{id}/insights` | `application/json` |
| `guidant_get_workflow_metrics` | Workflow performance | `guidant://workflow/{id}/metrics` | `application/json` |
| `guidant_get_decision_options` | Decision options | `guidant://project/{id}/decisions/options` | `application/json` |

### Resource Templates Design

```javascript
// Project Resources
{
  uriTemplate: "guidant://project/{projectId}/state",
  name: "Project State",
  description: "Current project state and configuration",
  mimeType: "application/json"
}

{
  uriTemplate: "guidant://project/{projectId}/phase/{phase}/deliverables",
  name: "Phase Deliverables",
  description: "Deliverables for specific project phase",
  mimeType: "application/json"
}

// Analytics Resources
{
  uriTemplate: "guidant://analytics/{projectId}/insights/{timeframe}",
  name: "Project Insights",
  description: "Analytics insights for specified timeframe",
  mimeType: "application/json"
}

// Workflow Resources
{
  uriTemplate: "guidant://workflow/{workflowId}/execution/{executionId}",
  name: "Workflow Execution",
  description: "Workflow execution status and results",
  mimeType: "application/json"
}
```

**Resource Count**: 8 tools → 4 Resource categories with URI templates

---

## 🔧 TOOLS (Model-Controlled Executable Functions)

### Consolidation Strategy by Domain

#### 1. **Workflow Executor** (Consolidates 15 tools)
**Current Tools**:
- `guidant_research_market`, `guidant_research_competitors`, `guidant_research_technology`
- `guidant_execute_research_workflow`, `guidant_execute_onboarding_workflow`
- `guidant_execute_development_workflow`, `guidant_validate_workflow`
- `guidant_create_workflow`, `guidant_update_workflow_step`
- `guidant_classify_project`, `guidant_adapt_workflow`
- `guidant_orchestrate_tools`, `guidant_execute_tool_chain`
- `guidant_monitor_workflow`, `guidant_optimize_workflow`

**Consolidated Tool**:
```javascript
{
  name: "guidant_execute_workflow",
  description: "Execute workflow operations with intelligent routing",
  parameters: {
    operation: {
      enum: ["research", "onboarding", "development", "analysis", "optimization"],
      description: "Type of workflow operation"
    },
    workflow_type: {
      enum: ["market", "competitive", "technical", "user_feedback"],
      description: "Specific workflow variant"
    },
    context: { type: "object" },
    parameters: { type: "object" }
  },
  annotations: {
    title: "Workflow Executor",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: true,
    openWorldHint: true
  }
}
```

#### 2. **Project Manager** (Consolidates 8 tools)
**Current Tools**:
- `guidant_init_project`, `guidant_save_deliverable`, `guidant_advance_phase`
- `guidant_report_progress`, `guidant_complete_onboarding`
- `guidant_make_decision`, `guidant_create_custom_decision`
- `guidant_add_custom_term_mapping`

**Consolidated Tool**:
```javascript
{
  name: "guidant_manage_project",
  description: "Project management operations with state control",
  parameters: {
    operation: {
      enum: ["init", "advance_phase", "save_deliverable", "report_progress", "make_decision"],
      description: "Project management operation"
    },
    project_data: { type: "object" },
    decision_data: { type: "object", optional: true }
  },
  annotations: {
    title: "Project Manager",
    readOnlyHint: false,
    destructiveHint: true,
    idempotentHint: false,
    openWorldHint: false
  }
}
```

#### 3. **Analytics Engine** (Consolidates 9 tools)
**Current Tools**:
- `guidant_analyze_agent_capabilities`, `guidant_discover_agents`
- `guidant_validate_quality`, `guidant_assess_deliverable_quality`
- `guidant_track_analytics`, `guidant_generate_insights`
- `guidant_measure_performance`, `guidant_analyze_trends`
- `guidant_benchmark_metrics`

**Consolidated Tool**:
```javascript
{
  name: "guidant_analyze_data",
  description: "Analytics and insight generation operations",
  parameters: {
    operation: {
      enum: ["capabilities", "quality", "performance", "trends", "insights"],
      description: "Type of analysis"
    },
    target: { type: "string", description: "Analysis target (agent, deliverable, workflow)" },
    metrics: { type: "array", items: { type: "string" } }
  },
  annotations: {
    title: "Analytics Engine",
    readOnlyHint: true,
    destructiveHint: false,
    idempotentHint: true,
    openWorldHint: false
  }
}
```

#### 4. **Context Manager** (Consolidates 6 tools)
**Current Tools**:
- `guidant_answer_question`, `guidant_recover_session`
- `guidant_manage_preferences`, `guidant_update_context`
- `guidant_sync_state`, `guidant_restore_workflow`

**Consolidated Tool**:
```javascript
{
  name: "guidant_manage_context",
  description: "Context and session management operations",
  parameters: {
    operation: {
      enum: ["answer_question", "recover_session", "manage_preferences", "sync_state"],
      description: "Context management operation"
    },
    context_data: { type: "object" },
    session_id: { type: "string", optional: true }
  },
  annotations: {
    title: "Context Manager",
    readOnlyHint: false,
    destructiveHint: false,
    idempotentHint: true,
    openWorldHint: false
  }
}
```

**Tool Count**: 38 tools → 4 Consolidated Tools

---

## 📝 PROMPTS (User-Controlled Templates)

### Template-Based Tools → MCP Prompts

| Current Tool | Function | New Prompt Template |
|-------------|----------|-------------------|
| Analysis workflows | Guided analysis | `analyze-project-phase` |
| Onboarding flows | User guidance | `onboarding-wizard` |
| Decision frameworks | Decision support | `decision-framework` |
| Quality assessments | Quality guidance | `quality-assessment` |

### Prompt Definitions

```javascript
// Analysis Prompt
{
  name: "analyze-project-phase",
  description: "Analyze project phase progress and provide recommendations",
  arguments: [
    { name: "phase", description: "Project phase to analyze", required: true },
    { name: "focus_area", description: "Specific analysis focus", required: false },
    { name: "deliverables", description: "Deliverables to include", required: false }
  ]
}

// Onboarding Prompt  
{
  name: "onboarding-wizard",
  description: "Guide user through project onboarding process",
  arguments: [
    { name: "project_type", description: "Type of project", required: true },
    { name: "experience_level", description: "User experience level", required: false }
  ]
}

// Decision Framework Prompt
{
  name: "decision-framework",
  description: "Structured decision-making framework",
  arguments: [
    { name: "decision_type", description: "Type of decision", required: true },
    { name: "criteria", description: "Decision criteria", required: false },
    { name: "constraints", description: "Decision constraints", required: false }
  ]
}
```

**Prompt Count**: 4 Prompt Templates

---

## 🧠 SAMPLING (Server-Controlled Intelligence)

### New Capabilities (Not in current tools)

```javascript
// Intelligent Decision Making
{
  purpose: "adaptive_workflow_decisions",
  description: "Request LLM completions for workflow adaptation decisions",
  use_cases: ["workflow optimization", "error recovery", "context adaptation"]
}

// Content Generation
{
  purpose: "intelligent_content_generation", 
  description: "Generate contextual content based on project state",
  use_cases: ["documentation", "recommendations", "analysis summaries"]
}

// Predictive Analysis
{
  purpose: "predictive_insights",
  description: "Generate predictive insights from project data",
  use_cases: ["risk assessment", "timeline prediction", "resource optimization"]
}
```

**Sampling Count**: 3 New Intelligent Capabilities

---

## 📈 CONSOLIDATION SUMMARY

| MCP Primitive | Current Tools | New Count | Reduction |
|---------------|---------------|-----------|-----------|
| **Resources** | 8 data tools | 4 URI templates | 50% |
| **Tools** | 38 action tools | 4 consolidated tools | 89% |
| **Prompts** | 2 template tools | 4 prompt templates | +100% |
| **Sampling** | 0 | 3 new capabilities | +300% |
| **TOTAL** | **48 tools** | **15 primitives** | **69% reduction** |

## 🎯 Implementation Priority

1. **Phase 1**: Resources (Week 1) - Lowest risk, high impact
2. **Phase 2**: Tools (Week 2-3) - Highest complexity, highest value  
3. **Phase 3**: Prompts (Week 3) - Medium complexity, user experience
4. **Phase 4**: Sampling (Week 4) - New capability, future-focused

## ✅ Validation Criteria

- [ ] All 48 current tools mapped to MCP primitives
- [ ] URI templates follow RFC 6570 standard
- [ ] Tool annotations align with MCP specification
- [ ] Backward compatibility maintained through primitive mapping
- [ ] Performance benchmarks meet success criteria
