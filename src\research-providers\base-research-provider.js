/**
 * Base Research Provider
 * Abstract base class for all research providers in the Guidant system
 * Follows the established AI provider pattern from src/ai-providers/
 */

import { EventEmitter } from 'events';
import { ErrorHandlingManager } from './error-handling-manager.js';
import { PerformanceOptimizer } from './performance-optimizer.js';

/**
 * Rate limiter for API requests
 */
export class RateLimiter {
  constructor(config = {}) {
    this.requestsPerMinute = config.requestsPerMinute || 60;
    this.burstLimit = config.burstLimit || 10;
    this.requests = [];
    this.queue = [];
    this.processing = false;
  }

  async execute(fn) {
    return new Promise((resolve, reject) => {
      this.queue.push({ fn, resolve, reject });
      this.processQueue();
    });
  }

  async processQueue() {
    if (this.processing || this.queue.length === 0) return;
    
    this.processing = true;
    
    while (this.queue.length > 0) {
      // Clean old requests (older than 1 minute)
      const now = Date.now();
      this.requests = this.requests.filter(time => now - time < 60000);
      
      // Check if we can make a request
      if (this.requests.length >= this.requestsPerMinute) {
        // Wait until we can make another request
        const oldestRequest = Math.min(...this.requests);
        const waitTime = 60000 - (now - oldestRequest);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        continue;
      }
      
      // Process next request
      const { fn, resolve, reject } = this.queue.shift();
      this.requests.push(now);
      
      try {
        const result = await fn();
        resolve(result);
      } catch (error) {
        reject(error);
      }
    }
    
    this.processing = false;
  }
}

/**
 * Research cache for avoiding duplicate requests
 */
export class ResearchCache {
  constructor(config = {}) {
    this.enabled = config.enabled !== false;
    this.ttl = config.ttl || 3600000; // 1 hour default
    this.cache = new Map();
  }

  get(key) {
    if (!this.enabled) return null;
    
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  set(key, data) {
    if (!this.enabled) return;
    
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  clear() {
    this.cache.clear();
  }

  generateKey(method, params) {
    return `${method}:${JSON.stringify(params)}`;
  }
}

/**
 * Base Research Provider class
 * All research providers should extend this class
 */
export class BaseResearchProvider extends EventEmitter {
  constructor(config = {}) {
    super();
    
    this.config = config;
    this.name = config.name || 'BaseProvider';
    this.apiKey = config.apiKey;
    this.baseUrl = config.baseUrl;
    this.timeout = config.timeout || 30000;
    
    // Initialize rate limiter and cache
    this.rateLimiter = new RateLimiter(config.rateLimit || {});
    this.cache = new ResearchCache(config.cache || {});

    // Initialize performance optimization and error handling
    this.errorHandler = new ErrorHandlingManager({
      circuitBreaker: config.circuitBreaker || {},
      retry: config.retry || {},
      fallback: {
        fallbackStrategies: {
          search: {
            type: 'cache',
            cache: this.cache
          },
          research: {
            type: 'degraded_response'
          }
        }
      }
    });

    this.performanceOptimizer = new PerformanceOptimizer({
      monitor: {
        targetResponseTime: config.targetResponseTime || 5000,
        qualityThreshold: config.qualityThreshold || 0.8
      },
      cache: config.intelligentCache || {},
      request: config.requestOptimization || {}
    });

    // Request statistics
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      cacheHits: 0,
      averageResponseTime: 0
    };

    // Forward performance warnings
    this.performanceOptimizer.on('performanceWarning', (warning) => {
      this.emit('performanceWarning', warning);
    });

    this.errorHandler.on('circuitBreakerStateChange', (event) => {
      this.emit('circuitBreakerStateChange', event);
    });
  }

  /**
   * Abstract method - must be implemented by subclasses
   * @param {string} query - Search query
   * @param {object} options - Search options
   * @returns {Promise<object>} Search results
   */
  async search(query, options = {}) {
    throw new Error(`search method must be implemented by ${this.constructor.name}`);
  }

  /**
   * Abstract method - must be implemented by subclasses
   * @returns {Promise<boolean>} Configuration validity
   */
  async validateConfig() {
    throw new Error(`validateConfig method must be implemented by ${this.constructor.name}`);
  }

  /**
   * Make HTTP request with rate limiting and caching
   * @param {string} endpoint - API endpoint
   * @param {object} params - Request parameters
   * @param {object} options - Request options
   * @returns {Promise<object>} Response data
   */
  async makeRequest(endpoint, params = {}, options = {}) {
    const operation = `${this.name}_${endpoint}`;
    const context = {
      endpoint,
      params,
      options,
      provider: this.name,
      query: params.query || params.q || 'unknown'
    };

    return await this.performanceOptimizer.executeOptimized(
      operation,
      async () => {
        return await this.errorHandler.executeWithErrorHandling(
          operation,
          async () => {
            return await this.rateLimiter.execute(async () => {
              return await this._executeRequest(endpoint, params, options);
            });
          },
          {
            ...context,
            cacheKey: this.cache.generateKey(endpoint, params),
            retryOptions: options.retry
          }
        );
      },
      context
    );
  }

  /**
   * Execute the actual HTTP request
   * @param {string} endpoint - API endpoint
   * @param {object} params - Request parameters
   * @param {object} options - Request options
   * @returns {Promise<object>} Response data
   */
  async _executeRequest(endpoint, params, options) {
    const url = `${this.baseUrl}${endpoint}`;
    const requestOptions = {
      method: options.method || 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        ...options.headers
      },
      body: JSON.stringify(params),
      signal: AbortSignal.timeout(this.timeout)
    };

    const response = await fetch(url, requestOptions);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return await response.json();
  }

  /**
   * Update request statistics
   * @param {boolean} success - Whether request was successful
   * @param {number} responseTime - Response time in milliseconds
   */
  updateStats(success, responseTime) {
    this.stats.totalRequests++;
    
    if (success) {
      this.stats.successfulRequests++;
    } else {
      this.stats.failedRequests++;
    }
    
    // Update average response time
    const totalTime = this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime;
    this.stats.averageResponseTime = totalTime / this.stats.totalRequests;
  }

  /**
   * Get provider statistics
   * @returns {object} Provider statistics
   */
  getStats() {
    return {
      provider: this.name,
      basic: {
        ...this.stats,
        successRate: this.stats.totalRequests > 0
          ? (this.stats.successfulRequests / this.stats.totalRequests) * 100
          : 0,
        cacheHitRate: this.stats.totalRequests > 0
          ? (this.stats.cacheHits / this.stats.totalRequests) * 100
          : 0
      },
      performance: this.performanceOptimizer.getStats(),
      errorHandling: this.errorHandler.getStats(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Reset provider statistics
   */
  resetStats() {
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      cacheHits: 0,
      averageResponseTime: 0
    };
  }

  /**
   * Test provider connectivity and configuration
   * @returns {Promise<object>} Test results
   */
  async testConnection() {
    try {
      await this.validateConfig();
      
      // Perform a simple test request
      const testResult = await this.search('test query', { maxResults: 1 });
      
      return {
        success: true,
        provider: this.name,
        message: 'Connection test successful',
        stats: this.getStats()
      };
    } catch (error) {
      return {
        success: false,
        provider: this.name,
        message: `Connection test failed: ${error.message}`,
        error: error.message
      };
    }
  }
}

export default BaseResearchProvider;
