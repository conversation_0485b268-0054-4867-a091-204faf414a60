/**
 * Research Workflow Integration Tests
 * 
 * End-to-end tests for research workflows including provider orchestration,
 * MCP tool integration, and business decision workflows.
 */

import { describe, it, expect, beforeEach, afterEach, mock, spyOn } from 'bun:test';
import { performResearchOperation, testResearchServices } from '../../src/ai-integration/ai-services-unified.js';
import { ResearchInformedChoicePresenter } from '../../src/business-decisions/research-informed-choice-presenter.js';
import { WorkflowExecutorTool } from '../../mcp-server/src/primitives/tools/workflow-executor.js';

describe('Research Workflow Integration', () => {
  let mockInfrastructure;

  beforeEach(() => {
    // Mock infrastructure for MCP tools
    mockInfrastructure = {
      messageHandler: {
        createSuccessResponse: (message, data) => ({ success: true, message, data }),
        createErrorResponse: (code, message, data) => ({ success: false, error: message, data })
      }
    };

    // Mock environment variables for testing
    process.env.TAVILY_API_KEY = 'test-tavily-key';
    process.env.FIRECRAWL_API_KEY = 'test-firecrawl-key';
  });

  afterEach(() => {
    mock.restore();
    delete process.env.TAVILY_API_KEY;
    delete process.env.FIRECRAWL_API_KEY;
  });

  describe('Research Services Integration', () => {
    it('should test all research services connectivity', async () => {
      // Mock successful responses for all providers
      const mockFetch = spyOn(global, 'fetch')
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ results: [] }) // Tavily
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ libraries: [{ context7CompatibleLibraryID: '/test/lib' }] }) // Context7
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true, data: { content: 'test' } }) // Firecrawl
        });

      const testResults = await testResearchServices();

      expect(testResults.totalProviders).toBe(3);
      expect(testResults.allHealthy).toBe(true);
      expect(testResults.results).toHaveLength(3);

      const tavilyResult = testResults.results.find(r => r.name === 'Tavily');
      const context7Result = testResults.results.find(r => r.name === 'Context7');
      const firecrawlResult = testResults.results.find(r => r.name === 'Firecrawl');

      expect(tavilyResult.success).toBe(true);
      expect(context7Result.success).toBe(true);
      expect(firecrawlResult.success).toBe(true);

      mockFetch.mockRestore();
    });

    it('should handle partial service failures gracefully', async () => {
      // Mock mixed success/failure responses
      const mockFetch = spyOn(global, 'fetch')
        .mockRejectedValueOnce(new Error('Tavily API error'))
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ libraries: [{ context7CompatibleLibraryID: '/test/lib' }] })
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 401,
          statusText: 'Unauthorized'
        });

      const testResults = await testResearchServices();

      expect(testResults.totalProviders).toBe(3);
      expect(testResults.allHealthy).toBe(false);
      
      const healthyCount = testResults.results.filter(r => r.success).length;
      expect(healthyCount).toBe(1); // Only Context7 should succeed

      mockFetch.mockRestore();
    });
  });

  describe('Research Operation Workflow', () => {
    it('should perform market research operation successfully', async () => {
      // Mock Tavily search response
      const mockTavilyResponse = {
        results: [
          {
            title: 'Restaurant Industry Trends 2024',
            url: 'https://example.com/trends',
            content: 'The restaurant industry is experiencing significant growth...',
            score: 0.9
          },
          {
            title: 'Food Delivery Market Analysis',
            url: 'https://example.com/delivery',
            content: 'Food delivery services have expanded rapidly...',
            score: 0.8
          }
        ]
      };

      // Mock AI synthesis response
      const mockAIResponse = {
        content: JSON.stringify({
          summary: 'Market research shows strong growth in restaurant discovery and food delivery sectors.',
          recommendations: [
            'Focus on mobile-first design for restaurant discovery',
            'Integrate with popular delivery platforms'
          ],
          confidence: 0.85,
          sources: ['https://example.com/trends', 'https://example.com/delivery']
        })
      };

      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockTavilyResponse)
      });

      // Mock AI operation for synthesis
      const mockPerformAI = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performAIOperation')
        .mockResolvedValue(mockAIResponse);

      const researchRequest = {
        type: 'market_research',
        query: 'restaurant discovery app market trends 2024',
        context: {
          domain: 'restaurant_discovery',
          businessModel: 'marketplace'
        },
        providers: ['tavily'],
        maxResults: 5
      };

      const result = await performResearchOperation(researchRequest);

      expect(result.query).toBe('restaurant discovery app market trends 2024');
      expect(result.type).toBe('market_research');
      expect(result.synthesis.summary).toContain('Market research shows strong growth');
      expect(result.synthesis.recommendations).toHaveLength(2);
      expect(result.metadata.sourcesCount).toBe(2);

      mockFetch.mockRestore();
      mockPerformAI.mockRestore();
    });

    it('should handle multi-provider research orchestration', async () => {
      // Mock responses for multiple providers
      const mockTavilyResponse = { results: [{ title: 'Tavily Result', content: 'Tavily content' }] };
      const mockContext7Response = { libraries: [{ context7CompatibleLibraryID: '/react/react' }] };
      const mockContext7DocsResponse = { content: 'React documentation content' };

      const mockFetch = spyOn(global, 'fetch')
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockTavilyResponse)
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockContext7Response)
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockContext7DocsResponse)
        });

      // Mock AI synthesis
      const mockPerformAI = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performAIOperation')
        .mockResolvedValue({
          content: JSON.stringify({
            summary: 'Combined research from web and technical documentation',
            recommendations: ['Use React for frontend', 'Consider market trends'],
            confidence: 0.8,
            sources: ['web', 'documentation']
          })
        });

      const researchRequest = {
        type: 'technical_research',
        query: 'React framework for restaurant app',
        providers: ['tavily', 'context7'],
        maxResults: 3
      };

      const result = await performResearchOperation(researchRequest);

      expect(result.providers).toEqual(['tavily', 'context7']);
      expect(result.synthesis.summary).toContain('Combined research');
      expect(result.metadata.providersUsed).toHaveLength(2);

      mockFetch.mockRestore();
      mockPerformAI.mockRestore();
    });
  });

  describe('Research-Informed Decision Workflow', () => {
    it('should generate research-informed framework selection', async () => {
      // Mock research operation
      const mockResearchResult = {
        synthesis: {
          summary: 'React shows strong market adoption and technical advantages',
          recommendations: [
            { title: 'React', confidence: 0.9, businessImpact: { revenue: 'High developer productivity' } },
            { title: 'Vue', confidence: 0.7, businessImpact: { revenue: 'Easier learning curve' } }
          ],
          sources: ['https://example.com/react-trends']
        },
        metadata: { confidenceLevel: 0.85 }
      };

      const mockPerformResearch = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performResearchOperation')
        .mockResolvedValue(mockResearchResult);

      const presenter = new ResearchInformedChoicePresenter();
      
      const projectContext = {
        domain: 'restaurant_discovery',
        businessModel: 'marketplace',
        expertise: { category: 'intermediate' },
        budget: { category: 'medium' }
      };

      const presentation = await presenter.getResearchInformedDecisionOptions(
        'framework_selection',
        projectContext,
        { enableResearch: true }
      );

      expect(presentation.title).toContain('framework');
      expect(presentation.researchBacking).toBeDefined();
      expect(presentation.researchBacking.confidence).toBe(0.85);
      expect(presentation.options.length).toBeGreaterThan(0);
      expect(presentation.presentationStyle).toBe('intermediate');

      // Check that options have research enhancements
      const reactOption = presentation.options.find(opt => opt.title === 'React');
      if (reactOption) {
        expect(reactOption.researchInsights).toBeDefined();
        expect(reactOption.confidence).toBeGreaterThan(0);
      }

      mockPerformResearch.mockRestore();
    });

    it('should adapt presentation for different expertise levels', async () => {
      const mockResearchResult = {
        synthesis: { summary: 'Research summary', recommendations: [] },
        metadata: { confidenceLevel: 0.8 }
      };

      const mockPerformResearch = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performResearchOperation')
        .mockResolvedValue(mockResearchResult);

      const presenter = new ResearchInformedChoicePresenter();

      // Test novice presentation
      const novicePresentation = await presenter.getResearchInformedDecisionOptions(
        'database_selection',
        { expertise: { category: 'novice' } },
        { enableResearch: true }
      );

      // Test advanced presentation
      const advancedPresentation = await presenter.getResearchInformedDecisionOptions(
        'database_selection',
        { expertise: { category: 'advanced' } },
        { enableResearch: true }
      );

      expect(novicePresentation.presentationStyle).toBe('novice');
      expect(advancedPresentation.presentationStyle).toBe('advanced');

      // Novice should have simpler language and focus areas
      expect(novicePresentation.adaptations.focusAreas).toContain('ease_of_use');
      expect(advancedPresentation.adaptations.focusAreas).toContain('technical_details');

      mockPerformResearch.mockRestore();
    });
  });

  describe('MCP Tool Integration', () => {
    it('should execute research workflow via MCP tool', async () => {
      const workflowExecutor = new WorkflowExecutorTool(mockInfrastructure);

      // Mock research operation
      const mockResearchResult = {
        synthesis: { summary: 'MCP research completed', recommendations: [] },
        metadata: { confidenceLevel: 0.8, sourcesCount: 3 }
      };

      const mockPerformResearch = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performResearchOperation')
        .mockResolvedValue(mockResearchResult);

      const args = {
        operation: 'research',
        workflow_type: 'market',
        context: {
          project_id: 'test-project',
          domain: 'ecommerce',
          phase: 'planning'
        },
        parameters: {
          query: 'ecommerce platform market analysis',
          providers: ['tavily'],
          max_results: 5
        }
      };

      const result = await workflowExecutor.execute(args);

      expect(result.success).toBe(true);
      expect(result.data.workflow_id).toContain('research-market');
      expect(result.data.status).toBe('completed');
      expect(result.data.results.synthesis.summary).toBe('MCP research completed');
      expect(result.data.metrics.sources_analyzed).toBe(3);

      mockPerformResearch.mockRestore();
    });

    it('should execute research services test via MCP tool', async () => {
      const workflowExecutor = new WorkflowExecutorTool(mockInfrastructure);

      // Mock test results
      const mockTestResults = {
        totalProviders: 3,
        allHealthy: true,
        results: [
          { name: 'Tavily', success: true, message: 'Connected successfully' },
          { name: 'Context7', success: true, message: 'Connected successfully' },
          { name: 'Firecrawl', success: true, message: 'Connected successfully' }
        ]
      };

      const mockTestServices = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'testResearchServices')
        .mockResolvedValue(mockTestResults);

      const args = {
        operation: 'test_research_services',
        parameters: {}
      };

      const result = await workflowExecutor.execute(args);

      expect(result.success).toBe(true);
      expect(result.data.summary.all_healthy).toBe(true);
      expect(result.data.summary.healthy_count).toBe(3);
      expect(result.data.summary.total_count).toBe(3);

      mockTestServices.mockRestore();
    });

    it('should handle MCP tool errors gracefully', async () => {
      const workflowExecutor = new WorkflowExecutorTool(mockInfrastructure);

      // Mock research operation failure
      const mockPerformResearch = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performResearchOperation')
        .mockRejectedValue(new Error('Research API unavailable'));

      const args = {
        operation: 'research',
        workflow_type: 'technical',
        context: { project_id: 'test-project' },
        parameters: { query: 'test query' }
      };

      const result = await workflowExecutor.execute(args);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Research workflow failed');
      expect(result.data.fallback_data).toBeDefined();

      mockPerformResearch.mockRestore();
    });
  });

  describe('Performance and Quality', () => {
    it('should complete research operations within time limits', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ results: [{ title: 'Fast result' }] })
      });

      const mockPerformAI = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performAIOperation')
        .mockResolvedValue({ content: JSON.stringify({ summary: 'Quick synthesis' }) });

      const startTime = Date.now();
      
      const result = await performResearchOperation({
        type: 'market_research',
        query: 'quick test',
        providers: ['tavily'],
        maxResults: 1
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
      expect(result.synthesis.summary).toBe('Quick synthesis');

      mockFetch.mockRestore();
      mockPerformAI.mockRestore();
    });

    it('should maintain research quality standards', async () => {
      const mockHighQualityResponse = {
        results: [
          {
            title: 'Comprehensive Market Analysis Report',
            content: 'Detailed analysis with multiple data points and insights...',
            score: 0.95,
            url: 'https://authoritative-source.com/report'
          }
        ]
      };

      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockHighQualityResponse)
      });

      const mockPerformAI = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performAIOperation')
        .mockResolvedValue({
          content: JSON.stringify({
            summary: 'High-quality research synthesis with actionable insights',
            confidence: 0.9,
            recommendations: ['Recommendation 1', 'Recommendation 2']
          })
        });

      const result = await performResearchOperation({
        type: 'market_research',
        query: 'comprehensive market analysis',
        providers: ['tavily']
      });

      expect(result.synthesis.confidence).toBeGreaterThan(0.8);
      expect(result.synthesis.recommendations.length).toBeGreaterThan(0);
      expect(result.synthesis.summary.length).toBeGreaterThan(50);

      mockFetch.mockRestore();
      mockPerformAI.mockRestore();
    });
  });
});
