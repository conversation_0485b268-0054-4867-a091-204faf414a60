/**
 * Error Handling Manager for Research Providers
 * 
 * Implements robust error handling with fallback mechanisms,
 * circuit breakers, and intelligent retry strategies.
 */

import { EventEmitter } from 'events';

/**
 * Circuit Breaker for API calls
 * Prevents cascading failures by temporarily disabling failing services
 */
export class CircuitBreaker extends EventEmitter {
  constructor(config = {}) {
    super();
    this.failureThreshold = config.failureThreshold || 5;
    this.resetTimeout = config.resetTimeout || 60000; // 1 minute
    this.monitoringPeriod = config.monitoringPeriod || 300000; // 5 minutes
    
    this.state = 'CLOSED'; // CLOSED, OPEN, HALF_OPEN
    this.failures = 0;
    this.lastFailureTime = null;
    this.successCount = 0;
    this.requestCount = 0;
    
    // Reset failure count periodically
    setInterval(() => this.resetFailureCount(), this.monitoringPeriod);
  }

  /**
   * Execute function with circuit breaker protection
   * @param {Function} fn - Function to execute
   * @returns {Promise} Function result or circuit breaker error
   */
  async execute(fn) {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.resetTimeout) {
        this.state = 'HALF_OPEN';
        this.emit('stateChange', { state: this.state, reason: 'timeout_reset' });
      } else {
        throw new Error('Circuit breaker is OPEN - service temporarily unavailable');
      }
    }

    this.requestCount++;
    
    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  /**
   * Handle successful execution
   */
  onSuccess() {
    this.successCount++;
    
    if (this.state === 'HALF_OPEN') {
      this.state = 'CLOSED';
      this.failures = 0;
      this.emit('stateChange', { state: this.state, reason: 'recovery_success' });
    }
  }

  /**
   * Handle failed execution
   */
  onFailure() {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
      this.emit('stateChange', { state: this.state, reason: 'failure_threshold_exceeded' });
    }
  }

  /**
   * Reset failure count (called periodically)
   */
  resetFailureCount() {
    if (this.state === 'CLOSED') {
      this.failures = Math.max(0, this.failures - 1);
    }
  }

  /**
   * Get circuit breaker status
   */
  getStatus() {
    return {
      state: this.state,
      failures: this.failures,
      successCount: this.successCount,
      requestCount: this.requestCount,
      failureRate: this.requestCount > 0 ? (this.failures / this.requestCount) * 100 : 0
    };
  }
}

/**
 * Retry Strategy Manager
 * Implements intelligent retry with exponential backoff
 */
export class RetryManager {
  constructor(config = {}) {
    this.maxRetries = config.maxRetries || 3;
    this.baseDelay = config.baseDelay || 1000; // 1 second
    this.maxDelay = config.maxDelay || 30000; // 30 seconds
    this.backoffMultiplier = config.backoffMultiplier || 2;
    this.jitterFactor = config.jitterFactor || 0.1;
  }

  /**
   * Execute function with retry logic
   * @param {Function} fn - Function to execute
   * @param {object} options - Retry options
   * @returns {Promise} Function result
   */
  async executeWithRetry(fn, options = {}) {
    const maxRetries = options.maxRetries || this.maxRetries;
    let lastError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        // Don't retry on certain error types
        if (this.isNonRetryableError(error) || attempt === maxRetries) {
          throw error;
        }

        // Calculate delay with exponential backoff and jitter
        const delay = this.calculateDelay(attempt);
        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  /**
   * Check if error should not be retried
   * @param {Error} error - Error to check
   * @returns {boolean} Whether error is non-retryable
   */
  isNonRetryableError(error) {
    const nonRetryablePatterns = [
      /unauthorized/i,
      /forbidden/i,
      /not found/i,
      /bad request/i,
      /invalid.*key/i,
      /quota.*exceeded/i
    ];

    return nonRetryablePatterns.some(pattern => 
      pattern.test(error.message) || pattern.test(error.code)
    );
  }

  /**
   * Calculate delay for retry attempt
   * @param {number} attempt - Attempt number
   * @returns {number} Delay in milliseconds
   */
  calculateDelay(attempt) {
    const exponentialDelay = this.baseDelay * Math.pow(this.backoffMultiplier, attempt);
    const jitter = exponentialDelay * this.jitterFactor * Math.random();
    const totalDelay = exponentialDelay + jitter;
    
    return Math.min(totalDelay, this.maxDelay);
  }

  /**
   * Sleep for specified duration
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise} Sleep promise
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Fallback Manager
 * Manages fallback strategies when primary services fail
 */
export class FallbackManager {
  constructor(config = {}) {
    this.fallbackStrategies = config.fallbackStrategies || {};
    this.fallbackTimeout = config.fallbackTimeout || 10000; // 10 seconds
  }

  /**
   * Execute with fallback strategies
   * @param {string} operation - Operation name
   * @param {Function} primaryFn - Primary function
   * @param {object} context - Execution context
   * @returns {Promise} Result or fallback result
   */
  async executeWithFallback(operation, primaryFn, context = {}) {
    try {
      return await primaryFn();
    } catch (primaryError) {
      console.warn(`Primary operation failed for ${operation}:`, primaryError.message);
      
      const fallbackStrategy = this.fallbackStrategies[operation];
      if (!fallbackStrategy) {
        throw primaryError;
      }

      try {
        return await this.executeFallbackStrategy(fallbackStrategy, context, primaryError);
      } catch (fallbackError) {
        console.error(`Fallback failed for ${operation}:`, fallbackError.message);
        throw new Error(`Both primary and fallback failed: ${primaryError.message}`);
      }
    }
  }

  /**
   * Execute fallback strategy
   * @param {object} strategy - Fallback strategy
   * @param {object} context - Execution context
   * @param {Error} primaryError - Primary error
   * @returns {Promise} Fallback result
   */
  async executeFallbackStrategy(strategy, context, primaryError) {
    switch (strategy.type) {
      case 'cache':
        return this.executeCacheFallback(strategy, context);
      
      case 'alternative_provider':
        return this.executeAlternativeProvider(strategy, context);
      
      case 'degraded_response':
        return this.executeDegradedResponse(strategy, context, primaryError);
      
      case 'static_response':
        return this.executeStaticResponse(strategy, context);
      
      default:
        throw new Error(`Unknown fallback strategy: ${strategy.type}`);
    }
  }

  /**
   * Execute cache fallback
   */
  async executeCacheFallback(strategy, context) {
    if (strategy.cache && typeof strategy.cache.get === 'function') {
      const cached = strategy.cache.get(context.cacheKey);
      if (cached) {
        return {
          ...cached,
          metadata: {
            ...cached.metadata,
            source: 'cache_fallback',
            timestamp: new Date().toISOString()
          }
        };
      }
    }
    throw new Error('Cache fallback failed - no cached data available');
  }

  /**
   * Execute alternative provider fallback
   */
  async executeAlternativeProvider(strategy, context) {
    if (strategy.alternativeProvider && typeof strategy.alternativeProvider.execute === 'function') {
      return await strategy.alternativeProvider.execute(context);
    }
    throw new Error('Alternative provider fallback failed - no provider available');
  }

  /**
   * Execute degraded response fallback
   */
  async executeDegradedResponse(strategy, context, primaryError) {
    return {
      query: context.query || 'unknown',
      results: [],
      error: primaryError.message,
      metadata: {
        source: 'degraded_fallback',
        timestamp: new Date().toISOString(),
        degradationReason: primaryError.message
      }
    };
  }

  /**
   * Execute static response fallback
   */
  async executeStaticResponse(strategy, context) {
    return {
      ...strategy.staticResponse,
      metadata: {
        ...strategy.staticResponse.metadata,
        source: 'static_fallback',
        timestamp: new Date().toISOString()
      }
    };
  }
}

/**
 * Error Handling Manager
 * Coordinates circuit breakers, retries, and fallbacks
 */
export class ErrorHandlingManager extends EventEmitter {
  constructor(config = {}) {
    super();
    this.circuitBreaker = new CircuitBreaker(config.circuitBreaker);
    this.retryManager = new RetryManager(config.retry);
    this.fallbackManager = new FallbackManager(config.fallback);
    
    // Forward circuit breaker events
    this.circuitBreaker.on('stateChange', (event) => {
      this.emit('circuitBreakerStateChange', event);
    });
  }

  /**
   * Execute operation with full error handling
   * @param {string} operation - Operation name
   * @param {Function} fn - Function to execute
   * @param {object} context - Execution context
   * @returns {Promise} Operation result
   */
  async executeWithErrorHandling(operation, fn, context = {}) {
    return await this.fallbackManager.executeWithFallback(
      operation,
      async () => {
        return await this.circuitBreaker.execute(async () => {
          return await this.retryManager.executeWithRetry(fn, context.retryOptions);
        });
      },
      context
    );
  }

  /**
   * Get error handling statistics
   */
  getStats() {
    return {
      circuitBreaker: this.circuitBreaker.getStatus(),
      timestamp: new Date().toISOString()
    };
  }
}

export default ErrorHandlingManager;
