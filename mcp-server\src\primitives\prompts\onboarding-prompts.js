/**
 * Onboarding Prompts <PERSON><PERSON>
 * Handles MCP Prompt requests for onboarding-related templates
 */

/**
 * Onboarding Prompt Handler
 * Provides structured onboarding prompts for various user scenarios
 */
export class OnboardingPromptHandler {
  constructor(existingInfrastructure = {}) {
    this.userManager = existingInfrastructure.userManager;
    this.projectManager = existingInfrastructure.projectManager;
    
    this.templates = {
      'onboarding-wizard': {
        name: 'onboarding-wizard',
        description: 'Guide users through comprehensive project onboarding process',
        arguments: [
          { name: 'project_type', description: 'Type of project for onboarding', required: true },
          { name: 'experience_level', description: 'User experience level (beginner/intermediate/advanced)', required: false },
          { name: 'role', description: 'User role in the project', required: false },
          { name: 'focus_areas', description: 'Specific areas to focus on during onboarding', required: false }
        ]
      },
      'team-onboarding': {
        name: 'team-onboarding',
        description: 'Onboard new team members to existing projects and workflows',
        arguments: [
          { name: 'team_role', description: 'Role the new member will play in the team', required: true },
          { name: 'project_phase', description: 'Current phase of the project', required: false },
          { name: 'team_size', description: 'Current team size', required: false },
          { name: 'urgency', description: 'How quickly the member needs to be productive', required: false }
        ]
      },
      'tool-onboarding': {
        name: 'tool-onboarding',
        description: 'Guide users through tool setup and configuration',
        arguments: [
          { name: 'tool_category', description: 'Category of tools to onboard (development/analytics/collaboration)', required: true },
          { name: 'integration_level', description: 'Level of tool integration needed', required: false },
          { name: 'automation_preferences', description: 'User preferences for automation', required: false }
        ]
      },
      'process-onboarding': {
        name: 'process-onboarding',
        description: 'Introduce users to project processes and methodologies',
        arguments: [
          { name: 'methodology', description: 'Project methodology (agile/waterfall/hybrid)', required: true },
          { name: 'process_maturity', description: 'Organization process maturity level', required: false },
          { name: 'compliance_requirements', description: 'Any compliance or regulatory requirements', required: false }
        ]
      }
    };
  }

  /**
   * Get prompt templates for this handler
   * @returns {Array} Array of prompt templates
   */
  getPromptTemplates() {
    return Object.values(this.templates);
  }

  /**
   * Handle prompt get request
   * @param {string} name - Prompt name
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async handlePromptGet(name, args = {}) {
    switch (name) {
      case 'onboarding-wizard':
        return await this.generateOnboardingWizardPrompt(args);
      case 'team-onboarding':
        return await this.generateTeamOnboardingPrompt(args);
      case 'tool-onboarding':
        return await this.generateToolOnboardingPrompt(args);
      case 'process-onboarding':
        return await this.generateProcessOnboardingPrompt(args);
      default:
        throw new Error(`Unknown onboarding prompt: ${name}`);
    }
  }

  /**
   * Generate onboarding wizard prompt
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async generateOnboardingWizardPrompt(args) {
    const { 
      project_type, 
      experience_level = 'intermediate', 
      role = 'team member', 
      focus_areas = 'setup, workflow, tools' 
    } = args;

    const promptContent = `# Project Onboarding Wizard: ${project_type}

## Welcome to Your ${project_type} Project!

This comprehensive onboarding guide will help you get started as a **${role}** with **${experience_level}** experience level, focusing on **${focus_areas}**.

## Onboarding Roadmap

### Phase 1: Project Understanding (Day 1)
**Objective**: Gain comprehensive understanding of the project context and goals

#### Project Overview
- **Project Type**: ${project_type}
- **Your Role**: ${role}
- **Experience Level**: ${experience_level}
- **Focus Areas**: ${focus_areas}

#### Key Questions to Explore:
1. **Project Purpose**: What problem does this ${project_type} project solve?
2. **Success Criteria**: How will project success be measured?
3. **Timeline**: What are the key milestones and deadlines?
4. **Stakeholders**: Who are the key people you'll work with?
5. **Scope**: What's included and excluded from this project?
6. **Constraints**: What limitations or requirements must be considered?

#### Learning Activities:
- Review project documentation and requirements
- Understand business context and user needs
- Familiarize yourself with project goals and success metrics
- Identify key stakeholders and communication channels

### Phase 2: Environment Setup (Days 2-3)
**Objective**: Set up your development and collaboration environment

#### Technical Setup Checklist:
${project_type === 'software development' ? `
- [ ] Development environment configuration
- [ ] Code repository access and setup
- [ ] Build and deployment tools installation
- [ ] Testing framework setup
- [ ] Code quality tools configuration` : ''}
${project_type === 'data analysis' ? `
- [ ] Data analysis tools installation
- [ ] Database and data source access
- [ ] Visualization tools setup
- [ ] Statistical software configuration
- [ ] Data quality and validation tools` : ''}
${project_type === 'design' ? `
- [ ] Design software and tools setup
- [ ] Asset libraries and resources access
- [ ] Collaboration and review tools
- [ ] Brand guidelines and style guides
- [ ] Prototyping and testing tools` : ''}
- [ ] Communication tools setup (Slack, Teams, etc.)
- [ ] Project management tools access
- [ ] Documentation and knowledge base access
- [ ] Security and access permissions configured

#### Collaboration Setup:
- Join relevant communication channels
- Set up calendar and meeting access
- Configure notification preferences
- Establish working hours and availability
- Set up status reporting mechanisms

### Phase 3: Workflow Integration (Days 4-5)
**Objective**: Understand and integrate into team workflows

#### Workflow Understanding:
- **Development Process**: How does work flow from idea to completion?
- **Quality Assurance**: What quality gates and review processes exist?
- **Communication Patterns**: How does the team communicate and collaborate?
- **Decision Making**: How are decisions made and documented?
- **Problem Resolution**: How are issues identified and resolved?

#### ${experience_level} Level Expectations:
${experience_level === 'beginner' ? `
**As a beginner, focus on:**
- Learning fundamental concepts and terminology
- Understanding basic workflows and processes
- Building foundational skills through guided practice
- Asking questions and seeking mentorship
- Starting with simple, well-defined tasks` : ''}
${experience_level === 'intermediate' ? `
**As an intermediate contributor, focus on:**
- Understanding complex workflows and dependencies
- Taking ownership of moderate-complexity tasks
- Contributing to process improvements
- Mentoring newer team members
- Balancing independence with collaboration` : ''}
${experience_level === 'advanced' ? `
**As an advanced contributor, focus on:**
- Leading complex initiatives and workflows
- Architecting solutions and making technical decisions
- Mentoring team members and sharing expertise
- Driving process improvements and best practices
- Taking ownership of critical project components` : ''}

### Phase 4: First Contributions (Week 2)
**Objective**: Make meaningful contributions while building confidence

#### Starter Tasks (Choose based on ${role} role):
- **Low Risk**: Tasks with clear requirements and minimal dependencies
- **High Learning**: Tasks that expose you to key project components
- **Collaborative**: Tasks that involve working with team members
- **Visible Impact**: Tasks with clear, measurable outcomes

#### Success Metrics for First Week:
- [ ] Complete environment setup and access verification
- [ ] Understand project structure and key components
- [ ] Successfully complete first assigned task
- [ ] Establish working relationships with team members
- [ ] Demonstrate understanding of basic workflows
- [ ] Contribute to team meetings and discussions

## Focus Area Deep Dives

${focus_areas.includes('setup') ? `
### Setup Mastery
- **Environment Configuration**: Optimize your development setup
- **Tool Proficiency**: Become efficient with essential tools
- **Automation**: Set up automated workflows and shortcuts
- **Troubleshooting**: Learn common setup issues and solutions
` : ''}

${focus_areas.includes('workflow') ? `
### Workflow Excellence
- **Process Understanding**: Master team workflows and procedures
- **Collaboration Skills**: Effective communication and teamwork
- **Quality Practices**: Understand and apply quality standards
- **Continuous Improvement**: Contribute to workflow optimization
` : ''}

${focus_areas.includes('tools') ? `
### Tool Mastery
- **Core Tools**: Become proficient with essential project tools
- **Advanced Features**: Explore advanced tool capabilities
- **Integration**: Understand how tools work together
- **Efficiency**: Develop efficient tool usage patterns
` : ''}

## Onboarding Checklist

### Week 1: Foundation
- [ ] Project overview and context understanding
- [ ] Environment setup and tool configuration
- [ ] Team introductions and relationship building
- [ ] Basic workflow and process familiarization
- [ ] First task assignment and completion

### Week 2: Integration
- [ ] Deeper workflow understanding and practice
- [ ] Increased task complexity and responsibility
- [ ] Active participation in team activities
- [ ] Feedback collection and adjustment
- [ ] Confidence building and skill development

### Week 3: Contribution
- [ ] Independent task execution
- [ ] Proactive problem-solving and communication
- [ ] Contribution to team discussions and decisions
- [ ] Mentoring or helping other team members
- [ ] Process improvement suggestions

### Week 4: Mastery
- [ ] Full productivity and contribution
- [ ] Leadership in specific areas or tasks
- [ ] Knowledge sharing and documentation
- [ ] Onboarding feedback and recommendations
- [ ] Planning for continued growth and development

## Success Indicators
You'll know your onboarding is successful when:
- You can work independently on assigned tasks
- You understand how your work fits into the bigger picture
- You feel comfortable asking questions and seeking help
- You can contribute meaningfully to team discussions
- You're excited about the project and your role in it
- You have clear next steps for continued growth

## Getting Help
- **Immediate Questions**: Ask your assigned buddy or mentor
- **Technical Issues**: Use team technical support channels
- **Process Questions**: Consult team leads or project managers
- **Career Development**: Discuss with your manager or HR
- **General Support**: Use team general communication channels

Remember: Successful onboarding is a collaborative effort. Don't hesitate to ask questions, seek clarification, and request help when needed. Your success is the team's success!`;

    return {
      description: `Comprehensive onboarding wizard for ${project_type} project`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  /**
   * Generate team onboarding prompt
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async generateTeamOnboardingPrompt(args) {
    const { 
      team_role, 
      project_phase = 'development', 
      team_size = 'medium', 
      urgency = 'standard' 
    } = args;

    const promptContent = `# Team Onboarding: ${team_role}

## Team Integration Objective
Successfully integrate a new **${team_role}** into the team during the **${project_phase}** phase with **${urgency}** urgency.

## Team Context
- **New Role**: ${team_role}
- **Project Phase**: ${project_phase}
- **Team Size**: ${team_size}
- **Integration Urgency**: ${urgency}

## Integration Strategy

### Immediate Integration (Days 1-3)
${urgency === 'urgent' ? `
**Accelerated Integration Plan:**
- Immediate buddy assignment and intensive support
- Fast-track essential training and setup
- Priority access to critical resources and information
- Daily check-ins and rapid feedback loops
- Streamlined onboarding focusing on immediate needs` : `
**Standard Integration Plan:**
- Structured introduction to team members and roles
- Comprehensive overview of current project status
- Systematic setup of tools and access permissions
- Gradual introduction to team workflows and processes
- Regular check-ins and feedback sessions`}

### Role-Specific Integration for ${team_role}

${team_role.includes('developer') || team_role.includes('engineer') ? `
#### Technical Integration
- Code repository access and branch strategy explanation
- Development environment setup and configuration
- Code review process and quality standards
- Testing procedures and deployment workflows
- Architecture overview and technical documentation review

#### Collaboration Integration
- Pair programming sessions with experienced team members
- Code review participation and feedback processes
- Technical discussion forums and knowledge sharing
- Problem-solving approaches and escalation procedures
- Innovation and improvement contribution opportunities` : ''}

${team_role.includes('designer') ? `
#### Design Integration
- Design system and brand guidelines familiarization
- Design tool setup and asset library access
- User research and feedback integration processes
- Design review and approval workflows
- Collaboration with development and product teams

#### Creative Integration
- Design thinking and methodology alignment
- User experience standards and best practices
- Prototyping and testing procedures
- Design documentation and handoff processes
- Creative brainstorming and ideation sessions` : ''}

${team_role.includes('manager') || team_role.includes('lead') ? `
#### Leadership Integration
- Team dynamics and individual strengths assessment
- Current challenges and opportunities overview
- Decision-making processes and authority levels
- Stakeholder relationships and communication patterns
- Performance management and development approaches

#### Strategic Integration
- Project goals and success metrics understanding
- Resource allocation and planning responsibilities
- Risk management and mitigation strategies
- Process improvement and optimization opportunities
- Team development and growth planning` : ''}

### Team Dynamics Integration

#### Relationship Building
- One-on-one meetings with each team member
- Understanding individual working styles and preferences
- Learning team communication patterns and norms
- Identifying collaboration opportunities and synergies
- Building trust and establishing credibility

#### Cultural Integration
- Team values and principles understanding
- Communication styles and feedback culture
- Meeting patterns and decision-making processes
- Conflict resolution and problem-solving approaches
- Celebration and recognition practices

## ${project_phase} Phase Considerations

${project_phase === 'planning' ? `
**Planning Phase Integration:**
- Requirements gathering and analysis processes
- Stakeholder identification and engagement strategies
- Resource planning and allocation decisions
- Risk assessment and mitigation planning
- Timeline development and milestone definition` : ''}

${project_phase === 'development' ? `
**Development Phase Integration:**
- Active development workflows and sprint processes
- Quality assurance and testing procedures
- Progress tracking and reporting mechanisms
- Issue resolution and escalation processes
- Continuous integration and deployment practices` : ''}

${project_phase === 'testing' ? `
**Testing Phase Integration:**
- Testing strategies and quality standards
- Bug tracking and resolution processes
- User acceptance testing procedures
- Performance and security testing protocols
- Release preparation and deployment planning` : ''}

${project_phase === 'deployment' ? `
**Deployment Phase Integration:**
- Release management and deployment procedures
- Monitoring and alerting systems
- Incident response and support processes
- User training and documentation
- Post-deployment optimization and maintenance` : ''}

## Success Metrics

### Week 1 Goals
- [ ] Complete team introductions and relationship building
- [ ] Understand current project status and immediate priorities
- [ ] Set up necessary tools and access permissions
- [ ] Begin contributing to team activities and discussions
- [ ] Identify areas for immediate impact and contribution

### Week 2 Goals
- [ ] Take ownership of specific tasks or responsibilities
- [ ] Demonstrate understanding of team workflows and processes
- [ ] Provide valuable input to team decisions and discussions
- [ ] Build productive working relationships with key team members
- [ ] Show progress toward full productivity in the ${team_role} role

### Month 1 Goals
- [ ] Achieve full productivity and contribution in assigned role
- [ ] Become a trusted and valued team member
- [ ] Contribute to team improvements and optimizations
- [ ] Mentor or support other team members as appropriate
- [ ] Demonstrate leadership and initiative in relevant areas

## Integration Support

### Buddy System
- Assign an experienced team member as primary support
- Daily check-ins during first week
- Weekly check-ins during first month
- Open communication channel for questions and support
- Feedback and adjustment based on integration progress

### Team Support
- Regular team feedback on integration progress
- Adjustment of responsibilities based on capabilities
- Inclusion in all relevant team activities and decisions
- Recognition and celebration of early contributions
- Continuous support for growth and development

Focus on creating a welcoming, supportive environment that enables the new ${team_role} to quickly become a productive and valued team member while maintaining team cohesion and project momentum.`;

    return {
      description: `Team onboarding prompt for ${team_role} role`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  // Additional prompt generation methods would be implemented here...
  async generateToolOnboardingPrompt(args) {
    const { tool_category, integration_level = 'standard', automation_preferences = 'balanced' } = args;
    
    const promptContent = `# Tool Onboarding: ${tool_category}

## Tool Setup Objective
Configure and master **${tool_category}** tools with **${integration_level}** integration and **${automation_preferences}** automation.

## Tool Configuration Framework
[Detailed tool setup and configuration guide would be generated here based on the tool category]

## Success Criteria
- Efficient tool usage and proficiency
- Proper integration with existing workflows
- Optimal automation configuration
- Team collaboration enhancement

This is a placeholder for the full tool onboarding prompt implementation.`;

    return {
      description: `Tool onboarding prompt for ${tool_category}`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  async generateProcessOnboardingPrompt(args) {
    const { methodology, process_maturity = 'developing', compliance_requirements = 'standard' } = args;
    
    const promptContent = `# Process Onboarding: ${methodology}

## Process Integration Objective
Master **${methodology}** methodology with **${process_maturity}** maturity level and **${compliance_requirements}** compliance.

## Process Learning Framework
[Detailed process learning and integration guide would be generated here based on the methodology]

## Success Criteria
- Complete understanding of methodology principles
- Effective application in daily work
- Compliance with all requirements
- Contribution to process improvements

This is a placeholder for the full process onboarding prompt implementation.`;

    return {
      description: `Process onboarding prompt for ${methodology}`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  /**
   * Validate prompt arguments
   * @param {string} name - Prompt name
   * @param {object} args - Prompt arguments
   * @returns {object} Validation result
   */
  validatePrompt(name, args) {
    const template = this.templates[name];
    if (!template) {
      return { valid: false, error: `Unknown prompt: ${name}` };
    }

    const requiredArgs = template.arguments.filter(arg => arg.required);
    const missingArgs = requiredArgs.filter(arg => !args.hasOwnProperty(arg.name));

    if (missingArgs.length > 0) {
      return {
        valid: false,
        error: `Missing required arguments: ${missingArgs.map(arg => arg.name).join(', ')}`
      };
    }

    return { valid: true };
  }
}

export default OnboardingPromptHandler;
