/**
 * Analysis Prompts <PERSON><PERSON>
 * Handles MCP Prompt requests for analysis-related templates
 */

/**
 * Analysis Prompt Handler
 * Provides structured analysis prompts for various project analysis scenarios
 */
export class AnalysisPromptHandler {
  constructor(existingInfrastructure = {}) {
    this.projectManager = existingInfrastructure.projectManager;
    this.analyticsManager = existingInfrastructure.analyticsManager;
    
    this.templates = {
      'analyze-project-phase': {
        name: 'analyze-project-phase',
        description: 'Analyze project phase progress and provide comprehensive recommendations',
        arguments: [
          { name: 'phase', description: 'Project phase to analyze', required: true },
          { name: 'focus_area', description: 'Specific area to focus analysis on', required: false },
          { name: 'timeframe', description: 'Analysis timeframe (e.g., "last 30 days")', required: false },
          { name: 'project_id', description: 'Project identifier for context', required: false }
        ]
      },
      'analyze-deliverable-quality': {
        name: 'analyze-deliverable-quality',
        description: 'Comprehensive quality analysis of project deliverables',
        arguments: [
          { name: 'deliverable_type', description: 'Type of deliverable to analyze', required: true },
          { name: 'quality_criteria', description: 'Specific quality criteria to evaluate', required: false },
          { name: 'benchmark', description: 'Quality benchmark to compare against', required: false }
        ]
      },
      'analyze-performance-metrics': {
        name: 'analyze-performance-metrics',
        description: 'Detailed performance metrics analysis and optimization recommendations',
        arguments: [
          { name: 'metric_type', description: 'Type of performance metric to analyze', required: true },
          { name: 'baseline_period', description: 'Baseline period for comparison', required: false },
          { name: 'target_improvement', description: 'Target improvement percentage', required: false }
        ]
      },
      'analyze-team-productivity': {
        name: 'analyze-team-productivity',
        description: 'Team productivity analysis with actionable insights',
        arguments: [
          { name: 'team_size', description: 'Number of team members', required: false },
          { name: 'methodology', description: 'Development methodology used', required: false },
          { name: 'focus_metrics', description: 'Specific productivity metrics to focus on', required: false }
        ]
      }
    };
  }

  /**
   * Get prompt templates for this handler
   * @returns {Array} Array of prompt templates
   */
  getPromptTemplates() {
    return Object.values(this.templates);
  }

  /**
   * Handle prompt get request
   * @param {string} name - Prompt name
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async handlePromptGet(name, args = {}) {
    switch (name) {
      case 'analyze-project-phase':
        return await this.generateProjectPhaseAnalysisPrompt(args);
      case 'analyze-deliverable-quality':
        return await this.generateDeliverableQualityPrompt(args);
      case 'analyze-performance-metrics':
        return await this.generatePerformanceMetricsPrompt(args);
      case 'analyze-team-productivity':
        return await this.generateTeamProductivityPrompt(args);
      default:
        throw new Error(`Unknown analysis prompt: ${name}`);
    }
  }

  /**
   * Generate project phase analysis prompt
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async generateProjectPhaseAnalysisPrompt(args) {
    const { phase, focus_area, timeframe = 'current', project_id } = args;
    
    // Get project context if available
    let projectContext = {};
    if (project_id && this.projectManager) {
      try {
        projectContext = await this.projectManager.getProjectContext(project_id);
      } catch (error) {
        console.warn('Could not fetch project context:', error.message);
      }
    }

    const promptContent = `# Project Phase Analysis: ${phase}

## Analysis Objective
Conduct a comprehensive analysis of the **${phase}** phase${focus_area ? ` with specific focus on **${focus_area}**` : ''}.

## Project Context
${project_id ? `- **Project**: ${projectContext.name || project_id}` : '- **Project**: General analysis'}
${projectContext.methodology ? `- **Methodology**: ${projectContext.methodology}` : ''}
${projectContext.team_size ? `- **Team Size**: ${projectContext.team_size} members` : ''}
- **Analysis Timeframe**: ${timeframe}
- **Current Progress**: ${projectContext.progress || 'Unknown'}%

## Analysis Framework

### 1. Phase Status Assessment
- **Completion Status**: Evaluate current completion percentage
- **Milestone Progress**: Review achieved vs planned milestones
- **Timeline Adherence**: Assess schedule compliance
- **Resource Utilization**: Analyze resource allocation efficiency

### 2. Quality Metrics
- **Deliverable Quality**: Assess quality of phase outputs
- **Process Adherence**: Evaluate methodology compliance
- **Standards Compliance**: Check against quality standards
- **Technical Debt**: Identify accumulated technical debt

### 3. Performance Indicators
- **Velocity**: Measure development/delivery speed
- **Throughput**: Analyze work completion rates
- **Cycle Time**: Evaluate time from start to completion
- **Defect Rate**: Track quality issues and bugs

### 4. Risk Assessment
- **Current Risks**: Identify active risk factors
- **Risk Mitigation**: Evaluate mitigation effectiveness
- **Future Risks**: Anticipate upcoming challenges
- **Contingency Plans**: Review backup strategies

${focus_area ? `### 5. Focused Analysis: ${focus_area}
Provide detailed analysis specifically targeting ${focus_area}, including:
- Current state assessment
- Performance against benchmarks
- Improvement opportunities
- Specific recommendations` : ''}

## Analysis Questions
Please provide detailed responses to the following:

1. **Overall Phase Health**: How would you rate the overall health of the ${phase} phase (1-10)?
2. **Key Achievements**: What are the most significant accomplishments in this phase?
3. **Major Challenges**: What are the primary obstacles or challenges encountered?
4. **Resource Efficiency**: How effectively are resources being utilized?
5. **Quality Assessment**: What is the quality level of deliverables and processes?
6. **Timeline Performance**: Is the phase on track with planned timelines?
7. **Team Performance**: How is the team performing in this phase?
8. **Process Effectiveness**: Are current processes working effectively?

## Deliverable Requirements

### Analysis Report Structure
1. **Executive Summary** (2-3 paragraphs)
2. **Detailed Findings** (organized by analysis framework sections)
3. **Metrics and KPIs** (quantitative assessment)
4. **Risk Analysis** (current and future risks)
5. **Recommendations** (prioritized action items)
6. **Next Steps** (immediate and long-term actions)

### Recommendation Format
For each recommendation, provide:
- **Priority**: High/Medium/Low
- **Impact**: Expected improvement or benefit
- **Effort**: Required resources and time
- **Timeline**: Suggested implementation schedule
- **Success Criteria**: How to measure success

## Success Criteria
The analysis should result in:
- Clear understanding of phase status and performance
- Actionable recommendations for improvement
- Risk mitigation strategies
- Optimized resource allocation
- Enhanced team productivity
- Improved quality outcomes

Please conduct this analysis thoroughly and provide specific, actionable insights that will drive meaningful improvements in the ${phase} phase.`;

    return {
      description: `Comprehensive analysis prompt for ${phase} phase${focus_area ? ` focusing on ${focus_area}` : ''}`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  /**
   * Generate deliverable quality analysis prompt
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async generateDeliverableQualityPrompt(args) {
    const { deliverable_type, quality_criteria, benchmark = 'industry standard' } = args;

    const promptContent = `# Deliverable Quality Analysis: ${deliverable_type}

## Quality Assessment Objective
Conduct a comprehensive quality analysis of **${deliverable_type}** deliverables against **${benchmark}** benchmarks.

## Quality Framework

### 1. Content Quality
- **Accuracy**: Information correctness and factual validity
- **Completeness**: Coverage of all required elements
- **Clarity**: Clear communication and understanding
- **Relevance**: Alignment with objectives and requirements

### 2. Structural Quality
- **Organization**: Logical structure and flow
- **Consistency**: Uniform formatting and style
- **Navigation**: Easy access to information
- **Presentation**: Professional appearance and layout

### 3. Technical Quality
- **Standards Compliance**: Adherence to technical standards
- **Best Practices**: Implementation of industry best practices
- **Maintainability**: Ease of updates and modifications
- **Scalability**: Ability to handle growth and changes

### 4. Process Quality
- **Review Process**: Quality of review and approval process
- **Version Control**: Proper versioning and change management
- **Documentation**: Supporting documentation quality
- **Traceability**: Links to requirements and dependencies

${quality_criteria ? `### 5. Specific Criteria: ${quality_criteria}
Focus on evaluating: ${quality_criteria}` : ''}

## Assessment Questions
1. **Overall Quality Rating**: Rate the deliverable quality (1-10) against ${benchmark}
2. **Strengths**: What are the key quality strengths?
3. **Weaknesses**: What quality issues need attention?
4. **Compliance**: How well does it meet standards and requirements?
5. **User Experience**: How well does it serve its intended users?
6. **Improvement Potential**: What are the biggest improvement opportunities?

## Quality Metrics
Evaluate and score (1-10) each dimension:
- Content accuracy and completeness
- Structural organization and clarity
- Technical implementation quality
- Process adherence and documentation
- User satisfaction and usability
- Maintainability and sustainability

## Recommendations
Provide specific, actionable recommendations for:
- Immediate quality improvements
- Process enhancements
- Standards compliance
- Long-term quality strategy

Focus on practical, implementable solutions that will measurably improve deliverable quality.`;

    return {
      description: `Quality analysis prompt for ${deliverable_type} deliverables`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  /**
   * Generate performance metrics analysis prompt
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async generatePerformanceMetricsPrompt(args) {
    const { metric_type, baseline_period = 'last 30 days', target_improvement = '10%' } = args;

    const promptContent = `# Performance Metrics Analysis: ${metric_type}

## Performance Analysis Objective
Analyze **${metric_type}** performance metrics with baseline comparison from **${baseline_period}** and target improvement of **${target_improvement}**.

## Metrics Framework

### 1. Current Performance
- **Current Values**: Latest metric measurements
- **Trend Analysis**: Performance trends over time
- **Variance**: Consistency and stability of performance
- **Peak Performance**: Best performance periods

### 2. Baseline Comparison
- **Baseline Period**: ${baseline_period}
- **Performance Delta**: Change from baseline
- **Improvement Rate**: Rate of performance change
- **Benchmark Comparison**: Against industry standards

### 3. Performance Drivers
- **Contributing Factors**: What drives good performance
- **Inhibiting Factors**: What limits performance
- **External Influences**: Environmental factors
- **Process Impact**: How processes affect performance

### 4. Optimization Opportunities
- **Quick Wins**: Immediate improvement opportunities
- **Strategic Improvements**: Long-term optimization areas
- **Resource Requirements**: What's needed for improvements
- **Risk Assessment**: Risks of optimization efforts

## Analysis Questions
1. **Current State**: What is the current ${metric_type} performance level?
2. **Trend Direction**: Is performance improving, declining, or stable?
3. **Root Causes**: What are the primary factors affecting performance?
4. **Bottlenecks**: What are the main performance constraints?
5. **Optimization Potential**: How much improvement is realistically achievable?
6. **Resource Impact**: What resources are needed for optimization?

## Target Achievement Plan
Develop a plan to achieve **${target_improvement}** improvement:
- **Milestone Targets**: Intermediate performance goals
- **Implementation Strategy**: How to achieve improvements
- **Timeline**: Realistic timeframe for improvements
- **Success Metrics**: How to measure progress
- **Risk Mitigation**: How to handle potential setbacks

## Recommendations
Provide prioritized recommendations for:
1. **Immediate Actions** (0-30 days)
2. **Short-term Improvements** (1-3 months)
3. **Long-term Optimization** (3-12 months)

Each recommendation should include expected impact, required effort, and success criteria.`;

    return {
      description: `Performance metrics analysis prompt for ${metric_type}`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  /**
   * Generate team productivity analysis prompt
   * @param {object} args - Prompt arguments
   * @returns {object} Prompt content
   */
  async generateTeamProductivityPrompt(args) {
    const { team_size = 'unknown', methodology = 'agile', focus_metrics = 'velocity, quality, satisfaction' } = args;

    const promptContent = `# Team Productivity Analysis

## Productivity Assessment Objective
Analyze team productivity for a **${team_size}** member team using **${methodology}** methodology, focusing on **${focus_metrics}**.

## Productivity Framework

### 1. Output Metrics
- **Velocity**: Work completion rate and consistency
- **Throughput**: Volume of deliverables produced
- **Quality**: Defect rates and rework requirements
- **Cycle Time**: Time from start to completion

### 2. Efficiency Metrics
- **Resource Utilization**: How effectively team capacity is used
- **Process Efficiency**: Waste reduction and optimization
- **Tool Effectiveness**: Impact of tools and automation
- **Knowledge Sharing**: Collaboration and learning efficiency

### 3. Engagement Metrics
- **Team Satisfaction**: Morale and job satisfaction levels
- **Collaboration Quality**: Teamwork and communication effectiveness
- **Skill Development**: Learning and growth progress
- **Work-Life Balance**: Sustainability and burnout prevention

### 4. Process Metrics
- **Methodology Adherence**: Following ${methodology} practices
- **Meeting Effectiveness**: Productive use of meeting time
- **Decision Speed**: Time to make and implement decisions
- **Feedback Loops**: Quality and speed of feedback cycles

## Analysis Questions
1. **Overall Productivity**: How would you rate overall team productivity (1-10)?
2. **Productivity Trends**: Is productivity improving, stable, or declining?
3. **Team Strengths**: What are the team's key productivity strengths?
4. **Productivity Barriers**: What obstacles limit team productivity?
5. **Process Effectiveness**: How well are current processes working?
6. **Tool Impact**: How do current tools affect productivity?
7. **Team Dynamics**: How do team interactions affect productivity?
8. **Improvement Potential**: What's the biggest productivity opportunity?

## Focus Areas: ${focus_metrics}
Provide detailed analysis for each focus metric:
${focus_metrics.split(',').map(metric => `
### ${metric.trim()}
- Current performance level
- Trends and patterns
- Contributing factors
- Improvement opportunities
`).join('')}

## Productivity Enhancement Plan
Develop recommendations for:
1. **Process Improvements**: Optimize workflows and procedures
2. **Tool Optimization**: Enhance tool usage and automation
3. **Team Development**: Skills and collaboration improvements
4. **Environment Enhancement**: Workspace and culture improvements
5. **Measurement Systems**: Better productivity tracking

## Success Metrics
Define how to measure productivity improvements:
- Quantitative metrics (velocity, cycle time, quality scores)
- Qualitative indicators (satisfaction, collaboration, innovation)
- Leading indicators (process adherence, tool adoption)
- Lagging indicators (delivery outcomes, customer satisfaction)

Focus on actionable insights that will drive measurable productivity improvements while maintaining team satisfaction and work quality.`;

    return {
      description: `Team productivity analysis prompt for ${team_size} member team`,
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: promptContent
          }
        }
      ]
    };
  }

  /**
   * Validate prompt arguments
   * @param {string} name - Prompt name
   * @param {object} args - Prompt arguments
   * @returns {object} Validation result
   */
  validatePrompt(name, args) {
    const template = this.templates[name];
    if (!template) {
      return { valid: false, error: `Unknown prompt: ${name}` };
    }

    const requiredArgs = template.arguments.filter(arg => arg.required);
    const missingArgs = requiredArgs.filter(arg => !args.hasOwnProperty(arg.name));

    if (missingArgs.length > 0) {
      return {
        valid: false,
        error: `Missing required arguments: ${missingArgs.map(arg => arg.name).join(', ')}`
      };
    }

    return { valid: true };
  }
}

export default AnalysisPromptHandler;
