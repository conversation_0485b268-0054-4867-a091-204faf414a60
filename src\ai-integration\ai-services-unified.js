/**
 * Unified AI Services Layer for Guidant
 *
 * This module is the central entry point for all AI operations within the application.
 * It follows the TaskMaster pattern of having a unified service that orchestrates
 * configuration, provider selection, and execution of AI tasks. This abstracts
 * the underlying complexity from the rest of the codebase.
 */

import dotenv from 'dotenv';
import { configManager } from '../config/config-manager.js';
import { GoogleVertexProvider } from '../ai-providers/GoogleVertexProvider.js';
// Import other providers here as they are created
// import { AnthropicProvider } from '../ai-providers/AnthropicProvider.js';

// Load environment variables
dotenv.config();

const providerMap = {
  'vertex-ai': GoogleVertexProvider,
  'google-vertex': GoogleVertexProvider,
  // 'anthropic': AnthropicProvider,
};

/**
 * Retrieves the appropriate AI provider class for a given provider name.
 *
 * @param {string} providerName The name of the provider (e.g., 'google-vertex').
 * @returns {class} The provider class corresponding to the name.
 * @throws {Error} If the provider is not supported or not found in the map.
 */
function getProviderClass(providerName) {
  const ProviderClass = providerMap[providerName];
  if (!ProviderClass) {
    throw new Error(`Unsupported provider: ${providerName}. Please check your configuration.`);
  }
  return ProviderClass;
}

/**
 * Performs an AI generation task based on a specified role.
 *
 * This is the primary function to be called from other parts of the application.
 * It handles getting the configuration, instantiating the correct provider,
 * and executing the generation task.
 *
 * @param {string} role The role for the AI task (e.g., 'main', 'analysis').
 * @param {string} prompt The input prompt for the AI model.
 * @param {object} options Runtime options to pass to the provider (e.g., temperature).
 * @returns {Promise<object>} A promise that resolves to the AI's response.
 */
export async function performAIOperation(role, prompt, options = {}) {
  try {
    // 1. Get the configuration for the specified role
    const roleConfig = configManager.getRoleConfig(role);

    // 2. Get the appropriate provider class
    const ProviderClass = getProviderClass(roleConfig.provider);

    // 3. Instantiate the provider with its configuration
    const provider = new ProviderClass(roleConfig);

    // 4. Execute the generation task
    console.log(`🤖 Performing AI operation with role: '${role}' using provider: '${provider.getName()}' and model: '${provider.getModelId()}'`);
    const result = await provider.generate(prompt, options);

    // 5. Return the result
    return result;

  } catch (error) {
    console.error(`Error during AI operation for role '${role}': ${error.message}`);
    // Here you could implement fallback logic if desired
    // For now, we re-throw to let the caller handle it.
    throw error;
  }
}

/**
 * Perform research operation using integrated research providers
 * @param {object} request - Research request configuration
 * @returns {Promise<object>} Research results with synthesis
 */
export async function performResearchOperation(request) {
  try {
    const { type, query, context = {}, providers = ['tavily'], maxResults = 5 } = request;

    // Get research configuration from config manager
    const researchConfig = configManager.getResearchConfig();

    // Import research providers dynamically to avoid circular dependencies
    const { TavilyProvider } = await import('../research-providers/tavily-provider.js');
    const { Context7Provider } = await import('../research-providers/context7-provider.js');
    const { FirecrawlProvider } = await import('../research-providers/firecrawl-provider.js');

    // Initialize providers with configuration from config.json
    const providerInstances = {
      tavily: new TavilyProvider(researchConfig.providers.tavily),
      context7: new Context7Provider(researchConfig.providers.context7),
      firecrawl: new FirecrawlProvider(researchConfig.providers.firecrawl)
    };

    // Collect research results from requested providers
    const results = [];
    for (const providerName of providers) {
      if (providerInstances[providerName]) {
        try {
          const providerResult = await providerInstances[providerName].search(query, {
            maxResults,
            context,
            type
          });
          results.push({
            provider: providerName,
            results: providerResult.results || [],
            metadata: providerResult.metadata || {}
          });
        } catch (error) {
          console.warn(`Research provider ${providerName} failed:`, error.message);
        }
      }
    }

    // Synthesize results using AI
    const synthesis = await synthesizeResearchResults(results, query, context);

    // Extract all individual results for easy access
    const allResults = results.flatMap(r => r.results);

    return {
      query,
      type,
      providers,
      results: allResults,  // ← Add this for easy access to raw results
      synthesis,
      metadata: {
        confidenceLevel: synthesis.confidence || 0.7,
        sourcesCount: allResults.length,
        providersUsed: results.map(r => r.provider),
        timestamp: new Date().toISOString()
      }
    };
  } catch (error) {
    console.error('Research operation failed:', error);
    throw new Error(`Research operation failed: ${error.message}`);
  }
}

/**
 * Test research services connectivity
 * @returns {Promise<object>} Test results for all research services
 */
export async function testResearchServices() {
  const testResults = {
    timestamp: new Date().toISOString(),
    totalProviders: 0,
    results: [],
    allHealthy: false
  };

  try {
    // Get research configuration from config manager
    const researchConfig = configManager.getResearchConfig();

    // Test Tavily
    try {
      const { TavilyProvider } = await import('../research-providers/tavily-provider.js');
      const tavily = new TavilyProvider(researchConfig.providers.tavily);
      await tavily.search('test query', { maxResults: 1 });
      testResults.results.push({ name: 'Tavily', success: true, message: 'Connected successfully' });
    } catch (error) {
      testResults.results.push({ name: 'Tavily', success: false, message: error.message });
    }

    // Test Context7
    try {
      const { Context7Provider } = await import('../research-providers/context7-provider.js');
      const context7 = new Context7Provider(researchConfig.providers.context7);
      await context7.search('test query', { maxResults: 1 });
      testResults.results.push({ name: 'Context7', success: true, message: 'Connected successfully' });
    } catch (error) {
      testResults.results.push({ name: 'Context7', success: false, message: error.message });
    }

    // Test Firecrawl
    try {
      const { FirecrawlProvider } = await import('../research-providers/firecrawl-provider.js');
      const firecrawl = new FirecrawlProvider(researchConfig.providers.firecrawl);
      await firecrawl.search('https://example.com', { maxResults: 1 });
      testResults.results.push({ name: 'Firecrawl', success: true, message: 'Connected successfully' });
    } catch (error) {
      testResults.results.push({ name: 'Firecrawl', success: false, message: error.message });
    }

    testResults.totalProviders = testResults.results.length;
    testResults.allHealthy = testResults.results.every(r => r.success);

  } catch (error) {
    console.error('Research services test failed:', error);
  }

  return testResults;
}

/**
 * Synthesize research results using AI
 * @param {Array} results - Research results from providers
 * @param {string} query - Original query
 * @param {object} context - Research context
 * @returns {Promise<object>} Synthesized research
 */
async function synthesizeResearchResults(results, query, context) {
  try {
    // Combine all research results
    const allResults = results.flatMap(r => r.results);

    if (allResults.length === 0) {
      return {
        summary: 'No research results found',
        sources: [],
        recommendations: [],
        confidence: 0.1
      };
    }

    // Create synthesis prompt
    const synthesisPrompt = `
Synthesize the following research results for the query: "${query}"

Research Results:
${allResults.map((result, index) => `
${index + 1}. ${result.title || 'Untitled'}
   ${result.description || result.content || 'No description'}
   Source: ${result.url || result.source || 'Unknown'}
`).join('\n')}

Context: ${JSON.stringify(context, null, 2)}

Please provide:
1. A concise summary of key findings
2. Main recommendations based on the research
3. Confidence level (0-1) in the findings
4. List of sources used

Format as JSON with: summary, recommendations, confidence, sources
`;

    // Use AI to synthesize results
    const synthesisResult = await performAIOperation('research', synthesisPrompt, {
      temperature: 0.3,
      maxTokens: 1000
    });

    // Parse AI response
    let synthesis;
    try {
      synthesis = JSON.parse(synthesisResult.content || synthesisResult.text || '{}');
    } catch (parseError) {
      // Fallback if AI doesn't return valid JSON
      synthesis = {
        summary: synthesisResult.content || synthesisResult.text || 'Research synthesis available',
        recommendations: [],
        confidence: 0.7,
        sources: allResults.map(r => r.url || r.source).filter(Boolean)
      };
    }

    return synthesis;
  } catch (error) {
    console.warn('AI synthesis failed, using fallback:', error.message);

    // Fallback synthesis
    return {
      summary: `Research found ${results.length} sources with relevant information about: ${query}`,
      recommendations: ['Review research sources for detailed information'],
      confidence: 0.5,
      sources: results.flatMap(r => r.results.map(res => res.url || res.source)).filter(Boolean)
    };
  }
}

/**
 * A simple example of how to use the unified service.
 * This can be used for testing purposes.
 */
export async function testUnifiedService() {
    console.log("--- Testing Unified AI Service ---");
    const testPrompt = "Explain the concept of a 'unified AI service layer' in a software application.";

    try {
        console.log("\n--- Testing 'main' role ---");
        const mainResult = await performAIOperation('main', testPrompt);
        console.log("Main Role Response:", mainResult.text);
        console.log("Usage:", mainResult.usage);

        console.log("\n--- Testing 'analysis' role ---");
        const analysisResult = await performAIOperation('analysis', testPrompt);
        console.log("Analysis Role Response:", analysisResult.text);
        console.log("Usage:", analysisResult.usage);

    } catch (error) {
        console.error("Unified service test failed:", error);
    }
}