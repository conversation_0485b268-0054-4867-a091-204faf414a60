{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "D:/"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "tavily": {"command": "npx", "args": ["-y", "tavily-mcp@0.2.3"], "env": {"TAVILY_API_KEY": "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"}, "alwaysAllow": []}}}