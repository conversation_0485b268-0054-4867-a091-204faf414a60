{"models": {"main": {"provider": "vertex-ai", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 1000000, "temperature": 0.7, "description": "Fast, efficient model for general tasks"}, "analysis": {"provider": "openrouter", "modelId": "google/gemini-2.0-flash-exp:free", "maxTokens": 100000, "temperature": 0.2, "description": "Advanced reasoning model for complex analysis (using OpenRouter until Vertex AI is enabled)"}, "research": {"provider": "vertex-ai", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 1000000, "temperature": 0.1, "supportsThinking": true, "thinkingConfig": {"thinkingBudget": 1000}, "description": "Research synthesis using our advanced research providers (Tavily + Context7 + Firecrawl)"}, "generation": {"provider": "vertex-ai", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 1000000, "temperature": 0.8, "description": "Creative content generation"}, "fallback": {"provider": "mistral", "modelId": "mistral-large-latest", "maxTokens": 100000, "temperature": 0.7, "description": "Fallback when primary models fail"}}, "providers": {"vertex-ai": {"projectId": "lustrous-bond-462912-d5", "location": "us-central1", "credentialsPath": "D:\\Fluxitude\\Projects\\Keys\\lustrous-bond-462912-d5-05ad0e7fd254.json", "safetySettings": [{"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"}, {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}, {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"}, {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"}], "flashConfig": {"providerOptions": {"google": {"thinkingConfig": {"thinkingBudget": -1}}}}, "proConfig": {"providerOptions": {}}}, "openrouter": {"apiKey": "${OPENROUTER_API_KEY}", "baseUrl": "https://openrouter.ai/api/v1"}}, "research": {"providers": {"tavily": {"apiKey": "${TAVILY_API_KEY}", "baseUrl": "https://api.tavily.com", "rateLimit": {"requestsPerMinute": 100, "burstLimit": 10, "retryAfter": 60, "exponentialBackoff": true, "maxRetries": 3}, "defaultOptions": {"searchDepth": "basic", "maxResults": 10, "includeImages": false, "includeAnswer": true, "includeRawContent": false}, "contextualOptions": {"market_research": {"searchDepth": "advanced", "maxResults": 15, "includeDomains": ["statista.com", "mckinsey.com", "deloitte.com", "pwc.com", "bcg.com", "bain.com", "accenture.com", "gartner.com", "forrester.com", "idc.com"], "maxAge": 90}, "competitive_analysis": {"searchDepth": "advanced", "maxResults": 20, "includeDomains": ["crunchbase.com", "pitchbook.com", "cbinsights.com", "similarweb.com", "semrush.com"], "maxAge": 30}, "news_monitoring": {"searchDepth": "basic", "maxResults": 25, "includeDomains": ["reuters.com", "bloomberg.com", "wsj.com", "ft.com", "techcrunch.com", "venturebeat.com", "theverge.com", "ars-technica.com"], "maxAge": 7}, "fact_verification": {"searchDepth": "advanced", "maxResults": 12, "includeDomains": ["snopes.com", "factcheck.org", "politifact.com", "reuters.com", "ap.org", "bbc.com", "npr.org"], "maxAge": 365}, "technical_trends": {"searchDepth": "advanced", "maxResults": 15, "includeDomains": ["stackoverflow.com", "github.com", "medium.com", "dev.to", "hackernews.com", "reddit.com"], "maxAge": 30}}, "ranking": {"relevanceThreshold": 0.7, "maxAge": 30, "domainWeights": {"statista.com": 0.25, "mckinsey.com": 0.25, "deloitte.com": 0.2, "pwc.com": 0.2, "bcg.com": 0.2, "bain.com": 0.2, "accenture.com": 0.15, "gartner.com": 0.25, "forrester.com": 0.2, "idc.com": 0.2, "reuters.com": 0.2, "bloomberg.com": 0.2, "wsj.com": 0.18, "ft.com": 0.18, "economist.com": 0.18, "harvard.edu": 0.22, "mit.edu": 0.22, "stanford.edu": 0.22, "nature.com": 0.25, "science.org": 0.25, "ieee.org": 0.2, "acm.org": 0.2, "arxiv.org": 0.15, "github.com": 0.15, "stackoverflow.com": 0.18}, "contentQualityFactors": {"minContentLength": 500, "authorityBoost": 0.1, "recencyBoost": 0.15, "citationBoost": 0.1}}, "fallback": {"enabled": true, "providers": ["bing", "google"], "timeout": 15000}}, "context7": {"baseUrl": "https://mcp.context7.com", "fallbackUrls": ["https://backup.context7.com", "https://api.context7.io"], "rateLimit": {"requestsPerMinute": 60, "burstLimit": 15, "retryAfter": 60, "exponentialBackoff": true, "maxRetries": 3}, "defaultTokens": 10000, "minTokens": 1000, "maxTokens": 50000, "tokenAllocation": {"quick_reference": 2000, "detailed_guide": 8000, "comprehensive_analysis": 15000, "full_documentation": 25000}, "preferredLanguages": ["javascript", "typescript", "python"], "languageContexts": {"web_frontend": ["javascript", "typescript", "html", "css", "scss", "less", "react", "vue", "angular"], "web_backend": ["javascript", "typescript", "python", "java", "go", "php", "ruby", "c#"], "mobile": ["swift", "kotlin", "dart", "react-native", "flutter"], "data_science": ["python", "r", "sql", "matlab", "scala"], "devops": ["bash", "powershell", "yaml", "docker", "kubernetes"], "enterprise": ["java", "c#", "sql", "xml", "soap"], "systems": ["c", "c++", "rust", "go", "assembly"], "ai_ml": ["python", "r", "julia", "tensorflow", "pytorch"]}, "filtering": {"relevanceThreshold": 0.6, "codeSnippetWeight": 0.3, "officialDocWeight": 0.4, "communityWeight": 0.2, "freshnessWeight": 0.1, "preferredSources": ["docs.microsoft.com", "developer.mozilla.org", "docs.python.org", "docs.oracle.com", "golang.org", "rust-lang.org", "reactjs.org", "vuejs.org", "angular.io", "svelte.dev", "nextjs.org", "nuxtjs.org", "nodejs.org", "expressjs.com", "fastapi.tiangolo.com", "flask.palletsprojects.com", "django.readthedocs.io", "spring.io", "docs.spring.io", "hibernate.org", "maven.apache.org", "gradle.org", "kubernetes.io", "docker.com", "terraform.io", "ansible.com", "jenkins.io", "docs.aws.amazon.com", "cloud.google.com", "docs.microsoft.com/azure", "docs.digitalocean.com", "pytorch.org", "tensorflow.org", "scikit-learn.org", "pandas.pydata.org", "numpy.org", "developer.apple.com", "developer.android.com", "flutter.dev", "reactnative.dev"], "excludeSources": ["w3schools.com", "tutorialspoint.com", "geeksforgeeks.org"], "qualityIndicators": {"hasCodeExamples": 0.3, "hasOfficialMaintainer": 0.4, "hasRecentUpdates": 0.2, "hasComprehensiveAPI": 0.3, "hasBestPractices": 0.2}}, "specializedQueries": {"api_documentation": {"tokens": 15000, "focusAreas": ["endpoints", "parameters", "responses", "authentication", "rate_limits", "examples"], "preferredFormats": ["openapi", "swagger", "postman"]}, "framework_guides": {"tokens": 12000, "focusAreas": ["getting_started", "configuration", "best_practices", "common_patterns", "troubleshooting"], "includeVersions": true}, "library_integration": {"tokens": 8000, "focusAreas": ["installation", "basic_usage", "advanced_features", "compatibility", "migration"], "includeDependencies": true}, "troubleshooting": {"tokens": 6000, "focusAreas": ["common_errors", "debugging", "performance", "security", "compatibility"], "includeStackOverflow": true}}, "caching": {"enabled": true, "ttl": 7200, "versionAware": true, "invalidateOnUpdate": true}}, "firecrawl": {"apiKey": "${FIRECRAWL_API_KEY}", "baseUrl": "https://api.firecrawl.dev", "fallbackUrls": ["https://backup.firecrawl.dev", "https://api-eu.firecrawl.dev"], "rateLimit": {"requestsPerMinute": 100, "concurrentBrowsers": 5, "burstLimit": 20, "retryAfter": 60, "exponentialBackoff": true, "maxRetries": 3, "queueTimeout": 300000}, "defaultOptions": {"formats": ["markdown", "html", "structured"], "onlyMainContent": true, "timeout": 30000, "waitFor": 2000, "removeBase64Images": true, "includeTags": ["article", "main", "section", "div", "p", "h1", "h2", "h3", "h4", "h5", "h6", "ul", "ol", "li", "table", "tr", "td", "th"], "excludeTags": ["nav", "footer", "aside", "advertisement", "popup", "modal"]}, "contextualOptions": {"competitive_analysis": {"formats": ["markdown", "structured"], "extractDepth": "advanced", "includeMetadata": true, "analyzeSEO": true, "extractPricing": true, "extractFeatures": true, "maxDepth": 3, "followInternalLinks": true}, "content_research": {"formats": ["markdown", "html"], "extractDepth": "basic", "includeImages": true, "preserveFormatting": true, "extractAuthors": true, "extractDates": true, "maxDepth": 2}, "technical_documentation": {"formats": ["markdown", "structured"], "extractDepth": "advanced", "preserveCodeBlocks": true, "extractAPIEndpoints": true, "followDocLinks": true, "maxDepth": 4}, "market_intelligence": {"formats": ["structured", "markdown"], "extractDepth": "advanced", "extractFinancials": true, "extractMetrics": true, "extractContacts": true, "analyzeTrends": true, "maxDepth": 2}, "news_aggregation": {"formats": ["markdown"], "extractDepth": "basic", "extractAuthors": true, "extractDates": true, "extractSources": true, "categorizeContent": true, "maxDepth": 1}}, "processing": {"maxContentLength": 100000, "preferredFormats": ["markdown", "structured", "html"], "contentFilters": {"minWordCount": 50, "maxWordCount": 50000, "removeBoilerplate": true, "removeDuplicates": true, "languageDetection": true, "qualityThreshold": 0.6}, "structuredExtraction": {"extractTables": true, "extractLists": true, "extractHeaders": true, "extractLinks": true, "extractImages": true, "extractMetadata": true, "extractSchema": true}, "performance": {"enableJavaScript": true, "enableCSS": false, "enableImages": false, "enableFonts": false, "userAgent": "Guidant Research Bot 1.0", "viewport": {"width": 1920, "height": 1080}, "mobile": false}}, "crawling": {"maxPages": 100, "maxDepth": 3, "respectRobotsTxt": true, "crawlDelay": 1000, "allowExternalLinks": false, "followRedirects": true, "maxRedirects": 5, "includePaths": [], "excludePaths": ["/admin", "/login", "/register", "/cart", "/checkout", "/account"], "excludeExtensions": [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".zip", ".rar"], "sitemapFirst": true, "deduplicateUrls": true}, "monitoring": {"trackPerformance": true, "logErrors": true, "alertOnFailure": true, "retryFailedPages": true, "maxRetryAttempts": 2}}}, "orchestration": {"cacheEnabled": true, "cacheTTL": 3600, "maxConcurrentRequests": 5, "timeoutMs": 30000, "synthesisModel": "research", "intelligentRouting": {"enabled": true, "loadBalancing": true, "failoverEnabled": true, "providerWeights": {"tavily": 1.0, "context7": 1.0, "firecrawl": 0.8}, "contextualRouting": {"market_research": ["tavily"], "competitive_analysis": ["tavily", "firecrawl"], "technical_documentation": ["context7"], "library_research": ["context7"], "web_content_extraction": ["firecrawl"], "news_monitoring": ["tavily"], "fact_verification": ["tavily"], "api_documentation": ["context7"], "website_analysis": ["firecrawl"], "trend_analysis": ["tavily", "firecrawl"], "code_examples": ["context7"], "best_practices": ["context7", "tavily"], "troubleshooting": ["context7", "tavily"], "performance_optimization": ["context7", "tavily"], "security_research": ["tavily", "context7"], "compliance_research": ["tavily"], "patent_research": ["tavily"], "academic_research": ["tavily"], "financial_analysis": ["tavily", "firecrawl"], "user_research": ["tavily", "firecrawl"]}}, "qualityControl": {"enabled": true, "minimumSources": 2, "crossValidation": true, "confidenceThreshold": 0.7, "factChecking": true, "biasDetection": true, "sourceCredibility": true}, "performance": {"adaptiveTimeout": true, "circuitBreaker": {"enabled": true, "failureThreshold": 5, "recoveryTimeout": 60000}, "bulkProcessing": {"enabled": true, "batchSize": 10, "parallelBatches": 3}, "resourceManagement": {"memoryLimit": "2GB", "cpuLimit": "80%", "diskCacheLimit": "1GB"}}, "analytics": {"enabled": true, "trackUsage": true, "trackPerformance": true, "trackQuality": true, "trackCosts": true, "reportingInterval": 3600, "retentionPeriod": 2592000}, "security": {"apiKeyRotation": true, "encryptCache": true, "sanitizeInputs": true, "validateSources": true, "contentFiltering": true, "rateLimitByUser": true}, "experimental": {"enabled": false, "features": {"multimodalResearch": false, "realTimeCollaboration": false, "predictiveResearch": false, "autoSummarization": true, "semanticSearch": true, "knowledgeGraphs": false}}}}, "global": {"logLevel": "info", "debug": false, "projectName": "Guidant", "defaultRole": "main", "retryAttempts": 3, "timeout": 60000}}