/**
 * Project Manager - Consolidated MCP Tool
 * Consolidates 8+ project management tools into a single operation-based tool
 */

import { MCPMessageHandler } from '../../utils/mcp-message-handler.js';
import { VisualAssetManager } from '../../../../src/visual-generation/assets/visual-asset-manager.js'; // Adjusted path
import { VisualAsset, WireframeAsset, DiagramAsset, PrototypeAsset } from '../../../../src/visual-generation/assets/visual-asset-models.js'; // Adjusted path

/**
 * Project Manager Tool
 * Handles all project management operations with state control
 */
export class ProjectManagerTool {
  constructor(existingInfrastructure = {}) {
    this.projectManager = existingInfrastructure.projectManager;
    this.deliverableManager = existingInfrastructure.deliverableManager;
    // Assuming projectRoot is available or can be determined. For now, using default.
    // The VisualAssetManager from src/visual-generation/assets/visual-asset-manager.js expects projectRoot.
    // Let's assume it's available via existingInfrastructure or a default.
    const projectRoot = existingInfrastructure.projectRoot || process.cwd(); // Placeholder for actual project root
    this.visualAssetManager = new VisualAssetManager(projectRoot);
    this.messageHandler = new MCPMessageHandler();
  }

  /**
   * Get tool definition for MCP registration
   * @returns {object} MCP tool definition
   */
  getToolDefinition() {
    return {
      name: "guidant_manage_project",
      description: "Project management operations with comprehensive state control and lifecycle management",
      inputSchema: {
        type: "object",
        properties: {
          operation: {
            type: "string",
            enum: [
              "init", "advance_phase", "save_deliverable", "report_progress",
              "make_decision", "complete_onboarding", "update_state", "get_status",
              "create_visual_asset", "get_visual_asset", "update_visual_asset", "list_visual_assets", "delete_visual_asset"
            ],
            description: "Project management operation to execute"
          },
          project_id: {
            type: "string",
            description: "Project identifier (required for most operations)"
          },
          project_data: {
            type: "object",
            description: "Project configuration and data",
            properties: {
              name: { type: "string" },
              description: { type: "string" },
              type: { type: "string" },
              phase: { type: "string" },
              configuration: { type: "object" },
              metadata: { type: "object" }
            }
          },
          deliverable_data: {
            type: "object",
            description: "Deliverable information for save operations",
            properties: {
              name: { type: "string" },
              type: { type: "string" },
              content: { type: "string" },
              phase: { type: "string" },
              metadata: { type: "object" }
            }
          },
          decision_data: {
            type: "object",
            description: "Decision information for decision operations",
            properties: {
              decision_type: { type: "string" },
              options: { type: "array" },
              criteria: { type: "object" },
              selected_option: { type: "string" },
              rationale: { type: "string" }
            }
          },
          progress_data: {
            type: "object",
            description: "Progress reporting information",
            properties: {
              phase: { type: "string" },
              completion_percentage: { type: "number" },
              milestones: { type: "array" },
              blockers: { type: "array" },
              next_steps: { type: "array" }
            }
          }
        },
"visual_asset_id": {
            "type": "string",
            "description": "Identifier for the visual asset (for get, update, delete operations)"
          },
          "asset_type_filter": {
            "type": "string",
            "enum": ["wireframe", "diagram", "prototype", "unknown", "all"],
            "description": "Optional filter for listing visual assets by type"
          },
          "visual_asset_data": {
            "type": "object",
            "description": "Data for creating or updating a visual asset",
            "properties": {
              "id": { "type": "string", "description": "Asset ID (optional, will be generated if not provided for creation)" },
              "type": { "type": "string", "enum": ["wireframe", "diagram", "prototype", "unknown"], "description": "Type of visual asset" },
              "title": { "type": "string", "description": "Title of the visual asset" },
              "description": { "type": "string", "description": "Description of the visual asset" },
              "content": { "type": "string", "description": "Main content of the asset (e.g., JSON string, text)" },
              "metadata": {
                "type": "object",
                "description": "Asset metadata",
                "properties": {
                  "sourceRequirements": { "type": "array", "items": { "type": "string" } },
                  "generationMethod": { "type": "string" },
                  "generatorVersion": { "type": "string" },
                  "validationStatus": { "type": "string" },
                  "tags": { "type": "array", "items": { "type": "string" } },
                  "category": { "type": "string" },
                  "priority": { "type": "string" },
                  "complexity": { "type": "string" },
                  "tailwindEnabled": {"type": "boolean"},
                  "tailwindData": {"type": "object"}
                  // Allow additional custom metadata properties
                },
                "additionalProperties": true
              },
              "version": {
                "type": "object",
                "description": "Asset version information",
                "properties": {
                  "major": { "type": "integer" },
                  "minor": { "type": "integer" },
                  "patch": { "type": "integer" },
                  "history": { "type": "array", "items": { "type": "object" } }
                }
              },
              "businessIntelligence": {
                "type": "object",
                "description": "Business intelligence data",
                "properties": {
                  "usageCount": { "type": "integer" },
                  "userFeedback": { "type": "array", "items": { "type": "object" } },
                  "performanceMetrics": { "type": "object" },
                  "businessValue": { "type": "string" },
                  "stakeholderApproval": { "type": "string" },
                  "iterationCount": { "type": "integer" },
                  "lastAccessed": { "type": "string", "format": "date-time" }
                },
                "additionalProperties": true
              },
              "createdAt": { "type": "string", "format": "date-time" },
              "updatedAt": { "type": "string", "format": "date-time" },
              // WireframeAsset specific fields
              "layout": { "type": "object" },
              "components": { "type": "array", "items": { "type": "object" } }, // Can be used by Wireframe and Prototype
              "interactions": { "type": "array", "items": { "type": "object" } }, // Can be used by Wireframe and Prototype
              "breakpoints": { "type": "array", "items": { "type": "string" } },
              "gridSystem": { "type": "object" },
              "asciiContent": { "type": "string" },
              // DiagramAsset specific fields
              "diagramType": { "type": "string" },
              "mermaidSyntax": { "type": "string" },
              "nodes": { "type": "array", "items": { "type": "object" } },
              "edges": { "type": "array", "items": { "type": "object" } },
              "userFlow": { "type": "object" },
              "decisionPoints": { "type": "array", "items": { "type": "object" } },
              "errorPaths": { "type": "array", "items": { "type": "object" } },
              // PrototypeAsset specific fields
              "htmlContent": { "type": "string" },
              "cssContent": { "type": "string" },
              "jsContent": { "type": "string" },
              "designSystem": { "type": "object" },
              "responsiveBreakpoints": { "type": "object" },
              "accessibilityFeatures": { "type": "array", "items": { "type": "string" } }
            },
            "additionalProperties": true // Allow other fields for specific asset types not explicitly listed
          },
        required: ["operation"],
        additionalProperties: false
      },
      annotations: {
        title: "Project Manager",
        description: "Consolidated project lifecycle management with state control",
        readOnlyHint: false,
        destructiveHint: true,
        idempotentHint: false,
        openWorldHint: false
      }
    };
  }

  /**
   * Execute project management operation
   * @param {object} args - Tool arguments
   * @returns {object} Execution result
   */
  async execute(args) {
    try {
      const { operation, project_id, project_data = {}, deliverable_data = {}, decision_data = {}, progress_data = {}, visual_asset_id, visual_asset_data = {} } = args;

      // Validate required parameters based on operation
      this.validateOperationParameters(operation, args);

      // Route to appropriate handler based on operation
      switch (operation) {
        case "init":
          return await this.initializeProject(project_data);
        case "advance_phase":
          return await this.advancePhase(project_id, project_data);
        case "save_deliverable":
          return await this.saveDeliverable(project_id, deliverable_data);
        case "report_progress":
          return await this.reportProgress(project_id, progress_data);
        case "make_decision":
          return await this.makeDecision(project_id, decision_data);
        case "complete_onboarding":
          return await this.completeOnboarding(project_id, project_data);
        case "update_state":
          return await this.updateProjectState(project_id, project_data);
        case "get_status":
          return await this.getProjectStatus(project_id);
        case "create_visual_asset":
          return await this.createVisualAsset(project_id, visual_asset_data);
        case "get_visual_asset":
          return await this.getVisualAsset(project_id, visual_asset_id);
        case "update_visual_asset":
          return await this.updateVisualAsset(project_id, visual_asset_id, visual_asset_data);
        case "list_visual_assets":
          return await this.listVisualAssets(project_id, args.asset_type_filter); // Pass filter from args
        case "delete_visual_asset":
          return await this.deleteVisualAsset(project_id, visual_asset_id);
        default:
          throw new Error(`Unknown project management operation: ${operation}`);
      }
    } catch (error) {
      return this.messageHandler.createErrorResponse(
        'PROJECT_MANAGEMENT_ERROR',
        `Project management operation failed: ${error.message}`,
        { operation: args.operation, error: error.message }
      );
    }
  }

  /**
   * Validate operation parameters
   * @param {string} operation - Operation type
   * @param {object} args - All arguments
   */
  validateOperationParameters(operation, args) {
    const requiresProjectId = ['advance_phase', 'save_deliverable', 'report_progress', 'make_decision', 'complete_onboarding', 'update_state', 'get_status', 'create_visual_asset', 'get_visual_asset', 'update_visual_asset', 'list_visual_assets', 'delete_visual_asset'];
    const requiresProjectData = ['init'];
    const requiresDeliverableData = ['save_deliverable'];
    const requiresDecisionData = ['make_decision'];
    const requiresVisualAssetId = ['get_visual_asset', 'update_visual_asset', 'delete_visual_asset'];
    const requiresVisualAssetData = ['create_visual_asset', 'update_visual_asset'];

    if (requiresProjectId.includes(operation) && !args.project_id) {
      throw new Error(`Operation '${operation}' requires project_id parameter`);
    }

    if (requiresProjectData.includes(operation) && (!args.project_data || !args.project_data.name)) {
      throw new Error(`Operation '${operation}' requires project_data with name`);
    }

    if (requiresDeliverableData.includes(operation) && (!args.deliverable_data || !args.deliverable_data.name)) {
      throw new Error(`Operation '${operation}' requires deliverable_data with name`);
    }

    if (requiresDecisionData.includes(operation) && (!args.decision_data || !args.decision_data.decision_type)) {
      throw new Error(`Operation '${operation}' requires decision_data with decision_type`);
    }

    if (requiresVisualAssetId.includes(operation) && !args.visual_asset_id) {
      throw new Error(`Operation '${operation}' requires visual_asset_id parameter`);
    }

    if (requiresVisualAssetData.includes(operation) && (!args.visual_asset_data || !args.visual_asset_data.type || !args.visual_asset_data.title)) {
      throw new Error(`Operation '${operation}' requires visual_asset_data with at least type and title`);
    }
  }

  /**
   * Initialize new project
   * @param {object} projectData - Project configuration
   * @returns {object} Initialization result
   */
  async initializeProject(projectData) {
    const projectId = `project-${Date.now()}`;
    
    const project = {
      id: projectId,
      name: projectData.name,
      description: projectData.description || '',
      type: projectData.type || 'general',
      phase: 'initialization',
      status: 'active',
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      configuration: {
        methodology: projectData.methodology || 'agile',
        quality_gates: projectData.quality_gates || ['review', 'testing'],
        automation_level: projectData.automation_level || 'standard',
        ...projectData.configuration
      },
      metadata: {
        created_by: 'system',
        version: '1.0',
        ...projectData.metadata
      }
    };

    // Mock project creation - would integrate with actual project management system
    const result = {
      project_id: projectId,
      status: 'initialized',
      project: project,
      next_steps: [
        'Complete project configuration',
        'Set up development environment',
        'Define initial requirements',
        'Begin onboarding process'
      ],
      estimated_timeline: {
        setup_phase: '1-2 days',
        development_phase: '2-4 weeks',
        testing_phase: '3-5 days',
        deployment_phase: '1-2 days'
      }
    };

    return this.messageHandler.createSuccessResponse(
      `Project '${projectData.name}' initialized successfully`,
      result
    );
  }

  /**
   * Advance project to next phase
   * @param {string} projectId - Project identifier
   * @param {object} projectData - Phase transition data
   * @returns {object} Phase advancement result
   */
  async advancePhase(projectId, projectData) {
    const currentPhase = projectData.current_phase || 'initialization';
    const nextPhase = this.getNextPhase(currentPhase);

    const phaseTransition = {
      project_id: projectId,
      from_phase: currentPhase,
      to_phase: nextPhase,
      transitioned_at: new Date().toISOString(),
      transition_criteria: {
        deliverables_completed: projectData.deliverables_completed || [],
        quality_gates_passed: projectData.quality_gates_passed || [],
        approvals_received: projectData.approvals_received || []
      },
      phase_summary: {
        duration: projectData.phase_duration || '2 weeks',
        achievements: projectData.achievements || [],
        lessons_learned: projectData.lessons_learned || [],
        metrics: projectData.metrics || {}
      }
    };

    const result = {
      project_id: projectId,
      phase_transition: phaseTransition,
      new_phase_info: {
        phase: nextPhase,
        objectives: this.getPhaseObjectives(nextPhase),
        deliverables: this.getPhaseDeliverables(nextPhase),
        estimated_duration: this.getPhaseEstimatedDuration(nextPhase)
      },
      recommendations: this.getPhaseRecommendations(nextPhase)
    };

    return this.messageHandler.createSuccessResponse(
      `Project advanced from ${currentPhase} to ${nextPhase}`,
      result
    );
  }

  /**
   * Save project deliverable
   * @param {string} projectId - Project identifier
   * @param {object} deliverableData - Deliverable information
   * @returns {object} Save result
   */
  async saveDeliverable(projectId, deliverableData) {
    const deliverableId = `deliverable-${Date.now()}`;
    
    const deliverable = {
      id: deliverableId,
      project_id: projectId,
      name: deliverableData.name,
      type: deliverableData.type || 'document',
      content: deliverableData.content || '',
      phase: deliverableData.phase || 'development',
      status: 'saved',
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      version: '1.0',
      metadata: {
        size: deliverableData.content?.length || 0,
        format: deliverableData.format || 'text',
        ...deliverableData.metadata
      }
    };

    // Mock quality assessment
    const qualityAssessment = {
      score: Math.floor(Math.random() * 20) + 80, // 80-100
      completeness: Math.floor(Math.random() * 15) + 85, // 85-100
      issues: [],
      recommendations: [
        'Consider adding more detailed examples',
        'Include references to related deliverables'
      ]
    };

    const result = {
      deliverable_id: deliverableId,
      project_id: projectId,
      deliverable: deliverable,
      quality_assessment: qualityAssessment,
      storage_info: {
        location: `.guidant/deliverables/${deliverableId}`,
        backup_created: true,
        version_tracked: true
      }
    };

    return this.messageHandler.createSuccessResponse(
      `Deliverable '${deliverableData.name}' saved successfully`,
      result
    );
  }

  /**
   * Report project progress
   * @param {string} projectId - Project identifier
   * @param {object} progressData - Progress information
   * @returns {object} Progress report result
   */
  async reportProgress(projectId, progressData) {
    const progressReport = {
      project_id: projectId,
      report_date: new Date().toISOString(),
      phase: progressData.phase || 'development',
      overall_progress: {
        completion_percentage: progressData.completion_percentage || 65,
        milestones_completed: progressData.milestones?.filter(m => m.completed).length || 3,
        milestones_total: progressData.milestones?.length || 5,
        on_track: progressData.completion_percentage >= 60
      },
      current_activities: progressData.current_activities || [
        'Feature implementation',
        'Unit testing',
        'Documentation updates'
      ],
      blockers: progressData.blockers || [],
      next_steps: progressData.next_steps || [
        'Complete current feature set',
        'Begin integration testing',
        'Prepare for phase review'
      ],
      metrics: {
        velocity: progressData.velocity || 8.5,
        quality_score: progressData.quality_score || 88,
        team_satisfaction: progressData.team_satisfaction || 4.2
      }
    };

    return this.messageHandler.createSuccessResponse(
      'Progress report generated successfully',
      progressReport
    );
  }

  /**
   * Make project decision
   * @param {string} projectId - Project identifier
   * @param {object} decisionData - Decision information
   * @returns {object} Decision result
   */
  async makeDecision(projectId, decisionData) {
    const decisionId = `decision-${Date.now()}`;
    
    const decision = {
      id: decisionId,
      project_id: projectId,
      type: decisionData.decision_type,
      description: decisionData.description || '',
      options_considered: decisionData.options || [],
      selected_option: decisionData.selected_option,
      rationale: decisionData.rationale || '',
      criteria: decisionData.criteria || {},
      made_by: 'system',
      made_at: new Date().toISOString(),
      impact_assessment: {
        scope: decisionData.impact_scope || 'medium',
        risk_level: decisionData.risk_level || 'low',
        affected_areas: decisionData.affected_areas || []
      }
    };

    const result = {
      decision_id: decisionId,
      project_id: projectId,
      decision: decision,
      implementation_plan: {
        immediate_actions: decisionData.immediate_actions || [],
        timeline: decisionData.implementation_timeline || '1 week',
        success_criteria: decisionData.success_criteria || []
      },
      follow_up: {
        review_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        monitoring_metrics: decisionData.monitoring_metrics || []
      }
    };

    return this.messageHandler.createSuccessResponse(
      `Decision '${decisionData.decision_type}' recorded successfully`,
      result
    );
  }

  /**
   * Create a new visual asset
   * @param {string} projectId - Project identifier
   * @param {object} visualAssetData - Data for the new visual asset
   * @returns {object} Creation result
   */
  async createVisualAsset(projectId, visualAssetData) {
    try {
      let assetInstance;
      switch (visualAssetData.type) {
        case 'wireframe':
          assetInstance = new WireframeAsset(visualAssetData);
          break;
        case 'diagram':
          assetInstance = new DiagramAsset(visualAssetData); // Corrected typo
          break;
        case 'prototype':
          assetInstance = new PrototypeAsset(visualAssetData);
          break;
        default:
          assetInstance = new VisualAsset(visualAssetData);
      }

      const result = await this.visualAssetManager.saveAsset(assetInstance);
      // Check for a property that indicates success, e.g., assetId or path from saveAsset's return
      if (result && result.assetId && result.path) {
        return this.messageHandler.createSuccessResponse(
          `Visual asset '${visualAssetData.title}' created successfully under project ${projectId}`,
          { projectId, ...result }
        );
      } else {
        throw new Error(result.error || 'Failed to save visual asset');
      }
    } catch (error) {
      return this.messageHandler.createErrorResponse(
        'VISUAL_ASSET_CREATION_ERROR',
        `Failed to create visual asset: ${error.message}`,
        { projectId, visualAssetData, error: error.message }
      );
    }
  }

  /**
   * Get a visual asset by ID
   * @param {string} projectId - Project identifier
   * @param {string} visualAssetId - Visual asset identifier
   * @returns {object} Asset retrieval result
   */
  async getVisualAsset(projectId, visualAssetId) {
    try {
      const asset = await this.visualAssetManager.loadAsset(visualAssetId); // loadAsset returns the asset object or throws
      if (asset) { // Check if asset is truthy (successfully loaded)
        return this.messageHandler.createSuccessResponse(
          `Visual asset '${visualAssetId}' retrieved successfully for project ${projectId}`,
          { projectId, asset }
        );
      } else {
        throw new Error(`Visual asset with ID ${visualAssetId} not found.`);
      }
    } catch (error) {
      return this.messageHandler.createErrorResponse(
        'VISUAL_ASSET_RETRIEVAL_ERROR',
        `Failed to retrieve visual asset: ${error.message}`,
        { projectId, visualAssetId, error: error.message }
      );
    }
  }

  /**
   * Update an existing visual asset
   * @param {string} projectId - Project identifier
   * @param {string} visualAssetId - Visual asset identifier
   * @param {object} visualAssetData - Data to update the visual asset
   * @returns {object} Update result
   */
  async updateVisualAsset(projectId, visualAssetId, visualAssetData) {
    try {
      // Load existing asset to maintain its instance type and merge updates
      const existingAsset = await this.visualAssetManager.loadAsset(visualAssetId);
      if (!existingAsset) {
        throw new Error(`Visual asset with ID ${visualAssetId} not found for update.`);
      }

      // Create a new instance of the correct type with merged data
      // The VisualAsset.update method handles versioning and updatedAt
      existingAsset.update(visualAssetData);


      const result = await this.visualAssetManager.saveAsset(existingAsset); // saveAsset should handle updates
      if (result && result.assetId && result.path) { // Check for success properties
        return this.messageHandler.createSuccessResponse(
          `Visual asset '${visualAssetId}' updated successfully for project ${projectId}`,
          { projectId, ...result }
        );
      } else {
        throw new Error(result.error || 'Failed to update visual asset');
      }
    } catch (error) {
      return this.messageHandler.createErrorResponse(
        'VISUAL_ASSET_UPDATE_ERROR',
        `Failed to update visual asset: ${error.message}`,
        { projectId, visualAssetId, visualAssetData, error: error.message }
      );
    }
  }

  /**
   * List visual assets for a project
   * @param {string} projectId - Project identifier
   * @param {string} [assetTypeFilter] - Optional filter by asset type
   * @returns {object} List of assets
   */
  async listVisualAssets(projectId, assetTypeFilter) {
    try {
      const criteria = assetTypeFilter && assetTypeFilter !== 'all' ? { type: assetTypeFilter } : {};
      // Assuming listAssets in VisualAssetManager takes criteria like { type: 'wireframe' }
      // And that it's not directly tied to a projectId, so we filter/manage association here or assume manager handles it.
      // For now, we'll just pass the type filter.
      const assetsArray = await this.visualAssetManager.listAssets(criteria); // This specific manager returns an array directly.

      if (Array.isArray(assetsArray)) {
        return this.messageHandler.createSuccessResponse(
          `Visual assets listed successfully for project ${projectId}`,
          // The VisualAssetManager from src/visual-generation/assets/visual-asset-manager.js
          // returns a simple array of asset registry entries.
          // We don't have totalAssets or filteredCount directly from it in this version.
          { projectId, assets: assetsArray, count: assetsArray.length }
        );
      } else {
        throw new Error('Failed to list visual assets or an invalid format was returned.');
      }
    } catch (error) {
      return this.messageHandler.createErrorResponse(
        'VISUAL_ASSET_LISTING_ERROR',
        `Failed to list visual assets: ${error.message}`,
        { projectId, assetTypeFilter, error: error.message }
      );
    }
  }

  /**
   * Delete a visual asset
   * @param {string} projectId - Project identifier
   * @param {string} visualAssetId - Visual asset identifier
   * @returns {object} Deletion result
   */
  async deleteVisualAsset(projectId, visualAssetId) {
    try {
      const result = await this.visualAssetManager.deleteAsset(visualAssetId);
      if (result && result.deleted) { // Check for the 'deleted' property
        return this.messageHandler.createSuccessResponse(
          `Visual asset '${visualAssetId}' deleted successfully from project ${projectId}`,
          { projectId, ...result }
        );
      } else {
        throw new Error(result.error || `Failed to delete visual asset ${visualAssetId}`);
      }
    } catch (error) {
      return this.messageHandler.createErrorResponse(
        'VISUAL_ASSET_DELETION_ERROR',
        `Failed to delete visual asset: ${error.message}`,
        { projectId, visualAssetId, error: error.message }
      );
    }
  }
  // Removed extraneous ); and } that were here

  /**
   * Complete project onboarding
   * @param {string} projectId - Project identifier
   * @param {object} projectData - Onboarding completion data
   * @returns {object} Onboarding completion result
   */
  async completeOnboarding(projectId, projectData) {
    const onboardingResult = {
      project_id: projectId,
      completed_at: new Date().toISOString(),
      onboarding_summary: {
        duration: projectData.onboarding_duration || '2 hours',
        steps_completed: projectData.steps_completed || [
          'Project setup',
          'Tool configuration',
          'Workflow introduction',
          'First task assignment'
        ],
        knowledge_areas_covered: projectData.knowledge_areas || [
          'Project structure',
          'Development workflow',
          'Quality standards',
          'Communication channels'
        ]
      },
      readiness_assessment: {
        technical_readiness: projectData.technical_readiness || 85,
        process_readiness: projectData.process_readiness || 90,
        team_readiness: projectData.team_readiness || 88,
        overall_readiness: 88
      },
      next_phase: 'active_development',
      recommendations: [
        'Begin with low-complexity tasks',
        'Regular check-ins during first week',
        'Access to mentoring resources'
      ]
    };

    return this.messageHandler.createSuccessResponse(
      'Project onboarding completed successfully',
      onboardingResult
    );
  }

  /**
   * Update project state
   * @param {string} projectId - Project identifier
   * @param {object} projectData - State update data
   * @returns {object} State update result
   */
  async updateProjectState(projectId, projectData) {
    const stateUpdate = {
      project_id: projectId,
      updated_at: new Date().toISOString(),
      changes: projectData.changes || {},
      previous_state: projectData.previous_state || {},
      new_state: projectData.new_state || {},
      change_summary: {
        fields_modified: Object.keys(projectData.changes || {}),
        change_type: projectData.change_type || 'configuration_update',
        impact_level: projectData.impact_level || 'low'
      }
    };

    return this.messageHandler.createSuccessResponse(
      'Project state updated successfully',
      stateUpdate
    );
  }

  /**
   * Get project status
   * @param {string} projectId - Project identifier
   * @returns {object} Project status
   */
  async getProjectStatus(projectId) {
    const status = {
      project_id: projectId,
      status_as_of: new Date().toISOString(),
      current_phase: 'development',
      overall_health: 'good',
      progress: {
        completion_percentage: 65,
        milestones_completed: 3,
        milestones_total: 5
      },
      metrics: {
        velocity: 8.5,
        quality_score: 88,
        team_satisfaction: 4.2
      },
      active_issues: [],
      upcoming_milestones: [
        { name: 'Feature freeze', date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() },
        { name: 'Testing phase', date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString() }
      ]
    };

    return this.messageHandler.createSuccessResponse(
      'Project status retrieved successfully',
      status
    );
  }

  // Helper methods for phase management
  getNextPhase(currentPhase) {
    const phases = ['initialization', 'planning', 'development', 'testing', 'deployment', 'maintenance'];
    const currentIndex = phases.indexOf(currentPhase);
    return currentIndex < phases.length - 1 ? phases[currentIndex + 1] : 'completed';
  }

  getPhaseObjectives(phase) {
    const objectives = {
      planning: ['Define requirements', 'Create project plan', 'Set up team'],
      development: ['Implement features', 'Write tests', 'Create documentation'],
      testing: ['Execute test plans', 'Fix bugs', 'Validate requirements'],
      deployment: ['Deploy to production', 'Monitor performance', 'Train users']
    };
    return objectives[phase] || ['Complete phase activities'];
  }

  getPhaseDeliverables(phase) {
    const deliverables = {
      planning: ['Project plan', 'Requirements document', 'Team charter'],
      development: ['Source code', 'Unit tests', 'Technical documentation'],
      testing: ['Test results', 'Bug reports', 'Quality assessment'],
      deployment: ['Deployment guide', 'User manual', 'Performance report']
    };
    return deliverables[phase] || ['Phase deliverables'];
  }

  getPhaseEstimatedDuration(phase) {
    const durations = {
      planning: '1-2 weeks',
      development: '4-8 weeks',
      testing: '1-2 weeks',
      deployment: '3-5 days'
    };
    return durations[phase] || '1-2 weeks';
  }

  getPhaseRecommendations(phase) {
    const recommendations = {
      planning: ['Involve stakeholders in planning', 'Define clear success criteria'],
      development: ['Follow coding standards', 'Implement continuous integration'],
      testing: ['Automate testing where possible', 'Include user acceptance testing'],
      deployment: ['Plan rollback strategy', 'Monitor system performance']
    };
    return recommendations[phase] || ['Follow best practices for this phase'];
  }
}

/**
 * Register project manager tool with MCP server
 * @param {object} server - MCP server instance
 * @param {object} existingInfrastructure - Existing tool infrastructure
 */
export function registerProjectManagerTool(server, existingInfrastructure = {}) {
  const tool = new ProjectManagerTool(existingInfrastructure);
  const definition = tool.getToolDefinition();

  server.addTool(definition, async (args) => {
    return await tool.execute(args);
  });

  console.log('🔧 Registered consolidated Project Manager tool');
}

export default ProjectManagerTool;
