/**
 * Conversation Management Integration (Task 3.4)
 * 
 * Integrates conversation intelligence with existing WLR-004 conversation management system.
 * Ensures research timing works with current conversation flow and maintains conversation 
 * continuity during background research.
 */

import { EventEmitter } from 'events';
import { ContextAnalysisEngine } from './context-analysis-engine.js';
import { ResearchTimingPatterns } from './research-timing-patterns.js';
import { AutonomousResearchOrchestrator } from './autonomous-research-orchestrator.js';
import { getSessionRecoveryManager } from '../workflow-logic/session-recovery-manager.js';
import { ContextManager } from '../workflow-logic/context-manager.js';

/**
 * Conversation Intelligence Manager
 * Orchestrates conversation intelligence with WLR-004 conversation management
 */
export class ConversationIntelligenceManager extends EventEmitter {
  constructor(config = {}) {
    super();
    this.config = config;
    this.projectRoot = config.projectRoot || process.cwd();
    
    // Initialize conversation intelligence components
    this.contextAnalysisEngine = new ContextAnalysisEngine(config.contextAnalysis || {});
    this.researchTimingPatterns = new ResearchTimingPatterns(config.researchTiming || {});
    this.autonomousResearchOrchestrator = new AutonomousResearchOrchestrator(config.research || {});
    
    // Initialize WLR-004 components
    this.sessionManager = getSessionRecoveryManager(this.projectRoot);
    this.contextManager = new ContextManager(this.projectRoot);
    
    // State management
    this.activeConversations = new Map();
    this.backgroundResearchJobs = new Map();
    this.conversationHistory = new Map();
    
    // Setup event listeners
    this.setupEventListeners();
  }

  /**
   * Setup event listeners for conversation intelligence integration
   */
  setupEventListeners() {
    // Listen for research timing events
    this.researchTimingPatterns.on('milestoneDetected', (milestone) => {
      this.handleMilestoneDetected(milestone);
    });

    this.researchTimingPatterns.on('researchTriggered', (trigger) => {
      this.handleResearchTriggered(trigger);
    });

    // Listen for autonomous research events
    this.autonomousResearchOrchestrator.on('researchCompleted', (result) => {
      this.handleResearchCompleted(result);
    });

    this.autonomousResearchOrchestrator.on('researchFailed', (error) => {
      this.handleResearchFailed(error);
    });

    this.autonomousResearchOrchestrator.on('researchQueued', (job) => {
      this.handleResearchQueued(job);
    });
  }

  /**
   * Initialize conversation intelligence for a session
   * @param {string} sessionId - Session ID
   * @param {object} options - Initialization options
   * @returns {Promise<boolean>} Success status
   */
  async initializeConversationIntelligence(sessionId, options = {}) {
    try {
      // Get session from WLR-004
      const session = await this.sessionManager.getSession(sessionId);
      if (!session) {
        throw new Error(`Session ${sessionId} not found`);
      }

      // Initialize conversation state
      const conversationState = {
        sessionId,
        session,
        conversationHistory: [],
        lastAnalysis: null,
        activeResearchJobs: [],
        researchResults: new Map(),
        intelligenceEnabled: options.enableIntelligence !== false,
        backgroundResearchEnabled: options.enableBackgroundResearch !== false
      };

      this.activeConversations.set(sessionId, conversationState);
      this.conversationHistory.set(sessionId, []);

      // Start autonomous research orchestrator if enabled
      if (conversationState.backgroundResearchEnabled) {
        this.autonomousResearchOrchestrator.start();
      }

      this.emit('conversationIntelligenceInitialized', { sessionId, options });
      return true;
    } catch (error) {
      console.error('Failed to initialize conversation intelligence:', error);
      this.emit('initializationError', { sessionId, error });
      return false;
    }
  }

  /**
   * Process conversation message with intelligence
   * @param {string} sessionId - Session ID
   * @param {object} message - Conversation message
   * @returns {Promise<object>} Processing result
   */
  async processConversationMessage(sessionId, message) {
    try {
      const conversationState = this.activeConversations.get(sessionId);
      if (!conversationState) {
        throw new Error(`Conversation intelligence not initialized for session ${sessionId}`);
      }

      // Add message to conversation history
      const timestampedMessage = {
        ...message,
        timestamp: new Date().toISOString(),
        sessionId
      };

      conversationState.conversationHistory.push(timestampedMessage);
      const sessionHistory = this.conversationHistory.get(sessionId) || [];
      sessionHistory.push(timestampedMessage);
      this.conversationHistory.set(sessionId, sessionHistory);

      // Update WLR-004 session with last message
      await this.sessionManager.updateLastMessage(
        sessionId,
        message.content || message.text || 'Message processed',
        `Conversation intelligence: ${sessionHistory.length} messages processed`
      );

      let result = {
        sessionId,
        messageProcessed: true,
        intelligenceResults: null,
        researchTriggered: false,
        backgroundJobs: []
      };

      // Process with conversation intelligence if enabled
      if (conversationState.intelligenceEnabled) {
        result.intelligenceResults = await this.analyzeConversationIntelligence(sessionId);
        
        // Check for research triggers
        if (conversationState.backgroundResearchEnabled) {
          const researchJobs = await this.checkResearchTriggers(sessionId);
          result.researchTriggered = researchJobs.length > 0;
          result.backgroundJobs = researchJobs;
        }
      }

      this.emit('messageProcessed', result);
      return result;
    } catch (error) {
      console.error('Failed to process conversation message:', error);
      this.emit('messageProcessingError', { sessionId, message, error });
      throw error;
    }
  }

  /**
   * Analyze conversation intelligence for a session
   * @param {string} sessionId - Session ID
   * @returns {Promise<object>} Analysis results
   */
  async analyzeConversationIntelligence(sessionId) {
    try {
      const conversationState = this.activeConversations.get(sessionId);
      if (!conversationState) {
        throw new Error(`Conversation state not found for session ${sessionId}`);
      }

      const conversationHistory = conversationState.conversationHistory;
      
      // Perform context analysis
      const contextAnalysis = await this.contextAnalysisEngine.analyzeContext(conversationHistory);
      
      // Analyze research timing patterns
      const timingAnalysis = this.researchTimingPatterns.analyzeTimingPatterns(
        conversationHistory,
        contextAnalysis.projectContext,
        conversationState.lastAnalysis
      );

      // Store analysis results
      conversationState.lastAnalysis = {
        contextAnalysis,
        timingAnalysis,
        analyzedAt: new Date().toISOString()
      };

      // Update WLR-004 session with analysis insights
      await this.updateSessionWithIntelligence(sessionId, conversationState.lastAnalysis);

      return conversationState.lastAnalysis;
    } catch (error) {
      console.error('Conversation intelligence analysis failed:', error);
      throw error;
    }
  }

  /**
   * Check for research triggers based on conversation analysis
   * @param {string} sessionId - Session ID
   * @returns {Promise<Array>} Research jobs triggered
   */
  async checkResearchTriggers(sessionId) {
    try {
      const conversationState = this.activeConversations.get(sessionId);
      if (!conversationState || !conversationState.lastAnalysis) {
        return [];
      }

      const { timingAnalysis } = conversationState.lastAnalysis;
      const researchJobs = [];

      // Process research triggers
      for (const trigger of timingAnalysis.researchTriggers || []) {
        // Check if this trigger hasn't been processed yet
        const existingJob = conversationState.activeResearchJobs.find(job => job.triggerId === trigger.id);
        if (existingJob) {
          continue;
        }

        // Submit research request
        const researchRequest = this.createResearchRequest(trigger, conversationState);
        const jobId = this.autonomousResearchOrchestrator.submitResearch(researchRequest);

        const researchJob = {
          jobId,
          triggerId: trigger.id,
          trigger,
          sessionId,
          submittedAt: new Date().toISOString(),
          status: 'queued'
        };

        conversationState.activeResearchJobs.push(researchJob);
        this.backgroundResearchJobs.set(jobId, researchJob);
        researchJobs.push(researchJob);
      }

      return researchJobs;
    } catch (error) {
      console.error('Failed to check research triggers:', error);
      return [];
    }
  }

  /**
   * Update WLR-004 session with conversation intelligence insights
   * @param {string} sessionId - Session ID
   * @param {object} analysis - Analysis results
   */
  async updateSessionWithIntelligence(sessionId, analysis) {
    try {
      const { contextAnalysis, timingAnalysis } = analysis;

      // Update session context with project insights
      await this.sessionManager.updateSession(sessionId, session => ({
        ...session,
        projectContext: {
          ...session.projectContext,
          // Enhance with AI insights
          projectDomain: contextAnalysis.projectContext.domain,
          businessModel: contextAnalysis.projectContext.businessModel,
          targetAudience: contextAnalysis.projectContext.targetAudience,
          lastAction: `Intelligence analysis: ${timingAnalysis.milestonesDetected || 0} milestones detected`
        },
        // Add intelligence metadata
        intelligenceData: {
          lastAnalysis: analysis.analyzedAt,
          contextConfidence: contextAnalysis.projectContext.confidence,
          informationGaps: contextAnalysis.informationGaps?.length || 0,
          researchTriggers: timingAnalysis.triggersGenerated || 0
        }
      }));

      // Update orientation with intelligence insights
      const orientationMessage = this.generateIntelligenceOrientationMessage(analysis);
      await this.contextManager.updateOrientation(sessionId, orientationMessage);

    } catch (error) {
      console.error('Failed to update session with intelligence:', error);
    }
  }

  /**
   * Create research request from trigger
   * @param {object} trigger - Research trigger
   * @param {object} conversationState - Conversation state
   * @returns {object} Research request
   */
  createResearchRequest(trigger, conversationState) {
    return {
      id: trigger.id,
      type: trigger.type,
      query: trigger.query || trigger.description,
      context: {
        sessionId: conversationState.sessionId,
        projectContext: conversationState.lastAnalysis?.contextAnalysis?.projectContext,
        conversationLength: conversationState.conversationHistory.length,
        trigger: trigger
      },
      priority: trigger.priority || 'medium',
      providers: trigger.suggestedProviders || ['tavily'],
      maxRetries: 3,
      timeout: 30000
    };
  }

  /**
   * Generate orientation message with intelligence insights
   * @param {object} analysis - Analysis results
   * @returns {string} Orientation message
   */
  generateIntelligenceOrientationMessage(analysis) {
    const { contextAnalysis, timingAnalysis } = analysis;
    const projectContext = contextAnalysis.projectContext;

    let message = '';

    if (projectContext.domain) {
      message += `Working on a ${projectContext.domain} project`;
      if (projectContext.businessModel) {
        message += ` with ${projectContext.businessModel} business model`;
      }
      message += '. ';
    }

    if (contextAnalysis.informationGaps?.length > 0) {
      message += `${contextAnalysis.informationGaps.length} information gaps identified. `;
    }

    if (timingAnalysis.researchTriggers?.length > 0) {
      message += `${timingAnalysis.researchTriggers.length} research opportunities detected. `;
    }

    return message || 'Conversation intelligence active and monitoring for research opportunities.';
  }

  /**
   * Handle milestone detected event
   * @param {object} milestone - Detected milestone
   */
  async handleMilestoneDetected(milestone) {
    try {
      // Find the session for this milestone
      const sessionId = milestone.sessionId;
      if (!sessionId) return;

      // Update session with milestone information
      await this.sessionManager.updateLastMessage(
        sessionId,
        `Milestone detected: ${milestone.type}`,
        `Conversation milestone: ${milestone.description || milestone.type}`
      );

      this.emit('milestoneProcessed', { sessionId, milestone });
    } catch (error) {
      console.error('Failed to handle milestone detected:', error);
    }
  }

  /**
   * Handle research triggered event
   * @param {object} trigger - Research trigger
   */
  async handleResearchTriggered(trigger) {
    try {
      const sessionId = trigger.sessionId;
      if (!sessionId) return;

      // Update session with research trigger information
      await this.sessionManager.updateLastMessage(
        sessionId,
        `Research triggered: ${trigger.type}`,
        `Background research initiated: ${trigger.description || trigger.type}`
      );

      this.emit('researchTriggerProcessed', { sessionId, trigger });
    } catch (error) {
      console.error('Failed to handle research triggered:', error);
    }
  }

  /**
   * Handle research completed event
   * @param {object} result - Research result
   */
  async handleResearchCompleted(result) {
    try {
      const researchJob = this.backgroundResearchJobs.get(result.jobId);
      if (!researchJob) return;

      const sessionId = researchJob.sessionId;
      const conversationState = this.activeConversations.get(sessionId);

      if (conversationState) {
        // Store research result
        conversationState.researchResults.set(result.jobId, result.result);

        // Update job status
        researchJob.status = 'completed';
        researchJob.completedAt = new Date().toISOString();
        researchJob.result = result.result;

        // Update session with research completion
        await this.sessionManager.updateLastMessage(
          sessionId,
          `Research completed: ${researchJob.trigger.type}`,
          `Background research finished: ${result.result?.summary || 'Research data available'}`
        );

        // Remove from active jobs
        conversationState.activeResearchJobs = conversationState.activeResearchJobs.filter(
          job => job.jobId !== result.jobId
        );
      }

      this.emit('backgroundResearchCompleted', { sessionId, jobId: result.jobId, result });
    } catch (error) {
      console.error('Failed to handle research completed:', error);
    }
  }

  /**
   * Handle research failed event
   * @param {object} error - Research error
   */
  async handleResearchFailed(error) {
    try {
      const researchJob = this.backgroundResearchJobs.get(error.jobId);
      if (!researchJob) return;

      const sessionId = researchJob.sessionId;

      // Update job status
      researchJob.status = 'failed';
      researchJob.failedAt = new Date().toISOString();
      researchJob.error = error.error;

      // Update session with research failure
      await this.sessionManager.updateLastMessage(
        sessionId,
        `Research failed: ${researchJob.trigger.type}`,
        `Background research encountered an error: ${error.error?.message || 'Unknown error'}`
      );

      this.emit('backgroundResearchFailed', { sessionId, jobId: error.jobId, error });
    } catch (error) {
      console.error('Failed to handle research failed:', error);
    }
  }

  /**
   * Handle research queued event
   * @param {object} job - Queued job
   */
  async handleResearchQueued(job) {
    try {
      const researchJob = this.backgroundResearchJobs.get(job.jobId);
      if (!researchJob) return;

      const sessionId = researchJob.sessionId;

      // Update session with research queue status
      await this.sessionManager.updateLastMessage(
        sessionId,
        `Research queued: ${researchJob.trigger.type}`,
        `Background research queued for processing`
      );

      this.emit('backgroundResearchQueued', { sessionId, jobId: job.jobId });
    } catch (error) {
      console.error('Failed to handle research queued:', error);
    }
  }

  /**
   * Get conversation intelligence status for a session
   * @param {string} sessionId - Session ID
   * @returns {object|null} Intelligence status
   */
  getConversationIntelligenceStatus(sessionId) {
    const conversationState = this.activeConversations.get(sessionId);
    if (!conversationState) {
      return null;
    }

    return {
      sessionId,
      intelligenceEnabled: conversationState.intelligenceEnabled,
      backgroundResearchEnabled: conversationState.backgroundResearchEnabled,
      conversationLength: conversationState.conversationHistory.length,
      lastAnalysis: conversationState.lastAnalysis?.analyzedAt || null,
      activeResearchJobs: conversationState.activeResearchJobs.length,
      completedResearchJobs: conversationState.researchResults.size,
      status: 'active'
    };
  }

  /**
   * Get research results for a session
   * @param {string} sessionId - Session ID
   * @returns {Array} Research results
   */
  getResearchResults(sessionId) {
    const conversationState = this.activeConversations.get(sessionId);
    if (!conversationState) {
      return [];
    }

    return Array.from(conversationState.researchResults.entries()).map(([jobId, result]) => ({
      jobId,
      result,
      job: this.backgroundResearchJobs.get(jobId)
    }));
  }

  /**
   * Maintain conversation continuity during background research
   * @param {string} sessionId - Session ID
   * @returns {Promise<object>} Continuity status
   */
  async maintainConversationContinuity(sessionId) {
    try {
      const conversationState = this.activeConversations.get(sessionId);
      if (!conversationState) {
        throw new Error(`Conversation state not found for session ${sessionId}`);
      }

      // Check if background research is affecting conversation flow
      const activeJobs = conversationState.activeResearchJobs.filter(job => job.status === 'processing');

      if (activeJobs.length > 0) {
        // Update session to indicate background processing
        await this.sessionManager.updateLastMessage(
          sessionId,
          'Background research in progress',
          `${activeJobs.length} research jobs running in background`
        );

        // Generate continuity message
        const continuityMessage = this.generateContinuityMessage(activeJobs);
        await this.contextManager.updateOrientation(sessionId, continuityMessage);
      }

      return {
        sessionId,
        continuityMaintained: true,
        activeBackgroundJobs: activeJobs.length,
        message: activeJobs.length > 0 ? 'Background research in progress' : 'No background activity'
      };
    } catch (error) {
      console.error('Failed to maintain conversation continuity:', error);
      return {
        sessionId,
        continuityMaintained: false,
        error: error.message
      };
    }
  }

  /**
   * Generate continuity message for background research
   * @param {Array} activeJobs - Active research jobs
   * @returns {string} Continuity message
   */
  generateContinuityMessage(activeJobs) {
    if (activeJobs.length === 0) {
      return 'Conversation intelligence monitoring for research opportunities.';
    }

    const jobTypes = activeJobs.map(job => job.trigger.type).join(', ');
    return `Background research in progress (${jobTypes}). Conversation continues normally while research completes.`;
  }

  /**
   * Cleanup conversation intelligence for a session
   * @param {string} sessionId - Session ID
   * @returns {Promise<boolean>} Success status
   */
  async cleanupConversationIntelligence(sessionId) {
    try {
      const conversationState = this.activeConversations.get(sessionId);
      if (!conversationState) {
        return true; // Already cleaned up
      }

      // Cancel any active research jobs
      for (const job of conversationState.activeResearchJobs) {
        this.backgroundResearchJobs.delete(job.jobId);
      }

      // Clean up state
      this.activeConversations.delete(sessionId);
      this.conversationHistory.delete(sessionId);

      // Update session to indicate cleanup
      await this.sessionManager.updateLastMessage(
        sessionId,
        'Conversation intelligence cleanup completed',
        'Background research stopped and state cleaned up'
      );

      this.emit('conversationIntelligenceCleanedUp', { sessionId });
      return true;
    } catch (error) {
      console.error('Failed to cleanup conversation intelligence:', error);
      return false;
    }
  }

  /**
   * Stop autonomous research orchestrator
   */
  stop() {
    this.autonomousResearchOrchestrator.stop();
    this.emit('stopped');
  }

  /**
   * Get overall statistics
   * @returns {object} Statistics
   */
  getStats() {
    return {
      activeConversations: this.activeConversations.size,
      totalBackgroundJobs: this.backgroundResearchJobs.size,
      orchestratorStats: this.autonomousResearchOrchestrator.getStats(),
      conversationHistorySize: Array.from(this.conversationHistory.values())
        .reduce((total, history) => total + history.length, 0)
    };
  }
}

/**
 * Factory function to create conversation intelligence manager
 * @param {object} config - Configuration
 * @returns {ConversationIntelligenceManager} Manager instance
 */
export function createConversationIntelligenceManager(config = {}) {
  return new ConversationIntelligenceManager(config);
}

export default ConversationIntelligenceManager;
