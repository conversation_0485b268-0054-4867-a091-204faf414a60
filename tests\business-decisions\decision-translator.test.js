import { test, expect, beforeEach, afterEach, describe } from "bun:test";
import { translateTechnicalToBusinessTerms, DecisionTranslator } from '../../src/business-decisions/decision-translator.js';

describe('Decision Translator', () => {
  let translator;
  
  beforeEach(() => {
    // Create a translator instance
    translator = new DecisionTranslator();
    
    // Enable mock mode for the AI translator
    translator.aiTranslator.setMockMode(true);
  });
  
  describe('translateTechnicalToBusinessTerms', () => {
    test('should translate known technical terms to business language', async () => {
      // Mock the DecisionTranslator constructor to return our instance
      const originalDecisionTranslator = global.DecisionTranslator;
      global.DecisionTranslator = function() { return translator; };
      
      const technicalDescription = 'This framework uses API calls to connect to the database.';
      const result = await translateTechnicalToBusinessTerms(technicalDescription, 'novice');
      
      // Should replace known terms
      expect(result).toContain('Building foundation');
      expect(result).toContain('Data connection');
      expect(result).toContain('Information storage');
      
      // Restore the original constructor
      global.DecisionTranslator = originalDecisionTranslator;
    });
    
    test('should use AI translation for unknown technical terms', async () => {
      // Mock the DecisionTranslator constructor to return our instance
      const originalDecisionTranslator = global.DecisionTranslator;
      global.DecisionTranslator = function() { return translator; };
      
      // Force the _isDescriptionTechnical method to return true
      const originalIsDescriptionTechnical = translator._isDescriptionTechnical;
      translator._isDescriptionTechnical = () => true;
      
      const technicalDescription = 'This uses GraphQL with Apollo Client for state management.';
      const result = await translateTechnicalToBusinessTerms(technicalDescription, 'novice');
      
      // Should have used AI translation for unknown terms
      expect(result).toBe('AI-translated business description');
      
      // Restore the original methods
      global.DecisionTranslator = originalDecisionTranslator;
      translator._isDescriptionTechnical = originalIsDescriptionTechnical;
    });
    
    test('should use less simplified language for intermediate users', async () => {
      const technicalDescription = 'This framework uses API calls to connect to the database.';
      const result = await translateTechnicalToBusinessTerms(technicalDescription, 'intermediate');
      
      // Should use less simplified terms for intermediate users
      expect(result).toBe(technicalDescription);
    });
    
    test('should use technical language for advanced users but with business context', async () => {
      const technicalDescription = 'React uses a virtual DOM for efficient rendering.';
      const result = await translateTechnicalToBusinessTerms(technicalDescription, 'advanced');
      
      // For advanced users, should keep technical terms but add business context
      expect(result).toBe(technicalDescription);
    });
  });
}); 