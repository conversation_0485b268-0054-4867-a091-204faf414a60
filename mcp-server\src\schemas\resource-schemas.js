/**
 * MCP Resource Schemas
 * Defines URI templates and schemas for Guidant MCP resources
 */

/**
 * Resource URI Templates following RFC 6570
 */
export const RESOURCE_TEMPLATES = {
  // Project Resources
  project_state: {
    uriTemplate: "guidant://project/{projectId}/state",
    name: "Project State",
    description: "Current project state, configuration, and metadata",
    mimeType: "application/json"
  },

  project_phase: {
    uriTemplate: "guidant://project/{projectId}/phase/{phase}",
    name: "Project Phase Data",
    description: "Phase-specific information, deliverables, and progress",
    mimeType: "application/json"
  },

  current_task: {
    uriTemplate: "guidant://project/{projectId}/current-task",
    name: "Current Task",
    description: "Active task information and execution context",
    mimeType: "application/json"
  },

  project_deliverables: {
    uriTemplate: "guidant://project/{projectId}/deliverables/{type?}",
    name: "Project Deliverables",
    description: "Project deliverables, optionally filtered by type",
    mimeType: "application/json"
  },

  // Deliverable Resources
  deliverable_analysis: {
    uriTemplate: "guidant://deliverable/{deliverableId}/analysis",
    name: "Deliverable Analysis",
    description: "Comprehensive analysis and quality assessment of deliverable",
    mimeType: "application/json"
  },

  deliverable_content: {
    uriTemplate: "guidant://deliverable/{deliverableId}/content",
    name: "Deliverable Content",
    description: "Raw content and metadata of deliverable",
    mimeType: "application/json"
  },

  deliverable_history: {
    uriTemplate: "guidant://deliverable/{deliverableId}/history",
    name: "Deliverable History",
    description: "Version history and change tracking for deliverable",
    mimeType: "application/json"
  },

  // Analytics Resources
  project_insights: {
    uriTemplate: "guidant://analytics/{projectId}/insights/{timeframe?}",
    name: "Project Insights",
    description: "Analytics insights and trends for specified timeframe",
    mimeType: "application/json"
  },

  performance_metrics: {
    uriTemplate: "guidant://analytics/{projectId}/metrics/{metric?}",
    name: "Performance Metrics",
    description: "Performance metrics and KPIs, optionally filtered by metric type",
    mimeType: "application/json"
  },

  quality_assessment: {
    uriTemplate: "guidant://analytics/{projectId}/quality/{component?}",
    name: "Quality Assessment",
    description: "Quality assessment data for project components",
    mimeType: "application/json"
  },

  // Workflow Resources
  workflow_definition: {
    uriTemplate: "guidant://workflow/{workflowId}/definition",
    name: "Workflow Definition",
    description: "Workflow structure, steps, and configuration",
    mimeType: "application/json"
  },

  workflow_execution: {
    uriTemplate: "guidant://workflow/{workflowId}/execution/{executionId}",
    name: "Workflow Execution",
    description: "Workflow execution status, results, and timeline",
    mimeType: "application/json"
  },

  workflow_metrics: {
    uriTemplate: "guidant://workflow/{workflowId}/metrics/{timeframe?}",
    name: "Workflow Metrics",
    description: "Workflow performance metrics for specified timeframe",
    mimeType: "application/json"
  },

  // Session Resources
  session_state: {
    uriTemplate: "guidant://session/{sessionId}/state",
    name: "Session State",
    description: "Current session state and context information",
    mimeType: "application/json"
  },

  session_history: {
    uriTemplate: "guidant://session/{sessionId}/history",
    name: "Session History",
    description: "Session interaction history and conversation flow",
    mimeType: "application/json"
  },

  // Decision Resources
  decision_options: {
    uriTemplate: "guidant://project/{projectId}/decisions/options/{decisionType?}",
    name: "Decision Options",
    description: "Available decision options and recommendations",
    mimeType: "application/json"
  },

  decision_history: {
    uriTemplate: "guidant://project/{projectId}/decisions/history",
    name: "Decision History",
    description: "Historical decisions and their outcomes",
    mimeType: "application/json"
  }
};

/**
 * Resource content schemas for validation
 */
export const RESOURCE_CONTENT_SCHEMAS = {
  project_state: {
    type: "object",
    properties: {
      projectId: { type: "string" },
      name: { type: "string" },
      description: { type: "string" },
      phase: { type: "string" },
      status: { type: "string", enum: ["active", "paused", "completed", "cancelled"] },
      created: { type: "string", format: "date-time" },
      updated: { type: "string", format: "date-time" },
      configuration: { type: "object" },
      metadata: { type: "object" }
    },
    required: ["projectId", "name", "phase", "status"]
  },

  project_phase: {
    type: "object",
    properties: {
      projectId: { type: "string" },
      phase: { type: "string" },
      status: { type: "string" },
      progress: { type: "number", minimum: 0, maximum: 100 },
      deliverables: { type: "array", items: { type: "object" } },
      tasks: { type: "array", items: { type: "object" } },
      timeline: { type: "object" },
      dependencies: { type: "array", items: { type: "string" } }
    },
    required: ["projectId", "phase", "status", "progress"]
  },

  current_task: {
    type: "object",
    properties: {
      taskId: { type: "string" },
      projectId: { type: "string" },
      name: { type: "string" },
      description: { type: "string" },
      status: { type: "string" },
      priority: { type: "string", enum: ["low", "medium", "high", "critical"] },
      assignee: { type: "string" },
      created: { type: "string", format: "date-time" },
      updated: { type: "string", format: "date-time" },
      context: { type: "object" }
    },
    required: ["taskId", "projectId", "name", "status"]
  },

  deliverable_analysis: {
    type: "object",
    properties: {
      deliverableId: { type: "string" },
      analysisId: { type: "string" },
      timestamp: { type: "string", format: "date-time" },
      quality_score: { type: "number", minimum: 0, maximum: 100 },
      completeness: { type: "number", minimum: 0, maximum: 100 },
      insights: { type: "array", items: { type: "object" } },
      recommendations: { type: "array", items: { type: "string" } },
      issues: { type: "array", items: { type: "object" } },
      metadata: { type: "object" }
    },
    required: ["deliverableId", "analysisId", "timestamp", "quality_score"]
  },

  workflow_execution: {
    type: "object",
    properties: {
      executionId: { type: "string" },
      workflowId: { type: "string" },
      status: { type: "string", enum: ["pending", "running", "completed", "failed", "cancelled"] },
      started: { type: "string", format: "date-time" },
      completed: { type: "string", format: "date-time" },
      steps: { type: "array", items: { type: "object" } },
      results: { type: "object" },
      errors: { type: "array", items: { type: "object" } },
      metrics: { type: "object" }
    },
    required: ["executionId", "workflowId", "status", "started"]
  }
};

/**
 * Resource template utilities
 */
export class ResourceTemplateUtils {
  /**
   * Get all resource templates
   * @returns {Array} Array of resource templates
   */
  static getAllTemplates() {
    return Object.entries(RESOURCE_TEMPLATES).map(([key, template]) => ({
      key,
      ...template
    }));
  }

  /**
   * Get resource templates by category
   * @param {string} category - Category name (project, deliverable, analytics, workflow, session, decision)
   * @returns {Array} Filtered resource templates
   */
  static getTemplatesByCategory(category) {
    return this.getAllTemplates().filter(template => 
      template.key.startsWith(category)
    );
  }

  /**
   * Find template by URI pattern
   * @param {string} uri - URI to match
   * @returns {object|null} Matching template or null
   */
  static findTemplateByURI(uri) {
    for (const [key, template] of Object.entries(RESOURCE_TEMPLATES)) {
      // Simple pattern matching - could be enhanced with URI template parser
      const pattern = template.uriTemplate
        .replace(/\{[^}]+\}/g, '[^/]+')
        .replace(/\{[^}]+\?\}/g, '[^/]*');
      
      const regex = new RegExp(`^${pattern}$`);
      if (regex.test(uri)) {
        return { key, ...template };
      }
    }
    return null;
  }

  /**
   * Validate resource content against schema
   * @param {string} templateKey - Template key
   * @param {object} content - Content to validate
   * @returns {object} Validation result
   */
  static validateContent(templateKey, content) {
    const schema = RESOURCE_CONTENT_SCHEMAS[templateKey];
    if (!schema) {
      return { valid: true, errors: [] }; // No schema defined
    }

    // Basic validation - could be enhanced with a proper JSON schema validator
    const errors = [];
    
    if (schema.required) {
      for (const field of schema.required) {
        if (!content.hasOwnProperty(field)) {
          errors.push(`Missing required field: ${field}`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

export default RESOURCE_TEMPLATES;
