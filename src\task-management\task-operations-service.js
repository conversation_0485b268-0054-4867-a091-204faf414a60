/**
 * Task Operations Service for Guidant
 * Provides higher-level operations related to tasks,
 * such as complexity scoring, and eventually, more advanced
 * business logic beyond simple persistence.
 */

import { TaskPersistenceService } from './task-persistence-service.js';
import { RichTaskSchema } from './task-schema.js'; // For referencing schema details if needed

export class TaskOperationsService {
  constructor(projectRoot = process.cwd()) {
    this.persistenceService = new TaskPersistenceService(projectRoot);
  }

  /**
   * Calculates the complexity score for a task based on predefined factors.
   * The formula is: weighted_average(technical * 0.4, dependencies * 0.3, impact * 0.2, urgency * 0.1)
   * All factors are expected on a 1-10 scale, but the ticket specifies 1-4, 1-3, 1-3, 1-2.
   * We will assume input factors are normalized to a 1-10 scale by the caller or another service,
   * or we define a normalization logic here. For now, let's assume factors are provided.
   *
   * @param {object} factors - An object containing the scoring factors.
   * @param {number} factors.technicalDifficulty - Technical difficulty (1-10).
   * @param {number} factors.dependenciesComplexity - Complexity of dependencies (1-10).
   * @param {number} factors.businessImpact - Business impact (1-10).
   * @param {number} factors.timeSensitivity - Urgency or time sensitivity (1-10).
   * @returns {number} The calculated complexity score, rounded to one decimal place.
   */
  calculateComplexityScore(factors) {
    const {
      technicalDifficulty = 5, // Default to medium if not provided
      dependenciesComplexity = 5,
      businessImpact = 5,
      timeSensitivity = 5,
    } = factors;

    // Validate inputs (basic validation)
    const validateFactor = (value, name) => {
      if (typeof value !== 'number' || value < 1 || value > 10) {
        throw new Error(`Invalid ${name} factor: ${value}. Must be a number between 1 and 10.`);
      }
      return value;
    };

    try {
      const tech = validateFactor(technicalDifficulty, 'technicalDifficulty');
      const deps = validateFactor(dependenciesComplexity, 'dependenciesComplexity');
      const impact = validateFactor(businessImpact, 'businessImpact');
      const urgency = validateFactor(timeSensitivity, 'timeSensitivity');

      const score = (tech * 0.4) + (deps * 0.3) + (impact * 0.2) + (urgency * 0.1);
      return parseFloat(score.toFixed(1));
    } catch (error) {
      console.error("Error calculating complexity score:", error.message);
      // Return a default or throw, depending on desired handling
      return -1; // Indicate error
    }
  }

  /**
   * Updates a task with its calculated complexity score.
   * @param {string} taskId - The ID of the task to update.
   * @param {object} scoringFactors - Factors for calculating complexity.
   * @returns {Promise<Object|null>} The updated task object with complexityScore, or null.
   */
  async scoreTaskComplexity(taskId, scoringFactors) {
    const task = await this.persistenceService.getTaskById(taskId);
    if (!task) {
      throw new Error(`Task with ID ${taskId} not found for complexity scoring.`);
    }

    const complexityScore = this.calculateComplexityScore(scoringFactors);
    if (complexityScore === -1) {
        throw new Error(`Could not calculate complexity for task ${taskId} due to invalid factors.`);
    }

    return this.persistenceService.updateTask(taskId, { complexityScore });
  }

  // Future methods for TASK-003 & TASK-006 could be added here:
  // - Advanced dependency validation
  // - Business context integration for prioritization
  // - User preference integration

  /**
   * Sorts an array of tasks based on a multi-factor prioritization logic.
   * Current logic:
   * 1. Explicit Priority field (High > Medium > Low)
   * 2. Business Impact (higher is better - from task.businessContext.businessImpact)
   * 3. Complexity Score (lower is better - from task.complexityScore)
   * 4. Creation Date (older first)
   * This method can be expanded to include user preferences from UserPreferenceManager.
   * @param {Array<Object>} tasks - An array of task objects to sort.
   * @returns {Array<Object>} The sorted array of tasks.
   */
  prioritizeTasks(tasks = []) {
    if (!Array.isArray(tasks) || tasks.length === 0) {
      return [];
    }

    const priorityOrder = { 'high': 1, 'medium': 2, 'low': 3 };

    return [...tasks].sort((a, b) => {
      // Factor 1: Explicit Priority
      const priorityA = priorityOrder[a.priority?.toLowerCase()] || 3;
      const priorityB = priorityOrder[b.priority?.toLowerCase()] || 3;
      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }

      // Factor 2: Business Impact (higher is better)
      // Assuming businessImpact is a numerical score (e.g., 1-10)
      const impactA = typeof a.businessContext?.businessImpact === 'number' ? a.businessContext.businessImpact : 0;
      const impactB = typeof b.businessContext?.businessImpact === 'number' ? b.businessContext.businessImpact : 0;
      if (impactA !== impactB) {
        return impactB - impactA; // Sort descending for impact
      }

      // Factor 3: Complexity Score (lower is better)
      const complexityA = typeof a.complexityScore === 'number' ? a.complexityScore : Infinity;
      const complexityB = typeof b.complexityScore === 'number' ? b.complexityScore : Infinity;
      if (complexityA !== complexityB) {
        return complexityA - complexityB;
      }

      // Factor 4: Creation Date (older first)
      return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
    });
  }
}

// Example Usage:
// const taskOpsService = new TaskOperationsService();
// (async () => {
//   try {
//     // Assume a task with ID 'task_abc' exists
//     const factors = {
//       technicalDifficulty: 7,
//       dependenciesComplexity: 5,
//       businessImpact: 8,
//       timeSensitivity: 6
//     };
//     const updatedTask = await taskOpsService.scoreTaskComplexity('task_abc', factors);
//     if (updatedTask) {
//       console.log('Task with complexity score:', updatedTask);
//     }
//   } catch (e) {
//     console.error('Service Error:', e.message);
//   }
// })();