/**
 * Rate Limiting Performance Tests
 * 
 * Tests for rate limiting compliance, performance under load,
 * and concurrent request handling across research providers.
 */

import { describe, it, expect, beforeEach, afterEach, mock, spyOn } from 'bun:test';
import { TavilyProvider } from '../../src/research-providers/tavily-provider.js';
import { Context7Provider } from '../../src/research-providers/context7-provider.js';
import { FirecrawlProvider } from '../../src/research-providers/firecrawl-provider.js';
import { performResearchOperation } from '../../src/ai-integration/ai-services-unified.js';

describe('Rate Limiting Performance Tests', () => {
  let providers;

  beforeEach(() => {
    providers = {
      tavily: new TavilyProvider({
        apiKey: 'test-key',
        rateLimit: { requestsPerMinute: 60, burstLimit: 10 }
      }),
      context7: new Context7Provider({
        rateLimit: { requestsPerMinute: 100 }
      }),
      firecrawl: new FirecrawlProvider({
        apiKey: 'test-key',
        rateLimit: { requestsPerMinute: 100, concurrentBrowsers: 5 }
      })
    };
  });

  afterEach(() => {
    mock.restore();
  });

  describe('Individual Provider Rate Limiting', () => {
    it('should enforce Tavily rate limits correctly', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ results: [] })
      });

      const provider = providers.tavily;
      const requests = [];
      const startTime = Date.now();

      // Attempt to make requests at maximum rate
      for (let i = 0; i < 15; i++) {
        requests.push(
          provider.search(`test query ${i}`).catch(error => ({ error: error.message }))
        );
      }

      const results = await Promise.all(requests);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should have some rate limit errors for requests beyond burst limit
      const rateLimitErrors = results.filter(r => r.error && r.error.includes('Rate limit'));
      expect(rateLimitErrors.length).toBeGreaterThan(0);

      // Should take time to process due to rate limiting
      expect(duration).toBeGreaterThan(1000); // At least 1 second

      mockFetch.mockRestore();
    });

    it('should handle Context7 rate limits with token management', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ libraries: [] })
      });

      const provider = providers.context7;
      const requests = [];

      // Make rapid requests to test rate limiting
      for (let i = 0; i < 20; i++) {
        requests.push(
          provider.search(`library-${i}`).catch(error => ({ error: error.message }))
        );
      }

      const results = await Promise.all(requests);
      
      // Should have some rate limit enforcement
      const rateLimitErrors = results.filter(r => r.error && r.error.includes('Rate limit'));
      expect(rateLimitErrors.length).toBeGreaterThan(0);

      mockFetch.mockRestore();
    });

    it('should enforce Firecrawl concurrency limits', async () => {
      const mockFetch = spyOn(global, 'fetch').mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({
            ok: true,
            json: () => Promise.resolve({ success: true, data: { content: 'test' } })
          }), 500) // Simulate slow response
        )
      );

      const provider = providers.firecrawl;
      const urls = Array.from({ length: 10 }, (_, i) => `https://example.com/${i}`);
      
      const startTime = Date.now();
      const results = await provider.bulkScrape(urls, { concurrency: 3 });
      const endTime = Date.now();
      const duration = endTime - startTime;

      // With concurrency limit of 3 and 500ms per request,
      // 10 requests should take at least 1.5 seconds (3 batches)
      expect(duration).toBeGreaterThan(1400);
      expect(results).toHaveLength(10);

      mockFetch.mockRestore();
    });
  });

  describe('Multi-Provider Rate Limiting', () => {
    it('should handle concurrent requests across multiple providers', async () => {
      const mockFetch = spyOn(global, 'fetch')
        .mockResolvedValue({
          ok: true,
          json: () => Promise.resolve({ results: [] })
        });

      const mockPerformAI = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performAIOperation')
        .mockResolvedValue({ content: JSON.stringify({ summary: 'test' }) });

      const requests = [];
      
      // Create multiple research operations simultaneously
      for (let i = 0; i < 5; i++) {
        requests.push(
          performResearchOperation({
            type: 'market_research',
            query: `concurrent test ${i}`,
            providers: ['tavily', 'context7'],
            maxResults: 2
          }).catch(error => ({ error: error.message }))
        );
      }

      const results = await Promise.all(requests);
      
      // Most should succeed, but some may hit rate limits
      const successful = results.filter(r => !r.error);
      const failed = results.filter(r => r.error);

      expect(successful.length).toBeGreaterThan(0);
      
      // If any failed, they should be due to rate limiting
      failed.forEach(result => {
        expect(result.error).toMatch(/rate limit|timeout/i);
      });

      mockFetch.mockRestore();
      mockPerformAI.mockRestore();
    });

    it('should distribute load evenly across providers', async () => {
      const tavilyCallCount = { count: 0 };
      const context7CallCount = { count: 0 };

      const mockFetch = spyOn(global, 'fetch').mockImplementation((url) => {
        if (url.includes('tavily.com')) {
          tavilyCallCount.count++;
        } else if (url.includes('context7.com')) {
          context7CallCount.count++;
        }
        
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ results: [] })
        });
      });

      const mockPerformAI = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performAIOperation')
        .mockResolvedValue({ content: JSON.stringify({ summary: 'test' }) });

      // Perform multiple research operations
      const requests = [];
      for (let i = 0; i < 6; i++) {
        requests.push(
          performResearchOperation({
            type: 'technical_research',
            query: `load test ${i}`,
            providers: ['tavily', 'context7'],
            maxResults: 1
          })
        );
      }

      await Promise.all(requests);

      // Both providers should have been called
      expect(tavilyCallCount.count).toBeGreaterThan(0);
      expect(context7CallCount.count).toBeGreaterThan(0);

      mockFetch.mockRestore();
      mockPerformAI.mockRestore();
    });
  });

  describe('Performance Under Load', () => {
    it('should maintain response times under moderate load', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ results: [{ title: 'test', content: 'test' }] })
      });

      const mockPerformAI = spyOn(await import('../../src/ai-integration/ai-services-unified.js'), 'performAIOperation')
        .mockResolvedValue({ content: JSON.stringify({ summary: 'test synthesis' }) });

      const responseTimes = [];
      const requests = [];

      // Create moderate load (10 concurrent requests)
      for (let i = 0; i < 10; i++) {
        const startTime = Date.now();
        requests.push(
          performResearchOperation({
            type: 'market_research',
            query: `performance test ${i}`,
            providers: ['tavily'],
            maxResults: 1
          }).then(result => {
            const endTime = Date.now();
            responseTimes.push(endTime - startTime);
            return result;
          })
        );
      }

      const results = await Promise.all(requests);

      // Calculate performance metrics
      const avgResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);

      expect(results).toHaveLength(10);
      expect(avgResponseTime).toBeLessThan(3000); // Average under 3 seconds
      expect(maxResponseTime).toBeLessThan(5000); // Max under 5 seconds

      mockFetch.mockRestore();
      mockPerformAI.mockRestore();
    });

    it('should handle burst traffic gracefully', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ results: [] })
      });

      const provider = providers.tavily;
      const burstSize = 20;
      const requests = [];

      // Create burst of requests
      const startTime = Date.now();
      for (let i = 0; i < burstSize; i++) {
        requests.push(
          provider.search(`burst test ${i}`).catch(error => ({ error: error.message }))
        );
      }

      const results = await Promise.all(requests);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should handle burst without crashing
      expect(results).toHaveLength(burstSize);
      
      // Some requests should succeed within burst limit
      const successful = results.filter(r => !r.error);
      expect(successful.length).toBeGreaterThan(0);
      
      // Rate limited requests should fail gracefully
      const rateLimited = results.filter(r => r.error && r.error.includes('Rate limit'));
      expect(rateLimited.length).toBeGreaterThan(0);

      mockFetch.mockRestore();
    });
  });

  describe('Rate Limit Recovery', () => {
    it('should recover from rate limit errors', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ results: [] })
      });

      const provider = providers.tavily;

      // First, exhaust the rate limit
      const initialRequests = [];
      for (let i = 0; i < 15; i++) {
        initialRequests.push(
          provider.search(`exhaust ${i}`).catch(error => ({ error: error.message }))
        );
      }

      await Promise.all(initialRequests);

      // Wait for rate limit window to reset (simulate time passage)
      await new Promise(resolve => setTimeout(resolve, 1100)); // Wait 1.1 seconds

      // Try again - should succeed
      const recoveryResult = await provider.search('recovery test');
      expect(recoveryResult.query).toBe('recovery test');
      expect(recoveryResult.error).toBeUndefined();

      mockFetch.mockRestore();
    });

    it('should provide meaningful rate limit error messages', async () => {
      const provider = providers.tavily;
      
      // Mock rate limiter to always return false
      const mockCheckLimit = spyOn(provider.rateLimiter, 'checkLimit').mockResolvedValue(false);

      try {
        await provider.search('rate limit test');
        expect(true).toBe(false); // Should not reach here
      } catch (error) {
        expect(error.message).toContain('Rate limit exceeded');
        expect(error.message).toMatch(/try again|wait|limit/i);
      }

      mockCheckLimit.mockRestore();
    });
  });

  describe('Resource Management', () => {
    it('should clean up resources properly under load', async () => {
      const mockFetch = spyOn(global, 'fetch').mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ results: [] })
      });

      const provider = providers.firecrawl;
      const initialMemory = process.memoryUsage().heapUsed;

      // Perform many operations
      const requests = [];
      for (let i = 0; i < 50; i++) {
        requests.push(
          provider.scrapeUrl(`https://example.com/${i}`).catch(() => ({}))
        );
      }

      await Promise.all(requests);

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);

      mockFetch.mockRestore();
    });

    it('should handle connection pooling efficiently', async () => {
      const connectionCounts = { active: 0, max: 0 };

      const mockFetch = spyOn(global, 'fetch').mockImplementation(() => {
        connectionCounts.active++;
        connectionCounts.max = Math.max(connectionCounts.max, connectionCounts.active);
        
        return new Promise(resolve => {
          setTimeout(() => {
            connectionCounts.active--;
            resolve({
              ok: true,
              json: () => Promise.resolve({ results: [] })
            });
          }, 100);
        });
      });

      const provider = providers.tavily;
      const requests = [];

      // Create many concurrent requests
      for (let i = 0; i < 20; i++) {
        requests.push(provider.search(`connection test ${i}`));
      }

      await Promise.all(requests);

      // Should not create excessive concurrent connections
      expect(connectionCounts.max).toBeLessThan(15);

      mockFetch.mockRestore();
    });
  });

  describe('Error Handling Under Load', () => {
    it('should handle network errors gracefully under load', async () => {
      let callCount = 0;
      const mockFetch = spyOn(global, 'fetch').mockImplementation(() => {
        callCount++;
        if (callCount % 3 === 0) {
          return Promise.reject(new Error('Network error'));
        }
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({ results: [] })
        });
      });

      const provider = providers.tavily;
      const requests = [];

      for (let i = 0; i < 12; i++) {
        requests.push(
          provider.search(`error test ${i}`).catch(error => ({ error: error.message }))
        );
      }

      const results = await Promise.all(requests);

      // Should have mix of successes and failures
      const successful = results.filter(r => !r.error);
      const failed = results.filter(r => r.error);

      expect(successful.length).toBeGreaterThan(0);
      expect(failed.length).toBeGreaterThan(0);

      // Failed requests should have meaningful error messages
      failed.forEach(result => {
        expect(result.error).toMatch(/network|error|failed/i);
      });

      mockFetch.mockRestore();
    });
  });
});
