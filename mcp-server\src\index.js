import { FastMCP } from 'fastmcp';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import fs from 'fs';
import { initializeMCPArchitecture } from './orchestration/primitive-router.js';
// Migration removed - TASK-001 complete, no longer needed
import { createGuidantInfrastructure, validateGuidantInfrastructure } from './integration/guidant-infrastructure.js';

// Load environment variables
dotenv.config();

// Constants
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Guidant Evolution MCP Server
 * Provides AI coordination tools for systematic software development
 */
class GuidantEvolutionServer {
	constructor() {
		// Get version from package.json
		const packagePath = path.join(__dirname, '../../package.json');
		const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

		this.options = {
			name: 'Guidant Evolution',
			version: packageJson.version,
			description: 'AI Agent Workflow Framework for systematic software development'
		};

		this.server = new FastMCP(this.options);
		this.initialized = false;
		this.resourceRegistry = null;

		// Bind methods
		this.init = this.init.bind(this);
		this.start = this.start.bind(this);
		this.stop = this.stop.bind(this);
	}

	/**
	 * Create Guidant-specific infrastructure for MCP integration
	 */
	async createGuidantInfrastructure() {
		console.log('🔧 Creating Guidant infrastructure integration...');

		// Use the dedicated Guidant infrastructure module
		const infrastructure = await createGuidantInfrastructure();

		// Validate the infrastructure
		const validation = validateGuidantInfrastructure(infrastructure);

		if (!validation.valid) {
			console.warn('⚠️ Infrastructure validation issues:', validation.missing_components);
		}

		console.log(`✅ Guidant infrastructure ready (${validation.integration_level} integration)`);
		console.log(`📊 Components: ${validation.available_components.length}/${validation.available_components.length + validation.missing_components.length}`);

		return infrastructure;
	}

	/**
	 * Initialize the MCP server with AI coordination tools
	 */
	async init() {
		if (this.initialized) {
			console.log('🔄 MCP server already initialized');
			return;
		}

		console.log('🚀 Initializing Guidant Evolution MCP server...');

		// Create Guidant-specific infrastructure
		const guidantInfrastructure = await this.createGuidantInfrastructure();

		// Initialize consolidated MCP architecture (migration complete)
		console.log('🚀 Initializing consolidated MCP architecture...');

		// Initialize MCP architecture with all primitives
		this.mcpRouter = await initializeMCPArchitecture(this.server, guidantInfrastructure);

		console.log('✅ Consolidated MCP architecture initialized successfully');
		console.log('📊 Architecture: 5 consolidated tools, 48 operations, 90% reduction achieved');

		this.initialized = true;
		console.log('✅ MCP server initialization complete');

		// Log resource statistics if available
		if (this.resourceRegistry) {
			const stats = this.resourceRegistry.getStatistics();
			console.log(`📊 Resources: ${stats.templates} templates, ${stats.handlers} handlers`);
		} else if (this.mcpRouter) {
			const stats = this.mcpRouter.getRouterStatistics();
			console.log(`📊 MCP Router: ${stats.primitives.resources.available ? 'Resources enabled' : 'Resources disabled'}`);
		}

		return this;
	}

	/**
	 * Start the MCP server
	 */
	async start() {
		console.log('🌟 Starting Guidant Evolution MCP server...');

		if (!this.initialized) {
			await this.init();
		}

		console.log('🔌 Starting FastMCP server with stdio transport...');

		// Start the FastMCP server
		await this.server.start({
			transportType: 'stdio',
			timeout: 120000 // 2 minutes timeout
		});

		console.log('🎯 MCP server is running and ready for connections!');
		console.log('📡 Transport: stdio');
		console.log('⏱️  Timeout: 2 minutes');

		return this;
	}

	/**
	 * Stop the MCP server
	 */
	async stop() {
		if (this.server) {
			await this.server.stop();
		}
		return this;
	}
}

export default GuidantEvolutionServer;
