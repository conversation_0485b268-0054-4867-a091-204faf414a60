/**
 * MCP Tool Orchestrator
 * Enhanced tool execution orchestration with intelligent routing and coordination
 * Coordinates execution of consolidated MCP tools with workflow intelligence
 */

import { MCPMessageHandler } from '../utils/mcp-message-handler.js';

/**
 * Tool Orchestrator Class
 * Manages complex tool execution workflows and coordination
 */
export class ToolOrchestrator {
  constructor(options = {}) {
    this.messageHandler = new MCPMessageHandler();
    this.options = {
      enableParallelExecution: options.enableParallelExecution !== false,
      maxConcurrentTools: options.maxConcurrentTools || 3,
      executionTimeout: options.executionTimeout || 60000, // 60 seconds
      retryAttempts: options.retryAttempts || 2,
      enableCircuitBreaker: options.enableCircuitBreaker !== false,
      ...options
    };
    
    // Tool execution state
    this.activeExecutions = new Map();
    this.executionHistory = [];
    this.circuitBreakers = new Map();
    
    // Performance metrics
    this.metrics = {
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      averageExecutionTime: 0,
      lastExecutionTime: null
    };
  }

  /**
   * Orchestrate tool execution workflow
   * @param {object} workflow - Workflow definition
   * @param {object} context - Execution context
   * @returns {object} Orchestration result
   */
  async orchestrateWorkflow(workflow, context = {}) {
    const executionId = this.generateExecutionId();
    const startTime = Date.now();
    
    try {
      console.log(`🎯 Starting workflow orchestration: ${workflow.name || 'unnamed'} (${executionId})`);
      
      // Validate workflow definition
      this.validateWorkflow(workflow);
      
      // Initialize execution context
      const executionContext = {
        executionId,
        workflow,
        context,
        startTime,
        status: 'running',
        results: {},
        errors: []
      };
      
      this.activeExecutions.set(executionId, executionContext);
      
      // Execute workflow steps
      const result = await this.executeWorkflowSteps(workflow.steps, executionContext);
      
      // Update execution status
      executionContext.status = 'completed';
      executionContext.endTime = Date.now();
      executionContext.duration = executionContext.endTime - startTime;
      
      // Update metrics
      this.updateMetrics(executionContext);
      
      // Archive execution
      this.archiveExecution(executionContext);
      
      console.log(`✅ Workflow orchestration completed: ${executionId} (${executionContext.duration}ms)`);
      
      return this.messageHandler.createSuccessResponse(
        'Workflow orchestration completed successfully',
        {
          executionId,
          workflow: workflow.name,
          duration: executionContext.duration,
          results: result,
          metrics: this.getExecutionMetrics()
        }
      );
      
    } catch (error) {
      console.error(`❌ Workflow orchestration failed: ${executionId}`, error);
      
      // Update execution context with error
      const executionContext = this.activeExecutions.get(executionId);
      if (executionContext) {
        executionContext.status = 'failed';
        executionContext.error = error.message;
        executionContext.endTime = Date.now();
        executionContext.duration = executionContext.endTime - startTime;
        
        this.updateMetrics(executionContext);
        this.archiveExecution(executionContext);
      }
      
      return this.messageHandler.createErrorResponse(
        `Workflow orchestration failed: ${error.message}`,
        { executionId, workflow: workflow.name, error: error.message }
      );
    } finally {
      this.activeExecutions.delete(executionId);
    }
  }

  /**
   * Execute workflow steps
   * @param {Array} steps - Workflow steps
   * @param {object} executionContext - Execution context
   * @returns {object} Execution results
   */
  async executeWorkflowSteps(steps, executionContext) {
    const results = {};
    
    for (const step of steps) {
      try {
        console.log(`🔄 Executing step: ${step.name} (${step.tool})`);
        
        // Check circuit breaker
        if (this.isCircuitBreakerOpen(step.tool)) {
          throw new Error(`Circuit breaker open for tool: ${step.tool}`);
        }
        
        // Execute step
        const stepResult = await this.executeStep(step, executionContext, results);
        results[step.name] = stepResult;
        
        // Update circuit breaker on success
        this.recordSuccess(step.tool);
        
        console.log(`✅ Step completed: ${step.name}`);
        
      } catch (error) {
        console.error(`❌ Step failed: ${step.name}`, error);
        
        // Update circuit breaker on failure
        this.recordFailure(step.tool);
        
        // Handle step failure based on strategy
        if (step.onFailure === 'continue') {
          results[step.name] = { error: error.message, status: 'failed' };
          executionContext.errors.push({ step: step.name, error: error.message });
        } else if (step.onFailure === 'retry') {
          // Implement retry logic
          const retryResult = await this.retryStep(step, executionContext, results);
          results[step.name] = retryResult;
        } else {
          // Default: fail workflow
          throw error;
        }
      }
    }
    
    return results;
  }

  /**
   * Execute individual step
   * @param {object} step - Step definition
   * @param {object} executionContext - Execution context
   * @param {object} previousResults - Previous step results
   * @returns {object} Step result
   */
  async executeStep(step, executionContext, previousResults) {
    const stepStartTime = Date.now();
    
    // Prepare step parameters
    const parameters = this.prepareStepParameters(step, executionContext, previousResults);
    
    // Execute tool with timeout
    const toolResult = await this.executeWithTimeout(
      () => this.callTool(step.tool, parameters),
      this.options.executionTimeout
    );
    
    const stepDuration = Date.now() - stepStartTime;
    
    return {
      ...toolResult,
      stepMetadata: {
        stepName: step.name,
        tool: step.tool,
        duration: stepDuration,
        executedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Prepare step parameters
   * @param {object} step - Step definition
   * @param {object} executionContext - Execution context
   * @param {object} previousResults - Previous step results
   * @returns {object} Prepared parameters
   */
  prepareStepParameters(step, executionContext, previousResults) {
    let parameters = { ...step.parameters };
    
    // Replace parameter placeholders with values from previous steps
    if (step.parameterMapping) {
      for (const [paramName, mapping] of Object.entries(step.parameterMapping)) {
        if (mapping.source === 'previous_step' && mapping.step && mapping.field) {
          const sourceResult = previousResults[mapping.step];
          if (sourceResult && sourceResult.data) {
            parameters[paramName] = this.extractValue(sourceResult.data, mapping.field);
          }
        } else if (mapping.source === 'context' && mapping.field) {
          parameters[paramName] = this.extractValue(executionContext.context, mapping.field);
        }
      }
    }
    
    return parameters;
  }

  /**
   * Call tool with proper error handling
   * @param {string} toolName - Tool name
   * @param {object} parameters - Tool parameters
   * @returns {object} Tool result
   */
  async callTool(toolName, parameters) {
    // This would integrate with the actual MCP tool execution
    // For now, return a mock result
    return {
      success: true,
      data: { message: `Tool ${toolName} executed successfully`, parameters },
      metadata: { tool: toolName, executedAt: new Date().toISOString() }
    };
  }

  /**
   * Execute with timeout
   * @param {Function} fn - Function to execute
   * @param {number} timeout - Timeout in milliseconds
   * @returns {Promise} Execution result
   */
  async executeWithTimeout(fn, timeout) {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Execution timeout after ${timeout}ms`));
      }, timeout);
      
      fn()
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * Retry step execution
   * @param {object} step - Step definition
   * @param {object} executionContext - Execution context
   * @param {object} previousResults - Previous step results
   * @returns {object} Retry result
   */
  async retryStep(step, executionContext, previousResults) {
    const maxRetries = step.maxRetries || this.options.retryAttempts;
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 Retrying step: ${step.name} (attempt ${attempt}/${maxRetries})`);
        
        // Wait before retry (exponential backoff)
        if (attempt > 1) {
          const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s, etc.
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        return await this.executeStep(step, executionContext, previousResults);
        
      } catch (error) {
        lastError = error;
        console.warn(`⚠️ Retry attempt ${attempt} failed: ${error.message}`);
      }
    }
    
    throw lastError;
  }

  /**
   * Validate workflow definition
   * @param {object} workflow - Workflow to validate
   */
  validateWorkflow(workflow) {
    if (!workflow.steps || !Array.isArray(workflow.steps)) {
      throw new Error('Workflow must have steps array');
    }
    
    for (const step of workflow.steps) {
      if (!step.name || !step.tool) {
        throw new Error('Each step must have name and tool properties');
      }
    }
  }

  /**
   * Circuit breaker management
   */
  isCircuitBreakerOpen(toolName) {
    if (!this.options.enableCircuitBreaker) return false;
    
    const breaker = this.circuitBreakers.get(toolName);
    if (!breaker) return false;
    
    return breaker.state === 'open' && Date.now() < breaker.nextAttempt;
  }

  recordSuccess(toolName) {
    if (!this.options.enableCircuitBreaker) return;
    
    const breaker = this.circuitBreakers.get(toolName) || { failures: 0, state: 'closed' };
    breaker.failures = 0;
    breaker.state = 'closed';
    this.circuitBreakers.set(toolName, breaker);
  }

  recordFailure(toolName) {
    if (!this.options.enableCircuitBreaker) return;
    
    const breaker = this.circuitBreakers.get(toolName) || { failures: 0, state: 'closed' };
    breaker.failures++;
    
    if (breaker.failures >= 3) { // Open circuit after 3 failures
      breaker.state = 'open';
      breaker.nextAttempt = Date.now() + 30000; // 30 second timeout
    }
    
    this.circuitBreakers.set(toolName, breaker);
  }

  /**
   * Utility methods
   */
  generateExecutionId() {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  extractValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  updateMetrics(executionContext) {
    this.metrics.totalExecutions++;
    
    if (executionContext.status === 'completed') {
      this.metrics.successfulExecutions++;
    } else {
      this.metrics.failedExecutions++;
    }
    
    if (executionContext.duration) {
      const totalTime = this.metrics.averageExecutionTime * (this.metrics.totalExecutions - 1) + executionContext.duration;
      this.metrics.averageExecutionTime = totalTime / this.metrics.totalExecutions;
    }
    
    this.metrics.lastExecutionTime = new Date().toISOString();
  }

  archiveExecution(executionContext) {
    this.executionHistory.push({
      executionId: executionContext.executionId,
      workflow: executionContext.workflow.name,
      status: executionContext.status,
      duration: executionContext.duration,
      startTime: new Date(executionContext.startTime).toISOString(),
      endTime: executionContext.endTime ? new Date(executionContext.endTime).toISOString() : null,
      errorCount: executionContext.errors.length
    });
    
    // Keep only last 100 executions
    if (this.executionHistory.length > 100) {
      this.executionHistory = this.executionHistory.slice(-100);
    }
  }

  getExecutionMetrics() {
    return {
      ...this.metrics,
      activeExecutions: this.activeExecutions.size,
      circuitBreakers: Object.fromEntries(this.circuitBreakers),
      recentExecutions: this.executionHistory.slice(-10)
    };
  }
}

/**
 * Create and configure tool orchestrator
 * @param {object} options - Configuration options
 * @returns {ToolOrchestrator} Configured orchestrator
 */
export function createToolOrchestrator(options = {}) {
  return new ToolOrchestrator(options);
}

export default ToolOrchestrator;

// Re-export primitive router for backward compatibility
export { MCPPrimitiveRouter, initializeMCPArchitecture } from './primitive-router.js';
