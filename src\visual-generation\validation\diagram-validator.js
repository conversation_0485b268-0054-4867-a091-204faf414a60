/**
 * Diagram Validator
 * Comprehensive validation system for Mermaid diagrams including syntax validation,
 * layout optimization, readability analysis, and quality scoring
 */

/**
 * Diagram Validator
 */
export class DiagramValidator {
  constructor(config = {}) {
    this.config = {
      enableSyntaxValidation: config.enableSyntaxValidation !== false,
      enableLayoutOptimization: config.enableLayoutOptimization !== false,
      enableReadabilityAnalysis: config.enableReadabilityAnalysis !== false,
      enableQualityScoring: config.enableQualityScoring !== false,
      maxNodeCount: config.maxNodeCount || 50,
      maxConnectionCount: config.maxConnectionCount || 100,
      minReadabilityScore: config.minReadabilityScore || 0.7,
      ...config
    };

    this.validationRules = {
      syntax: new Map(),
      layout: new Map(),
      readability: new Map(),
      quality: new Map()
    };

    this.validationStats = {
      totalValidations: 0,
      syntaxErrors: 0,
      layoutIssues: 0,
      readabilityIssues: 0,
      qualityIssues: 0,
      optimizationsApplied: 0
    };

    this.initializeValidationRules();
  }

  /**
   * Comprehensive diagram validation
   */
  async validateDiagram(mermaidCode, diagramType = 'flowchart', context = {}) {
    this.validationStats.totalValidations++;

    const validation = {
      isValid: true,
      errors: [],
      warnings: [],
      suggestions: [],
      optimizations: [],
      qualityScore: 1.0,
      readabilityScore: 1.0,
      metadata: {
        diagramType,
        nodeCount: 0,
        connectionCount: 0,
        complexity: 'low',
        validatedAt: new Date().toISOString()
      }
    };

    try {
      // 1. Syntax Validation
      if (this.config.enableSyntaxValidation) {
        const syntaxResult = this.validateSyntax(mermaidCode, diagramType);
        validation.isValid = validation.isValid && syntaxResult.isValid;
        validation.errors.push(...syntaxResult.errors);
        validation.warnings.push(...syntaxResult.warnings);
        validation.metadata.nodeCount = syntaxResult.nodeCount;
        validation.metadata.connectionCount = syntaxResult.connectionCount;
      }

      // 2. Layout Optimization
      if (this.config.enableLayoutOptimization) {
        const layoutResult = this.analyzeLayout(mermaidCode, diagramType);
        validation.warnings.push(...layoutResult.warnings);
        validation.suggestions.push(...layoutResult.suggestions);
        validation.optimizations.push(...layoutResult.optimizations);
      }

      // 3. Readability Analysis
      if (this.config.enableReadabilityAnalysis) {
        const readabilityResult = this.analyzeReadability(mermaidCode, diagramType, context);
        validation.readabilityScore = readabilityResult.score;
        validation.warnings.push(...readabilityResult.warnings);
        validation.suggestions.push(...readabilityResult.suggestions);
        
        if (readabilityResult.score < this.config.minReadabilityScore) {
          validation.warnings.push(`Readability score ${readabilityResult.score.toFixed(2)} below minimum ${this.config.minReadabilityScore}`);
        }
      }

      // 4. Quality Scoring
      if (this.config.enableQualityScoring) {
        const qualityResult = this.calculateQualityScore(mermaidCode, diagramType, validation);
        validation.qualityScore = qualityResult.score;
        validation.suggestions.push(...qualityResult.suggestions);
        validation.metadata.complexity = qualityResult.complexity;
      }

      // Update statistics
      if (!validation.isValid) this.validationStats.syntaxErrors++;
      if (validation.warnings.length > 0) this.validationStats.layoutIssues++;
      if (validation.readabilityScore < this.config.minReadabilityScore) this.validationStats.readabilityIssues++;
      if (validation.qualityScore < 0.8) this.validationStats.qualityIssues++;
      if (validation.optimizations.length > 0) this.validationStats.optimizationsApplied++;

      return validation;

    } catch (error) {
      validation.isValid = false;
      validation.errors.push(`Validation error: ${error.message}`);
      return validation;
    }
  }

  /**
   * Validate Mermaid syntax
   */
  validateSyntax(mermaidCode, diagramType) {
    const result = {
      isValid: true,
      errors: [],
      warnings: [],
      nodeCount: 0,
      connectionCount: 0
    };

    const lines = mermaidCode.split('\n').map(line => line.trim()).filter(line => line.length > 0);

    // Check diagram type declaration
    const firstLine = lines[0];
    if (!this.isValidDiagramTypeDeclaration(firstLine, diagramType)) {
      result.errors.push(`Invalid diagram type declaration: ${firstLine}`);
      result.isValid = false;
    }

    // Validate nodes and connections
    const nodes = new Set();
    const connections = [];

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];
      
      // Skip comments and styling
      if (line.startsWith('%%') || line.startsWith('classDef') || line.startsWith('class ')) {
        continue;
      }

      // Validate node definitions and connections
      const nodeValidation = this.validateLine(line, diagramType);
      if (!nodeValidation.isValid) {
        result.errors.push(`Line ${i + 1}: ${nodeValidation.error}`);
        result.isValid = false;
      } else {
        nodes.add(...nodeValidation.nodes);
        connections.push(...nodeValidation.connections);
      }
    }

    result.nodeCount = nodes.size;
    result.connectionCount = connections.length;

    // Check for orphaned nodes
    const connectedNodes = new Set();
    connections.forEach(conn => {
      connectedNodes.add(conn.from);
      connectedNodes.add(conn.to);
    });

    const orphanedNodes = [...nodes].filter(node => !connectedNodes.has(node));
    if (orphanedNodes.length > 0) {
      result.warnings.push(`Orphaned nodes detected: ${orphanedNodes.join(', ')}`);
    }

    // Check node and connection limits
    if (result.nodeCount > this.config.maxNodeCount) {
      result.warnings.push(`Node count ${result.nodeCount} exceeds recommended maximum ${this.config.maxNodeCount}`);
    }

    if (result.connectionCount > this.config.maxConnectionCount) {
      result.warnings.push(`Connection count ${result.connectionCount} exceeds recommended maximum ${this.config.maxConnectionCount}`);
    }

    return result;
  }

  /**
   * Analyze diagram layout
   */
  analyzeLayout(mermaidCode, diagramType) {
    const result = {
      warnings: [],
      suggestions: [],
      optimizations: []
    };

    const lines = mermaidCode.split('\n');
    
    // Analyze node distribution
    const nodePositions = this.extractNodePositions(mermaidCode);
    if (nodePositions.length > 10) {
      result.suggestions.push('Consider breaking large diagrams into smaller, focused sub-diagrams');
    }

    // Check for long connection chains
    const connectionChains = this.findConnectionChains(mermaidCode);
    const longChains = connectionChains.filter(chain => chain.length > 5);
    if (longChains.length > 0) {
      result.suggestions.push('Long connection chains detected - consider adding intermediate grouping');
    }

    // Analyze branching complexity
    const branchingAnalysis = this.analyzeBranching(mermaidCode);
    if (branchingAnalysis.maxBranches > 4) {
      result.suggestions.push('High branching complexity detected - consider simplifying decision points');
    }

    // Check for layout optimization opportunities
    const layoutOptimizations = this.identifyLayoutOptimizations(mermaidCode, diagramType);
    result.optimizations.push(...layoutOptimizations);

    return result;
  }

  /**
   * Analyze diagram readability
   */
  analyzeReadability(mermaidCode, diagramType, context) {
    let score = 1.0;
    const warnings = [];
    const suggestions = [];

    // Node label analysis
    const nodeLabels = this.extractNodeLabels(mermaidCode);
    const longLabels = nodeLabels.filter(label => label.length > 30);
    if (longLabels.length > 0) {
      score -= 0.1;
      suggestions.push('Consider shortening long node labels for better readability');
    }

    // Connection density analysis
    const density = this.calculateConnectionDensity(mermaidCode);
    if (density > 0.7) {
      score -= 0.2;
      warnings.push('High connection density may impact readability');
    }

    // Diagram complexity analysis
    const complexity = this.calculateDiagramComplexity(mermaidCode);
    if (complexity > 0.8) {
      score -= 0.15;
      suggestions.push('Consider breaking complex diagram into multiple simpler diagrams');
    }

    // Color and styling analysis
    const stylingAnalysis = this.analyzeStyling(mermaidCode);
    if (!stylingAnalysis.hasColors && nodeLabels.length > 5) {
      score -= 0.05;
      suggestions.push('Consider adding colors to improve visual distinction');
    }

    // Context-specific analysis
    if (context.persona) {
      const personaReadability = this.analyzePersonaReadability(mermaidCode, context.persona);
      score *= personaReadability.multiplier;
      suggestions.push(...personaReadability.suggestions);
    }

    return {
      score: Math.max(0, score),
      warnings,
      suggestions
    };
  }

  /**
   * Calculate overall quality score
   */
  calculateQualityScore(mermaidCode, diagramType, validation) {
    let score = 1.0;
    const suggestions = [];

    // Syntax quality (40% weight)
    const syntaxQuality = validation.errors.length === 0 ? 1.0 : 0.5;
    score *= (0.4 * syntaxQuality + 0.6);

    // Readability quality (30% weight)
    score *= (0.3 * validation.readabilityScore + 0.7);

    // Structure quality (20% weight)
    const structureQuality = this.calculateStructureQuality(mermaidCode);
    score *= (0.2 * structureQuality + 0.8);

    // Best practices adherence (10% weight)
    const bestPracticesScore = this.calculateBestPracticesScore(mermaidCode, diagramType);
    score *= (0.1 * bestPracticesScore + 0.9);

    // Determine complexity
    const nodeCount = validation.metadata.nodeCount;
    let complexity = 'low';
    if (nodeCount > 20) complexity = 'high';
    else if (nodeCount > 10) complexity = 'medium';

    // Generate quality suggestions
    if (score < 0.9) {
      suggestions.push('Consider reviewing diagram structure and readability');
    }
    if (score < 0.7) {
      suggestions.push('Significant quality improvements needed');
    }
    if (score < 0.5) {
      suggestions.push('Diagram requires major restructuring');
    }

    return {
      score: Math.max(0, score),
      complexity,
      suggestions
    };
  }

  /**
   * Optimize diagram for better readability
   */
  optimizeDiagram(mermaidCode, diagramType, validationResult) {
    let optimizedCode = mermaidCode;
    const optimizations = [];

    // Apply layout optimizations
    if (validationResult.optimizations.length > 0) {
      for (const optimization of validationResult.optimizations) {
        const result = this.applyOptimization(optimizedCode, optimization);
        optimizedCode = result.code;
        if (result.applied) {
          optimizations.push(optimization.description);
        }
      }
    }

    // Apply readability improvements
    if (validationResult.readabilityScore < 0.8) {
      const readabilityResult = this.applyReadabilityImprovements(optimizedCode, diagramType);
      optimizedCode = readabilityResult.code;
      optimizations.push(...readabilityResult.improvements);
    }

    // Apply quality improvements
    if (validationResult.qualityScore < 0.8) {
      const qualityResult = this.applyQualityImprovements(optimizedCode, diagramType);
      optimizedCode = qualityResult.code;
      optimizations.push(...qualityResult.improvements);
    }

    return {
      optimizedCode,
      optimizations,
      improvementScore: this.calculateImprovementScore(mermaidCode, optimizedCode)
    };
  }

  /**
   * Get validation statistics
   */
  getValidationStats() {
    const total = this.validationStats.totalValidations;
    return {
      ...this.validationStats,
      syntaxErrorRate: total > 0 ? this.validationStats.syntaxErrors / total : 0,
      layoutIssueRate: total > 0 ? this.validationStats.layoutIssues / total : 0,
      readabilityIssueRate: total > 0 ? this.validationStats.readabilityIssues / total : 0,
      qualityIssueRate: total > 0 ? this.validationStats.qualityIssues / total : 0,
      optimizationRate: total > 0 ? this.validationStats.optimizationsApplied / total : 0
    };
  }

  // Helper methods
  initializeValidationRules() {
    // Initialize syntax rules
    this.validationRules.syntax.set('flowchart', {
      validDeclarations: ['flowchart TD', 'flowchart LR', 'flowchart TB', 'flowchart RL'],
      nodePatterns: [/^\s*\w+[\[\{(>].+[\]\})>]/, /^\s*\w+\s*-->/],
      connectionPatterns: [/-->/,  /-->|/, /-\.->/]
    });

    this.validationRules.syntax.set('sequenceDiagram', {
      validDeclarations: ['sequenceDiagram'],
      nodePatterns: [/participant\s+\w+/, /\w+\s*->>?\s*\w+/],
      connectionPatterns: [/->>/, /-->>/, /->>?/]
    });

    this.validationRules.syntax.set('stateDiagram', {
      validDeclarations: ['stateDiagram-v2', 'stateDiagram'],
      nodePatterns: [/^\s*\[\*\]/, /^\s*\w+\s*:/, /^\s*\w+\s*-->/],
      connectionPatterns: [/-->/, /:\s*.+/]
    });
  }

  isValidDiagramTypeDeclaration(line, expectedType) {
    const rules = this.validationRules.syntax.get(expectedType);
    if (!rules) return false;
    
    return rules.validDeclarations.some(decl => line.startsWith(decl));
  }

  validateLine(line, diagramType) {
    const result = {
      isValid: true,
      error: null,
      nodes: [],
      connections: []
    };

    // Basic line validation logic
    if (line.includes('-->')) {
      const parts = line.split('-->');
      if (parts.length === 2) {
        result.connections.push({
          from: parts[0].trim(),
          to: parts[1].trim()
        });
      }
    }

    // Extract node IDs
    const nodeMatches = line.match(/\b\w+\b/g);
    if (nodeMatches) {
      result.nodes = nodeMatches;
    }

    return result;
  }

  extractNodePositions(mermaidCode) {
    // Simplified implementation
    const matches = mermaidCode.match(/\w+[\[\{(]/g) || [];
    return matches.map((match, index) => ({ id: match, position: index }));
  }

  findConnectionChains(mermaidCode) {
    // Simplified implementation
    const connections = mermaidCode.match(/\w+\s*-->\s*\w+/g) || [];
    return [connections]; // Return as single chain for simplicity
  }

  analyzeBranching(mermaidCode) {
    const branches = mermaidCode.match(/\{.+?\}/g) || [];
    return {
      maxBranches: branches.length,
      totalBranches: branches.length
    };
  }

  identifyLayoutOptimizations(mermaidCode, diagramType) {
    const optimizations = [];
    
    if (mermaidCode.split('\n').length > 20) {
      optimizations.push({
        type: 'structure',
        description: 'Consider adding subgraph grouping for better organization'
      });
    }

    return optimizations;
  }

  extractNodeLabels(mermaidCode) {
    const matches = mermaidCode.match(/\[([^\]]+)\]/g) || [];
    return matches.map(match => match.slice(1, -1));
  }

  calculateConnectionDensity(mermaidCode) {
    const nodes = (mermaidCode.match(/\w+[\[\{(]/g) || []).length;
    const connections = (mermaidCode.match(/-->/g) || []).length;
    return nodes > 0 ? connections / nodes : 0;
  }

  calculateDiagramComplexity(mermaidCode) {
    const lines = mermaidCode.split('\n').length;
    const nodes = (mermaidCode.match(/\w+[\[\{(]/g) || []).length;
    const connections = (mermaidCode.match(/-->/g) || []).length;
    
    return Math.min(1.0, (lines + nodes + connections) / 100);
  }

  analyzeStyling(mermaidCode) {
    return {
      hasColors: mermaidCode.includes('fill:') || mermaidCode.includes('classDef'),
      hasClasses: mermaidCode.includes('class '),
      hasComments: mermaidCode.includes('%%')
    };
  }

  analyzePersonaReadability(mermaidCode, persona) {
    // Simplified persona-based analysis
    const multiplier = persona === 'Customer' ? 0.9 : 1.0; // Customers need simpler diagrams
    const suggestions = [];
    
    if (persona === 'Customer' && this.calculateDiagramComplexity(mermaidCode) > 0.5) {
      suggestions.push('Consider simplifying diagram for customer persona');
    }

    return { multiplier, suggestions };
  }

  calculateStructureQuality(mermaidCode) {
    // Simplified structure quality calculation
    const hasComments = mermaidCode.includes('%%');
    const hasGrouping = mermaidCode.includes('subgraph');
    const hasLabels = mermaidCode.includes('[') && mermaidCode.includes(']');
    
    let score = 0.5; // Base score
    if (hasComments) score += 0.2;
    if (hasGrouping) score += 0.2;
    if (hasLabels) score += 0.1;
    
    return Math.min(1.0, score);
  }

  calculateBestPracticesScore(mermaidCode, diagramType) {
    // Simplified best practices scoring
    let score = 0.7; // Base score
    
    if (mermaidCode.includes('classDef')) score += 0.1; // Has styling
    if (mermaidCode.includes('%%')) score += 0.1; // Has comments
    if (!mermaidCode.includes('node1')) score += 0.1; // Meaningful node names
    
    return Math.min(1.0, score);
  }

  applyOptimization(code, optimization) {
    // Simplified optimization application
    return {
      code,
      applied: false
    };
  }

  applyReadabilityImprovements(code, diagramType) {
    // Simplified readability improvements
    return {
      code,
      improvements: ['Applied readability improvements']
    };
  }

  applyQualityImprovements(code, diagramType) {
    // Simplified quality improvements
    return {
      code,
      improvements: ['Applied quality improvements']
    };
  }

  calculateImprovementScore(originalCode, optimizedCode) {
    // Simplified improvement score calculation
    return optimizedCode.length > originalCode.length ? 0.1 : 0.0;
  }
}
