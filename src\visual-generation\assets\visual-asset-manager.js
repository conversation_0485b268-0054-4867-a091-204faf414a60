/**
 * Visual Asset Manager
 * Manages storage, organization, and retrieval of visual assets
 */

import fs from 'fs/promises';
import path from 'path';
import { writeJSONFile, readJSONFile, updateJSONFile } from '../../file-management/reliable-file-manager.js';
import { WIREFRAMES_DIR } from '../../constants/paths.js';
import { VisualAsset, WireframeAsset, DiagramAsset, PrototypeAsset } from './visual-asset-models.js';

/**
 * Visual Asset File Structure Manager
 */
export class VisualAssetManager {
  constructor(projectRoot = process.cwd(), config = {}) {
    this.projectRoot = projectRoot;
    this.config = {
      baseDirectory: WIREFRAMES_DIR,
      subdirectories: {
        wireframes: 'ascii-wireframes',
        diagrams: 'mermaid-diagrams', 
        prototypes: 'html-prototypes',
        components: 'component-previews',
        metadata: '.metadata'
      },
      fileNaming: {
        includeTimestamp: true,
        includeVersion: true,
        format: '{type}_{title}_{version}_{timestamp}'
      },
      versioning: {
        enabled: true,
        maxVersions: 10,
        autoCleanup: true
      },
      ...config
    };
  }

  /**
   * Initialize visual asset directory structure
   */
  async initializeStructure() {
    const baseDir = path.join(this.projectRoot, this.config.baseDirectory);
    
    // Create base directory
    await fs.mkdir(baseDir, { recursive: true });
    
    // Create subdirectories
    for (const [key, subdir] of Object.entries(this.config.subdirectories)) {
      const subdirPath = path.join(baseDir, subdir);
      await fs.mkdir(subdirPath, { recursive: true });
      
      // Create index file for each subdirectory
      const indexPath = path.join(subdirPath, 'index.json');
      try {
        await fs.access(indexPath);
      } catch {
        await writeJSONFile(indexPath, {
          type: key,
          assets: [],
          createdAt: new Date().toISOString(),
          lastUpdated: new Date().toISOString()
        });
      }
    }
    
    // Create main asset registry
    const registryPath = path.join(baseDir, 'asset-registry.json');
    try {
      await fs.access(registryPath);
    } catch {
      await writeJSONFile(registryPath, {
        version: '1.0.0',
        totalAssets: 0,
        assetsByType: {
          wireframe: 0,
          diagram: 0,
          prototype: 0
        },
        lastUpdated: new Date().toISOString(),
        assets: []
      });
    }
    
    return {
      baseDirectory: baseDir,
      subdirectories: Object.values(this.config.subdirectories).map(subdir => 
        path.join(baseDir, subdir)
      ),
      initialized: true
    };
  }

  /**
   * Save visual asset to appropriate directory
   */
  async saveAsset(asset) {
    if (!(asset instanceof VisualAsset)) {
      throw new Error('Asset must be an instance of VisualAsset');
    }

    const validation = asset.validate();
    if (!validation.isValid) {
      throw new Error(`Asset validation failed: ${validation.errors.join(', ')}`);
    }

    // Determine subdirectory based on asset type
    const subdirectory = this.getSubdirectoryForType(asset.type);
    const assetDir = path.join(this.projectRoot, this.config.baseDirectory, subdirectory);
    
    // Generate filename
    const filename = this.generateFilename(asset);
    const assetPath = path.join(assetDir, filename);
    
    // Save asset content based on type
    await this.saveAssetContent(asset, assetPath);
    
    // Save metadata
    await this.saveAssetMetadata(asset, assetPath);
    
    // Update indices
    await this.updateAssetIndices(asset, assetPath);
    
    // Handle versioning
    if (this.config.versioning.enabled) {
      await this.handleVersioning(asset, assetDir);
    }
    
    return {
      assetId: asset.id,
      path: assetPath,
      type: asset.type,
      version: asset.version.toString(),
      savedAt: new Date().toISOString()
    };
  }

  /**
   * Load visual asset by ID
   */
  async loadAsset(assetId) {
    const registry = await this.getAssetRegistry();
    const assetInfo = registry.assets.find(a => a.id === assetId);
    
    if (!assetInfo) {
      throw new Error(`Asset with ID ${assetId} not found`);
    }
    
    const assetPath = path.join(this.projectRoot, assetInfo.path);
    const metadataPath = this.getMetadataPath(assetPath);
    
    // Load metadata
    const metadata = await readJSONFile(metadataPath);
    
    // Load content based on type
    const content = await this.loadAssetContent(assetInfo.type, assetPath);
    
    // Create appropriate asset instance
    return this.createAssetInstance(assetInfo.type, {
      ...metadata,
      content
    });
  }

  /**
   * List assets by type or criteria
   */
  async listAssets(criteria = {}) {
    const registry = await this.getAssetRegistry();
    let assets = registry.assets;
    
    // Filter by type
    if (criteria.type) {
      assets = assets.filter(asset => asset.type === criteria.type);
    }
    
    // Filter by date range
    if (criteria.dateFrom || criteria.dateTo) {
      assets = assets.filter(asset => {
        const assetDate = new Date(asset.createdAt);
        if (criteria.dateFrom && assetDate < new Date(criteria.dateFrom)) return false;
        if (criteria.dateTo && assetDate > new Date(criteria.dateTo)) return false;
        return true;
      });
    }
    
    // Filter by tags
    if (criteria.tags && criteria.tags.length > 0) {
      assets = assets.filter(asset => 
        criteria.tags.some(tag => asset.metadata.tags.includes(tag))
      );
    }
    
    // Sort by criteria
    if (criteria.sortBy) {
      assets.sort((a, b) => {
        const aValue = this.getNestedValue(a, criteria.sortBy);
        const bValue = this.getNestedValue(b, criteria.sortBy);
        
        if (criteria.sortOrder === 'desc') {
          return bValue > aValue ? 1 : -1;
        }
        return aValue > bValue ? 1 : -1;
      });
    }
    
    return assets;
  }

  /**
   * Delete visual asset
   */
  async deleteAsset(assetId) {
    const registry = await this.getAssetRegistry();
    const assetInfo = registry.assets.find(a => a.id === assetId);
    
    if (!assetInfo) {
      throw new Error(`Asset with ID ${assetId} not found`);
    }
    
    const assetPath = path.join(this.projectRoot, assetInfo.path);
    const metadataPath = this.getMetadataPath(assetPath);
    
    // Delete files
    try {
      await fs.unlink(assetPath);
      await fs.unlink(metadataPath);
    } catch (error) {
      console.warn(`Failed to delete asset files: ${error.message}`);
    }
    
    // Update registry
    const updatedRegistry = {
      ...registry,
      assets: registry.assets.filter(a => a.id !== assetId),
      totalAssets: registry.totalAssets - 1,
      lastUpdated: new Date().toISOString()
    };
    updatedRegistry.assetsByType[assetInfo.type]--;
    
    const registryPath = path.join(this.projectRoot, this.config.baseDirectory, 'asset-registry.json');
    await writeJSONFile(registryPath, updatedRegistry);
    
    return {
      deleted: true,
      assetId,
      deletedAt: new Date().toISOString()
    };
  }

  /**
   * Get asset statistics
   */
  async getAssetStatistics() {
    const registry = await this.getAssetRegistry();
    const baseDir = path.join(this.projectRoot, this.config.baseDirectory);
    
    // Calculate directory sizes
    const directorySizes = {};
    for (const [type, subdir] of Object.entries(this.config.subdirectories)) {
      const subdirPath = path.join(baseDir, subdir);
      directorySizes[type] = await this.getDirectorySize(subdirPath);
    }
    
    return {
      totalAssets: registry.totalAssets,
      assetsByType: registry.assetsByType,
      directorySizes,
      lastUpdated: registry.lastUpdated,
      storageHealth: await this.checkStorageHealth()
    };
  }

  // Helper methods
  getSubdirectoryForType(type) {
    const mapping = {
      wireframe: this.config.subdirectories.wireframes,
      diagram: this.config.subdirectories.diagrams,
      prototype: this.config.subdirectories.prototypes
    };
    return mapping[type] || this.config.subdirectories.wireframes;
  }

  generateFilename(asset) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const sanitizedTitle = asset.title.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
    const version = asset.version.toString().replace(/\./g, '-');
    
    if (this.config.fileNaming.includeTimestamp && this.config.fileNaming.includeVersion) {
      return `${asset.type}_${sanitizedTitle}_v${version}_${timestamp}.json`;
    } else if (this.config.fileNaming.includeVersion) {
      return `${asset.type}_${sanitizedTitle}_v${version}.json`;
    } else if (this.config.fileNaming.includeTimestamp) {
      return `${asset.type}_${sanitizedTitle}_${timestamp}.json`;
    }
    
    return `${asset.type}_${sanitizedTitle}.json`;
  }

  async saveAssetContent(asset, assetPath) {
    // Save the full asset as JSON
    await writeJSONFile(assetPath, asset.toJSON());
    
    // For specific types, save additional format files
    if (asset instanceof WireframeAsset && asset.asciiContent) {
      const asciiPath = assetPath.replace('.json', '.txt');
      await fs.writeFile(asciiPath, asset.asciiContent, 'utf8');
    }
    
    if (asset instanceof DiagramAsset && asset.mermaidSyntax) {
      const mermaidPath = assetPath.replace('.json', '.mmd');
      await fs.writeFile(mermaidPath, asset.mermaidSyntax, 'utf8');
    }
    
    if (asset instanceof PrototypeAsset && asset.htmlContent) {
      const htmlPath = assetPath.replace('.json', '.html');
      await fs.writeFile(htmlPath, asset.generateHTML(), 'utf8');
    }
  }

  async saveAssetMetadata(asset, assetPath) {
    const metadataPath = this.getMetadataPath(assetPath);
    const metadata = {
      id: asset.id,
      type: asset.type,
      title: asset.title,
      description: asset.description,
      metadata: asset.metadata.toJSON(),
      version: asset.version.toJSON(),
      businessIntelligence: asset.businessIntelligence.toJSON(),
      createdAt: asset.createdAt,
      updatedAt: asset.updatedAt,
      filePath: path.relative(this.projectRoot, assetPath)
    };
    
    await writeJSONFile(metadataPath, metadata);
  }

  getMetadataPath(assetPath) {
    const dir = path.dirname(assetPath);
    const filename = path.basename(assetPath, '.json');
    const metadataDir = path.join(dir, this.config.subdirectories.metadata);
    return path.join(metadataDir, `${filename}.meta.json`);
  }

  async updateAssetIndices(asset, assetPath) {
    // Update main registry
    const registryPath = path.join(this.projectRoot, this.config.baseDirectory, 'asset-registry.json');
    const registry = await readJSONFile(registryPath);
    
    // Check if asset already exists
    const existingIndex = registry.assets.findIndex(a => a.id === asset.id);
    const assetEntry = {
      id: asset.id,
      type: asset.type,
      title: asset.title,
      path: path.relative(this.projectRoot, assetPath),
      createdAt: asset.createdAt,
      updatedAt: asset.updatedAt,
      version: asset.version.toString(),
      metadata: asset.metadata.toJSON()
    };
    
    if (existingIndex >= 0) {
      registry.assets[existingIndex] = assetEntry;
    } else {
      registry.assets.push(assetEntry);
      registry.totalAssets++;
      registry.assetsByType[asset.type] = (registry.assetsByType[asset.type] || 0) + 1;
    }
    
    registry.lastUpdated = new Date().toISOString();
    await writeJSONFile(registryPath, registry);
    
    // Update type-specific index
    const subdirectory = this.getSubdirectoryForType(asset.type);
    const typeIndexPath = path.join(this.projectRoot, this.config.baseDirectory, subdirectory, 'index.json');
    const typeIndex = await readJSONFile(typeIndexPath);
    
    const typeExistingIndex = typeIndex.assets.findIndex(a => a.id === asset.id);
    if (typeExistingIndex >= 0) {
      typeIndex.assets[typeExistingIndex] = assetEntry;
    } else {
      typeIndex.assets.push(assetEntry);
    }
    
    typeIndex.lastUpdated = new Date().toISOString();
    await writeJSONFile(typeIndexPath, typeIndex);
  }

  async getAssetRegistry() {
    const registryPath = path.join(this.projectRoot, this.config.baseDirectory, 'asset-registry.json');
    try {
      return await readJSONFile(registryPath);
    } catch {
      // Initialize if doesn't exist
      await this.initializeStructure();
      return await readJSONFile(registryPath);
    }
  }

  async loadAssetContent(type, assetPath) {
    const assetData = await readJSONFile(assetPath);
    
    // Load additional format files if they exist
    if (type === 'wireframe') {
      const asciiPath = assetPath.replace('.json', '.txt');
      try {
        assetData.asciiContent = await fs.readFile(asciiPath, 'utf8');
      } catch {
        // ASCII file doesn't exist, use content from JSON
      }
    }
    
    if (type === 'diagram') {
      const mermaidPath = assetPath.replace('.json', '.mmd');
      try {
        assetData.mermaidSyntax = await fs.readFile(mermaidPath, 'utf8');
      } catch {
        // Mermaid file doesn't exist, use content from JSON
      }
    }
    
    return assetData;
  }

  createAssetInstance(type, data) {
    switch (type) {
      case 'wireframe':
        return new WireframeAsset(data);
      case 'diagram':
        return new DiagramAsset(data);
      case 'prototype':
        return new PrototypeAsset(data);
      default:
        return new VisualAsset(data);
    }
  }

  async handleVersioning(asset, assetDir) {
    if (!this.config.versioning.autoCleanup) return;
    
    // Find all versions of this asset
    const files = await fs.readdir(assetDir);
    const assetFiles = files.filter(file => 
      file.startsWith(`${asset.type}_${asset.title.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`)
    );
    
    if (assetFiles.length > this.config.versioning.maxVersions) {
      // Sort by creation time and remove oldest
      const fileStats = await Promise.all(
        assetFiles.map(async file => {
          const filePath = path.join(assetDir, file);
          const stats = await fs.stat(filePath);
          return { file, path: filePath, mtime: stats.mtime };
        })
      );
      
      fileStats.sort((a, b) => a.mtime - b.mtime);
      const filesToDelete = fileStats.slice(0, fileStats.length - this.config.versioning.maxVersions);
      
      for (const fileInfo of filesToDelete) {
        try {
          await fs.unlink(fileInfo.path);
          // Also delete metadata
          const metadataPath = this.getMetadataPath(fileInfo.path);
          await fs.unlink(metadataPath);
        } catch (error) {
          console.warn(`Failed to delete old version: ${error.message}`);
        }
      }
    }
  }

  async getDirectorySize(dirPath) {
    try {
      const files = await fs.readdir(dirPath, { recursive: true });
      let totalSize = 0;
      
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        const stats = await fs.stat(filePath);
        if (stats.isFile()) {
          totalSize += stats.size;
        }
      }
      
      return totalSize;
    } catch {
      return 0;
    }
  }

  async checkStorageHealth() {
    try {
      const baseDir = path.join(this.projectRoot, this.config.baseDirectory);
      await fs.access(baseDir);
      
      // Test write permissions
      const testFile = path.join(baseDir, '.health-check');
      await fs.writeFile(testFile, 'test');
      await fs.unlink(testFile);
      
      return { status: 'healthy', lastCheck: new Date().toISOString() };
    } catch (error) {
      return { 
        status: 'unhealthy', 
        error: error.message, 
        lastCheck: new Date().toISOString() 
      };
    }
  }

  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
}
