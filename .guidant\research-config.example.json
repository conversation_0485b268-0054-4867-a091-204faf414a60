{"_comment": "Example user configuration for research domains and libraries", "_instructions": "Copy this file to research-config.json and customize as needed", "domains": {"ui_practices": {"_comment": "Add your favorite UI/UX design resources", "add": ["ui.shadcn.com", "daisyui.com", "tailwindui.com", "your-company-design-system.com"], "remove": ["material.io"]}, "component_libraries": {"_comment": "Add component library documentation sites", "add": ["storybook.your-company.com", "bit.dev/your-org"]}, "custom_industry": {"_comment": "Add your own industry-specific domains", "add": ["industry-specific-site.com", "compliance-guidelines.org"]}}, "libraries": {"react": {"_comment": "Customize React component libraries", "add": ["shadcn/ui", "your-company-ui-kit", "radix-ui"], "remove": ["semantic-ui-react"]}, "vue": {"_comment": "Add Vue.js libraries you use", "add": ["headlessui-vue", "your-vue-components"]}, "internal": {"_comment": "Add your internal component libraries", "add": ["company-design-system", "internal-ui-kit"]}}, "research_priorities": {"_comment": "Adjust research priorities and weights", "accessibility": {"weight": 0.5, "max_results": 5, "search_depth": "advanced"}, "ui_practices": {"weight": 0.3, "max_results": 3, "search_depth": "basic"}, "performance": {"weight": 0.4, "max_results": 3, "search_depth": "basic"}}, "cache_settings": {"_comment": "Customize caching behavior", "ui_practices": {"duration": 7200000, "max_entries": 200}, "accessibility": {"duration": 14400000, "max_entries": 100}}, "industry_focus": {"_comment": "Configure industry-specific research", "primary_industry": "healthcare", "secondary_industries": ["finance", "government"], "compliance_requirements": ["HIPAA", "SOX", "GDPR"]}, "framework_preferences": {"_comment": "Set your preferred frameworks for research", "primary": "react", "secondary": ["vue", "svelte"], "css_framework": "tailwindcss", "component_library": "shadcn/ui"}, "research_filters": {"_comment": "Filter research results", "exclude_domains": ["outdated-framework.com", "deprecated-library.org"], "prefer_recent": true, "min_github_stars": 1000, "languages": ["en", "es", "fr"]}, "custom_queries": {"_comment": "Add custom research query templates", "accessibility": ["{component_type} WCAG 2.1 AA compliance best practices", "{component_type} screen reader accessibility patterns", "{component_type} keyboard navigation implementation"], "performance": ["{component_type} performance optimization techniques", "{component_type} lazy loading best practices", "{component_type} bundle size optimization"], "mobile": ["{component_type} mobile responsive design patterns", "{component_type} touch interaction guidelines", "{component_type} mobile accessibility"]}, "ai_enhancement": {"_comment": "Configure AI-powered research enhancement", "enable_ai_synthesis": true, "ai_confidence_threshold": 0.7, "max_ai_recommendations": 5, "prefer_code_examples": true}}