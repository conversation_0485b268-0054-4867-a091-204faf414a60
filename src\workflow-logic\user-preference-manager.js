/**
 * User Preference Manager for Guidant
 * Handles user preference tracking, learning, and application
 */

import path from 'path';
import {
  readJSONFile,
  updateJSONFile
} from '../file-management/reliable-file-manager.js';
import { USER_FEEDBACK, USER_PREFERENCES } from '../constants/paths.js';

/**
 * User Preference Manager
 * Provides functionality for tracking and applying user preferences
 */
export class UserPreferenceManager {
  constructor(projectRoot = process.cwd()) {
    this.projectRoot = projectRoot;
    this.userFeedbackPath = path.join(projectRoot, USER_FEEDBACK);
    this.userPreferencesPath = path.join(projectRoot, USER_PREFERENCES);
  }

  /**
   * Get user preferences
   * @returns {Promise<Object>} - User preferences
   */
  async getUserPreferences() {
    try {
      const result = await readJSONFile(this.userPreferencesPath);
      if (!result.success) {
        return this.getDefaultPreferences();
      }
      return result.data;
    } catch (error) {
      console.error('Failed to get user preferences:', error);
      return this.getDefaultPreferences();
    }
  }

  /**
   * Get default preferences
   * @returns {Object} - Default user preferences
   */
  getDefaultPreferences() {
    return {
      version: "1.0",
      lastUpdated: new Date().toISOString(),
      preferences: {
        technology: {
          frameworks: {},
          languages: {},
          databases: {},
          hosting: {}
        },
        design: {
          uiFrameworks: {},
          colorSchemes: {},
          layoutApproaches: {}
        },
        budget: {
          category: "medium", // e.g., "small", "medium", "large"
          value: 50000 // e.g., 10000, 50000, 100000
        },
        timeline: {
          category: "medium", // e.g., "short", "medium", "long"
          value: "3 months", // e.g., "1 month", "3 months", "6 months"
          approachPreference: "",
          milestonePriorities: {}
        },
        communication: {
          detailLevel: "standard",
          technicalTerms: false,
          visualPreference: true
        }
      },
      preferenceHistory: []
    };
  }

  /**
   * Update user preferences
   * @param {Function} updateFn - Function that receives preferences and returns updated preferences
   * @returns {Promise<Object|null>} - Updated preferences or null if failed
   */
  async updatePreferences(updateFn) {
    try {
      const result = await updateJSONFile(this.userPreferencesPath, async (preferences = this.getDefaultPreferences()) => {
        const updatedPreferences = updateFn(preferences);
        
        // Add to history if significant changes
        if (this.hasSignificantChanges(preferences, updatedPreferences)) {
          const historyEntry = {
            timestamp: new Date().toISOString(),
            changes: this.getChanges(preferences, updatedPreferences)
          };
          
          updatedPreferences.preferenceHistory = [
            ...(updatedPreferences.preferenceHistory || []),
            historyEntry
          ];
        }
        
        updatedPreferences.lastUpdated = new Date().toISOString();
        return updatedPreferences;
      });

      if (result.success) {
        return result.data;
      }
      return null;
    } catch (error) {
      console.error('Failed to update user preferences:', error);
      return null;
    }
  }

  /**
   * Set a specific preference
   * @param {string} category - Preference category (technology, design, timeline, communication)
   * @param {string} subcategory - Preference subcategory
   * @param {string} key - Preference key
   * @param {any} value - Preference value
   * @returns {Promise<boolean>} - True if successful
   */
  async setPreference(category, subcategory, key, value) {
    try {
      await this.updatePreferences(preferences => {
        const updatedPreferences = { ...preferences };
        
        if (!updatedPreferences.preferences[category]) {
          updatedPreferences.preferences[category] = {};
        }
        
        if (!updatedPreferences.preferences[category][subcategory]) {
          updatedPreferences.preferences[category][subcategory] = {};
        }
        
        if (typeof key === 'string') {
          updatedPreferences.preferences[category][subcategory][key] = value;
        } else {
          updatedPreferences.preferences[category][subcategory] = value;
        }
        
        return updatedPreferences;
      });
      
      return true;
    } catch (error) {
      console.error('Failed to set preference:', error);
      return false;
    }
  }

  /**
   * Get a specific preference
   * @param {string} category - Preference category
   * @param {string} subcategory - Preference subcategory
   * @param {string} key - Preference key
   * @param {any} defaultValue - Default value if preference not found
   * @returns {Promise<any>} - Preference value
   */
  async getPreference(category, subcategory, key = null, defaultValue = null) {
    try {
      const preferences = await this.getUserPreferences();
      
      if (!preferences.preferences[category]) {
        return defaultValue;
      }
      
      if (!preferences.preferences[category][subcategory]) {
        return defaultValue;
      }
      
      if (key === null) {
        return preferences.preferences[category][subcategory];
      }
      
      return preferences.preferences[category][subcategory][key] !== undefined
        ? preferences.preferences[category][subcategory][key]
        : defaultValue;
    } catch (error) {
      console.error('Failed to get preference:', error);
      return defaultValue;
    }
  }

  /**
   * Record user feedback
   * @param {Object} feedbackEntry - Feedback entry
   * @returns {Promise<boolean>} - True if successful
   */
  async recordFeedback(feedbackEntry) {
    try {
      // Ensure required fields
      if (!feedbackEntry.feedbackId || !feedbackEntry.timestamp || !feedbackEntry.feedback) {
        console.error('Invalid feedback entry: Missing required fields');
        return false;
      }
      
      // Update feedback file
      const result = await updateJSONFile(this.userFeedbackPath, async (feedback = []) => {
        // Remove any existing feedback with the same ID
        const filteredFeedback = feedback.filter(f => f.feedbackId !== feedbackEntry.feedbackId);
        
        // Add new feedback
        return [...filteredFeedback, feedbackEntry];
      });
      
      return result.success;
    } catch (error) {
      console.error('Failed to record feedback:', error);
      return false;
    }
  }

  /**
   * Learn preferences from user feedback
   * @returns {Promise<boolean>} - True if successful
   */
  async learnPreferencesFromFeedback() {
    try {
      const feedbackResult = await readJSONFile(this.userFeedbackPath);
      if (!feedbackResult.success) {
        return false;
      }
      
      const feedback = feedbackResult.data || [];
      if (feedback.length === 0) {
        return false;
      }
      
      // Process feedback to extract preference indicators
      const preferences = await this.getUserPreferences();
      let updatedPreferences = { ...preferences };
      let hasChanges = false;
      
      // Extract preferences from different categories
      const technologyPreferences = this.extractTechnologyPreferences(feedback);
      const designPreferences = this.extractDesignPreferences(feedback);
      const timelinePreferences = this.extractTimelinePreferences(feedback);
      const communicationPreferences = this.extractCommunicationPreferences(feedback);
      
      // Update preferences if there are changes
      if (Object.keys(technologyPreferences).length > 0) {
        updatedPreferences = this.updateTechnologyPreferences(updatedPreferences, technologyPreferences);
        hasChanges = true;
      }
      
      if (Object.keys(designPreferences).length > 0) {
        updatedPreferences = this.updateDesignPreferences(updatedPreferences, designPreferences);
        hasChanges = true;
      }
      
      if (Object.keys(timelinePreferences).length > 0) {
        updatedPreferences = this.updateTimelinePreferences(updatedPreferences, timelinePreferences);
        hasChanges = true;
      }
      
      if (Object.keys(communicationPreferences).length > 0) {
        updatedPreferences = this.updateCommunicationPreferences(updatedPreferences, communicationPreferences);
        hasChanges = true;
      }
      
      // Save updated preferences if there are changes
      if (hasChanges) {
        await this.updatePreferences(() => updatedPreferences);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Failed to learn preferences from feedback:', error);
      return false;
    }
  }

  /**
   * Extract technology preferences from feedback
   * @param {Array} feedback - User feedback data
   * @returns {Object} - Extracted technology preferences
   */
  extractTechnologyPreferences(feedback) {
    const technologyPreferences = {
      frameworks: {},
      languages: {},
      databases: {},
      hosting: {}
    };
    
    // Process each feedback entry
    feedback.forEach(entry => {
      // Skip entries without preference indicators
      if (!entry.preferenceIndicators || !entry.preferenceIndicators.technology) {
        return;
      }
      
      const techIndicators = entry.preferenceIndicators.technology;
      
      // Process technology indicators
      techIndicators.forEach(indicator => {
        const { type, name, preference } = indicator;
        
        if (type && name && preference) {
          // Map type to subcategory
          let subcategory = '';
          if (type === 'framework') subcategory = 'frameworks';
          else if (type === 'language') subcategory = 'languages';
          else if (type === 'database') subcategory = 'databases';
          else if (type === 'hosting') subcategory = 'hosting';
          
          if (subcategory && technologyPreferences[subcategory]) {
            // Increment or initialize preference count
            technologyPreferences[subcategory][name] = (technologyPreferences[subcategory][name] || 0) + preference;
          }
        }
      });
    });
    
    return technologyPreferences;
  }

  /**
   * Update technology preferences
   * @param {Object} preferences - Current preferences
   * @param {Object} technologyPreferences - New technology preferences
   * @returns {Object} - Updated preferences
   */
  updateTechnologyPreferences(preferences, technologyPreferences) {
    const updatedPreferences = { ...preferences };
    
    // Update each subcategory
    Object.keys(technologyPreferences).forEach(subcategory => {
      const subcategoryPrefs = technologyPreferences[subcategory];
      
      // Skip empty subcategories
      if (!subcategoryPrefs || Object.keys(subcategoryPrefs).length === 0) {
        return;
      }
      
      // Initialize subcategory if needed
      if (!updatedPreferences.preferences.technology[subcategory]) {
        updatedPreferences.preferences.technology[subcategory] = {};
      }
      
      // Update each preference in the subcategory
      Object.keys(subcategoryPrefs).forEach(name => {
        const value = subcategoryPrefs[name];
        
        // Only update if the preference is strong enough
        if (Math.abs(value) >= 2) {
          updatedPreferences.preferences.technology[subcategory][name] = value > 0;
        }
      });
    });
    
    return updatedPreferences;
  }

  /**
   * Extract design preferences from feedback
   * @param {Array} feedback - User feedback data
   * @returns {Object} - Extracted design preferences
   */
  extractDesignPreferences(feedback) {
    const designPreferences = {
      uiFrameworks: {},
      colorSchemes: {},
      layoutApproaches: {}
    };
    
    // Process each feedback entry
    feedback.forEach(entry => {
      // Skip entries without preference indicators
      if (!entry.preferenceIndicators || !entry.preferenceIndicators.design) {
        return;
      }
      
      const designIndicators = entry.preferenceIndicators.design;
      
      // Process design indicators
      designIndicators.forEach(indicator => {
        const { type, name, preference } = indicator;
        
        if (type && name && preference) {
          // Map type to subcategory
          let subcategory = '';
          if (type === 'uiFramework') subcategory = 'uiFrameworks';
          else if (type === 'colorScheme') subcategory = 'colorSchemes';
          else if (type === 'layout') subcategory = 'layoutApproaches';
          
          if (subcategory && designPreferences[subcategory]) {
            // Increment or initialize preference count
            designPreferences[subcategory][name] = (designPreferences[subcategory][name] || 0) + preference;
          }
        }
      });
    });
    
    return designPreferences;
  }

  /**
   * Update design preferences
   * @param {Object} preferences - Current preferences
   * @param {Object} designPreferences - New design preferences
   * @returns {Object} - Updated preferences
   */
  updateDesignPreferences(preferences, designPreferences) {
    const updatedPreferences = { ...preferences };
    
    // Update each subcategory
    Object.keys(designPreferences).forEach(subcategory => {
      const subcategoryPrefs = designPreferences[subcategory];
      
      // Skip empty subcategories
      if (!subcategoryPrefs || Object.keys(subcategoryPrefs).length === 0) {
        return;
      }
      
      // Initialize subcategory if needed
      if (!updatedPreferences.preferences.design[subcategory]) {
        updatedPreferences.preferences.design[subcategory] = {};
      }
      
      // Update each preference in the subcategory
      Object.keys(subcategoryPrefs).forEach(name => {
        const value = subcategoryPrefs[name];
        
        // Only update if the preference is strong enough
        if (Math.abs(value) >= 2) {
          updatedPreferences.preferences.design[subcategory][name] = value > 0;
        }
      });
    });
    
    return updatedPreferences;
  }

  /**
   * Extract timeline preferences from feedback
   * @param {Array} feedback - User feedback data
   * @returns {Object} - Extracted timeline preferences
   */
  extractTimelinePreferences(feedback) {
    const timelinePreferences = {
      approachPreference: '',
      milestonePriorities: {}
    };
    
    let approachCounts = {
      agile: 0,
      waterfall: 0,
      iterative: 0
    };
    
    // Process each feedback entry
    feedback.forEach(entry => {
      // Skip entries without preference indicators
      if (!entry.preferenceIndicators || !entry.preferenceIndicators.timeline) {
        return;
      }
      
      const timelineIndicators = entry.preferenceIndicators.timeline;
      
      // Process timeline indicators
      timelineIndicators.forEach(indicator => {
        const { type, name, preference } = indicator;
        
        if (type === 'approach' && name && preference) {
          // Count approach preferences
          if (name === 'agile' || name === 'waterfall' || name === 'iterative') {
            approachCounts[name] += preference;
          }
        } else if (type === 'milestone' && name && preference) {
          // Track milestone priorities
          timelinePreferences.milestonePriorities[name] = 
            (timelinePreferences.milestonePriorities[name] || 0) + preference;
        }
      });
    });
    
    // Determine preferred approach
    let maxCount = 0;
    Object.keys(approachCounts).forEach(approach => {
      if (approachCounts[approach] > maxCount) {
        maxCount = approachCounts[approach];
        timelinePreferences.approachPreference = approach;
      }
    });
    
    return timelinePreferences;
  }

  /**
   * Update timeline preferences
   * @param {Object} preferences - Current preferences
   * @param {Object} timelinePreferences - New timeline preferences
   * @returns {Object} - Updated preferences
   */
  updateTimelinePreferences(preferences, timelinePreferences) {
    const updatedPreferences = { ...preferences };
    
    // Update approach preference if significant
    if (timelinePreferences.approachPreference) {
      updatedPreferences.preferences.timeline.approachPreference = 
        timelinePreferences.approachPreference;
    }
    
    // Update milestone priorities
    const milestonePriorities = timelinePreferences.milestonePriorities;
    if (milestonePriorities && Object.keys(milestonePriorities).length > 0) {
      // Initialize if needed
      if (!updatedPreferences.preferences.timeline.milestonePriorities) {
        updatedPreferences.preferences.timeline.milestonePriorities = {};
      }
      
      // Update each milestone priority
      Object.keys(milestonePriorities).forEach(milestone => {
        const value = milestonePriorities[milestone];
        
        // Only update if the preference is strong enough
        if (Math.abs(value) >= 2) {
          updatedPreferences.preferences.timeline.milestonePriorities[milestone] = value;
        }
      });
    }
    
    return updatedPreferences;
  }

  /**
   * Extract communication preferences from feedback
   * @param {Array} feedback - User feedback data
   * @returns {Object} - Extracted communication preferences
   */
  extractCommunicationPreferences(feedback) {
    const communicationPreferences = {
      detailLevel: null,
      technicalTerms: null,
      visualPreference: null
    };
    
    let detailLevelCounts = {
      minimal: 0,
      standard: 0,
      detailed: 0
    };
    
    let technicalTermsCount = 0;
    let visualPreferenceCount = 0;
    let totalEntries = 0;
    
    // Process each feedback entry
    feedback.forEach(entry => {
      // Skip entries without preference indicators
      if (!entry.preferenceIndicators || !entry.preferenceIndicators.communication) {
        return;
      }
      
      const commIndicators = entry.preferenceIndicators.communication;
      totalEntries++;
      
      // Process communication indicators
      commIndicators.forEach(indicator => {
        const { type, preference } = indicator;
        
        if (type === 'detailLevel' && indicator.level) {
          // Count detail level preferences
          if (detailLevelCounts[indicator.level] !== undefined) {
            detailLevelCounts[indicator.level] += preference;
          }
        } else if (type === 'technicalTerms' && preference !== undefined) {
          // Track technical terms preference
          technicalTermsCount += preference;
        } else if (type === 'visualPreference' && preference !== undefined) {
          // Track visual preference
          visualPreferenceCount += preference;
        }
      });
    });
    
    // Determine preferred detail level
    if (totalEntries > 0) {
      let maxCount = 0;
      Object.keys(detailLevelCounts).forEach(level => {
        if (detailLevelCounts[level] > maxCount) {
          maxCount = detailLevelCounts[level];
          communicationPreferences.detailLevel = level;
        }
      });
      
      // Determine technical terms preference
      if (Math.abs(technicalTermsCount) >= 2) {
        communicationPreferences.technicalTerms = technicalTermsCount > 0;
      }
      
      // Determine visual preference
      if (Math.abs(visualPreferenceCount) >= 2) {
        communicationPreferences.visualPreference = visualPreferenceCount > 0;
      }
    }
    
    return communicationPreferences;
  }

  /**
   * Update communication preferences
   * @param {Object} preferences - Current preferences
   * @param {Object} communicationPreferences - New communication preferences
   * @returns {Object} - Updated preferences
   */
  updateCommunicationPreferences(preferences, communicationPreferences) {
    const updatedPreferences = { ...preferences };
    
    // Update detail level preference
    if (communicationPreferences.detailLevel) {
      updatedPreferences.preferences.communication.detailLevel = 
        communicationPreferences.detailLevel;
    }
    
    // Update technical terms preference
    if (communicationPreferences.technicalTerms !== null) {
      updatedPreferences.preferences.communication.technicalTerms = 
        communicationPreferences.technicalTerms;
    }
    
    // Update visual preference
    if (communicationPreferences.visualPreference !== null) {
      updatedPreferences.preferences.communication.visualPreference = 
        communicationPreferences.visualPreference;
    }
    
    return updatedPreferences;
  }

  /**
   * Check if there are significant changes between preferences
   * @param {Object} oldPreferences - Old preferences
   * @param {Object} newPreferences - New preferences
   * @returns {boolean} - True if there are significant changes
   */
  hasSignificantChanges(oldPreferences, newPreferences) {
    // Compare preferences at a high level
    return JSON.stringify(oldPreferences.preferences) !== JSON.stringify(newPreferences.preferences);
  }

  /**
   * Get changes between preferences
   * @param {Object} oldPreferences - Old preferences
   * @param {Object} newPreferences - New preferences
   * @returns {Object} - Changes object
   */
  getChanges(oldPreferences, newPreferences) {
    const changes = {};
    
    // Check each category
    Object.keys(newPreferences.preferences).forEach(category => {
      const oldCategory = oldPreferences.preferences[category] || {};
      const newCategory = newPreferences.preferences[category] || {};
      
      const categoryChanges = this.getChangesInCategory(oldCategory, newCategory);
      
      if (Object.keys(categoryChanges).length > 0) {
        changes[category] = categoryChanges;
      }
    });
    
    return changes;
  }

  /**
   * Get changes within a category
   * @param {Object} oldCategory - Old category preferences
   * @param {Object} newCategory - New category preferences
   * @returns {Object} - Changes object
   */
  getChangesInCategory(oldCategory, newCategory) {
    const changes = {};
    
    // Check each subcategory or direct property
    Object.keys(newCategory).forEach(key => {
      const oldValue = oldCategory[key];
      const newValue = newCategory[key];
      
      // If both are objects, recurse
      if (typeof oldValue === 'object' && oldValue !== null && 
          typeof newValue === 'object' && newValue !== null) {
        const subChanges = this.getChangesInCategory(oldValue, newValue);
        
        if (Object.keys(subChanges).length > 0) {
          changes[key] = subChanges;
        }
      }
      // Otherwise check for value changes
      else if (oldValue !== newValue) {
        changes[key] = {
          old: oldValue,
          new: newValue
        };
      }
    });
    
    return changes;
  }
}

/**
 * Create a singleton instance
 */
let userPreferenceManagerInstance = null;

/**
 * Get the user preference manager instance
 * @param {string} projectRoot - Project root directory
 * @returns {UserPreferenceManager} - User preference manager instance
 */
export function getUserPreferenceManager(projectRoot = process.cwd()) {
  if (!userPreferenceManagerInstance) {
    userPreferenceManagerInstance = new UserPreferenceManager(projectRoot);
  }
  return userPreferenceManagerInstance;
} 