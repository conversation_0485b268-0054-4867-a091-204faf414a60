/**
 * @file test-custom-tech-stack.js
 * @description Test script for custom tech stack handling in the Business Decision Translation System
 */

import { DecisionTranslator } from '../../src/business-decisions/decision-translator.js';
import { OptionPresenter } from '../../src/business-decisions/option-presenter.js';
import path from 'path';
import fs from 'fs/promises';

// Set project root to the current directory
const projectRoot = '.';

/**
 * Test custom tech stack handling
 */
async function testCustomTechStack() {
  console.log('🧪 Testing Custom Tech Stack Handling...');
  
  try {
    // Create translator and presenter instances
    const translator = new DecisionTranslator(projectRoot);
    const presenter = new OptionPresenter(projectRoot);
    console.log('✅ Translator and presenter instances created successfully');
    
    // Test adding a custom term mapping
    console.log('\n📝 Testing custom term mapping...');
    const customMapping = translator.addCustomTermMapping('Svelte', 'Lightweight user interface system');
    console.log('✅ Added custom term mapping:', customMapping['Svelte']);
    
    // Test creating a custom decision template
    console.log('\n📝 Testing custom decision template creation...');
    const customTemplate = translator.createCustomDecisionTemplate(
      'custom_state_management',
      'Choose Your State Management Solution',
      'Select how your application will manage and update state. This affects data flow, component communication, and application complexity.',
      [
        {
          title: 'Redux',
          description: 'A predictable state container for JavaScript apps',
          businessImpact: {
            level: 'high',
            description: 'Excellent for complex applications with many state changes'
          },
          timeImplication: {
            level: 'high',
            description: 'Requires more setup time and boilerplate code'
          },
          costImplication: {
            level: 'low',
            description: 'Free and open-source'
          },
          riskAssessment: {
            level: 'low',
            description: 'Well-established with large community support'
          },
          technicalDetails: 'Redux is a predictable state container for JavaScript apps, often used with React. It helps you write applications that behave consistently across different environments.',
          isRecommended: true
        },
        {
          title: 'MobX',
          description: 'Simple, scalable state management',
          businessImpact: {
            level: 'medium',
            description: 'Good for applications with reactive state requirements'
          },
          timeImplication: {
            level: 'medium',
            description: 'Less boilerplate than Redux but still requires learning'
          },
          costImplication: {
            level: 'low',
            description: 'Free and open-source'
          },
          riskAssessment: {
            level: 'medium',
            description: 'Smaller community than Redux but still well-supported'
          },
          technicalDetails: 'MobX is a battle-tested library that makes state management simple and scalable by transparently applying functional reactive programming.'
        },
        {
          title: 'Zustand',
          description: 'A small, fast state-management solution',
          businessImpact: {
            level: 'medium',
            description: 'Good for small to medium applications'
          },
          timeImplication: {
            level: 'low',
            description: 'Minimal setup and boilerplate'
          },
          costImplication: {
            level: 'low',
            description: 'Free and open-source'
          },
          riskAssessment: {
            level: 'medium',
            description: 'Newer library with growing community'
          },
          technicalDetails: 'Zustand is a small, fast and scalable bearbones state-management solution using simplified flux principles.'
        }
      ]
    );
    
    console.log('✅ Custom decision template created:');
    console.log(`  - ID: ${customTemplate.id}`);
    console.log(`  - Title: ${customTemplate.title}`);
    console.log(`  - Options: ${customTemplate.options.length}`);
    console.log(`  - isCustom: ${customTemplate.isCustom}`);
    
    // We need to use the same translator instance for the presenter
    // Let's create a new presenter with our existing translator
    const customPresenter = new OptionPresenter(projectRoot);
    customPresenter.translator = translator;
    
    // Test presenting the custom decision
    console.log('\n📝 Testing custom decision presentation...');
    const presentation = await customPresenter.presentDecision('custom_state_management');
    console.log('✅ Custom decision presentation created:');
    console.log(`  - Title: ${presentation.title}`);
    console.log(`  - Options: ${presentation.options.length}`);
    console.log(`  - Recommendation: ${presentation.recommendation ? presentation.recommendation.message : 'None'}`);
    console.log(`  - isCustomTemplate: ${presentation.isCustomTemplate}`);
    
    // Test recording a decision with custom details
    console.log('\n📝 Testing decision recording with custom details...');
    const customDetails = {
      version: '4.2.0',
      additionalLibraries: ['redux-toolkit', 'redux-persist'],
      integrationNotes: 'Will need to configure store with middleware'
    };
    
    const recordedDecision = await customPresenter.recordDecision(
      customTemplate.id, // Use the actual template ID
      'option_0', // Redux (first option)
      'We chose Redux because our application has complex state requirements and the team has experience with it.',
      customDetails
    );
    
    console.log('✅ Decision recorded with custom details:');
    console.log(`  - ID: ${recordedDecision.id}`);
    console.log(`  - Choice: ${recordedDecision.choiceTitle}`);
    console.log(`  - Custom Details: ${JSON.stringify(recordedDecision.customDetails)}`);
    
    // Read the decisions.json file to verify the decision was recorded
    const decisionsPath = path.join(projectRoot, '.guidant', 'context', 'decisions.json');
    const decisions = JSON.parse(await fs.readFile(decisionsPath, 'utf8'));
    const lastDecision = decisions[decisions.length - 1];
    
    console.log('\n📝 Verifying decision in decisions.json:');
    console.log(`  - ID: ${lastDecision.id}`);
    console.log(`  - Decision Title: ${lastDecision.decisionTitle}`);
    console.log(`  - Choice: ${lastDecision.choiceTitle}`);
    console.log(`  - Custom Details: ${JSON.stringify(lastDecision.customDetails || {})}`);
    
    console.log('\n✅ All custom tech stack tests passed!');
  } catch (error) {
    console.error('❌ Error testing custom tech stack:', error);
    console.error(error.stack);
  }
}

// Run the test
testCustomTechStack(); 