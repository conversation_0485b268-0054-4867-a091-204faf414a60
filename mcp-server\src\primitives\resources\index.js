/**
 * MCP Resources Registry
 * Central registry for all MCP Resource handlers and capabilities
 */

import { ProjectResourceHandler } from './project-resources.js';
import { DeliverableResourceHandler } from './deliverable-resources.js';
import { AnalyticsResourceHandler } from './analytics-resources.js';
import { WorkflowResourceHandler } from './workflow-resources.js';
import { RESOURCE_TEMPLATES } from '../../schemas/resource-schemas.js';
import { MCPResourceURI } from '../../utils/uri-template-parser.js';
import { MCPMessageHandler } from '../../utils/mcp-message-handler.js';

/**
 * MCP Resource Registry
 * Manages all resource handlers and provides unified resource access
 */
export class MCPResourceRegistry {
  constructor() {
    this.handlers = new Map();
    this.messageHandler = new MCPMessageHandler();
    this.initialized = false;
  }

  /**
   * Initialize the resource registry with existing tool infrastructure
   * @param {object} existingInfrastructure - Existing tool managers and handlers
   */
  async initialize(existingInfrastructure = {}) {
    if (this.initialized) return;

    // Initialize resource handlers with existing infrastructure or mocks
    const projectHandler = new ProjectResourceHandler(
      existingInfrastructure.projectManager || createMockProjectManager()
    );
    const deliverableHandler = new DeliverableResourceHandler(
      existingInfrastructure.deliverableManager || createMockDeliverableManager()
    );
    const analyticsHandler = new AnalyticsResourceHandler(
      existingInfrastructure.analyticsManager || createMockAnalyticsManager()
    );
    const workflowHandler = new WorkflowResourceHandler(
      existingInfrastructure.workflowManager || createMockWorkflowManager()
    );

    // Register handlers by resource type
    this.handlers.set('project', projectHandler);
    this.handlers.set('deliverable', deliverableHandler);
    this.handlers.set('analytics', analyticsHandler);
    this.handlers.set('workflow', workflowHandler);

    this.initialized = true;
    console.log(`📚 MCP Resource Registry initialized with ${this.handlers.size} handlers`);
  }

  /**
   * Register MCP resource capabilities with the server
   * @param {object} server - FastMCP server instance
   */
  registerCapabilities(server) {
    // FastMCP handles capabilities automatically when resources are added
    // Register each resource template with the server using FastMCP API

    try {
      const templates = this.getAllResourceTemplates();

      // Register each resource template with the server
      for (const template of templates) {
        if (server.addResource) {
          server.addResource(template);
        } else if (server.addResourceTemplate) {
          server.addResourceTemplate(template);
        }
      }

      console.log(`📡 MCP Resource capabilities registered (${templates.length} resources)`);
    } catch (error) {
      console.warn('⚠️ Resource registration skipped - FastMCP API not available:', error.message);
      console.log('📡 MCP Resource registry initialized (capabilities will be handled by router)');
    }
  }

  /**
   * Get all available resource templates
   * @returns {Array} Array of all resource templates
   */
  getAllResourceTemplates() {
    const templates = [];
    
    for (const handler of this.handlers.values()) {
      if (handler.getResourceTemplates) {
        templates.push(...handler.getResourceTemplates());
      }
    }

    return templates;
  }

  /**
   * Handle resource read request
   * @param {string} uri - Resource URI
   * @returns {object} Resource read response
   */
  async handleResourceRead(uri) {
    try {
      const handler = this.getHandlerForURI(uri);
      
      if (!handler) {
        throw new Error(`No handler found for resource URI: ${uri}`);
      }

      const content = await handler.handleResourceRead(uri);
      
      return {
        contents: [content]
      };
    } catch (error) {
      console.error(`Resource read error for ${uri}:`, error);
      
      // Return error as resource content (MCP pattern)
      return {
        contents: [{
          uri,
          mimeType: "application/json",
          text: JSON.stringify({
            error: {
              code: MCPMessageHandler.ErrorCodes.RESOURCE_NOT_FOUND,
              message: error.message,
              uri
            },
            metadata: {
              timestamp: new Date().toISOString(),
              resourceType: 'error'
            }
          }, null, 2)
        }]
      };
    }
  }

  /**
   * Handle resource subscribe request
   * @param {string} uri - Resource URI
   * @returns {object} Subscribe response
   */
  async handleResourceSubscribe(uri) {
    // Basic subscription support - could be enhanced with real-time updates
    const handler = this.getHandlerForURI(uri);
    
    if (!handler) {
      throw new Error(`No handler found for resource URI: ${uri}`);
    }

    // For now, just confirm subscription
    return {
      subscribed: true,
      uri
    };
  }

  /**
   * Get appropriate handler for a URI
   * @param {string} uri - Resource URI
   * @returns {object|null} Handler instance or null
   */
  getHandlerForURI(uri) {
    const parsed = MCPResourceURI.parse(uri);
    
    if (!parsed) {
      return null;
    }

    return this.handlers.get(parsed.type) || null;
  }

  /**
   * Add a new resource handler
   * @param {string} type - Resource type
   * @param {object} handler - Handler instance
   */
  addHandler(type, handler) {
    this.handlers.set(type, handler);
    console.log(`📚 Added resource handler for type: ${type}`);
  }

  /**
   * Remove a resource handler
   * @param {string} type - Resource type
   */
  removeHandler(type) {
    const removed = this.handlers.delete(type);
    if (removed) {
      console.log(`📚 Removed resource handler for type: ${type}`);
    }
    return removed;
  }

  /**
   * Get resource statistics
   * @returns {object} Resource registry statistics
   */
  getStatistics() {
    const stats = {
      handlers: this.handlers.size,
      templates: this.getAllResourceTemplates().length,
      types: Array.from(this.handlers.keys())
    };

    return stats;
  }

  /**
   * Validate resource URI
   * @param {string} uri - URI to validate
   * @returns {object} Validation result
   */
  validateURI(uri) {
    const parsed = MCPResourceURI.parse(uri);
    
    if (!parsed) {
      return {
        valid: false,
        error: 'Invalid URI format'
      };
    }

    const handler = this.handlers.get(parsed.type);
    
    if (!handler) {
      return {
        valid: false,
        error: `No handler for resource type: ${parsed.type}`
      };
    }

    return {
      valid: true,
      type: parsed.type,
      id: parsed.id,
      subpath: parsed.subpath
    };
  }
}

/**
 * Register MCP Resource capabilities with FastMCP server
 * @param {object} server - FastMCP server instance
 * @param {object} existingInfrastructure - Existing tool infrastructure
 * @returns {MCPResourceRegistry} Initialized resource registry
 */
export async function registerResourceCapabilities(server, existingInfrastructure = {}) {
  const registry = new MCPResourceRegistry();
  
  // Initialize with existing infrastructure
  await registry.initialize(existingInfrastructure);
  
  // Register capabilities with server
  registry.registerCapabilities(server);
  
  return registry;
}

/**
 * Create mock managers for testing/fallback
 */
export function createMockProjectManager() {
  return {
    async getProjectState({ projectId }) {
      return {
        name: `Mock Project ${projectId}`,
        description: 'Mock project for testing',
        currentPhase: 'development',
        status: 'active',
        created: new Date().toISOString(),
        configuration: {},
        metadata: {}
      };
    },

    async getCurrentTask({ projectId }) {
      return {
        id: 'mock-task-1',
        name: 'Mock Current Task',
        description: 'Mock task for testing',
        status: 'in_progress',
        priority: 'medium',
        assignee: 'system',
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        context: {}
      };
    },

    async analyzePhase({ projectId, phase }) {
      return {
        status: 'in_progress',
        progress: 50,
        deliverables: [],
        tasks: [],
        dependencies: [],
        insights: []
      };
    },

    async getDeliverables({ projectId, type }) {
      return [];
    }
  };
}

export function createMockDeliverableManager() {
  return {
    async analyzeDeliverable({ deliverableId }) {
      return {
        id: `analysis-${deliverableId}`,
        qualityScore: 85,
        completeness: 90,
        insights: ['Well structured content', 'Clear objectives'],
        recommendations: ['Add more examples', 'Include references'],
        issues: [],
        metadata: {}
      };
    },

    async getDeliverableContent({ deliverableId }) {
      return {
        name: `Mock Deliverable ${deliverableId}`,
        type: 'document',
        format: 'markdown',
        content: '# Mock Content\n\nThis is mock deliverable content.',
        size: 1024,
        created: new Date().toISOString(),
        metadata: {}
      };
    },

    async getDeliverableHistory({ deliverableId }) {
      return {
        versions: [{ version: '1.0', created: new Date().toISOString() }],
        changes: [],
        timeline: [],
        contributors: ['system'],
        metadata: {}
      };
    }
  };
}

export function createMockAnalyticsManager() {
  return {
    async extractInsights({ projectId, timeframe }) {
      return {
        trends: ['Increasing productivity', 'Stable quality metrics'],
        patterns: ['Daily peak at 10 AM', 'Weekly low on Fridays'],
        recommendations: ['Focus on morning productivity'],
        productivity: 85,
        quality: 90,
        metadata: {}
      };
    },

    async getPerformanceMetrics({ projectId, metric }) {
      return {
        overall: 85,
        throughput: 120,
        latency: 50,
        errorRate: 0.02,
        dailyTrends: [],
        metadata: {}
      };
    },

    async getQualityAssessment({ projectId, component }) {
      return {
        overall: 88,
        code: 85,
        documentation: 90,
        testing: 80,
        strengths: ['Good documentation'],
        weaknesses: ['Test coverage could be improved'],
        metadata: {}
      };
    }
  };
}

export function createMockWorkflowManager() {
  return {
    async getWorkflowDefinition({ workflowId }) {
      return {
        name: `Mock Workflow ${workflowId}`,
        description: 'Mock workflow for testing',
        version: '1.0',
        type: 'sequential',
        status: 'active',
        steps: [
          { id: 'step1', name: 'Initialize', type: 'action' },
          { id: 'step2', name: 'Process', type: 'action' },
          { id: 'step3', name: 'Finalize', type: 'action' }
        ],
        metadata: {}
      };
    },

    async getWorkflowExecution({ workflowId, executionId }) {
      return {
        status: 'completed',
        started: new Date(Date.now() - 3600000).toISOString(),
        completed: new Date().toISOString(),
        progress: 100,
        currentStep: 3,
        totalSteps: 3,
        results: { success: true },
        metadata: {}
      };
    },

    async getWorkflowMetrics({ workflowId, timeframe }) {
      return {
        totalExecutions: 150,
        successRate: 95,
        averageDuration: 1800,
        throughput: 10,
        errorRate: 0.05,
        metadata: {}
      };
    }
  };
}

export default MCPResourceRegistry;
