import React from 'react';
import { Box, Text, Newline } from 'ink';

export function VisualPreviewSection({ preview, compact = false, selected = false }) {
  if (!preview || !preview.type) {
    return null;
  }

  let previewContent;
  switch (preview.type) {
    case 'ascii_wireframe':
      previewContent = <Text>{preview.content}</Text>;
      break;
    case 'mermaid_diagram':
      previewContent = (
        <Box flexDirection="column">
          <Text bold>Mermaid Diagram Code:</Text>
          <Text>{preview.content}</Text>
          <Text color="gray">(Copy and paste into a Mermaid live editor or compatible viewer)</Text>
        </Box>
      );
      break;
    case 'html_prototype':
      previewContent = (
        <Box flexDirection="column">
          <Text bold>HTML Prototype:</Text>
          <Text>
            Path: <Text color="cyan">{preview.content}</Text>
          </Text>
          <Text color="gray">(Open this file in a web browser to view the prototype)</Text>
        </Box>
      );
      break;
    default:
      previewContent = <Text color="yellow">Unsupported preview type: {preview.type}</Text>;
  }

  return (
    <Box
      borderStyle="round"
      borderColor={selected ? 'cyan' : 'gray'}
      flexDirection="column"
      paddingX={compact ? 1 : 2}
      paddingY={compact ? 0 : 1}
      marginTop={1}
    >
      <Text bold color={selected ? 'cyan' : 'white'}>
        Visual Preview
      </Text>
      {!compact && <Newline />}
      <Box marginTop={compact ? 0 : 1}>{previewContent}</Box>
    </Box>
  );
}

export default VisualPreviewSection;