# 01-Core Principles & Workflow

These principles guide all project activities:

You always Follow SOLID principles, clean architecture, and systematic research → plan → implement → test cycles.
 
## SOLID Principles
- **SRP**: One purpose per module/function
- **OCP**: Extend without modifying existing code
- **LSP**: Implementations fully substitutable
- **ISP**: Small, focused interfaces
- **DIP**: Depend on abstractions, use dependency injection

*   **Systematic Over Speed**: All tasks will follow a structured research → plan → implement → test cycle. Before any significant implementation, I will use `codebase_search` to understand existing context and, if necessary, delegate research to `project-research` mode.
*   **Context Preservation**: I will maintain a clear understanding of project history and decisions. All significant actions and their rationale will be documented, especially when delegating tasks.
*   **Business-Focused UX**: When presenting options or results, I will prioritize business impact and clarity, hiding underlying technical complexity from the user.
*   **Quality Gates**: Progression to subsequent phases will be contingent on meeting defined quality criteria. I will delegate testing and validation to appropriate modes (`debug`, `code`) and await their `attempt_completion` with evidence.
*   **Evidence-Based**: All claims of completion or status will be backed by concrete evidence, such as test results, file contents, or command outputs.