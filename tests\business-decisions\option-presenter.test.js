import { test, expect, mock, beforeEach, afterEach, describe } from 'bun:test';
import fs from 'fs/promises';
import path from 'path';

// We'll test the actual implementation with the real framework_selection template
// since mocking ES modules is complex in Bun
describe('Option Presenter', () => {
  const mockProjectRoot = '/mock/project/root';
  let originalMkdir;
  let originalReadFile;
  let originalWriteFile;

  beforeEach(() => {
    // Store original functions
    originalMkdir = fs.mkdir;
    originalReadFile = fs.readFile;
    originalWriteFile = fs.writeFile;

    // Mock the file system operations
    fs.mkdir = mock(() => Promise.resolve());
    fs.readFile = mock(() => Promise.reject(new Error('File not found')));
    fs.writeFile = mock(() => Promise.resolve());
  });

  afterEach(() => {
    // Restore original functions
    fs.mkdir = originalMkdir;
    fs.readFile = originalReadFile;
    fs.writeFile = originalWriteFile;
  });
  
  describe('getDecisionOptions', () => {
    test('should return decision options with business-friendly descriptions', async () => {
      // Import here to avoid module loading issues
      const { getDecisionOptions } = await import('../../src/business-decisions/option-presenter.js');

      const result = await getDecisionOptions({
        context: 'framework_selection',
        userExpertiseLevel: 'novice',
        projectRoot: mockProjectRoot
      });

      // Check basic structure - using the actual framework_selection template
      expect(result.decisionId).toContain('framework_selection');
      expect(result.title).toBe('Choose Your Website Building Foundation');
      expect(result.description).toBe('Select the approach for building your website\'s user interface. This affects how quickly you can develop features and how your site will perform.');
      expect(result.options.length).toBe(4); // React, Vue, Angular, Custom

      // Check that the result has the expected structure
      expect(result.options[0].id).toBe('react');
      expect(result.options[0].title).toBe('React');

      // Check that options have the required business-friendly fields
      expect(result.options[0].id).toBe('react');
      expect(result.options[0].title).toBe('React');
      expect(result.options[0].description).toBeDefined();
      expect(result.options[0].businessImpact).toBeDefined();
      expect(result.options[0].timeImpact).toBeDefined();
      expect(result.options[0].costImpact).toBeDefined();
      expect(result.options[0].riskLevel).toBeDefined();

      // Should store decision in project context
      expect(fs.mkdir).toHaveBeenCalled();
      expect(fs.writeFile).toHaveBeenCalled();
    });

    test('should generate a custom template if none exists for the context', async () => {
      // Import here to avoid module loading issues
      const { getDecisionOptions } = await import('../../src/business-decisions/option-presenter.js');

      const result = await getDecisionOptions({
        context: 'nonexistent_context',
        userExpertiseLevel: 'novice',
        projectRoot: mockProjectRoot
      });

      // Should have generated a custom template
      expect(result.title).toContain('Nonexistent Context');
      expect(result.description).toContain('nonexistent context');
      expect(result.options.length).toBeGreaterThan(0);

      // Should have a custom option
      const customOption = result.options.find(opt => opt.isCustom);
      expect(customOption).toBeDefined();
      expect(customOption.title).toBe('Custom Choice');
    });
  });
});