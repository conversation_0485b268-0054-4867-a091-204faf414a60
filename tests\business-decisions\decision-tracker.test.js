import { test, expect, mock, beforeEach, afterEach, describe } from 'bun:test';
import fs from 'fs/promises';
import path from 'path';
import { recordDecision, getDecisionHistory } from '../../src/business-decisions/decision-tracker.js';

describe('Decision Tracker', () => {
  const mockProjectRoot = '/mock/project/root';
  let originalReadFile;
  let originalWriteFile;
  let mockDecisions;

  beforeEach(() => {
    // Create mock decisions data
    mockDecisions = {
      pendingDecisions: {
        'framework_selection_123': {
          context: 'framework_selection',
          title: 'Choose a Frontend Framework',
          description: 'Select the best frontend framework for your project',
          options: [
            {
              id: 'react',
              title: 'React',
              description: 'Business-friendly description of React',
              businessImpact: 'Good for interactive UIs',
              timeImpact: 'Medium setup time',
              costImpact: 'Free and open-source',
              riskLevel: 'low',
              isCustom: false
            },
            {
              id: 'vue',
              title: 'Vue.js',
              description: 'Business-friendly description of Vue',
              businessImpact: 'Easy to integrate with existing projects',
              timeImpact: 'Quick to get started',
              costImpact: 'Free and open-source',
              riskLevel: 'low',
              isCustom: false
            },
            {
              id: 'custom',
              title: 'Custom Choice',
              description: 'Your own custom frontend solution',
              businessImpact: 'Tailored to your specific needs',
              timeImpact: 'Depends on implementation',
              costImpact: 'Potentially higher development costs',
              riskLevel: 'medium',
              isCustom: true
            }
          ],
          recommendedOption: 'react',
          timestamp: '2025-01-28T12:00:00.000Z'
        }
      },
      completedDecisions: {}
    };

    // Store original functions
    originalReadFile = fs.readFile;
    originalWriteFile = fs.writeFile;

    // Mock the file system operations
    fs.readFile = mock(() => Promise.resolve(JSON.stringify(mockDecisions)));
    fs.writeFile = mock(() => Promise.resolve());
  });

  afterEach(() => {
    // Restore original functions
    fs.readFile = originalReadFile;
    fs.writeFile = originalWriteFile;
  });
  
  describe('recordDecision', () => {
    test('should record a decision with standard option', async () => {
      const result = await recordDecision({
        decisionId: 'framework_selection_123',
        choiceId: 'react',
        rationale: 'React has better ecosystem support for our needs',
        projectRoot: mockProjectRoot
      });
      
      // Check that the decision was recorded correctly
      expect(result.recordedDecision).toBeDefined();
      expect(result.recordedDecision.selectedOption).toBeDefined();
      expect(result.recordedDecision.selectedOption.id).toBe('react');
      expect(result.recordedDecision.rationale).toBe('React has better ecosystem support for our needs');
      expect(result.recordedDecision.isCustomChoice).toBe(false);
      
      // Check next steps
      expect(Array.isArray(result.nextSteps)).toBe(true);
      expect(result.nextSteps[0]).toContain('React');
      
      // Check that the decision was moved from pending to completed
      const writeFileCall = fs.writeFile.mock.calls[0];
      const writtenData = JSON.parse(writeFileCall[1]);
      expect(writtenData.pendingDecisions['framework_selection_123']).toBeUndefined();
      expect(writtenData.completedDecisions['framework_selection_123']).toBeDefined();
    });
    
    test('should record a decision with custom option and details', async () => {
      const customDetails = {
        frameworkName: 'SvelteKit',
        version: '2.0',
        specialFeatures: ['Server components', 'Zero bundle size']
      };
      
      const result = await recordDecision({
        decisionId: 'framework_selection_123',
        choiceId: 'custom',
        rationale: 'We need the specific features of SvelteKit',
        customDetails,
        projectRoot: mockProjectRoot
      });
      
      // Check that the custom decision was recorded correctly
      expect(result.recordedDecision.selectedOption).toBeDefined();
      expect(result.recordedDecision.selectedOption.id).toBe('custom');
      expect(result.recordedDecision.isCustomChoice).toBe(true);
      expect(result.recordedDecision.customDetails).toEqual(customDetails);
      
      // Check next steps for custom choice
      expect(result.nextSteps).toContain(
        'Since you chose a custom option, make sure to document your specific requirements.'
      );
    });
    
    test('should throw an error if the decision does not exist', async () => {
      await expect(async () => {
        await recordDecision({
          decisionId: 'nonexistent_decision',
          choiceId: 'option1',
          rationale: 'Some rationale',
          projectRoot: mockProjectRoot
        });
      }).toThrow(/not found/);
    });
    
    test('should throw an error if the option does not exist', async () => {
      await expect(async () => {
        await recordDecision({
          decisionId: 'framework_selection_123',
          choiceId: 'nonexistent_option',
          rationale: 'Some rationale',
          projectRoot: mockProjectRoot
        });
      }).toThrow(/not found/);
    });
  });
  
  describe('getDecisionHistory', () => {
    test('should return the full decision history', async () => {
      const history = await getDecisionHistory(mockProjectRoot);
      
      expect(history.pendingDecisions).toBeDefined();
      expect(history.completedDecisions).toBeDefined();
      expect(history.pendingDecisions['framework_selection_123']).toBeDefined();
    });
    
    test('should return empty history if no decisions file exists', async () => {
      // Make readFile throw to simulate missing file
      fs.readFile = mock(() => Promise.reject(new Error('File not found')));

      const history = await getDecisionHistory(mockProjectRoot);

      expect(history).toEqual({ pendingDecisions: {}, completedDecisions: {} });
    });
  });
}); 