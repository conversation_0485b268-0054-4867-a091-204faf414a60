/**
 * Legacy Model Configuration Compatibility Layer
 * 
 * This module provides backward compatibility for tests and legacy code
 * that expect the old model configuration interface. It wraps the new
 * config-manager.js and ai-providers architecture.
 */

import { configManager } from './config-manager.js';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Legacy model configuration object for backward compatibility
 */
export const modelConfig = {
  /**
   * Get model configuration for a specific role
   * @param {string} role - The role (main, analysis, research, generation, fallback)
   * @returns {Object} Model configuration
   */
  getModel(role = 'main') {
    try {
      // Get the role configuration from the config manager
      const roleConfig = configManager.getRoleConfig(role);

      // Extract the model-specific properties
      return {
        provider: roleConfig.provider,
        modelId: roleConfig.modelId,
        maxTokens: roleConfig.maxTokens || 4000,
        temperature: roleConfig.temperature || 0.7,
        description: roleConfig.description || `Model for ${role} tasks`
      };
    } catch (error) {
      console.warn(`Failed to get model config for role '${role}': ${error.message}`);

      // Fallback to main role if requested role doesn't exist
      if (role !== 'main') {
        try {
          return this.getModel('main');
        } catch (mainError) {
          // If main also fails, use ultimate fallback
        }
      }

      // Ultimate fallback
      return {
        provider: 'vertex-ai',
        modelId: 'gemini-2.5-flash-preview-05-20',
        maxTokens: 4000,
        temperature: 0.7,
        description: 'Fallback model configuration'
      };
    }
  },

  /**
   * Get API key for a provider
   * @param {string} provider - Provider name
   * @returns {string|null} API key or null if not found
   */
  getApiKey(provider) {
    const envVarMap = {
      'vertex-ai': ['GOOGLE_APPLICATION_CREDENTIALS', 'GOOGLE_CLOUD_PROJECT'],
      'google-vertex': ['GOOGLE_APPLICATION_CREDENTIALS', 'GOOGLE_CLOUD_PROJECT'],
      'anthropic': 'ANTHROPIC_API_KEY',
      'openai': 'OPENAI_API_KEY',
      'perplexity': 'PERPLEXITY_API_KEY',
      'openrouter': 'OPENROUTER_API_KEY',
      'mistral': 'MISTRAL_API_KEY'
    };

    const envVar = envVarMap[provider];
    if (!envVar) {
      return null;
    }

    // For Vertex AI, check multiple possible environment variables
    if (Array.isArray(envVar)) {
      for (const varName of envVar) {
        const value = process.env[varName];
        if (value) {
          return value;
        }
      }
      return null;
    }

    return process.env[envVar] || null;
  },

  /**
   * Validate if a provider-model combination is supported
   * @param {string} provider - Provider name
   * @param {string} modelId - Model ID
   * @returns {boolean} True if combination is valid
   */
  validateProviderModelCombination(provider, modelId) {
    if (!provider || !modelId) {
      return false;
    }

    try {
      const supportedModels = this.getSupportedModels();

      // Check if provider exists in supported models
      if (!supportedModels[provider]) {
        return false;
      }

      // For vertex-ai, check if it's a known Gemini model
      if (provider === 'vertex-ai' || provider === 'google-vertex') {
        // Check in both 'google' and 'vertex-ai' sections of supported models
        const googleModels = supportedModels['google'] || [];
        const vertexModels = supportedModels['vertex-ai'] || [];
        const allGoogleModels = [...googleModels, ...vertexModels];

        // Check if the model exists in the Google/Vertex models
        const modelExists = allGoogleModels.some(model => model.id === modelId);
        if (modelExists) {
          return true;
        }

        // Fallback: check against known Gemini model patterns
        const geminiModels = [
          'gemini-2.5-flash-preview-05-20',
          'gemini-2.5-pro-preview-05-06',
          'gemini-2.5-pro-preview-06-05',
          'gemini-1.5-flash',
          'gemini-1.5-pro'
        ];
        return geminiModels.some(model => modelId === model || modelId.includes(model.split('-').slice(0, 3).join('-')));
      }

      // For other providers, check if model exists in supported models
      const providerModels = supportedModels[provider];
      if (Array.isArray(providerModels)) {
        return providerModels.some(model => model.id === modelId);
      }

      return true; // Assume valid if we can't verify
    } catch (error) {
      console.warn(`Error validating provider-model combination: ${error.message}`);
      return false;
    }
  },

  /**
   * Validate a specific model configuration for a role
   * @param {string} role - The role to validate
   * @param {Object} options - Validation options
   * @returns {Object} Validation result
   */
  validateModel(role, options = {}) {
    const { requireApiKeys = true } = options;

    try {
      const model = this.getModel(role);
      const hasApiKey = !!this.getApiKey(model.provider);
      const isValidCombination = this.validateProviderModelCombination(model.provider, model.modelId);

      // For testing purposes, we can skip API key validation
      const isValid = requireApiKeys ? (hasApiKey && isValidCombination) : isValidCombination;

      return {
        role,
        provider: model.provider,
        modelId: model.modelId,
        hasApiKey,
        isValidCombination,
        isValid,
        issues: [
          ...(requireApiKeys && !hasApiKey ? [`Missing API key for provider: ${model.provider}`] : []),
          ...(!isValidCombination ? [`Invalid provider-model combination: ${model.provider}/${model.modelId}`] : [])
        ]
      };
    } catch (error) {
      return {
        role,
        provider: null,
        modelId: null,
        hasApiKey: false,
        isValidCombination: false,
        isValid: false,
        issues: [`Failed to validate model for role '${role}': ${error.message}`]
      };
    }
  },

  /**
   * Get supported models registry
   * @returns {Object} Supported models by provider
   */
  getSupportedModels() {
    try {
      const supportedModelsPath = path.join(process.cwd(), 'src/config/supported-models.json');
      if (fs.existsSync(supportedModelsPath)) {
        const data = fs.readFileSync(supportedModelsPath, 'utf8');
        const supportedModels = JSON.parse(data);

        // Augment with vertex-ai models if not present
        if (!supportedModels['vertex-ai']) {
          supportedModels['vertex-ai'] = [
            {
              id: 'gemini-2.5-flash-preview-05-20',
              name: 'Gemini 2.5 Flash Preview',
              allowed_roles: ['main', 'generation', 'fallback']
            },
            {
              id: 'gemini-2.5-pro-preview-05-06',
              name: 'Gemini 2.5 Pro Preview',
              allowed_roles: ['analysis', 'research']
            }
          ];
        }

        // Augment google models with our specific versions if not present
        if (supportedModels['google']) {
          const hasFlash = supportedModels['google'].some(m => m.id === 'gemini-2.5-flash-preview-05-20');
          if (!hasFlash) {
            supportedModels['google'].push({
              id: 'gemini-2.5-flash-preview-05-20',
              name: 'Gemini 2.5 Flash Preview',
              allowed_roles: ['main', 'generation', 'fallback']
            });
          }
        }

        return supportedModels;
      }
    } catch (error) {
      console.warn(`Could not load supported models: ${error.message}`);
    }

    // Fallback supported models structure
    return {
      'vertex-ai': [
        {
          id: 'gemini-2.5-flash-preview-05-20',
          name: 'Gemini 2.5 Flash Preview',
          allowed_roles: ['main', 'generation', 'fallback']
        },
        {
          id: 'gemini-2.5-pro-preview-05-06',
          name: 'Gemini 2.5 Pro Preview',
          allowed_roles: ['analysis', 'research']
        }
      ],
      'google': [
        {
          id: 'gemini-2.5-flash-preview-05-20',
          name: 'Gemini 2.5 Flash Preview',
          allowed_roles: ['main', 'generation', 'fallback']
        },
        {
          id: 'gemini-2.5-pro-preview-05-06',
          name: 'Gemini 2.5 Pro Preview',
          allowed_roles: ['analysis', 'research']
        }
      ],
      'anthropic': [
        {
          id: 'claude-3-5-sonnet-20241022',
          name: 'Claude 3.5 Sonnet',
          allowed_roles: ['main', 'analysis', 'generation', 'fallback']
        }
      ],
      'perplexity': [
        {
          id: 'sonar-pro',
          name: 'Sonar Pro',
          allowed_roles: ['research']
        }
      ],
      'mistral': [
        {
          id: 'mistral-large-latest',
          name: 'Mistral Large Latest',
          allowed_roles: ['main', 'analysis', 'generation', 'fallback']
        }
      ],
      'openrouter': [
        {
          id: 'google/gemini-2.0-flash-exp:free',
          name: 'Gemini 2.0 Flash Experimental (Free)',
          allowed_roles: ['main', 'generation']
        }
      ]
    };
  }
};

// Export for backward compatibility
export default modelConfig;
