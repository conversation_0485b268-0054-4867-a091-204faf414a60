import { test, expect, describe, beforeEach } from 'bun:test';
import { AgentDiscovery } from '../../src/agent-registry/agent-discovery.js';

describe('Tool Name Normalization Tests', () => {
  let agentDiscovery;

  beforeEach(() => {
    agentDiscovery = new AgentDiscovery();
  });

  test('should normalize alternative tool names to canonical names', () => {
    // Test with a mix of canonical and alternative names
    const toolNames = [
      'read_file',           // Canonical name
      'cat',                 // Alternative for read_file
      'get_file',            // Another alternative for read_file
      'tavily-search',       // Canonical name
      'perplexity_search',   // Alternative for tavily-search
      'web-search',          // Another alternative for tavily-search
      'git',                 // Canonical name
      'github',              // Alternative for git
      'unknown_tool'         // Not in registry
    ];
    
    const normalizedTools = agentDiscovery.normalizeToolNames(toolNames);
    
    // Verify normalization
    expect(normalizedTools).toContain('read_file');
    expect(normalizedTools).not.toContain('cat');
    expect(normalizedTools).not.toContain('get_file');
    expect(normalizedTools).toContain('tavily-search');
    expect(normalizedTools).not.toContain('perplexity_search');
    expect(normalizedTools).not.toContain('web-search');
    expect(normalizedTools).toContain('git');
    expect(normalizedTools).not.toContain('github');
    expect(normalizedTools).toContain('unknown_tool'); // Unknown tools are kept as-is
    
    // Count occurrences of canonical names (should be normalized to one instance each)
    const readFileCount = normalizedTools.filter(t => t === 'read_file').length;
    const tavilySearchCount = normalizedTools.filter(t => t === 'tavily-search').length;
    const gitCount = normalizedTools.filter(t => t === 'git').length;
    
    expect(readFileCount).toBe(3); // Original + 2 alternatives
    expect(tavilySearchCount).toBe(3); // Original + 2 alternatives
    expect(gitCount).toBe(2); // Original + 1 alternative
    
    // Total count should be preserved (9 input tools)
    expect(normalizedTools.length).toBe(9);
  });

  test('should handle case insensitive matching', () => {
    const toolNames = [
      'READ_FILE',          // Uppercase canonical name
      'Git',                // Mixed case canonical name
      'TAVILY-SEARCH',      // Uppercase canonical name
      'CAT',                // Uppercase alternative for read_file
      'perplexity_SEARCH',  // Mixed case alternative for tavily-search
      'GITHUB'              // Uppercase alternative for git
    ];
    
    const normalizedTools = agentDiscovery.normalizeToolNames(toolNames);
    
    // Should normalize to canonical names with correct casing
    expect(normalizedTools).toContain('read_file');
    expect(normalizedTools).not.toContain('READ_FILE');
    expect(normalizedTools).toContain('git');
    expect(normalizedTools).not.toContain('Git');
    expect(normalizedTools).toContain('tavily-search');
    expect(normalizedTools).not.toContain('TAVILY-SEARCH');
    
    // Count occurrences of canonical names (should be normalized to one instance each)
    const readFileCount = normalizedTools.filter(t => t === 'read_file').length;
    const gitCount = normalizedTools.filter(t => t === 'git').length;
    const tavilySearchCount = normalizedTools.filter(t => t === 'tavily-search').length;
    
    expect(readFileCount).toBe(2); // Original + 1 alternative
    expect(gitCount).toBe(2); // Original + 1 alternative
    expect(tavilySearchCount).toBe(2); // Original + 1 alternative
    
    // Total count should be preserved (6 input tools)
    expect(normalizedTools.length).toBe(6);
  });

  test('should preserve case for unknown tools', () => {
    const toolNames = ['Bash', 'SHELL', 'CMD', 'unknown_TOOL'];
    
    const normalizedTools = agentDiscovery.normalizeToolNames(toolNames);
    
    // Current behavior: case is preserved for unknown tools
    expect(normalizedTools).toContain('bash'); // Now normalized to canonical name
    expect(normalizedTools).not.toContain('Bash'); // Should be normalized
    expect(normalizedTools).toContain('bash'); // Should be normalized to canonical name
    expect(normalizedTools).not.toContain('SHELL'); // Should be normalized
    expect(normalizedTools).not.toContain('CMD'); // Should be normalized
    expect(normalizedTools).toContain('unknown_TOOL'); // This is unknown as-is
  });

  test('should handle empty tool list', () => {
    const normalizedTools = agentDiscovery.normalizeToolNames([]);
    expect(normalizedTools).toEqual([]);
  });
}); 