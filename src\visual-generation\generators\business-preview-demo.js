/**
 * Business Preview Demo
 * 
 * Demonstrates the corrected approach: business-friendly visual previews
 * that help non-technical users understand their software without technical complexity
 */

import { BusinessVisualPreviewGenerator } from './business-visual-previews.js';
import { writeFileSync } from 'fs';

async function createBusinessPreviewDemo() {
  console.log('🎯 Creating Business-Friendly Visual Preview Demo...');
  console.log('✅ Focus: Business value, not technical implementation');
  console.log('✅ Audience: Non-technical stakeholders');
  console.log('✅ Purpose: Requirement validation and communication\n');

  try {
    const generator = new BusinessVisualPreviewGenerator({
      showTechnicalDetails: false,
      focusOnBusinessValue: true,
      useSimpleLanguage: true
    });

    // Example business requirements (what a non-technical user would provide)
    const businessRequirements = {
      title: 'Customer Management System',
      description: 'Help our team manage customer information and orders',
      businessGoals: [
        'Track customer contact information',
        'Manage customer orders efficiently',
        'Improve customer service response time'
      ],
      componentSpecs: [
        {
          id: 'customer-form',
          name: 'Customer Information Form',
          type: 'form',
          description: 'Where staff enter new customer details',
          businessValue: 'Ensures we capture all necessary customer information'
        },
        {
          id: 'customer-list',
          name: 'Customer Directory',
          type: 'table',
          description: 'Shows all customers in an organized list',
          businessValue: 'Staff can quickly find any customer'
        },
        {
          id: 'order-tracking',
          name: 'Order Status Board',
          type: 'table',
          description: 'Displays current order status and progress',
          businessValue: 'Team can see which orders need attention'
        },
        {
          id: 'search-customers',
          name: 'Customer Search',
          type: 'search',
          description: 'Find specific customers quickly',
          businessValue: 'Faster customer service and support'
        }
      ],
      functionalRequirements: [
        {
          id: 'add-customer',
          title: 'Add New Customer',
          description: 'Staff can add new customer information to the system',
          businessValue: 'Grow customer database efficiently'
        },
        {
          id: 'update-orders',
          title: 'Update Order Status',
          description: 'Team can mark orders as processing, shipped, or delivered',
          businessValue: 'Keep customers informed about their orders'
        }
      ]
    };

    console.log('🔬 Generating business preview...');
    const preview = await generator.generatePreview(businessRequirements);

    console.log('✅ Business preview generated successfully!');
    console.log('📊 Preview Stats:');
    console.log('  - Business components:', preview.components.length);
    console.log('  - User flows:', preview.userFlows.length);
    console.log('  - Business values:', preview.businessValue.length);
    console.log('  - Layout sections:', preview.layout.sections.length);

    // Create business-friendly HTML report
    const businessReport = generateBusinessReport(preview);
    writeFileSync('business-preview-demo.html', businessReport, 'utf8');

    console.log('\n📄 Business Preview Report Generated:');
    console.log('  - File: business-preview-demo.html');
    console.log('  - Purpose: Stakeholder review and validation');
    console.log('  - Language: Business-friendly, no technical jargon');

    // Show sample output
    console.log('\n📋 Sample Business Descriptions:');
    preview.components.slice(0, 2).forEach(comp => {
      console.log(`\n• ${comp.name}:`);
      console.log(`  What it does: ${comp.whatItDoes}`);
      console.log(`  How users interact: ${comp.howUsersInteract}`);
      console.log(`  Business benefit: ${comp.businessBenefit}`);
    });

    console.log('\n🔄 Sample User Flow:');
    if (preview.userFlows.length > 0) {
      const flow = preview.userFlows[0];
      console.log(`• ${flow.name}: ${flow.description}`);
      flow.steps.forEach((step, index) => {
        console.log(`  ${index + 1}. ${step.action} → ${step.result}`);
      });
    }

    console.log('\n🎯 Next Steps for Business Users:');
    preview.nextSteps.forEach((step, index) => {
      console.log(`  ${index + 1}. ${step}`);
    });

    console.log('\n✅ CORRECTED APPROACH DEMONSTRATED:');
    console.log('  ✅ Business language, not technical terms');
    console.log('  ✅ Focus on user actions and business value');
    console.log('  ✅ Simple layout descriptions, not CSS classes');
    console.log('  ✅ Stakeholder communication, not developer specs');
    console.log('  ✅ Requirement validation, not technical prototypes');

    return preview;

  } catch (error) {
    console.error('❌ Business preview demo failed:', error);
    throw error;
  }
}

/**
 * Generate business-friendly HTML report
 */
function generateBusinessReport(preview) {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${preview.title} - Business Preview</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      color: #333;
    }
    .header {
      background: #f8f9fa;
      padding: 2rem;
      border-radius: 8px;
      margin-bottom: 2rem;
      text-align: center;
    }
    .section {
      margin-bottom: 2rem;
      padding: 1.5rem;
      border: 1px solid #e9ecef;
      border-radius: 8px;
    }
    .component {
      background: #fff;
      padding: 1rem;
      margin: 1rem 0;
      border-left: 4px solid #007bff;
      border-radius: 4px;
    }
    .flow-step {
      padding: 0.5rem;
      margin: 0.5rem 0;
      background: #f8f9fa;
      border-radius: 4px;
    }
    .business-value {
      color: #28a745;
      font-weight: 500;
    }
    .next-steps {
      background: #e7f3ff;
      padding: 1.5rem;
      border-radius: 8px;
      border-left: 4px solid #007bff;
    }
    h1, h2, h3 { color: #2c3e50; }
    .tag { 
      background: #007bff; 
      color: white; 
      padding: 0.25rem 0.5rem; 
      border-radius: 4px; 
      font-size: 0.875rem; 
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>${preview.title}</h1>
    <p>${preview.description}</p>
    <span class="tag">Business Preview</span>
  </div>

  <div class="section">
    <h2>🎯 Business Value</h2>
    <p>This software will help your business by:</p>
    <ul>
      ${preview.businessValue.map(value => `<li>${value}</li>`).join('')}
    </ul>
  </div>

  <div class="section">
    <h2>📋 What Users Will See and Do</h2>
    ${preview.components.map(comp => `
      <div class="component">
        <h3>${comp.name}</h3>
        <p><strong>What it does:</strong> ${comp.whatItDoes}</p>
        <p><strong>How users interact:</strong> ${comp.howUsersInteract}</p>
        <p class="business-value"><strong>Business benefit:</strong> ${comp.businessBenefit}</p>
        <p><em>Example:</em> ${comp.example}</p>
      </div>
    `).join('')}
  </div>

  <div class="section">
    <h2>🔄 How Users Will Use the System</h2>
    ${preview.userFlows.map(flow => `
      <div>
        <h3>${flow.name}</h3>
        <p>${flow.description}</p>
        ${flow.steps.map((step, index) => `
          <div class="flow-step">
            <strong>Step ${index + 1}:</strong> ${step.action} → ${step.result}
            <br><span class="business-value">Value: ${step.businessValue}</span>
          </div>
        `).join('')}
      </div>
    `).join('')}
  </div>

  <div class="section">
    <h2>🏗️ Simple Layout Overview</h2>
    <p>${preview.layout.description}</p>
    ${preview.layout.sections.map(section => `
      <div>
        <h3>${section.name}</h3>
        <p>${section.purpose}</p>
        <ul>
          ${section.components.map(comp => `
            <li><strong>${comp.name}:</strong> ${comp.purpose} (${comp.location})</li>
          `).join('')}
        </ul>
      </div>
    `).join('')}
  </div>

  <div class="next-steps">
    <h2>🚀 Next Steps</h2>
    <ol>
      ${preview.nextSteps.map(step => `<li>${step}</li>`).join('')}
    </ol>
    <p><strong>Remember:</strong> This is just a preview to help you validate your requirements. 
    Once approved, our AI agents will build the actual working software for you!</p>
  </div>

  <footer style="text-align: center; margin-top: 3rem; padding-top: 2rem; border-top: 1px solid #e9ecef; color: #6c757d;">
    <p>Generated by Guidant Business Preview System</p>
    <p>Focus: Business validation, not technical implementation</p>
  </footer>
</body>
</html>`;
}

// Run the demo
if (import.meta.url === `file://${process.argv[1]}`) {
  createBusinessPreviewDemo()
    .then(() => {
      console.log('\n🎉 Business Preview Demo Complete!');
      console.log('🌐 Open business-preview-demo.html to see the business-friendly preview');
      console.log('✅ This is the CORRECTED approach aligned with Guidant\'s purpose');
    })
    .catch(error => {
      console.error('❌ Demo failed:', error);
      process.exit(1);
    });
}
