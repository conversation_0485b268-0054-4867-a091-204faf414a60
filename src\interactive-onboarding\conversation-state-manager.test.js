import { describe, test, expect, beforeEach, mock } from "bun:test";
import { ConversationStateManager } from "./conversation-state-manager.js";
import fs from "fs";

// Create mock functions
const mockSaveSession = mock(() => Promise.resolve());
const mockGetSession = mock(() => Promise.resolve({}));
const mockListSessions = mock(() => Promise.resolve([]));
const mockCreateSession = mock(() => Promise.resolve({}));
const mockUpdateSession = mock(() => Promise.resolve({}));
const mockUpdateRecoveryPrompt = mock(() => Promise.resolve({}));
const mockUpdateLastMessage = mock(() => Promise.resolve({}));
const mockUpdateOrientation = mock(() => Promise.resolve({}));

// Mock the session recovery manager module
mock.module("../workflow-logic/session-recovery-manager.js", () => {
  return {
    getSessionRecoveryManager: () => ({
      saveSession: mockSaveSession,
      getSession: mockGetSession,
      listSessions: mockListSessions,
      createSession: mockCreateSession,
      updateSession: mockUpdateSession,
      updateRecoveryPrompt: mockUpdateRecoveryPrompt,
      updateLastMessage: mockUpdateLastMessage,
      updateOrientation: mockUpdateOrientation
    })
  };
});

describe("ConversationStateManager", () => {
  let stateManager;
  let projectRoot = "/test/project";
  
  beforeEach(() => {
    stateManager = new ConversationStateManager(projectRoot);
  });
  
  test("should initialize with default state", () => {
    expect(stateManager.projectRoot).toBe(projectRoot);
    expect(stateManager.sessionRecoveryManager).toBeDefined();
  });
  
  test("should initialize a new session", async () => {
    const options = { 
      projectIdea: "Test Project",
      projectType: "e-commerce"
    };
    
    // Mock fs.existsSync and fs.mkdirSync
    const originalExistsSync = fs.existsSync;
    const originalMkdirSync = fs.mkdirSync;
    const originalWriteFileSync = fs.writeFileSync;
    
    global.fs = {
      existsSync: mock(() => true),
      mkdirSync: mock(() => {}),
      writeFileSync: mock(() => {})
    };
    
    const session = await stateManager.initSession(options);
    
    expect(session).toBeDefined();
    expect(session.conversationData).toBeDefined();
    expect(session.conversationData.projectIdea).toBe(options.projectIdea);
    expect(session.conversationData.projectType).toBe(options.projectType);
    
    // Restore original fs functions
    global.fs.existsSync = originalExistsSync;
    global.fs.mkdirSync = originalMkdirSync;
    global.fs.writeFileSync = originalWriteFileSync;
  });
  
  test("should record an answer", async () => {
    const sessionId = "test-session";
    const questionId = "project-name";
    const answer = "Updated Project Name";
    
    // Mock getSession to return a session with conversationData
    mockGetSession.mockImplementationOnce(() => Promise.resolve({
      sessionId,
      conversationData: {
        id: sessionId,
        answers: {},
        questionHistory: [],
        skippedQuestions: [],
        navigationHistory: []
      }
    }));
    
    // Mock fs.writeFileSync
    const originalWriteFileSync = fs.writeFileSync;
    global.fs = {
      ...global.fs,
      writeFileSync: mock(() => {})
    };
    
    const updatedSession = await stateManager.recordAnswer(sessionId, questionId, answer);
    
    expect(updatedSession).toBeDefined();
    expect(updatedSession.conversationData.answers[questionId]).toBeDefined();
    expect(updatedSession.conversationData.answers[questionId].value).toBe(answer);
    
    // Restore original fs.writeFileSync
    global.fs.writeFileSync = originalWriteFileSync;
  });
  
  test("should complete a conversation", async () => {
    const sessionId = "test-session";
    
    // Mock getSession to return a session with conversationData
    mockGetSession.mockImplementationOnce(() => Promise.resolve({
      sessionId,
      conversationData: {
        id: sessionId,
        state: "in_progress",
        answers: {},
        questionHistory: [],
        skippedQuestions: [],
        navigationHistory: []
      }
    }));
    
    // Mock fs.writeFileSync
    const originalWriteFileSync = fs.writeFileSync;
    global.fs = {
      ...global.fs,
      writeFileSync: mock(() => {})
    };
    
    const completedSession = await stateManager.completeConversation(sessionId);
    
    expect(completedSession).toBeDefined();
    expect(completedSession.conversationData.state).toBe("completed");
    
    // Restore original fs.writeFileSync
    global.fs.writeFileSync = originalWriteFileSync;
  });
}); 