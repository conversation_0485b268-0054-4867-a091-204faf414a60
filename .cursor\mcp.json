{"mcpServers": {"tavily": {"command": "node", "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\tavily-mcp\\build\\index.js"], "env": {"TAVILY_API_KEY": "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"}}, "firecrawl": {"command": "node", "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\firecrawl-mcp\\dist\\index.js"], "env": {"FIRECRAWL_API_KEY": "fc-21ba33775c41473bbd81a685cbbe08ad"}}}, "node": {"path": "C:\\Program Files\\nodejs\\node.exe", "env": {}}}