/**
 * @file transition-rules.js
 * @description Defines the rules and constraints for phase transitions.
 * This centralized configuration makes the transition logic in the engine cleaner
 * and easier to manage.
 */

import { PHASE_IDS } from './phase-definitions.js';

/**
 * Defines the standard linear progression of phases.
 */
const FORWARD_PROGRESSION = {
  [PHASE_IDS.CONCEPT]: PHASE_IDS.REQUIREMENTS,
  [PHASE_IDS.REQUIREMENTS]: PHASE_IDS.DESIGN,
  [PHASE_IDS.DESIGN]: PHASE_IDS.ARCHITECTURE,
  [PHASE_IDS.ARCHITECTURE]: PHASE_IDS.IMPLEMENTATION,
  [PHASE_IDS.IMPLEMENTATION]: PHASE_IDS.DEPLOYMENT,
  [PHASE_IDS.DEPLOYMENT]: null, // End of the line
};

/**
 * Defines the rules for different types of transitions.
 */
export const TRANSITION_RULES = {
  /**
   * Standard forward movement.
   * @param {string} fromPhase - The phase transitioning from.
   * @param {string} toPhase - The phase transitioning to.
   * @returns {boolean} - True if the transition is a valid forward step.
   */
  FORWARD: (fromPhase, toPhase) => {
    return FORWARD_PROGRESSION[fromPhase] === toPhase;
  },

  /**
   * Allows moving backward in the process for iteration.
   * @param {string} fromPhase - The phase transitioning from.
   * @param {string} toPhase - The phase transitioning to.
   * @returns {boolean} - True if `toPhase` occurred before `fromPhase`.
   */
  BACKWARD: (fromPhase, toPhase) => {
    const fromIndex = Object.values(PHASE_IDS).indexOf(fromPhase);
    const toIndex = Object.values(PHASE_IDS).indexOf(toPhase);
    // Allow moving to any previous phase, but not the same one.
    return toIndex < fromIndex;
  },

  /**
   * Allows skipping phases for rapid prototyping.
   * For now, we allow skipping from concept to implementation directly.
   * @param {string} fromPhase - The phase transitioning from.
   * @param {string} toPhase - The phase transitioning to.
   * @returns {boolean} - True if the skip is allowed.
   */
  SKIP: (fromPhase, toPhase) => {
    const allowedSkips = {
      [PHASE_IDS.CONCEPT]: [PHASE_IDS.IMPLEMENTATION],
    };
    return allowedSkips[fromPhase]?.includes(toPhase) || false;
  },

  /**
   * Allows jumping to a phase in an emergency (e.g., critical bug fix).
   * For now, allows jumping from any phase to implementation.
   * @param {string} fromPhase - The phase transitioning from.
   * @param {string} toPhase - The phase transitioning to.
   * @returns {boolean} - True if the emergency jump is allowed.
   */
  EMERGENCY: (fromPhase, toPhase) => {
    return toPhase === PHASE_IDS.IMPLEMENTATION && fromPhase !== toPhase;
  },
  
  /**
   * Rules for parallel work.
   * Allows moving from 'requirements' to either 'design' or 'architecture'
   * to enable concurrent work on these two phases.
   * @param {string} fromPhase - The phase transitioning from.
   * @param {string} toPhase - The phase transitioning to.
   * @returns {boolean} - True if the parallel transition is allowed.
   */
  PARALLEL: (fromPhase, toPhase) => {
    if (fromPhase === PHASE_IDS.REQUIREMENTS) {
      return toPhase === PHASE_IDS.DESIGN || toPhase === PHASE_IDS.ARCHITECTURE;
    }
    return false;
  },
}; 