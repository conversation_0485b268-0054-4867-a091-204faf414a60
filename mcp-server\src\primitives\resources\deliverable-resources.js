/**
 * Deliverable Resources Handler
 * Handles MCP Resource requests for deliverable-related data
 */

import { URITemplateParser, MCPResourceURI } from '../../utils/uri-template-parser.js';
import { RESOURCE_TEMPLATES } from '../../schemas/resource-schemas.js';

/**
 * Deliverable Resource Handler
 * Converts existing deliverable analysis tools to MCP Resources
 */
export class DeliverableResourceHandler {
  constructor(existingDeliverableManager) {
    this.deliverableManager = existingDeliverableManager;
    this.templates = {
      'deliverable_analysis': RESOURCE_TEMPLATES.deliverable_analysis,
      'deliverable_content': RESOURCE_TEMPLATES.deliverable_content,
      'deliverable_history': RESOURCE_TEMPLATES.deliverable_history
    };
  }

  /**
   * Get resource templates for this handler
   * @returns {Array} Array of resource templates
   */
  getResourceTemplates() {
    return Object.values(this.templates);
  }

  /**
   * Handle resource read request
   * @param {string} uri - Resource URI
   * @returns {object} Resource content
   */
  async handleResourceRead(uri) {
    const parsed = MCPResourceURI.parse(uri);
    if (!parsed || parsed.type !== 'deliverable') {
      throw new Error(`Invalid deliverable resource URI: ${uri}`);
    }

    const { id: deliverableId, subpath } = parsed;

    try {
      switch (subpath) {
        case 'analysis':
          return await this.getDeliverableAnalysis(deliverableId, uri);
        case 'content':
          return await this.getDeliverableContent(deliverableId, uri);
        case 'history':
          return await this.getDeliverableHistory(deliverableId, uri);
        default:
          throw new Error(`Unknown deliverable resource path: ${subpath}`);
      }
    } catch (error) {
      throw new Error(`Failed to read deliverable resource: ${error.message}`);
    }
  }

  /**
   * Get deliverable analysis data
   * @param {string} deliverableId - Deliverable ID
   * @param {string} uri - Resource URI
   * @returns {object} Deliverable analysis resource
   */
  async getDeliverableAnalysis(deliverableId, uri) {
    try {
      // Use existing deliverable analysis tool
      const analysis = await this.deliverableManager.analyzeDeliverable({ 
        deliverableId 
      });
      
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          deliverableId,
          analysisId: analysis.id || `analysis-${Date.now()}`,
          timestamp: analysis.timestamp || new Date().toISOString(),
          quality_score: analysis.qualityScore || 0,
          completeness: analysis.completeness || 0,
          insights: analysis.insights || [],
          recommendations: analysis.recommendations || [],
          issues: analysis.issues || [],
          metrics: {
            readability: analysis.readability || 0,
            accuracy: analysis.accuracy || 0,
            relevance: analysis.relevance || 0,
            structure: analysis.structure || 0
          },
          metadata: {
            resourceType: 'deliverable_analysis',
            version: '1.0',
            analyzer: analysis.analyzer || 'system',
            lastAccessed: new Date().toISOString(),
            ...analysis.metadata
          }
        }, null, 2)
      };
    } catch (error) {
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          deliverableId,
          analysisId: `error-${Date.now()}`,
          timestamp: new Date().toISOString(),
          quality_score: 0,
          completeness: 0,
          insights: [],
          recommendations: ['Unable to analyze deliverable'],
          issues: [{ type: 'error', message: error.message }],
          metrics: {},
          metadata: {
            resourceType: 'deliverable_analysis',
            version: '1.0',
            error: error.message,
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    }
  }

  /**
   * Get deliverable content data
   * @param {string} deliverableId - Deliverable ID
   * @param {string} uri - Resource URI
   * @returns {object} Deliverable content resource
   */
  async getDeliverableContent(deliverableId, uri) {
    try {
      const content = await this.deliverableManager.getDeliverableContent({ 
        deliverableId 
      });
      
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          deliverableId,
          name: content.name || 'Unknown Deliverable',
          type: content.type || 'unknown',
          format: content.format || 'text',
          size: content.size || 0,
          content: content.content || '',
          attachments: content.attachments || [],
          tags: content.tags || [],
          created: content.created || new Date().toISOString(),
          updated: content.updated || new Date().toISOString(),
          version: content.version || '1.0',
          metadata: {
            resourceType: 'deliverable_content',
            version: '1.0',
            encoding: content.encoding || 'utf-8',
            checksum: content.checksum,
            lastAccessed: new Date().toISOString(),
            ...content.metadata
          }
        }, null, 2)
      };
    } catch (error) {
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          deliverableId,
          name: 'Content Not Available',
          type: 'error',
          format: 'text',
          size: 0,
          content: '',
          attachments: [],
          tags: [],
          created: new Date().toISOString(),
          updated: new Date().toISOString(),
          version: '1.0',
          metadata: {
            resourceType: 'deliverable_content',
            version: '1.0',
            error: error.message,
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    }
  }

  /**
   * Get deliverable history data
   * @param {string} deliverableId - Deliverable ID
   * @param {string} uri - Resource URI
   * @returns {object} Deliverable history resource
   */
  async getDeliverableHistory(deliverableId, uri) {
    try {
      const history = await this.deliverableManager.getDeliverableHistory({ 
        deliverableId 
      });
      
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          deliverableId,
          versions: history.versions || [],
          changes: history.changes || [],
          timeline: history.timeline || [],
          contributors: history.contributors || [],
          statistics: {
            totalVersions: history.versions?.length || 0,
            totalChanges: history.changes?.length || 0,
            firstCreated: history.firstCreated,
            lastModified: history.lastModified,
            averageChangeSize: history.averageChangeSize || 0
          },
          metadata: {
            resourceType: 'deliverable_history',
            version: '1.0',
            trackingEnabled: history.trackingEnabled || false,
            lastAccessed: new Date().toISOString(),
            ...history.metadata
          }
        }, null, 2)
      };
    } catch (error) {
      return {
        uri,
        mimeType: "application/json",
        text: JSON.stringify({
          deliverableId,
          versions: [],
          changes: [],
          timeline: [],
          contributors: [],
          statistics: {
            totalVersions: 0,
            totalChanges: 0,
            firstCreated: null,
            lastModified: null,
            averageChangeSize: 0
          },
          metadata: {
            resourceType: 'deliverable_history',
            version: '1.0',
            error: error.message,
            lastAccessed: new Date().toISOString()
          }
        }, null, 2)
      };
    }
  }

  /**
   * Check if this handler can handle a given URI
   * @param {string} uri - URI to check
   * @returns {boolean} True if this handler can process the URI
   */
  canHandle(uri) {
    const parsed = MCPResourceURI.parse(uri);
    return parsed && parsed.type === 'deliverable';
  }
}

export default DeliverableResourceHandler;
