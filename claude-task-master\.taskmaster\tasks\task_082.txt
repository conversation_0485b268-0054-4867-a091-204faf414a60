# Task ID: 82
# Title: Update supported-models.json with token limit fields
# Status: pending
# Dependencies: None
# Priority: high
# Description: Modify the supported-models.json file to include contextWindowTokens and maxOutputTokens fields for each model, replacing the ambiguous max_tokens field.
# Details:
For each model entry in supported-models.json:
1. Add `contextWindowTokens` field representing the total context window (input + output tokens)
2. Add `maxOutputTokens` field representing the maximum tokens the model can generate
3. Remove or deprecate the ambiguous `max_tokens` field if present

Research and populate accurate values for each model from official documentation:
- For OpenAI models (e.g., gpt-4o): contextWindowTokens=128000, maxOutputTokens=16384
- For Anthropic models (e.g., Claude 3.7): contextWindowTokens=200000, maxOutputTokens=8192
- For other providers, find official documentation or use reasonable defaults

Example entry:
```json
{
  "id": "claude-3-7-sonnet-20250219",
  "swe_score": 0.623,
  "cost_per_1m_tokens": { "input": 3.0, "output": 15.0 },
  "allowed_roles": ["main", "fallback"],
  "contextWindowTokens": 200000,
  "maxOutputTokens": 8192
}
```

# Test Strategy:
1. Validate JSON syntax after changes
2. Verify all models have the new fields with reasonable values
3. Check that the values align with official documentation from each provider
4. Ensure backward compatibility by maintaining any fields other systems might depend on
